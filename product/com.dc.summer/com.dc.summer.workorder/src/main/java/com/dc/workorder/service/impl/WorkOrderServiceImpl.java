package com.dc.workorder.service.impl;

import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.repository.mysql.column.SqlScript;
import com.dc.repository.mysql.mapper.OrderExecuteMapper;
import com.dc.repository.mysql.mapper.OrderExecuteResultMapper;
import com.dc.repository.mysql.mapper.OrderSchemaTaskMapper;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderExecute;
import com.dc.repository.mysql.model.OrderExecuteResult;
import com.dc.repository.mysql.model.OrderSchemaTask;
import com.dc.repository.mysql.service.OrderService;
import com.dc.springboot.core.model.exception.IdempotentException;
import com.dc.springboot.core.model.type.OrderCurrentStatus;
import com.dc.springboot.core.model.type.OrderTypeKey;
import com.dc.springboot.core.model.type.SqlScriptType;
import com.dc.springboot.core.model.workorder.*;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.model.type.WebAsyncTaskType;
import com.dc.summer.service.ExecuteService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.service.sql.WebSQLContextStatus;
import com.dc.workorder.model.type.ExecuteCommitType;
import com.dc.workorder.model.type.OrderExecuteStatus;
import com.dc.workorder.model.type.OrderScriptExecuteStatus;
import com.dc.workorder.service.execute.OrderExecuteResultService;
import com.dc.workorder.service.execute.WorkOrderExecuteService;
import com.dc.workorder.utils.SqlScriptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkOrderServiceImpl extends AbstractWorkOrderService {

    @Resource
    private OrderExecuteResultService executeResultService;

    @Resource
    private WorkOrderExecuteService workOrderExecute;

    @Resource
    private OrderExecuteResultMapper orderExecuteResultMapper;

    @Resource
    private OrderService orderService;

    @Resource
    private ExecuteService executeService;

    @Resource
    private OrderExecuteMapper orderExecuteMapper;

    @Resource
    private OrderSchemaTaskMapper orderSchemaTaskMapper;

    @Resource
    private SummerMapper summerMapper;

    @Override
    public Runnable asyncExecuteOrder(ExecuteOrderMessage message) {

        ExecuteOrderModel executeOrderModel = message.getExecuteOrderModel();

        if (!waitSet.add(executeOrderModel.getId())) {
            throw new IdempotentException("工单正在执行中");
        }

        return () -> super.executing(executeOrderModel, (order, orderExecute, orderTypeKey) -> {

            OrderApplyContent applyContent = order.getApply_content();

            List<String> scriptNames = getScriptNames(executeOrderModel, orderTypeKey, applyContent);

            boolean isSuccess = true;
            boolean interrupted = false;
            OrderScriptExecuteStatus status = OrderScriptExecuteStatus.success;

            //遍历执行脚本
            for (String scriptName : scriptNames) {

                if ((isSuccess || ExecuteCommitType.ERROR_CONTINUE == ExecuteCommitType.of(applyContent.getExecute_submit_way())) && !interrupted) {
                    updateOrderExecuteStatus(orderExecute, scriptName, OrderScriptExecuteStatus.executing, message.getExecuteOrderModel().getSqlScriptType());

                    //计算当前脚本执行耗时
                    long executeStart = System.currentTimeMillis();

                    try {
                        OrderScriptExecuteStatus orderExecuteStatus = workOrderExecute.runProducerAndConsumer(message, order, orderExecute, orderTypeKey, scriptName);
                        updateOrderExecuteStatus(orderExecute, scriptName, orderExecuteStatus, message.getExecuteOrderModel().getSqlScriptType());
                        status = orderExecuteStatus;
                        isSuccess = orderExecuteStatus == OrderScriptExecuteStatus.success;
                        interrupted = orderExecuteStatus == OrderScriptExecuteStatus.execute_interrupted;
                    } catch (Exception e) {
                        updateOrderExecuteStatus(orderExecute, scriptName, OrderScriptExecuteStatus.fail, message.getExecuteOrderModel().getSqlScriptType());
                        updateOrderExecuteFailReason(orderExecute, e.getMessage());
                        isSuccess = false;
                        status = OrderScriptExecuteStatus.fail;
                        log.error("执行工单生产者和消费者失败！", e);
                    } finally {
                        //获取每个脚本运行时间，更新累计总耗时
                        long executeEnd = System.currentTimeMillis();
                        updateOrderExecuteTime(orderExecute, scriptName, executeStart, executeEnd, message.getExecuteOrderModel().getSqlScriptType());
                        log.info("script name:[{}]执行总耗时：{}", scriptName, executeEnd - executeStart);
                    }
                } else {
                    updateOrderExecuteStatus(orderExecute, scriptName, OrderScriptExecuteStatus.execute_pause, message.getExecuteOrderModel().getSqlScriptType());
                }

            }

            return status;
        });
    }

    @Override
    public Runnable asyncExecuteOrderBatch(ExecuteOrderBatchMessage message) {

        ExecuteOrderModel executeOrderModel = message.getExecuteOrderModel();
        String schemaId = message.getSchema_list().stream().map(SchemaInfo::getSchema_id).collect(Collectors.joining("-")) + executeOrderModel.getName();

        if (!waitSetBatch.add(executeOrderModel.getId() + schemaId)) {
            throw new IdempotentException("工单正在执行中");
        }

        return () -> super.executingForBatch(message, schemaId, (order, pair, orderTypeKey) -> {

            OrderSchemaTask orderSchemaTask = pair.getFirst();

            OrderExecute orderExecute = orderSchemaTask.getOrderExecute();

            OrderApplyContent applyContent = order.getApply_content();

            List<String> scriptNames = getScriptNames(executeOrderModel, orderTypeKey, applyContent);

            boolean isSuccess = true;
            boolean interrupted = false;
            OrderScriptExecuteStatus status = OrderScriptExecuteStatus.success;

            //遍历执行脚本
            for (String scriptName : scriptNames) {

                if ((isSuccess || ExecuteCommitType.ERROR_CONTINUE == ExecuteCommitType.of(applyContent.getExecute_submit_way())) && !interrupted) {
                    updateOrderExecuteStatus(orderExecute, scriptName, OrderScriptExecuteStatus.executing, message.getExecuteOrderModel().getSqlScriptType());

                    //计算当前脚本执行耗时
                    long executeStart = System.currentTimeMillis();

                    try {
                        OrderScriptExecuteStatus orderExecuteStatus = workOrderExecute.runConsumerAndConsumerBatch(message, order, orderExecute, orderTypeKey, scriptName, pair);
                        updateOrderExecuteStatus(orderExecute, scriptName, orderExecuteStatus, message.getExecuteOrderModel().getSqlScriptType());
                        status = orderExecuteStatus;
                        isSuccess = orderExecuteStatus == OrderScriptExecuteStatus.success;
                        interrupted = orderExecuteStatus == OrderScriptExecuteStatus.execute_interrupted;
                    } catch (Exception e) {
                        updateOrderExecuteStatus(orderExecute, scriptName, OrderScriptExecuteStatus.fail, message.getExecuteOrderModel().getSqlScriptType());
                        updateOrderExecuteFailReason(orderExecute, scriptName, e.getMessage());
                        isSuccess = false;
                        status = OrderScriptExecuteStatus.fail;
                        log.error("执行工单生产者和消费者失败！", e);
                    } finally {
                        //获取每个脚本运行时间，更新累计总耗时
                        long executeEnd = System.currentTimeMillis();
                        updateOrderExecuteTime(orderExecute, scriptName, executeStart, executeEnd, message.getExecuteOrderModel().getSqlScriptType());
                        log.info("script name:[{}]执行总耗时：{}", scriptName, executeEnd - executeStart);
                    }
                } else {
                    updateOrderExecuteStatus(orderExecute, scriptName, OrderScriptExecuteStatus.execute_pause, message.getExecuteOrderModel().getSqlScriptType());
                }

            }

            return status;
        });
    }

    @Override
    public void interruptWorkOrder(WorkOrderInterruptMessage message) {
        Integer orderId = message.getOrderId();
        Order order = orderService.getById(orderId);

        //设置工单状态 中断中
        order.setCurrent_status(OrderCurrentStatus.INTERRUPTING.getValue());
        int updated = orderService.updateStatus(order, OrderCurrentStatus.IN_EXECUTION.getValue());

        //由于执行回退脚本不会影响工单执行状态，所以增加判断
        if (updated <= 0 && orderService.existsOrderExecute(orderId) == 0) {
            log.warn("工单: {},状态设置【中断中】失败", orderId);
            return;
        }

        int interrupted = 0b00;

        // 存在工单生产者上下文已被关闭但消费者还未结束场景
        try {
            //设置producer 中断状态
            String producerToken = order.getProducer_token();
            WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(producerToken);
            contextInfo.setStatus(WebSQLContextStatus.INTERRUPTED);
            contextInfo.getAsyncTasks().forEach(asyncTask -> asyncTask.setStatus(WebAsyncTaskType.INTERRUPTED));
        } catch (Exception e) {
            interrupted |= 0b01;
            log.warn("中断生产者异常: {}", e.getMessage());
            log.debug("中断生产者异常", e);
        }

        //设置consumer 中断状态
        try {
            List<String> consumerTokenList = Arrays.asList(order.getConsumer_token_list().split(","));
            consumerTokenList.forEach(token -> {
                WebSQLContextInfo consumerContext = WebSQLContextInfo.getSimpleContext(token);

                consumerContext.setStatus(WebSQLContextStatus.INTERRUPTED);
                executeService.interruptConnection(consumerContext);
            });
        } catch (Exception e) {
            interrupted |= 0b10;
            log.warn("中断消费者异常: {}", e.getMessage());
            log.debug("中断消费者异常", e);
        }

        if (interrupted == 0b11) {
            OrderExecute orderExecute = orderExecuteMapper.getOrderExecuteByOrderId(orderId);
            orderExecute.setCurrent_status(OrderExecuteStatus.INTERRUPTED.getValue());
            updateOrderExecuteStatus(orderExecute, null, OrderScriptExecuteStatus.execute_pause, null);
            orderExecuteMapper.updateExecuteStatusByOrderId(orderExecute);
            order.setCurrent_status(OrderCurrentStatus.INTERRUPTED.getValue());
            orderService.updateStatus(order);
        }
    }

    @Override
    public void interruptWorkOrderBatch(WorkOrderInterruptBatchMessage message) {
        Integer orderId = message.getOrderId();
        Order order = orderService.getById(orderId);

        // 设置状态为中断中
        List<OrderExecute> records = orderExecuteMapper.getOrderExecuteListByOrderId(orderId);
        int total = records.size();
        int interruptedCnt = 0;
        int successCnt = 0;
        for (OrderExecute execute : records) {
            if (OrderExecuteStatus.of(execute.getCurrent_status()) == OrderExecuteStatus.INTERRUPTED) {
                interruptedCnt++;
            } else if (OrderExecuteStatus.of(execute.getCurrent_status()) == OrderExecuteStatus.EXECUTE_SUCCESS) {
                successCnt++;
            }
        }

        if (interruptedCnt + successCnt == total - 1) {
            order.setCurrent_status(OrderCurrentStatus.INTERRUPTING.getValue());
            int updated = orderService.updateStatus(order, OrderCurrentStatus.IN_EXECUTION.getValue());

            //由于执行回退脚本不会影响工单执行状态，所以增加判断
            if (updated < 0 && orderService.existsOrderExecute(orderId) == 0) {
                log.warn("工单: {},状态设置【中断中】失败", orderId);
                return;
            }
        }



        OrderSchemaTask task = orderSchemaTaskMapper.getRecord(orderId, message.getSchemaId());
        if (orderExecuteMapper.updateInterruptStatus(task.getId(), OrderExecuteStatus.INTERRUPTED.getValue()) > 0) {
            log.info("修改当前schema状态为已中断， schemaId: " + message.getSchemaId());
        }
        // 存在工单生产者上下文已被关闭但消费者还未结束场景

        try {
            //设置producer 中断状态
            String producerToken = task.getProducer_token();
            WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(producerToken);
            contextInfo.setStatus(WebSQLContextStatus.INTERRUPTED);
            contextInfo.getAsyncTasks().forEach(asyncTask -> asyncTask.setStatus(WebAsyncTaskType.INTERRUPTED));
        } catch (Exception e) {
            log.warn("中断生产者异常: {}", e.getMessage());
            log.debug("中断生产者异常", e);
        }

        //设置consumer 中断状态
        try {
            List<String> consumerTokenList = Arrays.asList(task.getConsumer_token_list().split(","));
            consumerTokenList.forEach(token -> {
                WebSQLContextInfo consumerContext = WebSQLContextInfo.getSimpleContext(token);

                consumerContext.setStatus(WebSQLContextStatus.INTERRUPTED);
                executeService.interruptConnection(consumerContext);
            });
        } catch (Exception e) {
            log.warn("中断消费者异常: {}", e.getMessage());
            log.debug("中断消费者异常", e);
        }

    }

    private void updateOrderExecuteTime(OrderExecute orderExecute, String scriptName, long executeStart, long executeEnd, SqlScriptType sqlScriptType) {
        long totalTime = executeEnd - executeStart;
        OrderExecuteResult orderExecuteResult = new OrderExecuteResult();
        orderExecuteResult.setOrder_execute_id(orderExecute.getId());
        orderExecuteResult.setScript_name(scriptName);
        orderExecuteResult.setType(sqlScriptType.getValue());
        orderExecuteResult = orderExecuteResultMapper.getOrderExecuteResult(orderExecuteResult);
        if (orderExecuteResult.getTotal_time() != null && orderExecuteResult.getTotal_time() != 0) {
            totalTime = totalTime + orderExecuteResult.getTotal_time();
        }
        orderExecuteResult.setTotal_time(totalTime);
        orderExecuteResultMapper.updateTotalTime(orderExecuteResult);
    }

    private void updateOrderExecuteStatus(OrderExecute orderExecute, String name, OrderScriptExecuteStatus orderExecuteStatus, SqlScriptType sqlScriptType) {
        Integer value = sqlScriptType != null ? sqlScriptType.getValue() : null;
        OrderExecuteResult orderExecuteResult = new OrderExecuteResult(orderExecute.getId(), name, orderExecuteStatus.getValue(), value);
        executeResultService.updateStatus(orderExecuteResult);
    }

    private void updateOrderExecuteFailReason(OrderExecute orderExecute, String reason) {
        OrderExecuteResult orderExecuteResult = new OrderExecuteResult();
        orderExecuteResult.setOrder_execute_id(orderExecute.getId());
        orderExecuteResult.setExecute_fail_reason(reason);
        executeResultService.updateExecuteFailReason(orderExecuteResult);
    }

    private void updateOrderExecuteFailReason(OrderExecute orderExecute, String scriptName, String reason) {
        OrderExecuteResult orderExecuteResult = new OrderExecuteResult();
        orderExecuteResult.setOrder_execute_id(orderExecute.getId());
        orderExecuteResult.setExecute_fail_reason(reason);
        orderExecuteResult.setScript_name(scriptName);
        executeResultService.updateExecuteFailReason(orderExecuteResult);
    }

    @NotNull
    private static List<String> getScriptNames(ExecuteOrderModel executeOrderModel, OrderTypeKey orderTypeKey, OrderApplyContent applyContent) {
        List<String> scripts = new ArrayList<>();

        if (orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT || orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT_BATCH) {
            String executeName = executeOrderModel.getName();
            if (StringUtils.isBlank(executeName)) {
                // 默认全部执行
                // 因为applyContent解析的sql_script是Object需要带泛型的list转化为toJson
                List<SqlScript> sqlScriptList = applyContent.getSqlScripts();
                scripts = sqlScriptList.stream().map(SqlScript::getName).collect(Collectors.toList());
            } else {
                //单个脚本文件重试
                scripts.add(executeName);
            }
        } else if (orderTypeKey.isPrivilege()) {
            String resourceName = applyContent.getResource_list().get(0).getResource_name();
            scripts.add(resourceName);
        } else {
            String scriptName = SqlScriptUtils.getScriptName(applyContent, orderTypeKey, executeOrderModel.getSqlScriptType());
            scripts.add(scriptName);
        }
        return scripts;
    }

}
