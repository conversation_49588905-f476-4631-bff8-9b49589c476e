package com.dc.workorder.utils;

import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.DStatementList;
import com.dc.sqlparser.nodes.TObjectName;
import com.dc.sqlparser.nodes.TObjectNameList;
import com.dc.sqlparser.stmt.TInsertSqlStatement;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.parser.utils.DatabaseTypeUtils;
import com.dc.type.DatabaseType;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.atomic.AtomicReference;

public class IdentityInsertUtil {

    public static boolean existsIncrementField(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String sql, String tableName) throws Exception {

        final AtomicReference<String> incrementField = new AtomicReference<>();
        try {
            DBExecUtils.executeQuery(
                    monitor,
                    executionContext,
                    " Increment Field",
                    IdentityInsertUtil.blInsert4Sqlserver(tableName),
                    dbcResultSet -> {
                        if (dbcResultSet.nextRow()) {
                            if (dbcResultSet.getAttributeValue("column_name") != null) {
                                Object column_name = dbcResultSet.getAttributeValue("column_name");
                                incrementField.set(column_name.toString());
                            }
                        }
                    });
        } catch (DBException e) {
            throw new DBException(e.getMessage());
        }

        boolean existsIncrementField = false;
        if (!com.dc.utils.StringUtils.isNullOrWhiteSpace(incrementField.get())) {
            DPSqlParser tgSqlParser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.SQL_SERVER.getValue()));
            tgSqlParser.setSqltext(sql);
            tgSqlParser.parse();
            DStatementList sqlStatements = tgSqlParser.sqlstatements;
            TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) sqlStatements.get(0);
            TObjectNameList columnList = tInsertSqlStatement.getColumnList();

            for (TObjectName tObjectName : columnList) {
                String columnNameOnly = tObjectName.getColumnNameOnly();
                if (columnNameOnly.replaceAll("\"|\\[|]", "").equals(incrementField)) {
                    existsIncrementField = true;
                    break;
                }
            }
        }
        return existsIncrementField;
    }

    public static String insert4Sqlserver(String tableName, String sql) throws Exception {
        String[] names = tableName.split("\\.");
        if (names.length < 3) {
            throw new Exception("获取tableName失败！");
        }
        String schemaName = names[0];
        String dboName = new String();
        if (StringUtils.isNotBlank(names[1])) {
            dboName = String.format("[%s]", names[1]);
        }
        String newTableName = names[2];
        String resultName = String.format("[%s].%s.[%s]", schemaName, dboName, newTableName);
        return String.format("set identity_insert %s on;%s;set identity_insert %s off;", resultName, sql, resultName);
    }


    public static String blInsert4Sqlserver(String tableName) throws Exception {

        String[] names = tableName.split("\\.");
        if (names.length < 3) {
            throw new Exception("获取tableName失败！");
        }
        String schemaName = names[0];
        String dboName = names[1];
        String newTableName = names[2];
        String sNameSql = new String();
        if (StringUtils.isNotBlank(dboName)) {
            sNameSql = String.format("s.name = '%s' and ", dboName);
        }
        String incrementSql = "SELECT\n" +
                "\ts.name AS dbo_name,\n" +
                "\td.name table_name,\n" +
                "\ta.name column_name,\n" +
                "\t( CASE WHEN i.object_id > 0 THEN 1 ELSE 0 END ) is_increment\n" +
                "FROM\n" +
                "\t" + schemaName + ".sys.syscolumns a\n" +
                "\tINNER JOIN " + schemaName + ".sys.sysobjects d ON a.id= d.id AND d.xtype= 'U' AND d.name<> 'dtproperties'\n" +
                "\tINNER JOIN " + schemaName + ".sys.objects h ON d.id = h.object_id\n" +
                "\tINNER JOIN " + schemaName + ".sys.schemas s ON h.schema_id = s.schema_id\n" +
                "\tINNER JOIN " + schemaName + ".sys.identity_columns i ON i.object_id = h.object_id \n" +
                "\tAND i.column_id= a.colid where " + sNameSql + " d.name = '" + newTableName + "';";

        return incrementSql;
    }


}
