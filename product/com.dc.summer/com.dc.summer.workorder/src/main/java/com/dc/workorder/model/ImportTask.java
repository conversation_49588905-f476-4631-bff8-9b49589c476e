package com.dc.workorder.model;

import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderExecute;
import com.dc.repository.mysql.model.OrderSqlParse;
import lombok.Data;

import java.sql.Connection;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@Data
public class ImportTask extends ConnectionPart {

    Connection connection;
    Order order;
    String name;
    String runId;
    CountDownLatch downLatch;

    String ip;
    String hostname;
    String organization_name;

    String userId;

    Integer from;

    Integer size;

    String dc_parse;

    String session_id;

    OrderExecute orderExecute;

    String scriptType;

    DBCExecutionContext executionContext;

    DBPDataSource dataSource;

    String token;

    Integer scriptIndex;

    List<OrderSqlParse> orderSqlParseList;

    OrderApplyContent orderApplyContent;
}
