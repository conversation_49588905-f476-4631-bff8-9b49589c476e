package com.dc.workorder.service.execute;

import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSqlParse;
import com.dc.springboot.core.model.type.OrderTypeKey;
import com.dc.springboot.core.model.type.RetryExecuteType;
import com.dc.summer.DBException;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.type.WebAsyncTaskType;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.springboot.core.model.workorder.ExecuteOrderModel;
import com.dc.springboot.core.model.type.OrderSqlExecuteStatus;
import com.dc.workorder.model.type.OrderScriptExecuteStatus;
import com.dc.repository.mysql.service.OrderSqlParserService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class WorkOrderExecuteProducer {

    private final Object sync;

    private final WebSQLContextInfo contextInfo;

    private final String produceTaskId;

    private final List<WebAsyncTaskInfo> consumerTaskInfoList;

    private final Queue<OrderSqlParse> queue;

    private final Order order;

    private final String scriptName;

    private final ExecuteOrderModel executeOrderModel;

    private final OrderSqlParserService orderSqlParserService;

    private final int listCount;

    private final int maxSize;

    private OrderTypeKey orderTypeKey;

    private Integer schemaTaskId;

    public WorkOrderExecuteProducer(
            Object sync,
            Queue<OrderSqlParse> queue,
            Order order,
            String scriptName,
            ExecuteOrderModel executeOrderModel,
            OrderSqlParserService orderSqlParserService,
            int listCount,
            List<WebAsyncTaskInfo> consumerTaskInfoList,
            String produceTaskId,
            WebSQLContextInfo contextInfo,
            int maxSize) {
        this.sync = sync;
        this.queue = queue;
        this.order = order;
        this.scriptName = scriptName;
        this.executeOrderModel = executeOrderModel;
        this.orderSqlParserService = orderSqlParserService;
        this.listCount = listCount;
        this.consumerTaskInfoList = consumerTaskInfoList;
        this.produceTaskId = produceTaskId;
        this.contextInfo = contextInfo;
        this.maxSize = maxSize;
    }

    public WorkOrderExecuteProducer(
            OrderTypeKey orderTypeKey,
            Integer schemaTaskId,
            Object sync,
            Queue<OrderSqlParse> queue,
            Order order,
            String scriptName,
            ExecuteOrderModel executeOrderModel,
            OrderSqlParserService orderSqlParserService,
            int listCount,
            List<WebAsyncTaskInfo> consumerTaskInfoList,
            String produceTaskId,
            WebSQLContextInfo contextInfo,
            int maxSize) {
        this.orderTypeKey = orderTypeKey;
        this.schemaTaskId = schemaTaskId;
        this.sync = sync;
        this.queue = queue;
        this.order = order;
        this.scriptName = scriptName;
        this.executeOrderModel = executeOrderModel;
        this.orderSqlParserService = orderSqlParserService;
        this.listCount = listCount;
        this.consumerTaskInfoList = consumerTaskInfoList;
        this.produceTaskId = produceTaskId;
        this.contextInfo = contextInfo;
        this.maxSize = maxSize;
    }


    public OrderScriptExecuteStatus run() throws DBException, InterruptedException {

        log.info("生产者任务开始,order id: {}", order.getId());
        try {
            /*
              listCount 所有数据计数
              maxPage 最大页
              currPage 当前页
             */
            int limit = listCount;

            int maxPage = 0;

            if (listCount > 1000) {
                limit = 1000;
                maxPage = listCount / limit;
            } else if (listCount > 0) {
                maxPage = 1;
            }

            int currPage = 0;

            int sum = 0;
            int queueCount = 0;

            //向队列中插入sqlParser实体
            while (currPage <= maxPage && maxPage != 0) {

                for (WebAsyncTaskInfo taskInfo : consumerTaskInfoList) {
                    if (WebAsyncTaskType.CANCELED.equals(taskInfo.getStatus()) || WebAsyncTaskType.SYS_ERROR.equals(taskInfo.getStatus())) {
                        contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.SYS_ERROR);
                        throw new DBException("工单消费者被用户关闭，生产者终止。");
                    } else if (WebAsyncTaskType.SQL_ERROR.equals(taskInfo.getStatus())) {
                        contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.SQL_ERROR);
                        throw new DBException("工单消费者出现错误，生产者终止。");
                    } else if (WebAsyncTaskType.INTERRUPTED.equals(taskInfo.getStatus())) {
                        contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.INTERRUPTED);
                        throw new InterruptedException("工单消费者被中断，生产者终止。");
                    }
                }

                synchronized (queue) {
                    if (queue.size() > maxSize) {
                        queue.wait();
                    }
                }

                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("order_id", order.getId());
                queryMap.put("script_name", scriptName);
                queryMap.put("type", executeOrderModel.getSqlScriptType().getValue());
                queryMap.put("is_skip", 0);
                queryMap.put("limit", limit);
                queryMap.put("offset", currPage * limit);
                if (orderTypeKey != null && orderTypeKey == OrderTypeKey.APPLY_SQL_SCRIPT_BATCH) {
                    queryMap.put("order_schema_task_id", schemaTaskId);
                }
                List<OrderSqlParse> list = orderSqlParserService.querySqlParser(queryMap);
                sum += list.size();
                for (OrderSqlParse orderSqlParse : list) {
                    boolean goOnExecute = RetryExecuteType.GO_ON_EXECUTE == executeOrderModel.getRetryExecuteType() &&
                            OrderSqlExecuteStatus.execute_success.getValue().equals(orderSqlParse.getExecute_status());

                    boolean resumeFromInterrupt = RetryExecuteType.RESUME_FROM_INTERRUPT == executeOrderModel.getRetryExecuteType() &&
                            OrderSqlExecuteStatus.execute_success.getValue().equals(orderSqlParse.getExecute_status());

                    if (goOnExecute || resumeFromInterrupt) {
                        continue;
                    }
                    queueCount++;
                    queue.offer(orderSqlParse);
                }

                currPage++;

                synchronized (queue) {
                    queue.notifyAll();
                }
            }
            log.debug("累计读取的orderSqlParser ：{}", sum);
            log.debug("累计入队列数：{}", queueCount);

            //生产者插入数据完毕，更改生产者状态为待提交
            contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.WAIT_COMMIT);

            //循环等待消费者消费所有消息，遍历消费者的执行状态，判断是否失败
            while (true) {
                boolean needBreak = true;
                for (WebAsyncTaskInfo taskInfo : consumerTaskInfoList) {
                    if (WebAsyncTaskType.CANCELED.equals(taskInfo.getStatus()) || WebAsyncTaskType.SYS_ERROR.equals(taskInfo.getStatus())) {
                        contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.SYS_ERROR);
                        throw new DBException("工单消费者被用户关闭，生产者终止。");
                    } else if (WebAsyncTaskType.SQL_ERROR.equals(taskInfo.getStatus())) {
                        contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.SQL_ERROR);
                        throw new DBException("工单消费者出现错误，生产者终止。");
                    } else if (WebAsyncTaskType.INTERRUPTED.equals(taskInfo.getStatus())) {
                        contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.INTERRUPTED);
                        throw new InterruptedException("工单消费者被中断，生产者终止。");
                    } else if (taskInfo.getStatus() == null || WebAsyncTaskType.RUNNING.equals(taskInfo.getStatus())) {
                        needBreak = false;
                    }
                }

                if (needBreak) {
                    contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.FINISHED);
                    break;
                } else {
                    synchronized (queue) {
                        queue.wait(10000L);
                    }
                }
            }

            log.info("生产者任务结束, order id: {}", order.getId());
            contextInfo.setTaskStatus(produceTaskId, WebAsyncTaskType.FINISHED);
        } finally {
            synchronized (queue) {
                queue.notifyAll();
            }
        }

        return OrderScriptExecuteStatus.success;
    }

}
