# ExportContext导出重构文档

## 概述

本次重构解决了AbstractExportStrategy类的executeTransfer方法中调用exportDataByContext方法时参数不匹配的问题。通过应用多种设计模式，我们创建了一个更清晰、更可维护的导出架构。

## 问题分析

### 原始问题
- AbstractExportStrategy.executeTransfer方法调用exportDataByContext时传递了ExportContext参数
- 但WebDataTransfer类中的exportDataByContext方法只接受SqlExportMessage参数
- 导致参数类型不匹配，无法正常编译和运行

### 根本原因
- 代码结构混乱，缺乏统一的参数传递机制
- ExportContext和SqlExportMessage之间缺乏适配层
- 没有应用合适的设计模式来处理复杂的参数转换

## 解决方案

### 设计模式应用

#### 1. Builder Pattern（建造者模式）
**类**: `ExportParameterBuilder`
**目的**: 从ExportContext构建导出所需的各种参数
**优势**: 
- 简化复杂参数的构建过程
- 提供参数验证和日志记录功能
- 支持链式调用，代码更清晰

```java
ExportParameterBuilder parameterBuilder = new ExportParameterBuilder(context);
String userId = parameterBuilder.buildUserId();
String fileCharset = parameterBuilder.buildFileCharset();
boolean isValid = parameterBuilder.validateParameters();
```

#### 2. Strategy Pattern（策略模式）
**接口**: `ExportConfigurationStrategy`
**实现**: `DefaultExportConfigurationStrategy`
**目的**: 处理不同类型的导出配置
**优势**:
- 支持多种配置处理策略
- 易于扩展新的配置类型
- 配置处理逻辑与业务逻辑分离

```java
ExportConfigurationStrategy configStrategy = new DefaultExportConfigurationStrategy();
Map<String, Object> properties = configStrategy.processConfiguration(context);
```

#### 3. Adapter Pattern（适配器模式）
**类**: `ExportContextAdapter`
**目的**: 将ExportContext适配到现有的导出流程
**优势**:
- 避免大规模重构现有代码
- 提供向后兼容性
- 支持渐进式迁移

```java
ExportContextAdapter adapter = new ExportContextAdapter();
SqlExportMessage message = adapter.adaptToSqlExportMessage(context);
```

### 核心组件

#### 1. ExportParameterBuilder
- **职责**: 从ExportContext构建导出参数
- **特性**: 参数验证、日志记录、链式构建
- **方法**: buildUserId(), buildFileCharset(), validateParameters()等

#### 2. ExportConfigurationStrategy
- **职责**: 处理导出配置转换
- **特性**: 策略模式实现，支持多种配置类型
- **方法**: processConfiguration(), supports(), validateConfiguration()

#### 3. ExportContextAdapter
- **职责**: ExportContext与现有接口的适配
- **特性**: 适配器模式实现，提供向后兼容
- **方法**: adaptToSqlExportMessage(), extractParameters()

#### 4. WebDataTransfer新方法
- **方法**: `exportDataByContext(ExportContext context, ...)`
- **特性**: 直接接受ExportContext参数，避免类型转换
- **优势**: 保持ExportContext的清晰结构

## 实现细节

### 新增的exportDataByContext重载方法

```java
public WebSQLTransferResult exportDataByContext(
    DBRProgressMonitor monitor,
    DataTransferProcessorDescriptor processor,
    Supplier<List<WebSQLQueryResultSet>> supplier,
    ExportContext context,  // 直接接受ExportContext
    Function<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> streamFunction,
    WebDataTransferName transferZipName,
    WebDataTransferName transferFileName,
    boolean needToZipAndUpload,
    boolean isSingle,
    boolean deleteResultInfo,
    String customFileName)
```

### 支持方法

1. **processExportItemWithContext**: 使用ExportContext的导出项处理
2. **createDataContainerWithContext**: 使用ExportContext创建数据容器
3. **createResultFormatWithContext**: 使用ExportContext创建结果格式
4. **createDataDesensitizeProcessorWithContext**: 使用ExportContext创建脱敏处理器
5. **execWithContext**: 使用ExportContext执行导出

## 优势

### 1. 代码清晰性
- 避免了ExportContext到SqlExportMessage的不必要转换
- 保持了ExportContext的语义完整性
- 减少了参数传递的复杂性

### 2. 设计模式应用
- **建造者模式**: 简化复杂参数构建
- **策略模式**: 支持多种配置处理策略
- **适配器模式**: 提供向后兼容性

### 3. 可维护性
- 职责分离，每个类都有明确的职责
- 易于扩展新的导出类型和配置
- 支持单元测试和集成测试

### 4. 性能优化
- 避免不必要的对象转换
- 减少内存分配和垃圾回收
- 提高导出操作的执行效率

## 测试

### 单元测试
- `ExportContextIntegrationTest`: 验证整个导出流程
- 测试覆盖: 参数构建、配置处理、上下文验证

### 测试用例
1. 正常导出流程测试
2. 参数验证测试
3. 配置策略测试
4. 异常处理测试

## 使用示例

```java
// 1. 构建ExportContext
ExportContext context = ExportContext.builder()
    .token("user_token")
    .userId("wang")
    .formatConfig(ExportFormatConfig.builder()
        .exportType(ExportType.XLSX)
        .fileCharset("UTF-8")
        .build())
    .sqlExportModels(sqlModels)
    .build();

// 2. 使用新的导出方法
WebSQLTransferResult result = webDataTransfer.exportDataByContext(
    monitor, processor, supplier, context, streamFunction,
    transferZipName, transferFileName, true, true, false, customFileName);

// 3. 处理结果
if (result.isSuccess()) {
    log.info("导出成功: {}", result.getMessage());
} else {
    log.error("导出失败: {}", result.getMessage());
}
```

## 迁移指南

### 现有代码兼容性
- 原有的exportDataByContext(SqlExportMessage)方法保持不变
- 新代码建议使用exportDataByContext(ExportContext)方法
- 支持渐进式迁移，无需一次性修改所有代码

### 迁移步骤
1. 使用ExportTaskBuilder构建ExportContext
2. 调用新的exportDataByContext重载方法
3. 验证导出结果
4. 逐步迁移其他相关代码

## 总结

本次重构通过应用建造者模式、策略模式和适配器模式，成功解决了参数不匹配的问题，同时提高了代码的清晰性、可维护性和扩展性。新的架构支持向后兼容，可以渐进式迁移，降低了重构风险。
