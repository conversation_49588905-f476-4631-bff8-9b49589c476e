package com.dc.summer.service.runnable;

import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.EmailDataModel;
import com.dc.springboot.core.model.data.EmailMessageModel;
import com.dc.springboot.core.model.message.EmailMessage;
import com.dc.summer.config.SummerConfig;
import com.dc.springboot.core.model.execution.JobExportMessage;
import com.dc.type.DatabaseType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class EmailRunnable implements Runnable {


    private JobExportMessage message;

    private String filePath;

    private BackendClient backendClient;

    private SummerConfig config;

    private DatabaseType databaseType;

    public EmailRunnable(JobExportMessage message, String filePath, BackendClient backendClient, SummerConfig config, DatabaseType databaseType) {
        this.message = message;
        this.filePath = filePath;
        this.backendClient = backendClient;
        this.config = config;
        this.databaseType = databaseType;
    }

    @Override
    public void run() {
        EmailMessage emailMessage = new EmailMessage();
        EmailMessageModel emailMessageModel = new EmailMessageModel();
        EmailDataModel emailDataModel = new EmailDataModel();

        emailDataModel.setCode(message.getCode());
        emailDataModel.setDbType(databaseType.getValue());
        emailDataModel.setInstance(message.getInstanceDesc());
        emailDataModel.setSchema(message.getSchemaName());
        emailDataModel.setContent(message.getContent());
        emailDataModel.setFiles(filePath);
        emailDataModel.setEmail(message.getExportEmail());
        emailDataModel.setUserId(message.getUserId());
        emailDataModel.setEmailTitle(message.getEmailTitle());

        emailMessageModel.setMsgTpl("dc_export_email_attach");
        emailMessageModel.setData(emailDataModel);

        emailMessage.setData(emailMessageModel);
        backendClient.sendEmail(Client.getClient(config.getPath().getDcBackend()), emailMessage);
    }
}
