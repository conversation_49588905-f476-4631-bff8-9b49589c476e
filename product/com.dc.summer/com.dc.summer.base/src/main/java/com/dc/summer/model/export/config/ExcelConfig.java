package com.dc.summer.model.export.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;

/**
 * Excel导出配置类
 * 负责Excel特定的配置参数
 */
@Data
@Builder
@ApiModel("Excel导出配置")
public class ExcelConfig {

    @ApiModelProperty(value = "导出excel是否原格式", example = "1")
    @Builder.Default
    private int useOriginalFormat = 1;

    @ApiModelProperty(value = "导出excel数字类型是否为数字格式", example = "true")
    @Builder.Default
    private boolean useNumberFormat = true;

    @ApiModelProperty(value = "导出excel时间日期格式", example = "yyyy-MM-dd HH:mm:ss")
    @Builder.Default
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * 创建默认的Excel配置
     */
    public static ExcelConfig createDefault() {
        return ExcelConfig.builder()
                .useOriginalFormat(1)
                .useNumberFormat(true)
                .datetimeFormat("yyyy-MM-dd HH:mm:ss")
                .build();
    }

    /**
     * 验证Excel配置的有效性
     */
    public boolean isValid() {
        // 验证日期格式字符串
        if (datetimeFormat != null && !isValidDateFormat(datetimeFormat)) {
            return false;
        }
        
        // 验证原格式标志
        if (useOriginalFormat < 0 || useOriginalFormat > 1) {
            return false;
        }
        
        return true;
    }

    /**
     * 简单验证日期格式字符串
     */
    private boolean isValidDateFormat(String format) {
        try {
            // 这里可以添加更复杂的日期格式验证逻辑
            return format.matches(".*[yMdHms].*");
        } catch (Exception e) {
            return false;
        }
    }
}
