package com.dc.summer.model.chain.impl;

import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.sql.WebSQLContextInfo;

import java.util.Locale;

public class SyncSchemaChain extends AbstractChain<WebSQLQueryResult> {


    private final WebSQLContextInfo contextInfo;

    private final boolean isNeedSyncSchema;

    private final MessageService messageService;

    private final Integer connectionPattern;

    public SyncSchemaChain(WebSQLContextInfo contextInfo, boolean isNeedSyncSchema, Integer connectionPattern, MessageService messageService) {
        this.contextInfo = contextInfo;
        this.isNeedSyncSchema = isNeedSyncSchema;
        this.messageService = messageService;
        this.connectionPattern = connectionPattern;
    }


    @Override
    public boolean proceed(WebSQLQueryResult webSQLQueryResult) {
        // 需求点 // 账号直连不需要执行 schema 同步
        if (ConnectionPatternType.of(connectionPattern) == ConnectionPatternType.ACCOUNT_DIRECT_CONNECTION) {
            return true;
        }
        boolean noticeSyncSchema = false;
        // 自动提交
        if (Boolean.TRUE.equals(contextInfo.getAutoCommit())) {
            if (isNeedSyncSchema || contextInfo.isChangedSchema()) {
                noticeSyncSchema = true;
            }
            contextInfo.setChangedSchema(false);
        }
        // 手动提交
        else {
            if (isNeedSyncSchema) {
                if (contextInfo.getDataSource().getInfo().supportsDDLAutoCommit()) {
                    noticeSyncSchema = true;
                } else {
                    contextInfo.setChangedSchema(true);
                }
            } else if (contextInfo.isChangedSchema()) {
                String sql = webSQLQueryResult.getSql().toUpperCase(Locale.ROOT);
                if (sql.startsWith(SQLQueryType.ROLLBACK.name())) {
                    contextInfo.setChangedSchema(false);
                } else if (sql.startsWith(SQLQueryType.COMMIT.name())) {
                    noticeSyncSchema = true;
                    contextInfo.setChangedSchema(false);
                }
            }
        }

        if (noticeSyncSchema) {
            messageService.sendSyncSchemaMessage(contextInfo.getDataSourceContainer().getConnectionId());
        }

        return false;
    }
}
