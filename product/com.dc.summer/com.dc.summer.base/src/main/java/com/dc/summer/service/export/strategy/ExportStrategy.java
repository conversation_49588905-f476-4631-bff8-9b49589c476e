package com.dc.summer.service.export.strategy;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.sql.WebSQLTransferResult;

import java.util.List;

/**
 * 导出策略接口
 * 定义不同导出类型的处理策略
 */
public interface ExportStrategy {

    /**
     * 执行导出操作
     * 
     * @param context 导出上下文
     * @param resultSets 结果集列表
     * @return 导出结果
     */
    WebSQLTransferResult export(ExportContext context, List<WebSQLQueryResultSet> resultSets);

    /**
     * 判断是否支持指定的导出类型
     * 
     * @param exportType 导出类型
     * @return 是否支持
     */
    boolean supports(ExportType exportType);

    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取支持的导出类型列表
     * 
     * @return 支持的导出类型列表
     */
    List<ExportType> getSupportedTypes();

    /**
     * 验证导出参数
     * 
     * @param context 导出上下文
     * @return 验证结果
     */
    default boolean validateParameters(ExportContext context) {
        return context != null && context.isValid();
    }

    /**
     * 预处理导出数据
     * 
     * @param context 导出上下文
     * @param resultSets 结果集列表
     * @return 预处理后的结果集
     */
    default List<WebSQLQueryResultSet> preprocessData(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        return resultSets;
    }

    /**
     * 后处理导出结果
     * 
     * @param context 导出上下文
     * @param result 导出结果
     * @return 后处理后的结果
     */
    default WebSQLTransferResult postprocessResult(ExportContext context, WebSQLTransferResult result) {
        return result;
    }
}
