package com.dc.summer.model.utils;

import com.dc.summer.exec.handler.DefaultDataSourceHandler;
import com.dc.summer.exec.handler.ParserHandler;
import com.dc.type.DatabaseType;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.SQLUtils;

public class SqlTrimUtil {

    public static String trimSqlDelimiter(Integer dbType, String sql) {
        DefaultDataSourceHandler handle = DefaultDataSourceHandler.handle(DatabaseType.of(dbType));
        SQLSyntaxManager syntaxManager = new SQLSyntaxManager();
        syntaxManager.init(ParserHandler.getSqlDialectFromConnection(handle), handle.getPreferenceStore());
        return SQLUtils.trimQueryStatement(syntaxManager, sql, true);
    }

}
