package com.dc.summer.model.data.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("结果集索引模型")
public class ResultsIndexModel {

    @NotNull
    @ApiModelProperty(value = "sql序号", required = true, example = "0")
    private Integer sqlIndex;

    @NotNull
    @ApiModelProperty(value = "结果ID序号 - 第几个结果集，不传则无法导出", required = true, example = "0")
    private List<ResultModel> resultModels;

}
