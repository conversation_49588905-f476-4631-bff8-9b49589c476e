package com.dc.summer.service.export.factory;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.summer.service.export.strategy.ExportStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出策略工厂
 * 负责创建和管理不同的导出策略
 */
@Slf4j
@Component
public class ExportStrategyFactory {

    @Autowired
    private List<ExportStrategy> exportStrategies;

    private final Map<ExportType, ExportStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        log.info("初始化导出策略工厂，发现 {} 个策略", exportStrategies.size());
        
        for (ExportStrategy strategy : exportStrategies) {
            for (ExportType exportType : strategy.getSupportedTypes()) {
                if (strategyMap.containsKey(exportType)) {
                    log.warn("导出类型 {} 存在多个策略实现，将使用最后注册的策略: {}", 
                            exportType, strategy.getStrategyName());
                }
                strategyMap.put(exportType, strategy);
                log.info("注册导出策略: {} -> {}", exportType, strategy.getStrategyName());
            }
        }
        
        log.info("导出策略工厂初始化完成，共注册 {} 种导出类型", strategyMap.size());
    }

    /**
     * 根据导出类型获取对应的策略
     * 
     * @param exportType 导出类型
     * @return 导出策略
     * @throws IllegalArgumentException 如果不支持该导出类型
     */
    public ExportStrategy getStrategy(ExportType exportType) {
        if (exportType == null) {
            throw new IllegalArgumentException("导出类型不能为空");
        }
        
        ExportStrategy strategy = strategyMap.get(exportType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的导出类型: " + exportType);
        }
        
        log.debug("获取导出策略: {} -> {}", exportType, strategy.getStrategyName());
        return strategy;
    }

    /**
     * 检查是否支持指定的导出类型
     * 
     * @param exportType 导出类型
     * @return 是否支持
     */
    public boolean isSupported(ExportType exportType) {
        return exportType != null && strategyMap.containsKey(exportType);
    }

    /**
     * 获取所有支持的导出类型
     * 
     * @return 支持的导出类型列表
     */
    public List<ExportType> getSupportedTypes() {
        return List.copyOf(strategyMap.keySet());
    }

    /**
     * 获取策略统计信息
     * 
     * @return 策略统计信息
     */
    public Map<String, Object> getStrategyStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalStrategies", exportStrategies.size());
        stats.put("supportedTypes", strategyMap.size());
        stats.put("strategies", exportStrategies.stream()
                .map(ExportStrategy::getStrategyName)
                .toList());
        return stats;
    }

    /**
     * 验证所有策略的完整性
     * 
     * @return 验证结果
     */
    public boolean validateStrategies() {
        boolean allValid = true;
        
        for (ExportStrategy strategy : exportStrategies) {
            try {
                // 检查策略名称
                if (strategy.getStrategyName() == null || strategy.getStrategyName().trim().isEmpty()) {
                    log.error("策略名称为空: {}", strategy.getClass().getSimpleName());
                    allValid = false;
                }
                
                // 检查支持的类型
                if (strategy.getSupportedTypes() == null || strategy.getSupportedTypes().isEmpty()) {
                    log.error("策略未定义支持的导出类型: {}", strategy.getStrategyName());
                    allValid = false;
                }
                
                // 检查supports方法的一致性
                for (ExportType type : strategy.getSupportedTypes()) {
                    if (!strategy.supports(type)) {
                        log.error("策略 {} 的supports方法与getSupportedTypes不一致，类型: {}", 
                                strategy.getStrategyName(), type);
                        allValid = false;
                    }
                }
                
            } catch (Exception e) {
                log.error("验证策略时发生异常: {}", strategy.getStrategyName(), e);
                allValid = false;
            }
        }
        
        return allValid;
    }
}
