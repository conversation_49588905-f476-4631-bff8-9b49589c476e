
package com.dc.summer.model.data.generator;

import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDAttributeBindingMeta;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;

import java.util.Collection;
import java.util.List;

public class SQLGeneratorSelectManyFromData extends SQLGeneratorResultSet {

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, SQLGeneratorResultSetController object) {

        Collection<DBDAttributeBinding> keyAttributes = getKeyAttributes(monitor, object);
        sql.append("SELECT ");
        boolean hasAttr = false;
        for (DBSAttributeBase attr : getAllAttributes(monitor, object)) {
            if (hasAttr) sql.append(", ");
            sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML));
            hasAttr = true;
        }
        sql.append(getLineSeparator()).append("FROM ").append(getEntityName(object));
        sql.append(getLineSeparator()).append("WHERE ");
        boolean multiKey = keyAttributes.size() > 1;
        if (multiKey) sql.append("(");
        hasAttr = false;
        for (DBDAttributeBinding binding : keyAttributes) {
            if (hasAttr) sql.append(",");
            sql.append(DBUtils.getObjectFullName(binding.getAttribute(), DBPEvaluationContext.DML));
            hasAttr = true;
        }
        if (multiKey) sql.append(")");
        sql.append(" IN (");
        if (multiKey) sql.append("\n");
        List<Object[]> selectedRows = getSelectedRows();
        for (int i = 0; i < selectedRows.size(); i++) {
            Object[] selectRow = selectedRows.get(i);
            if (multiKey) sql.append("(");
            hasAttr = false;
            for (DBDAttributeBinding binding : keyAttributes) {
                if (hasAttr) sql.append(",");
                appendAttributeValue(getController(), sql, binding, selectRow);
                hasAttr = true;
            }
            if (multiKey) sql.append(")");
            if (i < selectedRows.size() - 1) sql.append(",");
            if (multiKey) sql.append("\n");
        }
        sql.append(");\n");
    }
}
