package com.dc.summer.model.utils;

import com.dc.summer.model.sql.SqlFieldData;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class BackupUtils {

    public static Boolean isTableStructureChanged(Map<String, SqlFieldData> rcTableStructureMap, Map<String, SqlFieldData> dbTableStructureMap) {

        try {
            // 若无表结构数据,默认变更
            if (rcTableStructureMap.size() == 0 || dbTableStructureMap.size() == 0) {
                return true;
            }
            // 字段个数不一致
            if (rcTableStructureMap.size() != dbTableStructureMap.size()) {
                return true;
            }

            for (Map.Entry<String, SqlFieldData> entry : rcTableStructureMap.entrySet()) {
                // 字段已被删除
                if (dbTableStructureMap.get(entry.getKey()) == null) {
                    return true;
                }
                // 字段类型改变
                if (!entry.getValue().getFieldType().equalsIgnoreCase(dbTableStructureMap.get(entry.getKey()).getFieldType())) {
                    return true;
                }
                // 字段长度改变
                if (!entry.getValue().getDataLength().equalsIgnoreCase(dbTableStructureMap.get(entry.getKey()).getDataLength())) {
                    return true;
                }
                // 主键改变
                if (!entry.getValue().getIsPrimaryKey().equals(dbTableStructureMap.get(entry.getKey()).getIsPrimaryKey())) {
                    return true;
                }
            }
            return false;
        } catch (Exception ignored) {
        }
        // 出异常默认表结构变更
        return true;
    }

    public static String generateInsertDataSql(String tableName, List<SqlFieldData> list, String rc_type, List<String> bigDataType) {
        if (null != list) {
            List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
            List<String> data = new ArrayList<>();
            List<String> big_data_field = new ArrayList<>();
            for (SqlFieldData item : list) {
                if (bigDataType.contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    big_data_field.add(item.getFieldName().toUpperCase(Locale.ROOT));
                    continue;
                }
                if (Arrays.asList("ROWID", "WHD_DC_ROW_ID").contains(item.getFieldType())) {
                    continue;
                }
                data.add("?");
                continue;
            }
            List<String> resultField = new ArrayList<>();
            for (String field : fields) {
                if (big_data_field.contains(field.toUpperCase(Locale.ROOT))) {
                    continue;
                }
                if (Arrays.asList("ROWID", "WHD_DC_ROW_ID").contains(field)) {
                    continue;
                }
                resultField.add(String.format("_%s", field));
            }
            String columns = StringUtils.join(resultField, "`,`");
            String values = StringUtils.join(data, ",");

            return String.format("INSERT INTO `%s` (%s,rc_type,transaction_index,row_id) VALUES (%s,%s,?,?);", tableName, "`" + columns + "`", values, "'" + rc_type + "'");
        }
        return "";
    }

    public static String createRelationTable(String tableName) {
        return "CREATE TABLE IF NOT EXISTS `" + tableName + "` (\n" +
                "  `id` bigint(20) NOT NULL AUTO_INCREMENT,\n" +
                "  `row_id` varchar(255) DEFAULT NULL,\n" +
                "  `rc_sql_id` bigint(20) DEFAULT NULL,\n" +
                "  `p_id` bigint(20) DEFAULT NULL,\n" +
                "  `root_id` bigint(20) DEFAULT NULL,\n" +
                "  `gmt_create` datetime(6) DEFAULT NULL,\n" +
                "  `gmt_modified` datetime(6) DEFAULT NULL,\n" +
                "  `is_delete` tinyint(4) DEFAULT '0' COMMENT '版本是否清除，0：未删除，1：已删除',\n" +
                "  `transaction_index` int(11) DEFAULT NULL COMMENT '与批次对应',\n" +
                "  `is_real_pk` tinyint(4) COMMENT '是否含有主键',\n" +
                "  PRIMARY KEY (`id`)\n" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8;";
    }

    public static String createDataTableSql(String tableName, List<SqlFieldData> fields, List<String> bigDataType) {
        StringBuilder columnSql = new StringBuilder();
        for (SqlFieldData sqlFieldData : fields) {
            String data_length = sqlFieldData.getDataLength();

            if ("ROWID".equals(sqlFieldData.getFieldType()) || bigDataType.contains(sqlFieldData.getFieldType().toUpperCase(Locale.ROOT))) {
                continue;
            }
            if (StringUtils.isBlank(data_length)
                    || !Arrays.asList("VARCHAR", "VARCHAR2", "CHAR", "NCHAR", "TEXT").contains(sqlFieldData.getFieldType().toUpperCase(Locale.ROOT))) {
                data_length = "255";
            }

            if (Integer.parseInt(data_length) > 255 ||
                    "0".equals(sqlFieldData.getDataLength()) ||
                    "FLOAT".equalsIgnoreCase(sqlFieldData.getFieldType()) ||
                    "DOUBLE".equalsIgnoreCase(sqlFieldData.getFieldType())) {
                columnSql.append(String.format("`_%s` TEXT DEFAULT NULL,", sqlFieldData.getFieldName()));
            } else {
                columnSql.append(String.format("`_%s` VARCHAR(%s) DEFAULT NULL,", sqlFieldData.getFieldName(), data_length));

            }
        }
        return "CREATE TABLE IF NOT EXISTS `" + tableName + "`(\n" +
                "   `id` bigint(20) NOT NULL AUTO_INCREMENT,\n" +
                columnSql +
                "   `rc_type` varchar(255) DEFAULT NULL,\n" +
                "   `transaction_index` int(11) DEFAULT NULL,\n" +
                "   `row_id` varchar(255) DEFAULT NULL,\n" +
                "   PRIMARY KEY ( `id` ),\n" +
                "   INDEX `idx_row_id` (`row_id`)\n" +
                ")ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    }

}
