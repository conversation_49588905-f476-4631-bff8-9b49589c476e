package com.dc.summer.service.export;

import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.TaskResultMessage;
import com.dc.summer.model.data.model.ResultsIndexModel;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * 结果集服务
 * 负责获取和管理SQL执行结果集
 */
@Slf4j
@Service
public class ResultSetService {

    @Autowired
    private ResultService resultService;

    /**
     * 获取结果集（优化版本，缓存失效时返回Optional.empty()）
     * 
     * @param context 导出上下文
     * @return 结果集列表的Optional包装
     */
    public Optional<List<WebSQLQueryResultSet>> getResultSetsOptional(ExportContext context) {
        try {
            List<SqlExportModel> sqlExportModels = context.getSqlExportModels();
            List<WebSQLQueryResultSet> resultSets = new LinkedList<>();

            for (SqlExportModel sqlExportModel : sqlExportModels) {
                String taskId = sqlExportModel.getTaskId();
                
                for (ResultsIndexModel resultIndex : sqlExportModel.getResultsIndexModels()) {
                    TaskResultMessage taskResultMessage = buildTaskResultMessage(
                            taskId, context.getToken(), resultIndex.getSqlIndex() + 1);
                    
                    try {
                        WebSQLExecuteInfo executeInfo = resultService.getStringValue(taskResultMessage);
                        validateExecuteInfo(executeInfo);
                        addResultSet(resultIndex, executeInfo, resultSets);
                        
                    } catch (Exception e) {
                        // 当缓存失效时，返回Optional.empty()而不是重新查询
                        log.info("获取结果集失败，返回空Optional以便在WebDataTransfer中处理重新查询: {}", e.getMessage());
                        return Optional.empty();
                    }
                }
            }

            log.debug("成功获取结果集，总数量: {}", resultSets.size());
            return Optional.of(resultSets);
            
        } catch (Exception e) {
            log.error("获取结果集时发生异常", e);
            return Optional.empty();
        }
    }

    /**
     * 获取结果集（传统版本，缓存失效时重新查询）
     * 
     * @param context 导出上下文
     * @param contextInfo SQL上下文信息
     * @return 结果集列表
     */
    public List<WebSQLQueryResultSet> getResultSets(ExportContext context, WebSQLContextInfo contextInfo) {
        List<SqlExportModel> sqlExportModels = context.getSqlExportModels();
        List<WebSQLQueryResultSet> resultSets = new LinkedList<>();

        for (SqlExportModel sqlExportModel : sqlExportModels) {
            String taskId = sqlExportModel.getTaskId();
            
            for (ResultsIndexModel resultIndex : sqlExportModel.getResultsIndexModels()) {
                TaskResultMessage taskResultMessage = buildTaskResultMessage(
                        taskId, context.getToken(), resultIndex.getSqlIndex() + 1);
                
                WebSQLExecuteInfo executeInfo = getExecuteInfoWithRetry(
                        taskResultMessage, taskId, resultIndex, contextInfo);
                
                addResultSet(resultIndex, executeInfo, resultSets);
            }
        }

        log.info("获取结果集完成，总数量: {}", resultSets.size());
        return resultSets;
    }

    /**
     * 构建任务结果消息
     */
    private TaskResultMessage buildTaskResultMessage(String taskId, String token, int stage) {
        TaskResultMessage taskResultMessage = new TaskResultMessage();
        taskResultMessage.setTaskId(taskId);
        taskResultMessage.setToken(token);
        taskResultMessage.setStage(stage);
        return taskResultMessage;
    }

    /**
     * 获取执行信息，失败时重试
     */
    private WebSQLExecuteInfo getExecuteInfoWithRetry(TaskResultMessage taskResultMessage, 
                                                     String taskId, 
                                                     ResultsIndexModel resultIndex, 
                                                     WebSQLContextInfo contextInfo) {
        try {
            WebSQLExecuteInfo executeInfo = resultService.getStringValue(taskResultMessage);
            validateExecuteInfo(executeInfo);
            return executeInfo;
            
        } catch (Exception e) {
            log.info("获取结果集失败，尝试重新查询: {}", e.getMessage());
            return retryGetExecuteInfo(taskResultMessage, taskId, resultIndex, contextInfo);
        }
    }

    /**
     * 重试获取执行信息
     */
    private WebSQLExecuteInfo retryGetExecuteInfo(TaskResultMessage taskResultMessage, 
                                                 String taskId, 
                                                 ResultsIndexModel resultIndex, 
                                                 WebSQLContextInfo contextInfo) {
        try {
            // 重新执行查询
            WebAsyncTaskInfo webAsyncTaskInfo = contextInfo.asyncTaskStatus(taskId, false);
            webAsyncTaskInfo.exec(String.valueOf(resultIndex.getSqlIndex()));
            
            // 重新获取结果
            WebSQLExecuteInfo executeInfo = resultService.getStringValue(taskResultMessage);
            validateExecuteInfo(executeInfo);
            
            log.info("重新查询成功，任务ID: {}, 索引: {}", taskId, resultIndex.getSqlIndex());
            return executeInfo;
            
        } catch (Exception ex) {
            log.error("重新查询失败，任务ID: {}, 索引: {}", taskId, resultIndex.getSqlIndex(), ex);
            throw new RuntimeException("获取结果集失败，请重新查询", ex);
        }
    }

    /**
     * 验证执行信息
     */
    private void validateExecuteInfo(WebSQLExecuteInfo executeInfo) {
        if (executeInfo == null) {
            throw new RuntimeException("执行信息为空");
        }
        
        // 这里可以添加更多的验证逻辑
        // 例如检查执行状态、错误信息等
    }

    /**
     * 添加结果集到列表
     */
    private void addResultSet(ResultsIndexModel resultIndex, 
                            WebSQLExecuteInfo executeInfo, 
                            List<WebSQLQueryResultSet> resultSets) {
        try {
            // 这里需要根据实际的业务逻辑来添加结果集
            // 原有的addResultSet方法逻辑需要迁移到这里
            
            if (executeInfo.getQueryResults() != null && !executeInfo.getQueryResults().isEmpty()) {
                // 根据resultIndex选择对应的结果集
                int index = Math.min(resultIndex.getSqlIndex(), executeInfo.getQueryResults().size() - 1);
                if (index >= 0 && executeInfo.getQueryResults().get(index).getResultSet() != null) {
                    resultSets.add(executeInfo.getQueryResults().get(index).getResultSet());
                    log.debug("添加结果集成功，索引: {}", resultIndex.getSqlIndex());
                }
            }
            
        } catch (Exception e) {
            log.error("添加结果集失败，索引: {}", resultIndex.getSqlIndex(), e);
            throw new RuntimeException("添加结果集失败", e);
        }
    }

    /**
     * 获取结果集统计信息
     */
    public ResultSetStats getResultSetStats(ExportContext context) {
        int totalModels = context.getSqlExportModels().size();
        int totalResultSets = context.getSqlExportModels().stream()
                .mapToInt(model -> model.getResultsIndexModels().size())
                .sum();
        
        return new ResultSetStats(totalModels, totalResultSets);
    }

    /**
     * 结果集统计信息
     */
    public static class ResultSetStats {
        private final int totalModels;
        private final int totalResultSets;

        public ResultSetStats(int totalModels, int totalResultSets) {
            this.totalModels = totalModels;
            this.totalResultSets = totalResultSets;
        }

        public int getTotalModels() { return totalModels; }
        public int getTotalResultSets() { return totalResultSets; }

        @Override
        public String toString() {
            return String.format("ResultSetStats{models=%d, resultSets=%d}", totalModels, totalResultSets);
        }
    }
}
