package com.dc.summer.model.data.message;

import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.export.config.ExcelConfig;
import com.dc.summer.model.export.config.ExportFormatConfig;
import com.dc.summer.model.export.config.SecurityConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 重构后的SQL导出消息类
 * 使用组合模式替代大量字段，提高代码可维护性
 * 
 * 注意：这是一个示例类，展示重构后的结构
 * 实际使用时可以逐步迁移现有的SqlExportMessage
 */
@Data
@ApiModel("重构后的SQL导出信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RefactoredSqlExportMessage extends Message {

    @NotBlank
    @ApiModelProperty(value = "用户ID", required = true, example = "wang")
    private String userId;

    @Valid
    @ApiModelProperty(value = "令牌配置")
    private TokenConfig tokenConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "导出格式配置", required = true)
    private ExportFormatConfig formatConfig;

    @Valid
    @ApiModelProperty(value = "Excel配置")
    private ExcelConfig excelConfig;

    @Valid
    @ApiModelProperty(value = "安全配置")
    private SecurityConfig securityConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "SQL选择模型列表", required = true)
    private List<SqlExportModel> sqlExportModels;

    @ApiModelProperty(value = "执行模型 - 包含SQL参数和结果集信息，用于避免重复查询")
    private List<ValidExecuteModel> batchExecuteModels;

    @ApiModelProperty(value = "结果集格式 - 用于避免重复查询时的格式信息")
    private ResultFormat resultFormat = new ResultFormat();

    @ApiModelProperty(value = "阶段 - 用于一条SQL生成多个结果集，导出需要传入stage，以区分是第几个结果集，从0开始")
    private int stage;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    /**
     * 从传统的SqlExportMessage转换
     */
    public static RefactoredSqlExportMessage fromLegacy(SqlExportMessage legacy) {
        RefactoredSqlExportMessage refactored = new RefactoredSqlExportMessage();
        
        // 基础信息
        refactored.setToken(legacy.getToken());
        refactored.setUserId(legacy.getUserId());
        refactored.setTokenConfig(legacy.getTokenConfig());
        refactored.setSqlExportModels(legacy.getSqlExportModels());
        refactored.setBatchExecuteModels(legacy.getBatchExecuteModels());
        refactored.setResultFormat(legacy.getResultFormat());
        refactored.setStage(legacy.getStage());
        refactored.setExecuteEvent(legacy.getExecuteEvent());
        
        // 格式配置
        refactored.setFormatConfig(ExportFormatConfig.builder()
                .exportType(legacy.getExportType())
                .fileCharset(legacy.getFileCharset())
                .textIdentifier(legacy.getTextIdentifier())
                .lineDelimiter(legacy.getLineDelimiter())
                .columnDelimiter(legacy.getColumnDelimiter())
                .otherDelimiter(legacy.getOtherDelimiter())
                .pageSelected(legacy.getPageSelected())
                .exportFileName(legacy.getExportFileName())
                .splitFileSize(legacy.getSplitFileSize())
                .build());
        
        // Excel配置
        refactored.setExcelConfig(ExcelConfig.builder()
                .useOriginalFormat(legacy.getExcelUseOriginalFormat())
                .useNumberFormat(legacy.isExcelUseNumberFormat())
                .datetimeFormat(legacy.getExcelDatetimeFormat())
                .build());
        
        // 安全配置
        refactored.setSecurityConfig(SecurityConfig.builder()
                .watermarkContent(legacy.getWatermarkContent())
                .watermarkAngle(legacy.getWatermarkAngle())
                .encryptPassword(legacy.getEncryptPassword())
                .exportDesensitize(legacy.isExportDesensitize())
                .build());
        
        return refactored;
    }

    /**
     * 转换为传统的SqlExportMessage（向后兼容）
     */
    public SqlExportMessage toLegacy() {
        SqlExportMessage legacy = new SqlExportMessage();
        
        // 基础信息
        legacy.setToken(this.getToken());
        legacy.setUserId(this.getUserId());
        legacy.setTokenConfig(this.getTokenConfig());
        legacy.setSqlExportModels(this.getSqlExportModels());
        legacy.setBatchExecuteModels(this.getBatchExecuteModels());
        legacy.setResultFormat(this.getResultFormat());
        legacy.setStage(this.getStage());
        legacy.setExecuteEvent(this.getExecuteEvent());
        
        // 格式配置
        if (this.getFormatConfig() != null) {
            legacy.setExportType(this.getFormatConfig().getExportType());
            legacy.setFileCharset(this.getFormatConfig().getFileCharset());
            legacy.setTextIdentifier(this.getFormatConfig().getTextIdentifier());
            legacy.setLineDelimiter(this.getFormatConfig().getLineDelimiter());
            legacy.setColumnDelimiter(this.getFormatConfig().getColumnDelimiter());
            legacy.setOtherDelimiter(this.getFormatConfig().getOtherDelimiter());
            legacy.setPageSelected(this.getFormatConfig().getPageSelected());
            legacy.setExportFileName(this.getFormatConfig().getExportFileName());
            legacy.setSplitFileSize(this.getFormatConfig().getSplitFileSize());
        }
        
        // Excel配置
        if (this.getExcelConfig() != null) {
            legacy.setExcelUseOriginalFormat(this.getExcelConfig().getUseOriginalFormat());
            legacy.setExcelUseNumberFormat(this.getExcelConfig().isUseNumberFormat());
            legacy.setExcelDatetimeFormat(this.getExcelConfig().getDatetimeFormat());
        }
        
        // 安全配置
        if (this.getSecurityConfig() != null) {
            legacy.setWatermarkContent(this.getSecurityConfig().getWatermarkContent());
            legacy.setWatermarkAngle(this.getSecurityConfig().getWatermarkAngle());
            legacy.setEncryptPassword(this.getSecurityConfig().getEncryptPassword());
            legacy.setExportDesensitize(this.getSecurityConfig().isExportDesensitize());
        }
        
        return legacy;
    }

    /**
     * 验证消息的完整性
     */
    public boolean isValid() {
        if (userId == null || formatConfig == null || sqlExportModels == null || sqlExportModels.isEmpty()) {
            return false;
        }
        
        if (!formatConfig.isValid()) {
            return false;
        }
        
        if (excelConfig != null && !excelConfig.isValid()) {
            return false;
        }
        
        if (securityConfig != null && !securityConfig.isValid()) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取导出类型
     */
    public ExportType getExportType() {
        return formatConfig != null ? formatConfig.getExportType() : null;
    }

    /**
     * 获取任务描述
     */
    public String getTaskDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("导出任务 - ");
        desc.append("类型: ").append(getExportType());
        desc.append(", 用户: ").append(userId);
        desc.append(", 结果集数量: ").append(sqlExportModels != null ? sqlExportModels.size() : 0);
        
        if (securityConfig != null) {
            desc.append(", 安全级别: ").append(securityConfig.getSecurityLevel());
        }
        
        return desc.toString();
    }
}
