package com.dc.summer.service.receiver;

import com.dc.summer.model.DBFetchProgress;
import com.dc.summer.model.data.result.DataResultNodeRow;
import com.dc.summer.model.data.result.DataResultVisitor;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPDataKind;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.summer.exec.model.data.AbstractDataReceiver;
import com.dc.springboot.core.model.type.BigDataType;
import com.dc.summer.model.type.WebDataFormat;
import com.dc.summer.model.type.WebSQLConstants;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.summer.service.data.OriginalData;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.service.sql.WebSQLDataDraw;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLResultsInfo;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.*;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.data.DBDValueError;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.sql.DBQuotaException;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.utils.CommonUtils;
import lombok.Getter;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public abstract class WebDataReceiver extends AbstractDataReceiver {

    protected static final Log log = Log.getLog(WebDataReceiver.class);

    protected List<Object[]> rows = new ArrayList<>();

    protected Number rowLimit;

    protected final WebSQLDataDraw dataDraw;

    protected final WebSQLContextInfo contextInfo;
    protected final WebDataFormat dataFormat;

    @Getter
    protected final WebSQLQueryResultSet webResultSet = new WebSQLQueryResultSet();

    public WebDataReceiver(WebSQLContextInfo contextInfo,
                           DBSDataContainer dataContainer,
                           WebDataFormat dataFormat) {
        super(dataContainer);
        this.contextInfo = contextInfo;
        this.dataFormat = dataFormat;
        this.dataDraw = new WebSQLDataDraw();
    }

    @Override
    public void fetchRow(DBCSession session, DBCResultSet resultSet) throws DBCException {
        Object[] row = new Object[bindings.length];
        for (int i = 0; i < bindings.length; i++) {
            DBDAttributeBinding binding = bindings[i];
            try {
                Object cellValue = binding.getValueHandler().fetchValueObject(
                        resultSet.getSession(),
                        resultSet,
                        binding.getMetaAttribute(),
                        i);
                row[i] = cellValue;
            } catch (Throwable e) {
                row[i] = new DBDValueError(e);
            }
        }
        rows.add(row);
        if (rowLimit != null && rows.size() > rowLimit.longValue()) {
            throw new DBQuotaException(
                    "Result set rows quota exceeded", WebSQLConstants.QUOTA_PROP_ROW_LIMIT, rowLimit.longValue(), rows.size());
        }
    }

    @Override
    public void fetchEnd(DBCSession session, DBCResultSet resultSet) throws DBCException {
        DBSEntity entity = dataContainer instanceof DBSEntity ? (DBSEntity) dataContainer : null;

        if (ArrayUtils.isEmpty(bindings)) {
            return;
        }
        boolean separateData = bindings[0].getDataKind() != DBPDataKind.DOCUMENT;
        List<Object[]> separateRows = new ArrayList<>(rows);
        DBDAttributeBinding[] separateBindings = bindings;

        try {
            DBExecUtils.bindAttributes(session, entity, resultSet, bindings, rows);
        } catch (DBException e) {
            log.error("Error binding attributes", e);
        }

        if (dataFormat != WebDataFormat.document) {
            convertComplexValuesToRelationalView(session);
        }

        bindingPrimaryKey(bindings);

        // Set proper order position
        for (int i = 0; i < bindings.length; i++) {
            DBDAttributeBinding binding = bindings[i];
            if (binding instanceof DBDAttributeBindingType) {
                // Type bindings are produced by dynamic map resolve
                // Their positions are valid only within parent value
                // In web we make plain list of attributes so we must reorder leaf attributes
                ((DBDAttributeBindingType) binding).setOrdinalPosition(i);
            }
        }

        initDateTimePreferences();

        // Convert row values
        if (separateData) {
            separateRows = rows;
            separateBindings = bindings;
        } else {
            webResultSet.setOriginalColumnsByBinding(separateBindings);
        }
        OriginalData originalData = new OriginalData(rows, needOriginalRows());

        boolean isContainClob = false;
        boolean isDateType = false;

        DataResultVisitor dataResultVisitor = getDataResultVisitor();

        DBFetchProgress fetchProgress = new DBFetchProgress(monitor);

        monitor.subTask("Start processing rows");

        for (int u = 0; u < separateRows.size(); u++) {
            Object[] row = separateRows.get(u);

            for (int i = 0; i < separateBindings.length; i++) {
                DBDAttributeBinding binding = separateBindings[i];

                isDateType |= DBPDataKind.DATETIME.equals(binding.getDataKind());

                // 格式化结果集数据
                row[i] = dataDraw.makeWebCellValue(
                        binding,
                        row[i]);

                int isBigDataType = blobDataFormat(row, i, binding, getDataDesensitizeProcessor().isDesensitization(row[i], i));
                isContainClob |= isBigDataType == BigDataType.CLOB.getValue();

                if (row[i] instanceof String) {
                    row[i] = row[i].toString().replaceAll("\u0000", " ");
                }

                originalData.add(row[i]);
                // 执行脱敏
                row[i] = getDataDesensitizeProcessor().execDesensitization(row[i], i, isBigDataType, separateBindings);
            }
            if (dataResultVisitor != null) {
                DataResultNodeRow dataResultNodeRow = new DataResultNodeRow();
                dataResultNodeRow.setRow(row);
                dataResultNodeRow.accept(dataResultVisitor);
                separateRows.set(u, dataResultNodeRow.getRow());
            }
            originalData.flow();
            fetchProgress.monitorRowFetch();
        }

        monitor.subTask(fetchProgress.getRowCount() + " rows processed");

        //db2 如果结果集已经是最后一行，则再次路由下一行，会报错
        try {
            webResultSet.setHasMoreData(resultSet.overMaxRow());
        } catch (DBCException e) {
            webResultSet.setHasMoreData(false);
            log.warn("超过最大行执行错误: " + e.getMessage());
        }
        webResultSet.setSensitiveColumns(getDataDesensitizeProcessor().getSensitiveColumns());
        webResultSet.setColumnsByBinding(bindings);
        webResultSet.setRows(separateRows.toArray(new Object[0][]));
        webResultSet.setOriginalRows(originalData.get());

        WebSQLResultsInfo resultsInfo = contextInfo.saveResult(dataContainer, bindings);
        resultsInfo.setContainClob(isContainClob);
        resultsInfo.setDateType(isDateType);
        fillWebSQLResultsInfo(resultsInfo);
        webResultSet.setResultsInfo(resultsInfo);

        if (!needCacheResult()) {
            contextInfo.dispose();
        }

        boolean isSingleEntity = DBExecUtils.detectSingleSourceTable(bindings) != null;

        webResultSet.setSingleEntity(isSingleEntity);

        DBDRowIdentifier rowIdentifier = resultsInfo.getDefaultRowIdentifier();
        webResultSet.setHasRowIdentifier(rowIdentifier != null && rowIdentifier.isValidIdentifier());
    }

    private void initDateTimePreferences() {
        DBPPreferenceStore store = ModelPreferences.getPreferences();
        if (getResultFormat() != null && StringUtils.isNotBlank(getResultFormat().getDateTimeFormat())) {
            store.setValue("dataformat.type.timestamp.pattern", getResultFormat().getDateTimeFormat());
            store.setValue("dataformat.type.timestamptz.pattern", getResultFormat().getDateTimeFormat());
        } else {
            store.setValue("dataformat.type.timestamp.pattern", "");
            store.setValue("dataformat.type.timestamptz.pattern", "");
        }
        if (getResultFormat() != null && StringUtils.isNotBlank(getResultFormat().getDateFormat())) {
            store.setValue("dataformat.type.date.pattern", getResultFormat().getDateFormat());
        } else {
            store.setValue("dataformat.type.date.pattern", "");
        }
        if (getResultFormat() != null && StringUtils.isNotBlank(getResultFormat().getTimeFormat())) {
            store.setValue("dataformat.type.time.pattern", getResultFormat().getTimeFormat());
        } else {
            store.setValue("dataformat.type.time.pattern", "");
        }
        monitor.subTask("Complete DateTime Preferences");
    }

    protected DataResultVisitor getDataResultVisitor() {
        return null;
    }

    protected boolean needCacheResult() {
        return false;
    }

    protected boolean needOriginalRows() {
        return false;
    }

    protected ResultFormat getResultFormat() {
        return new ResultFormat();
    }

    protected int blobDataFormat(Object[] row, int i, DBDAttributeBinding binding, boolean isDesensitization) {
        return BigDataType.NO.getValue();
    }

    protected DataDesensitizeProcessor getDataDesensitizeProcessor() {
        return new DataDesensitizeProcessor();
    }

    protected void fillWebSQLResultsInfo(WebSQLResultsInfo resultsInfo) {
    }

    @Override
    public void close() {
        rows.clear();
    }

    protected void convertComplexValuesToRelationalView(DBCSession session) {
        // Here we get leaf attributes and refetch them into plain tabl structure
        List<DBDAttributeBinding> leafBindings = new ArrayList<>();
        for (DBDAttributeBinding attr : bindings) {
            collectLeafBindings(attr, leafBindings, false);
        }
        if (CommonUtils.equalObjects(bindings, leafBindings)) {
            // No complex types
            return;
        }

        // Convert original rows into new rows with leaf attributes
        // Extract values for leaf attributes from original row
        DBDAttributeBinding[] leafAttributes = leafBindings.toArray(new DBDAttributeBinding[0]);
        List<Object[]> newRows = new ArrayList<>();
        for (Object[] row : rows) {
            Object[] newRow = new Object[leafBindings.size()];
            for (int i = 0; i < leafBindings.size(); i++) {
                DBDAttributeBinding leafAttr = leafBindings.get(i);
                try {
                    //Object topValue = row[leafAttr.getTopParent().getOrdinalPosition()];
                    Object cellValue = DBUtils.getAttributeValue(leafAttr, leafAttributes, row);
/*
                    Object cellValue = leafAttr.getValueHandler().getValueFromObject(
                        session,
                        leafAttr,
                        topValue,
                        false,
                        false);
*/
                    newRow[i] = cellValue;
                } catch (Exception e) {
                    newRow[i] = new DBDValueError(e);
                }
            }
            newRows.add(newRow);
        }
        this.bindings = leafAttributes;
        this.rows = newRows;
        session.getProgressMonitor().subTask("Complete View convert");
    }

    public void bindingPrimaryKey(DBDAttributeBinding[] bindings) {
        for (DBDAttributeBinding binding : bindings) {
            if (getPrimaryKeyColumns().stream().anyMatch(s -> binding.getName().equalsIgnoreCase(s))) {
                binding.setPrimaryKey(true);
            }
        }
        monitor.subTask("Complete PrimaryKey binding");
    }

    protected List<String> getPrimaryKeyColumns() {
        return Collections.emptyList();
    }

    protected void collectLeafBindings(DBDAttributeBinding attr, List<DBDAttributeBinding> leafBindings, boolean parentIsDoc) {
        List<DBDAttributeBinding> nestedBindings = attr.getNestedBindings();
        if (CommonUtils.isEmpty(nestedBindings) || parentIsDoc) {
            leafBindings.add(attr);
        } else {
            for (DBDAttributeBinding nested : nestedBindings) {
                collectLeafBindings(nested, leafBindings, attr.getDataKind() == DBPDataKind.DOCUMENT);
            }
        }
    }

}
