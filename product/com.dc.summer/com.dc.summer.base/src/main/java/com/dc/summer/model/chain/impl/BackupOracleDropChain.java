package com.dc.summer.model.chain.impl;

import com.dc.springboot.core.component.Resource;
import com.dc.summer.DBException;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.springboot.core.model.recovery.BackupModel;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.service.receiver.ResultObjectDataReceiver;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.repository.mysql.mapper.OracleRecycleBinMapper;
import com.dc.repository.mysql.model.OracleRecycleBin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class BackupOracleDropChain extends AbstractChain<WebSQLQueryResult> {

    private final DBRProgressMonitor monitor;

    private final BackupModel backupModel;

    private final boolean allowBackup;

    private final OracleRecycleBinMapper oracleRecycleBinMapper = Resource.getBean(OracleRecycleBinMapper.class);

    private final String userId;

    /**
     * 原始sql
     */
    private final String originSql;

    private DBPDataSource dataSource;

    private DBCExecutionContext executionContext;

    private final WebSQLContextInfo contextInfo;

    public BackupOracleDropChain(DBRProgressMonitor monitor,
                                 WebSQLContextInfo contextInfo,
                                 BackupModel backupModel,
                                 String originSql,
                                 String userId,
                                 String operation) {
        this.monitor = monitor;
        this.dataSource = contextInfo.getDataSource();
        try {
            this.executionContext = contextInfo.getExecutionContext();
        } catch (DBException e) {
            log.error("OracleDropChain error : ", e);
        }
        this.backupModel = backupModel;
        this.allowBackup = DatabaseType.ORACLE.getValue().equals(backupModel.getDbType()) && SQLConstants.KEYWORD_DROP.equalsIgnoreCase(operation);
        this.originSql = originSql;
        this.userId = userId;
        this.contextInfo = contextInfo;
    }

    @Override
    public boolean proceed(WebSQLQueryResult queryResult) {
        final boolean result = true;

        if (!allowBackup) {
            return result;
        }

        if (StringUtils.isBlank(backupModel.getBackupTableName())) {
            return result;
        }

        String backUpSql = String.format("SELECT OBJECT_NAME FROM dba_recyclebin WHERE OWNER = '%s' AND TYPE = 'TABLE' AND ORIGINAL_NAME = '%s' ORDER BY DROPTIME  DESC", backupModel.getSchemaName(), backupModel.getBackupTableName());

        String newTableName = null;
        List<Map<String, Object>> objects = new ArrayList<>();
        SimpleContainer simpleContainer = new SimpleContainer(dataSource);
        try (ResultObjectDataReceiver resultObjectDataReceiver = new ResultObjectDataReceiver(simpleContainer)) {
            DBExecUtils.tryExecuteRecover(executionContext, dataSource, param ->
                    DBExecUtils.executeQuery(monitor, executionContext, "Backup Select", backUpSql, resultObjectDataReceiver, false),
                    contextInfo.recoverBefore(), contextInfo.recoverAfter());
            objects.addAll(resultObjectDataReceiver.getObjects());
        } catch (DBException e) {
            try (ResultObjectDataReceiver resultObjectDataReceiver = new ResultObjectDataReceiver(simpleContainer)) {
                DBExecUtils.tryExecuteRecover(executionContext, dataSource, param ->
                        DBExecUtils.executeQuery(monitor, executionContext, "Backup Select", backUpSql, resultObjectDataReceiver, false),
                        contextInfo.recoverBefore(), contextInfo.recoverAfter());
                objects.addAll(resultObjectDataReceiver.getObjects());
            } catch (DBException dbException) {
                log.error("OracleDropChain proceed error : ", dbException);
            }
            log.error("执行备份SQL失败！", e);
        }
        if (!objects.isEmpty()) {
            newTableName = (String) objects.get(0).get("OBJECT_NAME");
        }
        if (StringUtils.isBlank(newTableName)) {
            log.debug("新表名为空");
            return result;
        }
        OracleRecycleBin oracleRecycleBin = new OracleRecycleBin();
        oracleRecycleBin.setInstance_id(backupModel.getConnectId());
        oracleRecycleBin.setInstance_name(backupModel.getInstanceName());
        oracleRecycleBin.setSchema_id(backupModel.getSchemaId());
        oracleRecycleBin.setSchema_name(backupModel.getSchemaName());
        oracleRecycleBin.setTable_name(backupModel.getBackupTableName());
        oracleRecycleBin.setSql(originSql);
        oracleRecycleBin.setTemporary_table_name(newTableName);
        oracleRecycleBin.setOperator(userId);
        oracleRecycleBinMapper.add(oracleRecycleBin);

        return result;
    }
}
