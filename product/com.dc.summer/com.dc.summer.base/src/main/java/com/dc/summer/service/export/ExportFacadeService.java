package com.dc.summer.service.export;

import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.export.builder.ExportTaskBuilder;
import com.dc.summer.service.export.factory.ExportStrategyFactory;
import com.dc.summer.service.export.strategy.ExportStrategy;
import com.dc.summer.service.sql.WebSQLContextInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 导出门面服务
 * 提供统一的导出服务入口，封装复杂的导出流程
 */
@Slf4j
@Service
public class ExportFacadeService {

    @Autowired
    private ExportStrategyFactory strategyFactory;

    @Autowired
    private ExportTaskBuilder taskBuilder;

    @Autowired
    private ResultSetService resultSetService;

    @Autowired
    private ResultService resultService;

    @Autowired
    private MessageService messageService;

    /**
     * 执行SQL导出
     * 
     * @param message SQL导出消息
     * @return 异步任务信息
     */
    public WebAsyncTaskInfo executeExport(SqlExportMessage message) {
        log.info("开始执行导出任务 - 用户: {}, 类型: {}", message.getUserId(), message.getExportType());
        
        try {
            // 1. 构建导出上下文
            ExportContext context = taskBuilder.buildFromSqlExportMessage(message);
            
            // 2. 验证上下文
            validateExportContext(context);
            
            // 3. 获取SQL上下文
            WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(
                    context.getToken(), context.getTokenConfig());
            
            // 4. 创建异步任务
            return createAsyncExportTask(context, contextInfo);
            
        } catch (Exception e) {
            log.error("创建导出任务失败", e);
            throw new RuntimeException("创建导出任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证导出上下文
     */
    private void validateExportContext(ExportContext context) {
        if (!context.isValid()) {
            throw new IllegalArgumentException("导出上下文验证失败");
        }
        
        if (!strategyFactory.isSupported(context.getFormatConfig().getExportType())) {
            throw new IllegalArgumentException("不支持的导出类型: " + context.getFormatConfig().getExportType());
        }
        
        log.debug("导出上下文验证通过: {}", context.getTaskDescription());
    }

    /**
     * 创建异步导出任务
     */
    private WebAsyncTaskInfo createAsyncExportTask(ExportContext context, WebSQLContextInfo contextInfo) {
        AtomicBoolean firstExecute = new AtomicBoolean(true);
        
        return contextInfo.createAsyncTask("SQL Export", (taskId, monitor) -> {
            try {
                log.info("执行导出任务: {}", context.getTaskDescription());
                
                // 1. 获取导出策略
                ExportStrategy strategy = strategyFactory.getStrategy(context.getFormatConfig().getExportType());
                
                // 2. 获取结果集数据
                List<WebSQLQueryResultSet> resultSets = getResultSets(context, contextInfo);
                
                // 3. 执行导出
                WebSQLTransferResult transferResult = strategy.export(context, resultSets);
                
                // 4. 保存结果
                saveExportResult(taskId, transferResult, contextInfo);
                
                // 5. 发送告警消息（如果需要）
                sendAlertMessageIfNeeded(context, transferResult, contextInfo, firstExecute);
                
                log.info("导出任务执行完成: {}", context.getTaskDescription());
                
            } catch (Exception e) {
                log.error("导出任务执行失败: {}", context.getTaskDescription(), e);
                throw e;
            } finally {
                firstExecute.set(false);
            }
        });
    }

    /**
     * 获取结果集数据
     */
    private List<WebSQLQueryResultSet> getResultSets(ExportContext context, WebSQLContextInfo contextInfo) {
        // 首先尝试从缓存获取结果集
        Optional<List<WebSQLQueryResultSet>> optionalResultSets = 
                resultSetService.getResultSetsOptional(context);
        
        if (optionalResultSets.isPresent()) {
            log.debug("从缓存获取结果集成功，数量: {}", optionalResultSets.get().size());
            return optionalResultSets.get();
        }
        
        // 如果缓存失效，返回空列表，让WebDataTransfer处理重新查询
        log.info("缓存失效，返回空结果集列表以便重新查询");
        return Collections.emptyList();
    }

    /**
     * 保存导出结果
     */
    private void saveExportResult(String taskId, WebSQLTransferResult transferResult, WebSQLContextInfo contextInfo) {
        try {
            WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo();
            executeInfo.setTransferResult(transferResult);
            resultService.saveStringValue(taskId, executeInfo, contextInfo.getExpirationTime());
            log.debug("导出结果保存成功，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("保存导出结果失败，任务ID: {}", taskId, e);
            throw new RuntimeException("保存导出结果失败", e);
        }
    }

    /**
     * 发送告警消息（如果需要）
     */
    private void sendAlertMessageIfNeeded(ExportContext context, WebSQLTransferResult transferResult, 
                                        WebSQLContextInfo contextInfo, AtomicBoolean firstExecute) {
        if (firstExecute.get() && 
            ConnectionPatternType.of(context.getExecuteEvent().getConnectionPattern()).existsSecurityCollaboration()) {
            
            try {
                int securityRuleSetId = contextInfo.getConnection().getSecurityRuleSetId();
                WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo();
                executeInfo.setTransferResult(transferResult);
                
                // 这里需要将ExportContext转换为SqlExportMessage以兼容现有的消息服务
                SqlExportMessage message = convertContextToMessage(context);
                messageService.sendAlertMessage(message, executeInfo, securityRuleSetId);
                
                log.info("安全告警消息发送成功");
            } catch (Exception e) {
                log.error("发送安全告警消息失败", e);
                // 不抛出异常，避免影响主流程
            }
        }
    }

    /**
     * 将ExportContext转换为SqlExportMessage（临时兼容方法）
     * TODO: 后续可以重构MessageService以直接支持ExportContext
     */
    private SqlExportMessage convertContextToMessage(ExportContext context) {
        SqlExportMessage message = new SqlExportMessage();
        message.setToken(context.getToken());
        message.setUserId(context.getUserId());
        message.setTokenConfig(context.getTokenConfig());
        message.setExecuteEvent(context.getExecuteEvent());
        // 设置其他必要字段...
        return message;
    }
}
