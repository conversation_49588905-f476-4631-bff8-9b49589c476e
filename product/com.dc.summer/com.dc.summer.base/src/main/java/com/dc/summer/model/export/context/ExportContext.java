package com.dc.summer.model.export.context;

import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.export.config.ExcelConfig;
import com.dc.summer.model.export.config.ExportFormatConfig;
import com.dc.summer.model.export.config.SecurityConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 导出上下文类
 * 包含导出操作所需的所有上下文信息
 */
@Data
@Builder
@ApiModel("导出上下文")
public class ExportContext {

    @NotBlank
    @ApiModelProperty(value = "会话令牌", required = true)
    private String token;

    @NotBlank
    @ApiModelProperty(value = "用户ID", required = true, example = "wang")
    private String userId;

    @Valid
    @ApiModelProperty(value = "令牌配置")
    private TokenConfig tokenConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "导出格式配置", required = true)
    private ExportFormatConfig formatConfig;

    @Valid
    @ApiModelProperty(value = "Excel配置")
    private ExcelConfig excelConfig;

    @Valid
    @ApiModelProperty(value = "安全配置")
    private SecurityConfig securityConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "SQL选择模型列表", required = true)
    private List<SqlExportModel> sqlExportModels;

    @ApiModelProperty(value = "执行模型 - 包含SQL参数和结果集信息，用于避免重复查询")
    private List<ValidExecuteModel> batchExecuteModels;

    @ApiModelProperty(value = "结果集格式 - 用于避免重复查询时的格式信息")
    @Builder.Default
    private ResultFormat resultFormat = new ResultFormat();

    @ApiModelProperty(value = "阶段 - 用于一条SQL生成多个结果集，导出需要传入stage，以区分是第几个结果集，从0开始")
    @Builder.Default
    private int stage = 0;

    @Valid
    @ApiModelProperty(value = "告警消息")
    @Builder.Default
    private ExecuteEvent executeEvent = new ExecuteEvent();

    /**
     * 创建默认的导出上下文
     */
    public static ExportContext createDefault(String token, String userId) {
        return ExportContext.builder()
                .token(token)
                .userId(userId)
                .formatConfig(ExportFormatConfig.builder().build())
                .excelConfig(ExcelConfig.createDefault())
                .securityConfig(SecurityConfig.createDefault())
                .resultFormat(new ResultFormat())
                .stage(0)
                .executeEvent(new ExecuteEvent())
                .build();
    }

    /**
     * 验证上下文的完整性
     */
    public boolean isValid() {
        if (token == null || userId == null || formatConfig == null) {
            return false;
        }
        
        if (!formatConfig.isValid()) {
            return false;
        }
        
        if (excelConfig != null && !excelConfig.isValid()) {
            return false;
        }
        
        if (securityConfig != null && !securityConfig.isValid()) {
            return false;
        }
        
        if (sqlExportModels == null || sqlExportModels.isEmpty()) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取导出任务描述
     */
    public String getTaskDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("导出任务 - ");
        desc.append("类型: ").append(formatConfig.getExportType());
        desc.append(", 用户: ").append(userId);
        desc.append(", 结果集数量: ").append(sqlExportModels.size());
        
        if (securityConfig != null) {
            desc.append(", 安全级别: ").append(securityConfig.getSecurityLevel());
        }
        
        return desc.toString();
    }

    /**
     * 是否需要Excel特定处理
     */
    public boolean needsExcelProcessing() {
        return formatConfig.getExportType().name().contains("XLS");
    }

    /**
     * 是否需要安全处理
     */
    public boolean needsSecurityProcessing() {
        return securityConfig != null && 
               (securityConfig.isWatermarkEnabled() || 
                securityConfig.isEncryptionEnabled() || 
                securityConfig.isExportDesensitize());
    }
}
