package com.dc.summer.service.export.builder;

import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.chain.StreamChainRunner;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.model.chain.impl.ToastChain;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.transfer.WebDataTransferName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;
import java.util.List;

/**
 * 导出参数建造者
 * 使用建造者模式从ExportContext构建导出所需的各种参数
 * 
 * 应用设计模式：
 * 1. Builder Pattern - 构建复杂的导出参数
 * 2. Strategy Pattern - 处理不同类型的配置策略
 */
@Slf4j
public class ExportParameterBuilder {

    private final ExportContext context;

    public ExportParameterBuilder(ExportContext context) {
        this.context = context;
    }

    /**
     * 构建用户ID
     */
    public String buildUserId() {
        return context.getUserId();
    }

    /**
     * 构建TokenConfig
     */
    public TokenConfig buildTokenConfig() {
        return context.getTokenConfig();
    }

    /**
     * 构建自定义文件名
     */
    public String buildCustomFileName() {
        return context.getFormatConfig().getExportFileName();
    }

    /**
     * 构建传输ZIP名称
     */
    public WebDataTransferName buildTransferZipName() {
        return new WebDataTransferName.ZipName();
    }

    /**
     * 构建传输文件名
     */
    public WebDataTransferName buildTransferFileName() {
        String customFileName = context.getFormatConfig().getExportFileName();
        if (StringUtils.isNotBlank(customFileName)) {
            return new WebDataTransferName.CustomResultSetName(customFileName);
        } else {
            return new WebDataTransferName.ResultSetName();
        }
    }

    /**
     * 构建是否需要压缩和上传
     */
    public boolean buildNeedToZipAndUpload() {
        // 根据导出配置决定是否需要压缩上传
        return true; // 默认需要压缩上传
    }

    /**
     * 构建是否为单个文件
     */
    public boolean buildIsSingle() {
        // 根据SQL模型数量判断是否为单个文件
        return context.getSqlExportModels().size() == 1;
    }

    /**
     * 构建是否删除结果信息
     */
    public boolean buildDeleteResultInfo() {
        // 默认不删除结果信息，保留用于后续处理
        return false;
    }

    /**
     * 构建来源类型
     */
    public OriginType buildOriginType() {
        // 根据上下文判断来源类型
        if (context.getBatchExecuteModels() != null && !context.getBatchExecuteModels().isEmpty()) {
            return OriginType.JOB_EXECUTE;
        }
        return OriginType.MANUAL_EXECUTE;
    }

    /**
     * 构建流处理函数
     * 使用策略模式处理不同的流处理需求
     */
    public Function<List<com.dc.springboot.core.model.result.WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> buildStreamFunction() {
        return resultSets -> ChainBuilder.build(new StreamChainRunner<WebSQLTransferResult>())
                .addChain(new ToastChain(resultSets));
    }

    /**
     * 构建文件编码
     */
    public String buildFileCharset() {
        return context.getFormatConfig().getFileCharset();
    }

    /**
     * 构建加密密码
     */
    public String buildEncryptPassword() {
        if (context.getSecurityConfig() != null) {
            return context.getSecurityConfig().getEncryptPassword();
        }
        return null;
    }

    /**
     * 构建是否使用Excel数字格式
     */
    public boolean buildExcelUseNumberFormat() {
        if (context.getExcelConfig() != null) {
            return context.getExcelConfig().isUseNumberFormat();
        }
        return false;
    }

    /**
     * 构建是否导出脱敏
     */
    public boolean buildExportDesensitize() {
        if (context.getSecurityConfig() != null) {
            return context.getSecurityConfig().isExportDesensitize();
        }
        return false;
    }

    /**
     * 构建水印内容
     */
    public String buildWatermarkContent() {
        if (context.getSecurityConfig() != null) {
            return context.getSecurityConfig().getWatermarkContent();
        }
        return null;
    }

    /**
     * 构建水印角度
     */
    public Integer buildWatermarkAngle() {
        if (context.getSecurityConfig() != null) {
            return context.getSecurityConfig().getWatermarkAngle();
        }
        return null;
    }

    /**
     * 构建分割文件大小
     */
    public Long buildSplitFileSize() {
        return context.getFormatConfig().getSplitFileSize();
    }

    /**
     * 验证构建参数的有效性
     */
    public boolean validateParameters() {
        if (context == null) {
            log.error("ExportContext不能为空");
            return false;
        }

        if (StringUtils.isBlank(context.getUserId())) {
            log.error("用户ID不能为空");
            return false;
        }

        if (context.getFormatConfig() == null) {
            log.error("导出格式配置不能为空");
            return false;
        }

        if (context.getSqlExportModels() == null || context.getSqlExportModels().isEmpty()) {
            log.error("SQL导出模型不能为空");
            return false;
        }

        return true;
    }

    /**
     * 记录构建信息
     */
    public void logBuildInfo() {
        log.info("构建导出参数 - 用户: {}, 导出类型: {}, 文件数量: {}", 
                context.getUserId(),
                context.getFormatConfig().getExportType(),
                context.getSqlExportModels().size());
    }
}
