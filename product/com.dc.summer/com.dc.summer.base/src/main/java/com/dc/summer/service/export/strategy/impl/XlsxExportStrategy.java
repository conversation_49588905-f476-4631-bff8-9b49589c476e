package com.dc.summer.service.export.strategy.impl;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.export.strategy.AbstractExportStrategy;
import com.dc.summer.service.sql.WebSQLTransferResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Excel导出策略实现
 * 处理XLSX和XLS格式的导出
 */
@Slf4j
@Component
public class XlsxExportStrategy extends AbstractExportStrategy {

    private static final List<ExportType> SUPPORTED_TYPES = Arrays.asList(
            ExportType.XLSX, 
            ExportType.XLS
    );

    @Override
    protected WebSQLTransferResult doExport(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.info("执行Excel导出策略");
        
        // 记录导出信息
        logExportInfo(context, resultSets);
        
        // 应用Excel特定的配置
        applyExcelConfiguration(context);
        
        // 执行导出
        return executeTransfer(context, resultSets);
    }

    @Override
    public boolean supports(ExportType exportType) {
        return SUPPORTED_TYPES.contains(exportType);
    }

    @Override
    public String getStrategyName() {
        return "Excel导出策略";
    }

    @Override
    public List<ExportType> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public boolean validateParameters(ExportContext context) {
        if (!super.validateParameters(context)) {
            return false;
        }
        
        // Excel特定的参数验证
        if (context.getExcelConfig() == null) {
            log.warn("Excel配置为空，将使用默认配置");
            return true; // 可以使用默认配置
        }
        
        return context.getExcelConfig().isValid();
    }

    @Override
    public List<WebSQLQueryResultSet> preprocessData(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.debug("Excel导出数据预处理");
        
        // Excel特定的数据预处理
        if (context.getExcelConfig() != null && context.getExcelConfig().isUseNumberFormat()) {
            // 处理数字格式
            processNumberFormat(resultSets);
        }
        
        return resultSets;
    }

    @Override
    public WebSQLTransferResult postprocessResult(ExportContext context, WebSQLTransferResult result) {
        log.debug("Excel导出结果后处理");
        
        // Excel特定的后处理
        if (context.needsSecurityProcessing()) {
            // 应用安全处理（水印、加密等）
            applySecurityProcessing(context, result);
        }
        
        return result;
    }

    /**
     * 应用Excel配置
     */
    private void applyExcelConfiguration(ExportContext context) {
        if (context.getExcelConfig() == null) {
            log.info("使用默认Excel配置");
            return;
        }
        
        log.info("应用Excel配置 - 原格式: {}, 数字格式: {}, 日期格式: {}", 
                context.getExcelConfig().getUseOriginalFormat(),
                context.getExcelConfig().isUseNumberFormat(),
                context.getExcelConfig().getDatetimeFormat());
    }

    /**
     * 处理数字格式
     */
    private void processNumberFormat(List<WebSQLQueryResultSet> resultSets) {
        log.debug("处理Excel数字格式");
        // 这里可以添加具体的数字格式处理逻辑
        // 例如：确保数字类型的数据在Excel中显示为数字而不是文本
    }

    /**
     * 应用安全处理
     */
    private void applySecurityProcessing(ExportContext context, WebSQLTransferResult result) {
        log.info("应用Excel安全处理");
        
        if (context.getSecurityConfig().isWatermarkEnabled()) {
            log.info("应用水印: {}, 角度: {}", 
                    context.getSecurityConfig().getWatermarkContent(),
                    context.getSecurityConfig().getWatermarkAngle());
            // 添加水印处理逻辑
        }
        
        if (context.getSecurityConfig().isEncryptionEnabled()) {
            log.info("应用Excel加密保护");
            // 添加加密处理逻辑
        }
        
        if (context.getSecurityConfig().isExportDesensitize()) {
            log.info("应用数据脱敏");
            // 添加脱敏处理逻辑
        }
    }
}
