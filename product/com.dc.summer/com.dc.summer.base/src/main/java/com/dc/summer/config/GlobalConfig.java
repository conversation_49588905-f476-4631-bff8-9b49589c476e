package com.dc.summer.config;

import com.dc.summer.registry.center.Global;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "summer.global", ignoreInvalidFields = true)
public class GlobalConfig extends Global.Config {

    @PostConstruct
    @Override
    public void init() {
        super.init();
    }

}
