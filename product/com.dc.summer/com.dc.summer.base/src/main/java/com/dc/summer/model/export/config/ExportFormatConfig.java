package com.dc.summer.model.export.config;

import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.type.TextIdentifierType;
import com.dc.summer.model.type.LineDelimiterType;
import com.dc.summer.model.type.PageSelectedType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;

import javax.validation.constraints.NotNull;

/**
 * 导出格式配置类
 * 负责文件格式相关的配置参数
 */
@Data
@Builder
@ApiModel("导出格式配置")
public class ExportFormatConfig {

    @NotNull
    @ApiModelProperty(value = "导出类型", required = true, example = "XLSX")
    private ExportType exportType;

    @ApiModelProperty(value = "文件编码", example = "utf-8")
    @Builder.Default
    private String fileCharset = "UTF-8";

    @ApiModelProperty(value = "文本识别符", example = "DOUBLE_QUOTE")
    @Builder.Default
    private TextIdentifierType textIdentifier = TextIdentifierType.DOUBLE_QUOTE;

    @ApiModelProperty(value = "行分隔符", example = "CRLF")
    @Builder.Default
    private LineDelimiterType lineDelimiter = LineDelimiterType.CRLF;

    @ApiModelProperty(value = "列分隔符", example = "SEMICOLON")
    private ColumnDelimiterType columnDelimiter;

    @ApiModelProperty(value = "其他分隔符", example = "@")
    private String otherDelimiter;

    @NotNull
    @ApiModelProperty(value = "已选择页面", required = true, example = "CURRENT_PAGE")
    @Builder.Default
    private PageSelectedType pageSelected = PageSelectedType.CURRENT_PAGE;

    @ApiModelProperty(value = "导出文件名")
    private String exportFileName;

    @ApiModelProperty(value = "拆分文件大小 - 单位MB")
    private Long splitFileSize;

    /**
     * 根据导出类型设置默认的列分隔符
     */
    public void setDefaultColumnDelimiter() {
        if (columnDelimiter == null) {
            switch (exportType) {
                case CSV:
                    this.columnDelimiter = ColumnDelimiterType.COMMA;
                    break;
                case TXT:
                    this.columnDelimiter = ColumnDelimiterType.TAB;
                    break;
                default:
                    // Excel等其他格式不需要列分隔符
                    break;
            }
        }
    }

    /**
     * 验证配置的有效性
     */
    public boolean isValid() {
        if (exportType == null) {
            return false;
        }
        
        // 对于文本格式，必须有列分隔符
        if ((exportType == ExportType.CSV || exportType == ExportType.TXT) 
            && columnDelimiter == null && otherDelimiter == null) {
            return false;
        }
        
        return true;
    }
}
