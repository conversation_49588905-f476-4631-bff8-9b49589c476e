package com.dc.summer.service.export.adapter;

import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.type.PageSelectedType;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.export.context.ExportContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 导出上下文适配器
 * 使用适配器模式将ExportContext适配到现有的导出流程
 * 
 * 应用设计模式：
 * 1. Adapter Pattern - 适配ExportContext到现有接口
 * 2. Facade Pattern - 简化复杂的转换逻辑
 */
@Slf4j
@Component
public class ExportContextAdapter {

    /**
     * 将ExportContext适配为SqlExportMessage
     * 注意：这个方法仅用于兼容现有代码，新代码应该直接使用ExportContext
     * 
     * @param context 导出上下文
     * @return SQL导出消息
     */
    public SqlExportMessage adaptToSqlExportMessage(ExportContext context) {
        log.debug("将ExportContext适配为SqlExportMessage");
        
        SqlExportMessage message = new SqlExportMessage();
        
        // 基础信息
        message.setToken(context.getToken());
        message.setUserId(context.getUserId());
        message.setTokenConfig(context.getTokenConfig());
        
        // SQL模型
        message.setSqlExportModels(context.getSqlExportModels());
        message.setBatchExecuteModels(context.getBatchExecuteModels());
        
        // 格式配置
        adaptFormatConfig(context, message);
        
        // Excel配置
        adaptExcelConfig(context, message);
        
        // 安全配置
        adaptSecurityConfig(context, message);
        
        // 其他配置
        message.setResultFormat(context.getResultFormat());
        message.setStage(context.getStage());
        message.setExecuteEvent(context.getExecuteEvent());
        
        log.debug("ExportContext适配完成");
        return message;
    }

    /**
     * 适配格式配置
     */
    private void adaptFormatConfig(ExportContext context, SqlExportMessage message) {
        var formatConfig = context.getFormatConfig();
        if (formatConfig == null) {
            return;
        }
        
        message.setExportType(formatConfig.getExportType());
        message.setFileCharset(formatConfig.getFileCharset());
        message.setLineDelimiter(formatConfig.getLineDelimiter());
        message.setTextIdentifier(formatConfig.getTextIdentifier());
        message.setColumnDelimiter(formatConfig.getColumnDelimiter());
        message.setOtherDelimiter(formatConfig.getOtherDelimiter());
        message.setPageSelected(formatConfig.getPageSelected());
        message.setSplitFileSize(formatConfig.getSplitFileSize());
    }

    /**
     * 适配Excel配置
     */
    private void adaptExcelConfig(ExportContext context, SqlExportMessage message) {
        var excelConfig = context.getExcelConfig();
        if (excelConfig == null) {
            return;
        }
        
        message.setExcelDatetimeFormat(excelConfig.getDatetimeFormat());
        message.setExcelUseOriginalFormat(excelConfig.isUseOriginalFormat() ? 1 : 0);
        message.setExcelUseNumberFormat(excelConfig.isUseNumberFormat());
    }

    /**
     * 适配安全配置
     */
    private void adaptSecurityConfig(ExportContext context, SqlExportMessage message) {
        var securityConfig = context.getSecurityConfig();
        if (securityConfig == null) {
            return;
        }
        
        message.setExportDesensitize(securityConfig.isExportDesensitize());
        message.setEncryptPassword(securityConfig.getEncryptPassword());
        message.setWatermarkContent(securityConfig.getWatermarkContent());
        message.setWatermarkAngle(securityConfig.getWatermarkAngle());
    }

    /**
     * 从ExportContext提取基础参数
     */
    public ExportParameters extractParameters(ExportContext context) {
        log.debug("从ExportContext提取参数");
        
        return ExportParameters.builder()
                .token(context.getToken())
                .userId(context.getUserId())
                .tokenConfig(context.getTokenConfig())
                .exportType(context.getFormatConfig().getExportType())
                .fileCharset(context.getFormatConfig().getFileCharset())
                .pageSelected(context.getFormatConfig().getPageSelected())
                .sqlExportModels(context.getSqlExportModels())
                .batchExecuteModels(context.getBatchExecuteModels())
                .resultFormat(context.getResultFormat())
                .stage(context.getStage())
                .executeEvent(context.getExecuteEvent())
                .build();
    }

    /**
     * 导出参数数据类
     */
    public static class ExportParameters {
        private String token;
        private String userId;
        private TokenConfig tokenConfig;
        private ExportType exportType;
        private String fileCharset;
        private PageSelectedType pageSelected;
        private List<SqlExportModel> sqlExportModels;
        private List<ValidExecuteModel> batchExecuteModels;
        private ResultFormat resultFormat;
        private int stage;
        private ExecuteEvent executeEvent;

        // Builder pattern implementation
        public static ExportParametersBuilder builder() {
            return new ExportParametersBuilder();
        }

        public static class ExportParametersBuilder {
            private ExportParameters parameters = new ExportParameters();

            public ExportParametersBuilder token(String token) {
                parameters.token = token;
                return this;
            }

            public ExportParametersBuilder userId(String userId) {
                parameters.userId = userId;
                return this;
            }

            public ExportParametersBuilder tokenConfig(TokenConfig tokenConfig) {
                parameters.tokenConfig = tokenConfig;
                return this;
            }

            public ExportParametersBuilder exportType(ExportType exportType) {
                parameters.exportType = exportType;
                return this;
            }

            public ExportParametersBuilder fileCharset(String fileCharset) {
                parameters.fileCharset = fileCharset;
                return this;
            }

            public ExportParametersBuilder pageSelected(PageSelectedType pageSelected) {
                parameters.pageSelected = pageSelected;
                return this;
            }

            public ExportParametersBuilder sqlExportModels(List<SqlExportModel> sqlExportModels) {
                parameters.sqlExportModels = sqlExportModels;
                return this;
            }

            public ExportParametersBuilder batchExecuteModels(List<ValidExecuteModel> batchExecuteModels) {
                parameters.batchExecuteModels = batchExecuteModels;
                return this;
            }

            public ExportParametersBuilder resultFormat(ResultFormat resultFormat) {
                parameters.resultFormat = resultFormat;
                return this;
            }

            public ExportParametersBuilder stage(int stage) {
                parameters.stage = stage;
                return this;
            }

            public ExportParametersBuilder executeEvent(ExecuteEvent executeEvent) {
                parameters.executeEvent = executeEvent;
                return this;
            }

            public ExportParameters build() {
                return parameters;
            }
        }

        // Getters
        public String getToken() { return token; }
        public String getUserId() { return userId; }
        public TokenConfig getTokenConfig() { return tokenConfig; }
        public ExportType getExportType() { return exportType; }
        public String getFileCharset() { return fileCharset; }
        public PageSelectedType getPageSelected() { return pageSelected; }
        public List<SqlExportModel> getSqlExportModels() { return sqlExportModels; }
        public List<ValidExecuteModel> getBatchExecuteModels() { return batchExecuteModels; }
        public ResultFormat getResultFormat() { return resultFormat; }
        public int getStage() { return stage; }
        public ExecuteEvent getExecuteEvent() { return executeEvent; }
    }
}
