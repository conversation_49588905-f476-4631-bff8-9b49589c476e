package com.dc.summer.model.export.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

/**
 * 安全配置类
 * 负责导出安全相关的配置参数
 */
@Data
@Builder
@ApiModel("安全配置")
public class SecurityConfig {

    @ApiModelProperty(value = "水印内容", example = "SUMMER")
    private String watermarkContent;

    @ApiModelProperty(value = "水印角度", example = "45")
    @Builder.Default
    private Integer watermarkAngle = 45;

    @ApiModelProperty(value = "加密密码", example = "xxx")
    private String encryptPassword;

    @ApiModelProperty(value = "是否脱敏导出", example = "false")
    @Builder.Default
    private boolean exportDesensitize = false;

    /**
     * 创建默认的安全配置
     */
    public static SecurityConfig createDefault() {
        return SecurityConfig.builder()
                .watermarkAngle(45)
                .exportDesensitize(false)
                .build();
    }

    /**
     * 是否启用了水印
     */
    public boolean isWatermarkEnabled() {
        return StringUtils.isNotBlank(watermarkContent);
    }

    /**
     * 是否启用了加密
     */
    public boolean isEncryptionEnabled() {
        return StringUtils.isNotBlank(encryptPassword);
    }

    /**
     * 验证安全配置的有效性
     */
    public boolean isValid() {
        // 验证水印角度范围
        if (watermarkAngle != null && (watermarkAngle < 0 || watermarkAngle > 360)) {
            return false;
        }
        
        // 验证加密密码强度（如果启用了加密）
//        if (isEncryptionEnabled() && !isValidPassword(encryptPassword)) {
//            return false;
//        }
        
        return true;
    }

    /**
     * 验证密码强度
     */
    private boolean isValidPassword(String password) {
        if (StringUtils.isBlank(password)) {
            return false;
        }
        
        // 密码长度至少6位
        if (password.length() < 6) {
            return false;
        }
        
        // 这里可以添加更复杂的密码强度验证逻辑
        return true;
    }

    /**
     * 获取安全级别描述
     */
    public String getSecurityLevel() {
        int level = 0;
        if (isWatermarkEnabled()) level++;
        if (isEncryptionEnabled()) level++;
        if (exportDesensitize) level++;
        
        switch (level) {
            case 0: return "无安全措施";
            case 1: return "低安全级别";
            case 2: return "中安全级别";
            case 3: return "高安全级别";
            default: return "未知安全级别";
        }
    }
}
