package com.dc.summer.service.impl;

import com.dc.repository.redis.client.DataSourceClient;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.database.DataSourceMessage;
import com.dc.springboot.core.model.log.SensitiveAuthDetail;
import com.dc.springboot.core.model.result.*;
import com.dc.springboot.core.model.sensitive.MaskRule;
import com.dc.summer.ModelPreferences;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCTransactionManager;
import com.dc.summer.model.log.LifeCycleMessage;
import com.dc.summer.model.log.PrivilegeMessage;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.handler.SqlLogHandler;
import com.dc.summer.model.data.AlertEventModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.log.SqlRecord;
import com.dc.summer.model.data.message.BatchAsyncExecuteMessage;
import com.dc.springboot.core.model.execution.SingleAsyncExecuteMessage;
import com.dc.springboot.core.model.execution.JobExportMessage;
import com.dc.summer.model.data.result.DataResultVisitor;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.exec.model.type.OperationType;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.SensitiveService;
import com.dc.summer.service.runnable.AlertRunnable;
import com.dc.summer.service.runnable.EmailRunnable;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    @Resource
    protected JSON json;

    @Resource
    private SummerConfig summerConfig;

    @Resource
    private SummerMapper summerMapper;

    @Resource
    private BackendClient backendClient;

    @Resource
    private DataSourceClient dataSourceClient;

    @SuppressWarnings("all")
    @Autowired(required = false)
    private SensitiveService sensitiveService;

    @Resource
    private SummerThreadScheduler scheduler;

    @Override
    public void sendAlertMessage(SingleAsyncExecuteMessage message, WebSQLExecuteInfo executeInfo, int securityRuleSetId) {
        try {
            ExecuteEvent executeEvent = message.getExecuteEvent();
            WebSQLQueryResult queryResult = executeInfo.getQueryResults().get(0);
            AlertEventModel alertEventModel = new AlertEventModel();
            alertEventModel.setMessage(queryResult.getMessage());
            alertEventModel.setAllOperations(message.getAllOperations());
            alertEventModel.setExecuted(queryResult.isExecuted());
            alertEventModel.setStatus(queryResult.getStatus());
            alertEventModel.setWhereClause(message.getWhereClause());
            alertEventModel.setOperation(message.getOperation());
            AlertRunnable alertRunnable = new AlertRunnable(List.of(alertEventModel), executeEvent, summerConfig.getPath().getDcBackend(), json, securityRuleSetId);
            scheduler.exec(ExecuteType.ALERT, alertRunnable);
        } catch (Exception e) {
            log.error("告警发送失败！", e);
        }
    }

    @Override
    public void sendAlertMessage(SqlExportMessage message, WebSQLExecuteInfo executeInfo, int securityRuleSetId) {
        try {
            ExecuteEvent executeEvent = message.getExecuteEvent();
            WebSQLTransferResult transferResult = executeInfo.getTransferResult();
            AlertEventModel alertEventModel = new AlertEventModel();
            alertEventModel.setMessage(transferResult.getMessage());
            alertEventModel.setAllOperations(List.of(OperationAuthConstant.export));
            alertEventModel.setExecuted(transferResult.isSuccess());
            alertEventModel.setStatus(transferResult.isSuccess() ? 1 : 0);
            alertEventModel.setOperation(OperationAuthConstant.export);
            AlertRunnable alertRunnable = new AlertRunnable(List.of(alertEventModel), executeEvent, summerConfig.getPath().getDcBackend(), json, securityRuleSetId);
            scheduler.exec(ExecuteType.ALERT, alertRunnable);
        } catch (Exception e) {
            log.error("告警发送失败！", e);
        }
    }

    @Override
    public void sendAlertMessageList(BatchAsyncExecuteMessage message, WebSQLExecuteInfo executeInfo, int securityRuleSetId) {
        try {
            for (ExecuteEvent executeEvent : message.getExecuteEvents()) {
                List<AlertEventModel> alertEventModelList = new ArrayList<>();
                for (int i = 0; i < executeInfo.getQueryResults().size(); i++) {
                    WebSQLQueryResult queryResult = executeInfo.getQueryResults().get(i);
                    AlertEventModel alertEventModel = new AlertEventModel();
                    alertEventModel.setMessage(queryResult.getMessage());
                    alertEventModel.setAllOperations(message.getBatchExecuteModels().get(i).getAllOperations());
                    alertEventModel.setExecuted(queryResult.isExecuted());
                    alertEventModel.setStatus(queryResult.getStatus());
                    alertEventModel.setWhereClause(message.getBatchExecuteModels().get(i).getWhereClause());
                    alertEventModel.setOperation(message.getBatchExecuteModels().get(i).getOperation());
                    alertEventModel.setAlert(message.getBatchExecuteModels().get(i).getSqlRecord().isAlert());
                    alertEventModelList.add(alertEventModel);
                }
                AlertRunnable alertRunnable = new AlertRunnable(alertEventModelList, executeEvent, summerConfig.getPath().getDcBackend(), json, securityRuleSetId);
                scheduler.exec(ExecuteType.ALERT, alertRunnable);
            }
        } catch (Exception e) {
            log.error("告警发送失败！", e);
        }
    }

    @Override
    public void printLogMessage(WebSQLContextInfo contextInfo, List<WebSQLQueryResult> queryResults, String windowId, String taskId) {
        for (WebSQLQueryResult queryResult : queryResults) {
            List<SensitiveAuthDetail> authDetailList = new ArrayList<>();
            SqlHistory sqlHistory = queryResult.getSqlHistory();
            SqlRecord sqlRecord = queryResult.getSqlRecord();
            // 访问者，走外部脱敏机制，需要重新给定脱敏标识
            if (com.dc.springboot.core.component.Resource.containsBean(DataResultVisitor.class)) {
                HashMap<String, SensitiveAuthDetail> tempMap = new HashMap<String, SensitiveAuthDetail>();
                if (queryResult.getResultSet() != null) {
                    sqlRecord.setSensitiveSelect(
                            queryResult.getResultSet().stream()
                                    .filter(Objects::nonNull)
                                    .filter(webSQLQueryResultSet -> webSQLQueryResultSet.getColumns() != null).map(WebSQLQueryResultSet::getColumns)
                                    .peek(webSQLQueryResultColumns -> Arrays.stream(webSQLQueryResultColumns).forEach(webSQLQueryResultColumn -> {
                                        SensitiveAuthDetail paSensiModel = webSQLQueryResultColumn.getPaSensiModel();
                                        if (paSensiModel == null) {
                                            return;
                                        }
                                        String s = paSensiModel.getIdentity();
                                        tempMap.putIfAbsent(s, paSensiModel);
                                    }))
                                    .anyMatch(columns ->
                                            Arrays.stream(columns)
                                                    .anyMatch(WebSQLQueryResultColumn::isSensitive))
                    );
                }
                if (tempMap.size() > 0) {
                    authDetailList = new ArrayList<>(tempMap.values());
                    for (SensitiveAuthDetail sensiModel : authDetailList) {
                        setSensInfo(sqlHistory, sensiModel);
                    }
                }
            }
            sqlHistory.setRecords(sqlRecord);
            if (queryResult.getSensitiveAuthDetailList() != null && queryResult.getSensitiveAuthDetailList().size() > 0) {
                authDetailList = queryResult.getSensitiveAuthDetailList();
            }
            this.printLogMessage(contextInfo, sqlHistory, authDetailList, windowId, taskId);
        }
    }


    @Override
    public void printLogMessage(WebSQLContextInfo contextInfo, SqlHistory sqlHistory, List<? extends SensitiveAuthDetail> sensitiveAuthDetailList, String windowId, String taskId) {
        DatabaseType databaseType = contextInfo.getDataSourceContainer().getDatabaseType();
        try {
            sqlHistory.setAutoCommit(contextInfo.getAutoCommit());
            sqlHistory.setSmartCommit(contextInfo.getSmartCommit());
            if (sqlHistory.getSessionId() == null) {
                sqlHistory.setSessionId(contextInfo.getExecutionContext().getProcessId());
            }
            sqlHistory.setWindowId(windowId);
            sqlHistory.setTaskId(taskId);

            if (databaseType != null) {
                sqlHistory.setDbType(databaseType.getValue());
                sqlHistory.setDbTypeZh(databaseType.getName());
                if (DatabaseType.ELASTIC_SEARCH.getValue().equals(databaseType.getValue())) {
                    sqlHistory.setSchemaName(null);
                }
            }
            sqlHistory.setOriginZhWithPattern(sqlHistory.getOrigin(), sqlHistory.getConnectionPatternZh());

            SqlLogHandler handler = SqlLogHandler.handler("sql");
            handler.appendRow(serializeSqlLogData(sqlHistory));
        } catch (Exception e) {
            log.error("打印 SqlHistory 失败！", e);
        }

        if (databaseType == DatabaseType.CALCITE) {
            return;
        }
        try {
            if (CollectionUtils.isNotEmpty(sensitiveAuthDetailList)) {
                for (SensitiveAuthDetail sensitiveAuthDetail : sensitiveAuthDetailList) {
                    String authTraceData = sensitiveAuthDetail.getAuthTraceData();
                    summerMapper.updateSensitiveAuthDetail(sensitiveAuthDetail, sqlHistory);
                    if (StringUtils.isBlank(sensitiveAuthDetail.getAuthTraceData()) && StringUtils.isNotBlank(authTraceData)) {
                        sensitiveAuthDetail.setAuthTraceData(authTraceData);
                    }
                    if (DatabaseType.ELASTIC_SEARCH.getValue().equals(databaseType.getValue())) {
                        sensitiveAuthDetail.setSchemaName(null);
                        if (sensitiveAuthDetail.getColumnName().contains("_source.")) {
                            String columnName = sensitiveAuthDetail.getColumnName().substring(sensitiveAuthDetail.getColumnName().indexOf(".") + 1);
                            sensitiveAuthDetail.setColumnName(columnName);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(sensitiveAuthDetailList)) {
                SqlLogHandler handler = SqlLogHandler.handler("sensitive");
                sensitiveAuthDetailList.stream()
                        .distinct()
                        .forEach(sensitive -> handler.appendRow(serializeSqlLogData(sensitive)));
            }
        } catch (Exception e) {
            log.error("打印 List<SensitiveAuthDetail> 失败！", e);
        }
    }

    private String serializeSqlLogData(Object object) {
        String data = null;
        if (object != null) {
            try {
                data = this.json.getObjectMapper().writeValueAsString(object);
            } catch (JsonProcessingException e) {
                log.error("序列化 SQL 日志数据失败。", e);
            }
        }
        if (sensitiveService != null) {
            data = sensitiveService.directDesensitization(data);
        }
        return data;
    }

    @Override
    public void sendSyncSchemaMessage(String connectionId) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("origin", "summer");
            map.put("connect_id", connectionId);
            backendClient.syncSchemas(Client.getClient(summerConfig.getPath().getDcBackend()), map);
        } catch (Exception e) {
            log.error("调用 PHP 同步 Schema 失败！", e);
        }
        try {
            DataSourceMessage message = new DataSourceMessage();
            message.setConnectionId(connectionId);
            dataSourceClient.refresh(message);
        } catch (Exception e) {
            log.error("调用 Redis 刷新数据源失败！", e);
        }
    }

    @Override
    public void sendMailMessage(JobExportMessage message, String filePath, DatabaseType databaseType) {
        try {
            EmailRunnable emailRunnable = new EmailRunnable(message, filePath, backendClient, summerConfig, databaseType);
            scheduler.exec(ExecuteType.SEND_EMAIL, emailRunnable);
        } catch (Exception e) {
            log.error("调用发送邮件接口失败！", e);
        }
    }

    @Override
    public void printLogLifeCycle(DBCExecutionContext context, LifeCycleMessage message) {
        if (OperationType.OPEN_CONNECTION.getValue() == message.getOperationType() && message.getPreferences() == null) {
            return;
        }
        if (message.getAutoCommit() == null && context instanceof DBCTransactionManager) {
            DBCTransactionManager transactionManager = (DBCTransactionManager) context;
            try {
                message.setAutoCommit(transactionManager.isAutoCommit());
            } catch (DBCException ignored) {
            }
        }
        if (context != null) {
            message.setSessionId(context.getProcessId());
            if (message.getAutoConnect() == null) {
                boolean autoConnect = context.getPreferenceStore().getBoolean(ModelPreferences.EXECUTE_RECOVER_CONNECTION_LOST_ENABLED);
                message.setAutoConnect(autoConnect);
            }
        }

        if (message.getMessage() == null) {
            message.setMessage("成功");
        }
        SqlLogHandler handler = SqlLogHandler.handler("life-cycle");
        handler.appendRow(serializeSqlLogData(message));
    }

    @Override
    public void printLogPrivilege(DBCExecutionContext context, PrivilegeMessage message) {
        SqlLogHandler handler = SqlLogHandler.handler("privilege");
        handler.appendRow(serializeSqlLogData(message));
    }

    private void setSensInfo(SqlHistory sqlHistory, SensitiveAuthDetail sensiModel) {
        sensiModel.setConnectId(sqlHistory.getConnectId());
        sensiModel.setSchemaId(sqlHistory.getSchemaId());
        sensiModel.setConnectionDesc(sqlHistory.getConnectDesc());
        sensiModel.setSqlId(sqlHistory.getSqlId());
        sensiModel.setDbType(Long.valueOf(sqlHistory.getDbType()));
        sensiModel.setEnvironment(sqlHistory.getEnvironment());
        sensiModel.setGmtCreate(sqlHistory.getGmtCreate());
        sensiModel.setGmtModified(sqlHistory.getGmtModified());
        sensiModel.setOrigin(sqlHistory.getOrigin());
        sensiModel.setOriginZh(sqlHistory.getOriginZh());
        List<String> organizationId = sqlHistory.getOrganizationId();
        if (organizationId != null) {
            sensiModel.setOrganizationId(new ArrayList<>(organizationId));
        }
        sensiModel.setOrganizationName(sqlHistory.getOrganizationName());
        List<Integer> userCatalogIds = sqlHistory.getUserCatalogIds();
        if (userCatalogIds != null) {
            sensiModel.setUserCatalogIds(new ArrayList<>(userCatalogIds));
        }

        if (sensiModel.getMaskTemp() != null) {
            ArrayList<MaskRule> rule = (ArrayList<MaskRule>) sensiModel.getMaskTemp();
            for (MaskRule maskRule : rule) {
                if (StringUtils.isBlank(maskRule.getSchemaId())) {
                    maskRule.setSchemaId(sqlHistory.getSchemaId());
                    maskRule.setConnectId(sqlHistory.getConnectId());
                }
                if (sqlHistory.getOrigin() != null) {
                    maskRule.setOrigin(sqlHistory.getOrigin());
                }
            }

            sensiModel.setAuthTraceData(JSON.toJSONString(rule));
            sensiModel.setMaskTemp(null);
        }
        if (StringUtils.isBlank(sensiModel.getSchemaId())) {
            sensiModel.setSchemaId(sqlHistory.getSchemaId());
        }
        if (StringUtils.isBlank(sensiModel.getConnectId())) {
            sensiModel.setConnectId(sqlHistory.getConnectId());
        }

        sensiModel.setInstanceCatalog(sqlHistory.getInstanceCatalog());
        sensiModel.setOrderRelevance(sqlHistory.getOrderRelevance());
    }
}
