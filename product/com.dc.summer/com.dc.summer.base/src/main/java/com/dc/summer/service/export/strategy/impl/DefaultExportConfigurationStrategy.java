package com.dc.summer.service.export.strategy.impl;

import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.export.strategy.ExportConfigurationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 默认导出配置策略实现
 * 处理通用的导出配置转换
 * 
 * 应用设计模式：
 * 1. Strategy Pattern - 实现配置处理策略
 * 2. Template Method Pattern - 提供默认的配置处理模板
 */
@Slf4j
@Component
public class DefaultExportConfigurationStrategy implements ExportConfigurationStrategy {

    @Override
    public Map<String, Object> processConfiguration(ExportContext context) {
        log.debug("处理默认导出配置");
        
        Map<String, Object> properties = new HashMap<>();
        
        // 处理基础格式配置
        processBasicFormatConfig(context, properties);
        
        // 处理安全配置
        processSecurityConfig(context, properties);
        
        // 处理Excel配置
        processExcelConfig(context, properties);
        
        // 处理阶段信息
        properties.put(DTConstants.RESULT_STAGE, context.getStage());
        
        log.debug("配置处理完成，属性数量: {}", properties.size());
        return properties;
    }

    /**
     * 处理基础格式配置
     */
    private void processBasicFormatConfig(ExportContext context, Map<String, Object> properties) {
        var formatConfig = context.getFormatConfig();
        
        // 行分隔符
        if (formatConfig.getLineDelimiter() != null) {
            properties.put(DTConstants.LINE_DELIMITER, formatConfig.getLineDelimiter().getValue());
        }
        
        // 文本标识符
        if (formatConfig.getTextIdentifier() != null) {
            properties.put(DTConstants.TEXT_IDENTIFIER, formatConfig.getTextIdentifier().getValue());
        }
        
        // 列分隔符
        if (formatConfig.getColumnDelimiter() != null) {
            if (formatConfig.getColumnDelimiter() == ColumnDelimiterType.OTHER) {
                properties.put(DTConstants.COLUMN_DELIMITER, formatConfig.getOtherDelimiter());
            } else {
                properties.put(DTConstants.COLUMN_DELIMITER, formatConfig.getColumnDelimiter().getValue());
            }
        }
    }

    /**
     * 处理安全配置
     */
    private void processSecurityConfig(ExportContext context, Map<String, Object> properties) {
        var securityConfig = context.getSecurityConfig();
        if (securityConfig == null) {
            return;
        }
        
        // 水印配置
        if (securityConfig.getWatermarkContent() != null) {
            properties.put(DTConstants.PROP_WATERMARK_CONTENT, securityConfig.getWatermarkContent());
        }
        
        if (securityConfig.getWatermarkAngle() != null) {
            properties.put(DTConstants.PROP_WATERMARK_ANGLE, securityConfig.getWatermarkAngle());
        }
    }

    /**
     * 处理Excel配置
     */
    private void processExcelConfig(ExportContext context, Map<String, Object> properties) {
        var excelConfig = context.getExcelConfig();
        if (excelConfig == null) {
            return;
        }
        
        // Excel日期时间格式
        if (excelConfig.getDatetimeFormat() != null) {
            properties.put("dateFormat", excelConfig.getDatetimeFormat());
        }
        
        // 其他Excel特定配置可以在这里添加
    }

    @Override
    public boolean supports(ExportContext context) {
        // 默认策略支持所有类型的导出上下文
        return true;
    }

    @Override
    public String getStrategyName() {
        return "默认导出配置策略";
    }

    @Override
    public boolean validateConfiguration(ExportContext context) {
        if (!ExportConfigurationStrategy.super.validateConfiguration(context)) {
            return false;
        }
        
        // 验证格式配置
        if (context.getFormatConfig() == null) {
            log.error("导出格式配置不能为空");
            return false;
        }
        
        if (context.getFormatConfig().getExportType() == null) {
            log.error("导出类型不能为空");
            return false;
        }
        
        return true;
    }
}
