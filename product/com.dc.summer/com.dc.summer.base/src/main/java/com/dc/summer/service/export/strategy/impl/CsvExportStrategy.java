package com.dc.summer.service.export.strategy.impl;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.export.strategy.AbstractExportStrategy;
import com.dc.summer.service.sql.WebSQLTransferResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * CSV导出策略实现
 * 处理CSV格式的导出
 */
@Slf4j
@Component
public class CsvExportStrategy extends AbstractExportStrategy {

    private static final List<ExportType> SUPPORTED_TYPES = Arrays.asList(
            ExportType.CSV
    );

    @Override
    protected WebSQLTransferResult doExport(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.info("执行CSV导出策略");
        
        // 记录导出信息
        logExportInfo(context, resultSets);
        
        // 应用CSV特定的配置
        applyCsvConfiguration(context);
        
        // 执行导出
        return executeTransfer(context, resultSets);
    }

    @Override
    public boolean supports(ExportType exportType) {
        return SUPPORTED_TYPES.contains(exportType);
    }

    @Override
    public String getStrategyName() {
        return "CSV导出策略";
    }

    @Override
    public List<ExportType> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public boolean validateParameters(ExportContext context) {
        if (!super.validateParameters(context)) {
            return false;
        }
        
        // CSV特定的参数验证
        if (context.getFormatConfig().getColumnDelimiter() == null && 
            context.getFormatConfig().getOtherDelimiter() == null) {
            log.error("CSV导出必须指定列分隔符");
            return false;
        }
        
        return true;
    }

    @Override
    public List<WebSQLQueryResultSet> preprocessData(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.debug("CSV导出数据预处理");
        
        // CSV特定的数据预处理
        processTextData(context, resultSets);
        
        return resultSets;
    }

    @Override
    public WebSQLTransferResult postprocessResult(ExportContext context, WebSQLTransferResult result) {
        log.debug("CSV导出结果后处理");
        
        // CSV特定的后处理
        if (context.needsSecurityProcessing() && context.getSecurityConfig().isExportDesensitize()) {
            // CSV格式主要支持脱敏处理
            applyDesensitization(context, result);
        }
        
        return result;
    }

    /**
     * 应用CSV配置
     */
    private void applyCsvConfiguration(ExportContext context) {
        log.info("应用CSV配置 - 列分隔符: {}, 文本识别符: {}, 行分隔符: {}, 编码: {}", 
                context.getFormatConfig().getColumnDelimiter(),
                context.getFormatConfig().getTextIdentifier(),
                context.getFormatConfig().getLineDelimiter(),
                context.getFormatConfig().getFileCharset());
    }

    /**
     * 处理文本数据
     */
    private void processTextData(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.debug("处理CSV文本数据格式");
        
        // 处理特殊字符转义
        // 处理文本识别符
        // 处理换行符等
        
        // 这里可以添加具体的文本处理逻辑
    }

    /**
     * 应用脱敏处理
     */
    private void applyDesensitization(ExportContext context, WebSQLTransferResult result) {
        log.info("应用CSV数据脱敏");
        
        // 添加脱敏处理逻辑
        // CSV格式的脱敏主要是对敏感字段进行掩码处理
    }
}
