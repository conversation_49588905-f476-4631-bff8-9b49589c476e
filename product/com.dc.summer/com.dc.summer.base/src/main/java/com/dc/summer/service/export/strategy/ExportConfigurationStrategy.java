package com.dc.summer.service.export.strategy;

import com.dc.summer.model.export.context.ExportContext;

import java.util.Map;

/**
 * 导出配置策略接口
 * 使用策略模式处理不同类型的导出配置
 * 
 * 应用设计模式：
 * 1. Strategy Pattern - 处理不同的配置策略
 * 2. Template Method Pattern - 定义配置处理的模板
 */
public interface ExportConfigurationStrategy {

    /**
     * 处理导出配置
     * 
     * @param context 导出上下文
     * @return 配置属性映射
     */
    Map<String, Object> processConfiguration(ExportContext context);

    /**
     * 判断是否支持指定的导出上下文
     * 
     * @param context 导出上下文
     * @return 是否支持
     */
    boolean supports(ExportContext context);

    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 验证配置参数
     * 
     * @param context 导出上下文
     * @return 验证结果
     */
    default boolean validateConfiguration(ExportContext context) {
        return context != null && context.isValid();
    }
}
