package com.dc.summer.service.export;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.type.PageSelectedType;
import com.dc.summer.model.export.config.ExportFormatConfig;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.export.builder.ExportParameterBuilder;
import com.dc.summer.service.export.strategy.impl.DefaultExportConfigurationStrategy;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.ArrayList;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExportContext集成测试
 * 验证新的导出流程是否正常工作
 */
public class ExportContextIntegrationTest {

    private ExportContext testContext;

    @BeforeEach
    void setUp() {
        // 构建测试用的ExportContext
        ExportFormatConfig formatConfig = ExportFormatConfig.builder()
                .exportType(ExportType.XLSX)
                .fileCharset("UTF-8")
                .pageSelected(PageSelectedType.CURRENT_PAGE)
                .exportFileName("test_export.xlsx")
                .build();

        testContext = ExportContext.builder()
                .token("test_token")
                .userId("test_user")
                .formatConfig(formatConfig)
                .sqlExportModels(new ArrayList<>())
                .build();
    }

    @Test
    void testExportParameterBuilder() {
        // 测试ExportParameterBuilder
        ExportParameterBuilder builder = new ExportParameterBuilder(testContext);
        
        // 验证参数构建
        assertTrue(builder.validateParameters(), "参数验证应该通过");
        assertEquals("test_user", builder.buildUserId(), "用户ID应该匹配");
        assertEquals("UTF-8", builder.buildFileCharset(), "文件编码应该匹配");
        assertTrue(builder.buildIsSingle(), "应该是单个文件");
        
        // 记录构建信息（不抛出异常即为成功）
        assertDoesNotThrow(() -> builder.logBuildInfo(), "记录构建信息不应该抛出异常");
    }

    @Test
    void testDefaultExportConfigurationStrategy() {
        // 测试DefaultExportConfigurationStrategy
        DefaultExportConfigurationStrategy strategy = new DefaultExportConfigurationStrategy();
        
        // 验证策略支持
        assertTrue(strategy.supports(testContext), "默认策略应该支持所有上下文");
        assertTrue(strategy.validateConfiguration(testContext), "配置验证应该通过");
        
        // 验证配置处理
        Map<String, Object> properties = strategy.processConfiguration(testContext);
        assertNotNull(properties, "配置属性不应该为空");
        assertEquals("默认导出配置策略", strategy.getStrategyName(), "策略名称应该匹配");
    }

    @Test
    void testExportContextValidation() {
        // 测试ExportContext验证
        assertTrue(testContext.isValid(), "测试上下文应该是有效的");
        assertNotNull(testContext.getTaskDescription(), "任务描述不应该为空");
        
        // 测试Excel处理需求
        assertTrue(testContext.needsExcelProcessing(), "应该需要Excel处理");
        
        // 测试安全处理需求（当前为false，因为没有设置安全配置）
        assertFalse(testContext.needsSecurityProcessing(), "不应该需要安全处理");
    }

    @Test
    void testExportContextBuilder() {
        // 测试ExportContext的建造者模式
        ExportContext builtContext = ExportContext.builder()
                .token("builder_test_token")
                .userId("builder_test_user")
                .formatConfig(ExportFormatConfig.builder()
                        .exportType(ExportType.CSV)
                        .fileCharset("GBK")
                        .pageSelected(PageSelectedType.ALL_PAGES)
                        .build())
                .sqlExportModels(new ArrayList<>())
                .build();

        assertNotNull(builtContext, "构建的上下文不应该为空");
        assertEquals("builder_test_token", builtContext.getToken(), "Token应该匹配");
        assertEquals("builder_test_user", builtContext.getUserId(), "用户ID应该匹配");
        assertEquals(ExportType.CSV, builtContext.getFormatConfig().getExportType(), "导出类型应该匹配");
        assertEquals("GBK", builtContext.getFormatConfig().getFileCharset(), "文件编码应该匹配");
    }

    @Test
    void testInvalidExportContext() {
        // 测试无效的ExportContext
        ExportContext invalidContext = ExportContext.builder()
                .token("")  // 空token
                .userId("")  // 空用户ID
                .formatConfig(null)  // 空格式配置
                .build();

        ExportParameterBuilder builder = new ExportParameterBuilder(invalidContext);
        assertFalse(builder.validateParameters(), "无效参数验证应该失败");
    }

    @Test
    void testExportContextWithSecurityConfig() {
        // 测试带安全配置的ExportContext
        ExportContext contextWithSecurity = testContext.toBuilder()
                .securityConfig(com.dc.summer.model.export.config.SecurityConfig.builder()
                        .watermarkEnabled(true)
                        .watermarkContent("CONFIDENTIAL")
                        .watermarkAngle(45)
                        .exportDesensitize(true)
                        .build())
                .build();

        assertTrue(contextWithSecurity.needsSecurityProcessing(), "应该需要安全处理");
        
        ExportParameterBuilder builder = new ExportParameterBuilder(contextWithSecurity);
        assertEquals("CONFIDENTIAL", builder.buildWatermarkContent(), "水印内容应该匹配");
        assertEquals(Integer.valueOf(45), builder.buildWatermarkAngle(), "水印角度应该匹配");
        assertTrue(builder.buildExportDesensitize(), "应该启用导出脱敏");
    }
}
