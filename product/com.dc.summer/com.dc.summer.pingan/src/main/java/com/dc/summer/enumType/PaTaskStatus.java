package com.dc.summer.enumType;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PaTaskStatus {

    SUBMIT("1", "审批中"),
    DONE("2", "完成"),
    ABANDON("3", "作废"),
    BACK("4", "第三方签报退回"),

    ;
    private final String value;
    private final String name;

    public static PaTaskStatus get(String value) {
        for (PaTaskStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return DONE;
    }


}
