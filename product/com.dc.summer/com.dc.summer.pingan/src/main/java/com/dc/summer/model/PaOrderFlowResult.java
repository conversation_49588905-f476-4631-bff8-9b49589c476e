package com.dc.summer.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class) /*平安回调对方是否驼峰参数*/
public class PaOrderFlowResult implements Serializable {

    /*序列id*/
    private String id;

    /*父任务流转id*/
    private String preFlowId;

    /*审批人所属部门编码*/
    private String flowOwnerDept;

    /*步骤类型 -1申请人|0顺序|1并行|2协同|3传阅*/
    private Integer stepType;

    /*处理意见， 默认如需html标签 咨询EOA*/
    private String handlePropose;

    /*处理状态 1转发|2征求|3返回修改|4普通同意|5不同意|6不同意且继续
    |7同意归档|8不同意继续归档|9作废|10归档|11修改人重新提交
    |12高层领导替换|13激活|14综合内勤|15退回操作其他没有处理人*/
    private String handleStatus;

    /*授权描述*/
    private String flowDelegateDesc;

    /*是否授权 0未授权(本人处理)|1授权 (他人处理)*/
    private String flowDelegate;

    /*处理步骤序号*/
    private Integer flowId;

    /*当前处理步骤审批状态 0未处理|1已处理|2授权|3征求意见|4其他人已处理*/
    private String flowState;

    /*当前审批步骤审批开始日期*/
    private Date flowEndDate;

    /*当前审批步骤审批结束日期*/
    private Long flowDoneDate;

    /*步骤描述 注意：此字段不是审批意见，而是handleStatus的中文说明,2.0的审批意见为handlePropose*/
    private String flowResult;

    /*步骤描述*/
    private String flowDesc;

    /*流程原始处理人*/
    private String flowRealOwner;

    /*流程原始处理人姓名*/
    private String flowRealOwnerName;

    /*流程原始处理人(不带域)*/
    private String flowRealMail;

    /*流程实际处理人*/
    private String flowOwner;

    /*流程实际处理人姓名*/
    private String flowOwnerName;

    /*流程实际处理人(不带域)*/
    private String flowOwnerMail;

    /*处理人级别 0申请人|2审批让|99管理员|100(新增审批让)|105秘书|106机要组|1110传阅人*/
    private String stageId;

    /*处理人级别描述*/
    private String stageIdDesc;

    @Deprecated
    private String enquirer; //没有用

    @Deprecated
    private String transmitter; //没有用


}
