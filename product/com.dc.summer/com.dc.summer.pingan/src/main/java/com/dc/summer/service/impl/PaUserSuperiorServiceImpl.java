package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.PaUserSuperior;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.PaUserSuperiorService;
import com.dc.utils.DateUtil;
import com.pingan.cdsf.driver.bridger.dto.HrxPageObject;
import com.pingan.cdsf.driver.bridger.dto.HrxParam;
import com.pingan.cdsf.driver.bridger.dto.PaUserSuperiorDto;
import com.pingan.cdsf.driver.bridger.service.HrxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.rmi.ServerException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Transactional
@Service
@Slf4j
public class PaUserSuperiorServiceImpl extends BaseServiceImpl<PaUserSuperiorMapper, PaUserSuperior> implements PaUserSuperiorService {

    @Resource
    public PaUserSuperiorMapper paUserSuperiorMapper;

    @Resource
    private SummerThreadScheduler scheduler;

    private static final int PAGE_SIZE = 1000;

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public SseEmitter syncUserSuperior(SseEmitter emitter) throws Exception {
        try {
            PaUserSuperior one = paUserSuperiorMapper.selectOne(new QueryWrapper<PaUserSuperior>().last("limit 1"));
            if (null != one) {
                throw new ServerException("pa_user_superior 不为空,全量同步终止.");
            }

            emitter.send("get hrx token.");
            HrxService hrxService = com.dc.springboot.core.component.Resource.getBean(HrxService.class);
            hrxService.getAccessToken();
            //异步处理，数据量大
            scheduler.exec(ExecuteType.SYNC_PA_TASK, () -> {

                try {

                    //先查第一页第1条数据得到接口返回totalCount计算页码
                    HrxParam firstPage = new HrxParam();
                    firstPage.setPage(1);
                    firstPage.setPageSize(1);
                    HrxPageObject<List<PaUserSuperiorDto>> incrementUserData = hrxService.getAllSuperiorUser(firstPage);
                    Integer totalCount = Integer.valueOf(incrementUserData.getTotalCount());
                    //计算总页数
                    Integer pageSize = totalCount % PAGE_SIZE == 0 ? (totalCount / PAGE_SIZE) : (totalCount / PAGE_SIZE + 1);

                    //有数据启动线程池查询(多查询一次无妨)
                    CountDownLatch latch = new CountDownLatch(pageSize);
                    for (int i = 1; i <= pageSize; i++) {
                        final int pageIndex = i;
                        scheduler.exec(ExecuteType.SYNC_PA_MASSIVE_DATA, () -> {
                            try {
                                //开始真正分页查询获取数据
                                HrxParam hrxParam = new HrxParam();
                                hrxParam.setPage(pageIndex);
                                hrxParam.setPageSize(PAGE_SIZE);
                                HrxPageObject<List<PaUserSuperiorDto>> currentPageData = hrxService.getAllSuperiorUser(hrxParam);
                                List<PaUserSuperiorDto> paUserSuperiorRecordList = currentPageData.getRecordList();
                                log.info("sync " + pageIndex + " page userSuperior done, count:" + paUserSuperiorRecordList.size());
                                if (ObjectUtils.isEmpty(paUserSuperiorRecordList)) {
                                    log.info(" paUserSuperiorRecordList isEmpty.");
                                    return;
                                }

                                List<PaUserSuperior> saveList = new ArrayList<>();
                                Date date = new Date();
                                for (PaUserSuperiorDto userSuperior : paUserSuperiorRecordList) {
                                    //保存
                                    PaUserSuperior save = new PaUserSuperior();
                                    save.setEmpUm(userSuperior.getEmpUm());
                                    save.setEmpId(userSuperior.getEmpId());
                                    save.setChrOutDate(userSuperior.getChrOutDate());
                                    save.setReportUm(userSuperior.getReportUm());
                                    save.setReportEmpId(userSuperior.getReportEmpId());
                                    save.setGmtCreate(date);
                                    save.setGmtModified(date);
                                    save.setIsDelete(0);
                                    saveList.add(save);
                                }

                                //保存
                                if (null != saveList && saveList.size() > 0) {
                                    log.info("saveList size : " + saveList.size());
                                    //一页数据2000个分5次提交
                                    final int subSize = 400;
                                    int subCount = saveList.size();
                                    for (int p = 0; p < subCount; p += subSize) {
                                        List<PaUserSuperior> part = saveList.subList(p, p + subSize > subCount ? subCount : p + subSize);
                                        paUserSuperiorMapper.insertBatchSomeColumn(part);
                                    }
                                }
                                emitter.send("sync " + pageIndex + " page userSuperior done. count:" + paUserSuperiorRecordList.size());
                            } catch (Exception e) {
                                e.printStackTrace();
                                try {
                                    emitter.send("sync " + pageIndex + " page userSuperior failed. 【ERROR】:" + StringUtils.substring(e.getMessage(), 0, 100) + "...");
                                } catch (Exception ex) {
                                }
                            } finally {
                                latch.countDown();
                            }
                        });
                        try {
                            //避免接口繁忙
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        log.error("latch await interrupted.", e);
                        Thread.currentThread().interrupt();
                    }
                    emitter.send("sync all userSuperior done. totalCount:" + totalCount);

                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        emitter.send("sync error: " + e.getMessage());
                    } catch (Exception ex) {
                    }

                } finally {
                    //关闭日志打印
                    emitter.complete();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            emitter.completeWithError(e); // 发送错误
            emitter.complete();
        }
        return emitter;
    }

    @Override
    public SseEmitter syncUserSuperiorDaily(SseEmitter emitter, String startTime, String endTime) throws Exception {

        try {
            emitter.send("get hrx token.");
            HrxService hrxService = com.dc.springboot.core.component.Resource.getBean(HrxService.class);
            hrxService.getAccessToken();

            //默认昨天
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String startDate = LocalDateTime.of(yesterday, LocalTime.MIN).format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1));
            String endDate = LocalDateTime.of(yesterday, LocalTime.MAX).format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1));

            //指定同步时间
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                startDate = (DateUtil.ymd_hms_sdf_1.format(new Date(Long.valueOf(startTime) * 1000)));
                endDate = (DateUtil.ymd_hms_sdf_1.format(new Date(Long.valueOf(endTime) * 1000)));
            }

            //异步处理，数据量大
            String finalStartDate = startDate;
            String finalEndDate = endDate;

            scheduler.exec(ExecuteType.SYNC_PA_TASK, () -> {
                try {

                    //先查第一页第1条数据得到接口返回totalCount计算页码
                    HrxParam firstPage = new HrxParam();
                    firstPage.setPage(1);
                    firstPage.setPageSize(1);
                    firstPage.setStartDate(finalStartDate);
                    firstPage.setEndDate(finalEndDate);
                    HrxPageObject<List<PaUserSuperiorDto>> incrementUserSuperiorData = hrxService.getSuperiorIncrementUser(firstPage);
                    Integer totalCount = Integer.valueOf(incrementUserSuperiorData.getTotalCount());
                    //计算总页数
                    Integer pageSize = totalCount % PAGE_SIZE == 0 ? (totalCount / PAGE_SIZE) : (totalCount / PAGE_SIZE + 1);
                    log.info("sync [" + finalStartDate + "~" + finalEndDate + "] userSuperior start. totalCount:" + totalCount);

                    //有数据启动线程池查询(多查询一次无妨)
                    CountDownLatch latch = new CountDownLatch(pageSize);
                    for (int i = 1; i <= pageSize; i++) {
                        final int pageIndex = i;
                        scheduler.exec(ExecuteType.SYNC_PA_MASSIVE_DATA, () -> {
                            try {
                                //开始真正分页查询获取数据
                                HrxParam hrxParam = new HrxParam();
                                hrxParam.setPage(pageIndex);
                                hrxParam.setPageSize(PAGE_SIZE);
                                hrxParam.setStartDate(finalStartDate);
                                hrxParam.setEndDate(finalEndDate);
                                HrxPageObject<List<PaUserSuperiorDto>> currentPageData = hrxService.getSuperiorIncrementUser(hrxParam);
                                List<PaUserSuperiorDto> paUserSuperiorRecordList = currentPageData.getRecordList();
                                log.info("sync " + pageIndex + " page userSuperior start, count:" + paUserSuperiorRecordList.size());
                                if (ObjectUtils.isEmpty(paUserSuperiorRecordList)) {
                                    log.info(" paUserSuperiorRecordList isEmpty.");
                                    return;
                                }

                                //查询已经存在的用户
                                List<String> empUmList = paUserSuperiorRecordList.stream().map(PaUserSuperiorDto::getEmpUm).distinct().collect(Collectors.toList());
                                List<PaUserSuperior> paUserSuperiorData = paUserSuperiorMapper.selectList(new QueryWrapper<PaUserSuperior>().lambda().in(PaUserSuperior::getEmpUm, empUmList));
                                //key: empId
                                Map<String, PaUserSuperior> userSuperiorDataMap = null;
                                if (null != paUserSuperiorData && paUserSuperiorData.size() > 0) {
                                    userSuperiorDataMap = paUserSuperiorData.stream().collect(Collectors.toMap(PaUserSuperior::getEmpUm, u -> u, (k1, k2) -> k1));
                                }

                                List<PaUserSuperior> saveList = new ArrayList<>();
                                List<PaUserSuperior> updateList = new ArrayList<>();


                                Date date = new Date();
                                for (PaUserSuperiorDto userSuperior : paUserSuperiorRecordList) {

                                    //接口特性，如果有数据说明一定存在更新
                                    if (null != userSuperiorDataMap && userSuperiorDataMap.containsKey(userSuperior.getEmpUm())) {
                                        PaUserSuperior update = userSuperiorDataMap.get(userSuperior.getEmpUm());
                                        update.setReportUm(userSuperior.getReportUm());
                                        update.setReportEmpId(userSuperior.getReportEmpId());
                                        update.setChrOutDate(userSuperior.getChrOutDate());
                                        update.setGmtModified(new Date());
                                        updateList.add(update);
                                    } else {
                                        //保存
                                        PaUserSuperior save = new PaUserSuperior();
                                        save.setEmpUm(userSuperior.getEmpUm());
                                        save.setEmpId(userSuperior.getEmpId());
                                        save.setReportUm(userSuperior.getReportUm());
                                        save.setChrOutDate(userSuperior.getChrOutDate());
                                        save.setReportEmpId(userSuperior.getReportEmpId());
                                        save.setGmtCreate(date);
                                        save.setGmtModified(date);
                                        save.setIsDelete(0);
                                        saveList.add(save);
                                    }
                                }

                                //保存
                                if (null != saveList && saveList.size() > 0) {
                                    log.info("saveList size : " + saveList.size());
                                    //一页数据2000个分5次提交
                                    final int subSize = 400;
                                    int subCount = saveList.size();
                                    for (int p = 0; p < subCount; p += subSize) {
                                        List<PaUserSuperior> part = saveList.subList(p, p + subSize > subCount ? subCount : p + subSize);
                                        paUserSuperiorMapper.insertBatchSomeColumn(part);
                                    }
                                }

                                //更新
                                if (null != updateList && updateList.size() > 0) {
                                    log.info("updateList size : " + updateList.size());
                                    //一页数据2000个分5次提交
                                    final int subSize = 400;
                                    int subCount = updateList.size();
                                    for (int p = 0; p < subCount; p += subSize) {
                                        List<PaUserSuperior> part = updateList.subList(p, p + subSize > subCount ? subCount : p + subSize);
                                        paUserSuperiorMapper.updateBatchById(part);
                                    }
                                }
                                emitter.send("sync " + pageIndex + " page userSuperior done. add:" + saveList.size() + ", update:" + updateList.size());
                            } catch (Exception e) {
                                e.printStackTrace();
                                try {
                                    emitter.send("sync " + pageIndex + " page userSuperior failed. 【ERROR】:" + StringUtils.substring(e.getMessage(), 0, 100) + "...");
                                } catch (Exception ex) {
                                }
                            } finally {
                                latch.countDown();
                            }
                        });
                        try {
                            //避免接口繁忙
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        log.error("latch await interrupted.", e);
                        Thread.currentThread().interrupt();
                    }
                    emitter.send("sync [" + finalStartDate + "~" + finalEndDate + "] userSuperior done. totalCount:" + totalCount);

                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        emitter.send("sync error: " + e.getMessage());
                    } catch (Exception ex) {
                    }

                } finally {
                    //关闭日志打印
                    emitter.complete();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            emitter.completeWithError(e); // 发送错误
            emitter.complete();
        }
        return emitter;
    }

//    private Map getAllUsersSuperior(Integer pageNum) throws Exception {
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        List<PaUserSuperior> userList = new ArrayList();
//        PaUserSuperior userMap1 = new PaUserSuperior();
//        userMap1.setChrOutDate( sdf.parse("2024-6-11 01:00:00"));
//        userMap1.setReportUm("WANGXUE924");
//        userMap1.setEmpId("00010011737");
//        userMap1.setReportEmpId( "00011113371");
//        userMap1.setEmpUm("XUBIN332");
//        userList.add(userMap1);
//
//        PaUserSuperior userMap2 = new PaUserSuperior();
//        userMap2.setChrOutDate( sdf.parse("2024-6-11 01:00:00"));
//        userMap2.setReportUm("WANGXUE924");
//        userMap2.setEmpId("00010011733");
//        userMap2.setReportEmpId( "00011113371");
//        userMap2.setEmpUm("XUBIN333");
//        userList.add(userMap2);
//
//        Map resultMap = new HashMap();
//        resultMap.put("recordList", userList);
//        resultMap.put("totalCount", 3000);
//        return resultMap;
//    }


}
