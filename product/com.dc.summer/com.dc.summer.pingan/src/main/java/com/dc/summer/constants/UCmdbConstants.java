package com.dc.summer.constants;

import com.dc.type.DatabaseType;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class UCmdbConstants {

    //因为使用DatabaseType 的dataSourceId名不太保守,它包含大小写下划线
    public static final Map<Integer, String> resourceNameMap = Map.ofEntries(
            Map.entry(DatabaseType.ORACLE.getValue(), "oracle"),
            Map.entry(DatabaseType.MYSQL.getValue(), "mysql"),
            Map.entry(DatabaseType.RASE_SQL.getValue(), "rasesql"),
            Map.entry(DatabaseType.TIDB.getValue(), "tidb"),
            Map.entry(DatabaseType.MONGODB.getValue(), "mongodb"),
            Map.entry(DatabaseType.REDIS.getValue(), "redis")
    );

    public static final Map<String, String> dbMap = Map.ofEntries(
            Map.entry("oracle", "DBT:O"),
            Map.entry("mysql", "DBT:M"),
            Map.entry("rasesql", "DBT:RA"),
            Map.entry("tidb", "DBT:TI"),
            Map.entry("mongodb", "DBT:MG"),
            Map.entry("redis", "DBT:R")
    );

    public static final Map<Integer, String> nameMap = Map.ofEntries(
            Map.entry(DatabaseType.ORACLE.getValue(), "oracle"),
            Map.entry(DatabaseType.MYSQL.getValue(), "mysql"),
            Map.entry(DatabaseType.RASE_SQL.getValue(), "rasesql"),
            Map.entry(DatabaseType.PG_SQL.getValue(), "rasesql"),//Pg对应平安rasesql
            Map.entry(DatabaseType.TIDB.getValue(), "tidb"),
            Map.entry(DatabaseType.MONGODB.getValue(), "mongodb"),
            Map.entry(DatabaseType.REDIS.getValue(), "redis")
    );

    public static final Map<String, String> paMap = Map.ofEntries(
            Map.entry("DBE:PRD", "生产环境"),
            Map.entry("STG", "测试环境"),
            Map.entry("DBE:DEV", "开发环境"),
            Map.entry("DBS:ON", "已上线"),
            Map.entry("DBS:MT", "维护中"),
            Map.entry("IFT:T", "传统架构"),
            Map.entry("T", "传统架构"),
            Map.entry("IFT:IN", "内部云"),
            Map.entry("IFT:PC", "公用云"),
            Map.entry("IFT:FC", "金融云"),
            Map.entry("IFT:SC", "政务云"),
            Map.entry("ODR:PRI", "主库"),
            Map.entry("ODR:LDG", "同城容灾"),
            Map.entry("ODR:RDG", "远程容灾"),
            Map.entry("ODR:LS", "从库"),
            Map.entry("DBL:P", "本地主库"),
            Map.entry("DBL:L", "同城灾备"),
            Map.entry("DBL:R", "远程灾备"),
            Map.entry("NOR:MAS", "主库"),
            Map.entry("NOR:LS", "从库"),
            Map.entry("NOR:LD", "同城容灾"),
            Map.entry("NOR:RS", "远程容灾"),
            Map.entry("RAT:C", "redis集群"),
            Map.entry("RAT:MS", "redis主从"),
            Map.entry("RAT:S", "单实例"),
            Map.entry("MDR:P", "主库"),
            Map.entry("MDR:LS", "从库"),
            Map.entry("MDR:RS", "容灾"),
            Map.entry("MDR:LS2", "同城从库"),
            Map.entry("OAT:SI", "单实例"),
            Map.entry("OAT:RAC", "RAC"),
            Map.entry("CRM:T", "传统"),
            Map.entry("CRM:C", "云"),
            Map.entry("CRM:E", "例外"),
            Map.entry("ECO:IC", "信创"),
            Map.entry("ECO:NIC", "非信创"),
            Map.entry("Y", "是"),
            Map.entry("N", "否")
    );


    public static final Map<String, List<String>> ruleList = Map.ofEntries(
            Map.entry("产险", Arrays.asList("产险", "产险新渠道", "产险好车主", "产险代理")),
            Map.entry("健康险", Arrays.asList("健康险")),
            Map.entry("金服", Arrays.asList("直通", "金服")),
            Map.entry("科技", Arrays.asList("平安科技", "公共应用")),
            Map.entry("陆控", Arrays.asList("普惠金融", "普惠立信", "平安消金", "众势催收")),
            Map.entry("寿险", Arrays.asList("寿险", "寿险新渠道", "寿险居家养老", "寿险银保科技", "健康互联居家")),
            Map.entry("信托", Arrays.asList("平安资本", "信托")),
            Map.entry("养老险", Arrays.asList("养老险")),
            Map.entry("银行", Arrays.asList("银行", "理财子", "团金会", "个金会")),
            Map.entry("资管", Arrays.asList("资产管理"))
    );
}
