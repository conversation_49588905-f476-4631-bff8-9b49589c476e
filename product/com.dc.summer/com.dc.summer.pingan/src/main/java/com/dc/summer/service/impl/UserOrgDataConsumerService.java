package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.SysOrgMapper;
import com.dc.repository.mysql.mapper.SysOrgUserMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.model.SysOrg;
import com.dc.summer.model.SyncUserOrgTaskDto;
import com.dc.summer.service.DataConsumerService;
import com.dc.summer.service.PaUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserOrgDataConsumerService implements DataConsumerService {


    @Resource
    SysOrgMapper sysOrgMapper;

    @Resource
    UserMapper userMapper;

    @Resource
    SysOrgUserMapper orgUserMapper;

    @Resource
    PaUserService paUserService;


    @Override
    public void doHandler(Object object) {

        try {
            SyncUserOrgTaskDto dto = (SyncUserOrgTaskDto) object;
            if (ObjectUtils.isEmpty(dto)) {
                return;
            }

            Map<String, SysOrg> sysOrgMap = new HashMap<>();
            List<SysOrg> saveList = dto.getSaveList();
            if (ObjectUtils.isEmpty(saveList)) {
                return;
            }

            //查询之前队列是否已经保存过，再此检查一下
            List<String> syncUuids = saveList.stream().map(SysOrg::getSyncUuid).collect(Collectors.toList());
            List<SysOrg> sysOrgData = sysOrgMapper.selectList(new QueryWrapper<SysOrg>().lambda().in(SysOrg::getSyncUuid, syncUuids));

            List<String> isExistsSyncUuids = null;
            if (null != sysOrgData && sysOrgData.size() > 0) {
                sysOrgMap = sysOrgData.stream().collect(Collectors.toMap(SysOrg::getSyncUuid, u -> u, (k1, k2) -> k1));
                isExistsSyncUuids = sysOrgData.stream().map(SysOrg::getSyncUuid).collect(Collectors.toList());
            }

            List<SysOrg> filterSaveList = new ArrayList<>();
            for (SysOrg org : saveList) {
                //排查已经存在的.可能上一批队列数据已经保存过了
                if (null != isExistsSyncUuids && isExistsSyncUuids.contains(org.getSyncUuid())) {
                    continue;
                }
                filterSaveList.add(org);
            }

            if (null != filterSaveList && filterSaveList.size() > 0) {
                log.info("saveList size : " + filterSaveList.size());
                //一页数据2000个分5次提交
                final int subSize = 400;
                int subCount = filterSaveList.size();
                for (int p = 0; p < subCount; p += subSize) {
                    List<SysOrg> part = filterSaveList.subList(p, p + subSize > subCount ? subCount : p + subSize);
                    sysOrgMapper.insertBatchSomeColumn(part);
                    //新建记录中存在绑定的数据 缓存起来，因为事务没提交
                    Map<String, SysOrg> insertMap = part.stream().collect(Collectors.toMap(SysOrg::getSyncUuid, u -> u, (k1, k2) -> k1));
                    sysOrgMap.putAll(insertMap);
                }
            }
            //------------------------根据dc.username 和PaUser.um 一致原则更新dc用户org--------------------
            paUserService.after(dto.getPaUserList(), sysOrgMap);


        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
