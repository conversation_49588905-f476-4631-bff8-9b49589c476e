package com.dc.summer.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ResponsePingAnModel {
    private Integer intercept;
    private Integer success;
    private Integer error;
    private Integer execute;
    private String time_start;
    private String time_end;
    private String instance_uuid;
    private Double success_percent = 0.0;
}

/**
 * http://192.168.3.137:7089/api/dams/sql/audits/search
 * <p>
 * {
 * "status":0,
 * "message":"操作成功",
 * "data":{
 * "intercept":11262,
 * "success":564474,
 * "error":21784,
 * "execute":597520
 * }
 * }
 */