package com.dc.summer.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("审批链")
@NoArgsConstructor
@AllArgsConstructor
public class PaApproveChainBean {

    @NotBlank(message = "UM账号不能为空")
    @ApiModelProperty(value = "UM账号,支持多个逗号分割", required = true, example = "UM1,UM2")
    private String um;  /*平安UM账号 逗号分割支持多个*/

    @NotNull(message = "审批类型不能为空")
    @ApiModelProperty(value = "审批类型，0：顺序审批|1：并行审批|2：协同审批|3：传阅", required = true, example = "1")
    private Integer type;

    @NotNull(message = "审批链顺序不能为空")
    @ApiModelProperty(value = "审批顺序", required = true, example = "0")
    private Integer step;

}
