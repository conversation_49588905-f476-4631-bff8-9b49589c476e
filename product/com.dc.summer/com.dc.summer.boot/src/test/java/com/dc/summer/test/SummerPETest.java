package com.dc.summer.test;

import com.dc.SummerPeApplication;
import com.dc.springboot.core.model.execution.PermissionModel;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.SingleAsyncExecuteMessage;
import com.dc.springboot.core.model.execution.SqlExecuteModel;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.log.SqlRecord;
import com.dc.springboot.core.model.result.WebSQLResultsBaseRow;
import com.dc.springboot.core.model.result.WebSQLResultsUpdateRow;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.springboot.core.model.sensitive.MaskNodeModel;
import com.dc.springboot.core.model.sensitive.SqlDesensitization;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.TaskSpringBootTest;
import com.dc.summer.config.GlobalConfig;
import com.dc.summer.model.data.message.*;
import com.dc.summer.model.data.model.ResultModel;
import com.dc.summer.model.data.model.ResultsIndexModel;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.summer.model.type.LineDelimiterType;
import com.dc.summer.model.type.PageSelectedType;
import com.dc.springboot.core.model.type.TextIdentifierType;
import com.dc.summer.registry.center.Global;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import com.google.gson.reflect.TypeToken;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.AssertionErrors;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest(classes = SummerPeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class SummerPETest extends TaskSpringBootTest {

    @Override
    protected List<DatabaseType> selectedDatabase() {
        return List.of(DatabaseType.ORACLE);
    }

    @BeforeEach
    public void init() {
        setupMockMvc();
        setupDataBase();
        GlobalConfig globalConfig = applicationContext.getBean(GlobalConfig.class);
        Global.setConfig(globalConfig);
    }

    @Order(0)
    @Test
    void testConnection() {
        test(dataBaseTest -> {
            TestConnectionMessage message = new TestConnectionMessage();
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            BeanUtils.copyProperties(connectionConfig, message);
            message.setDriverId(null);
            post("/execute/test-connection", message);
        });
    }

    @SneakyThrows
    @Order(1)
    @Test
    void prepareConnection() {
        test(dataBaseTest -> {
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            post("/execute/prepare-connection", connectionConfig);
        });
        Thread.sleep(2000L);
    }


    @Order(2)
    @Test
    void executeInitSql() {
        test(dataBaseTest -> {
            BatchSyncExecuteMessage message = new BatchSyncExecuteMessage();
            List<String> initSql = dataBaseTest.getInitSql();
            if (CollectionUtils.isEmpty(initSql)) {
                return;
            }
            List<ValidExecuteModel> validExecuteModels = initSql.stream().map(sql -> {
                ValidExecuteModel validExecuteModel = new ValidExecuteModel();
                validExecuteModel.setSql(sql);
                validExecuteModel.setPermissionModel(getPermissionModel());
                validExecuteModel.setUserId("123");
                validExecuteModel.setSqlHistory(getSqlHistory());
                validExecuteModel.setSqlRecord(getSqlRecord());
                return validExecuteModel;
            }).collect(Collectors.toList());
            message.setBatchExecuteModels(validExecuteModels);
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            message.setConnectionConfig(connectionConfig);
            post("/execute/batch-execute-sql", message);
        });
    }

    @Order(11)
    @Test
    void openSession() {
        test(dataBaseTest -> {
            ConnectionTokenMessage message = new ConnectionTokenMessage();
            message.setConnectionConfig(dataBaseTest.getConnectionConfig());
            message.setToken(dataBaseTest.getToken());
            TokenConfig tokenConfig = new TokenConfig();
            tokenConfig.setAutoCommit(true);
            tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
            tokenConfig.setExpirationTime(300L);
            tokenConfig.setPrefs("");
            tokenConfig.setCharset("");
            message.setTokenConfig(tokenConfig);
            post("/execute/open-session", message);
        });
    }

    @Order(12)
    @Test
    void executeSql() {
        task(dataBaseTest -> {
            String sql = dataBaseTest.getExecuteSql();
            SingleAsyncExecuteMessage message = new SingleAsyncExecuteMessage();
            message.setToken(dataBaseTest.getToken());
            message.setSqlHistory(getSqlHistory());
            message.setSensitiveAuthDetail(new ArrayList<>());
            message.setSqlRecord(getSqlRecord());
            message.setOperation(sql.split(" ")[0]);
            SqlExecuteModel sqlExecuteModel = new SqlExecuteModel();
            sqlExecuteModel.setSql(sql);
            sqlExecuteModel.setOffset(0);
            sqlExecuteModel.setLimit(10);
            message.setSqlExecuteModel(sqlExecuteModel);
            message.setPermissionModel(getPermissionModel());
            message.setUserId("123");
            message.setTableName("test");
            message.setPrimaryKeyColumns(List.of("id"));
            message.setSqlDesensitization(makeSqlDesensitization(dataBaseTest.getConnectionConfig().getDatabaseType()));
            return post("/execute/execute-sql", message);
        });
    }

    @Order(13)
    @Test
    void taskInfoExecuteSql() {
        taskInfo();
    }


    @Order(14)
    @Test
    void taskResultExecuteSql() {
        taskResult(null);
    }

    @Order(15)
    @Test
    void submitUpdate() {
        task(dataBaseTest -> {
            SqlUpdateMessage message = new SqlUpdateMessage();
            message.setToken(dataBaseTest.getToken());
            message.setSqlHistory(getSqlHistory());
            message.setResultId("1");
            message.setCreatedRows(getRows(dataBaseTest.getCreateData(), "create", 1));
            message.setAddedRows(getRows(dataBaseTest.getAddData(), "insert", 2));
            message.setUpdatedRows(getRows(dataBaseTest.getUpdateData(), dataBaseTest.getUpdateValues(), 3));
            message.setDeletedRows(getRows(dataBaseTest.getDeleteData(), "delete", 4));
            message.setUserId("123");
            return post("/execute/submit-update", message);
        });
    }

    @Order(16)
    @Test
    void taskInfoSubmitUpdate() {
        taskInfo();
    }

    @Order(17)
    @Test
    void taskResultSubmitUpdate() {
        taskResult(dataBaseTest -> CollectionUtils.isEmpty(dataBaseTest.getPrimaryKeyColumns()));
    }

    @Order(18)
    @Test
    void executeExport() {
        task((dataBaseTest, integer) -> {
            SqlExportMessage message = new SqlExportMessage();
            SqlExportModel sqlExportModel = new SqlExportModel();

            List<ResultsIndexModel> resultsIndexModels = new ArrayList<>();
            ResultsIndexModel resultsIndexModel = new ResultsIndexModel();
            resultsIndexModel.setSqlIndex(0);
            ResultModel resultModel = new ResultModel();
            resultModel.setResultIndex(0);
            resultsIndexModel.setResultModels(List.of(resultModel));
            resultsIndexModels.add(resultsIndexModel);
            sqlExportModel.setResultsIndexModels(resultsIndexModels);
            sqlExportModel.setTaskId(taskInfos.get(integer).getTaskId());
            message.setToken(dataBaseTest.getToken());
//            message.setSqlExportModels(Collections.singletonList(sqlExportModel));
            message.setSqlExportModels(List.of(sqlExportModel, sqlExportModel));
            message.setExportType(ExportType.TXT);
            message.setTextIdentifier(TextIdentifierType.DOUBLE_QUOTE);
            message.setLineDelimiter(LineDelimiterType.CRLF);
            message.setColumnDelimiter(ColumnDelimiterType.TAB);
            message.setPageSelected(PageSelectedType.CURRENT_PAGE);
            message.setUserId("123");
            return post("/execute/execute-export", message);
        });
    }

    @Order(19)
    @Test
    void taskInfoExecuteExport() {
        taskInfo();
    }

    @Order(110)
    @Test
    void taskResultExecuteExport() {
        taskResult(dataBaseTest -> CollectionUtils.isEmpty(dataBaseTest.getPrimaryKeyColumns()));
    }

    @Order(111)
    @Test
    void parseScript() {
        test(dataBaseTest -> {
            parseScript(dataBaseTest.getParseScriptForWhole(), dataBaseTest, true);
            parseScript(dataBaseTest.getParseScriptForSplit(), dataBaseTest, false);
        });
    }

    @Order(112)
    @Test
    void sqlGenerateDataSelect() {
        sqlGenerate("dataSelect");
    }

    @Order(113)
    @Test
    void sqlGenerateDataSelectMany() {
        sqlGenerate("dataSelectMany");
    }

    @Order(114)
    @Test
    void sqlGenerateDataInsert() {
        sqlGenerate("dataInsert");
    }

    @Order(115)
    @Test
    void sqlGenerateDataUpdate() {
        sqlGenerate("dataUpdate");
    }

    @Order(116)
    @Test
    void sqlGenerateDataDeleteByUniqueKey() {
        sqlGenerate("dataDeleteByUniqueKey");
    }

    @Order(117)
    @Test
    void sqlGenerateDdlByResultSet() {
        sqlGenerate("ddlByResultSet");
    }

    void sqlGenerate(String generatorId) {
        test(dataBaseTest -> {
            SqlGenerateMessage message = new SqlGenerateMessage();
            message.setToken(dataBaseTest.getToken());
            message.setTaskId("1");
            message.setResultId("1");
            message.setSqlIndex(0);
            message.setResultIndex(0);
            message.setRowNumbers(List.of(0, 1));
            message.setGeneratorId(generatorId);
            message.setFullyQualifiedNames(true);
            message.setCompactSQL(true);
            message.setExcludeAutoGeneratedColumn(true);
            message.setUseCustomDataFormat(true);
            post("/execute/sql-generate", message);
        });
    }

    private void parseScript(List<String> parseScript, DataBaseTest dataBaseTest, boolean equals) throws Exception {
        if (CollectionUtils.isEmpty(parseScript)) {
            return;
        }
        for (String script : parseScript) {
            ParseScriptMessage message = new ParseScriptMessage();
            message.setScript(script);
            message.setDatabaseType(dataBaseTest.getConnectionConfig().getDatabaseType());
            post("/execute/parse-script", message)
                    .andExpect(result -> {
                        WebSQLScriptInfo scriptInfo = fetchResultBody(result, new TypeToken<Result<WebSQLScriptInfo>>() {
                        });
                        if (equals) {
                            AssertionErrors.assertEquals("parser script error", 1, scriptInfo.getQueries().size());
                        } else {
                            AssertionErrors.assertNotEquals("parser script error", 1, scriptInfo.getQueries().size());
                        }
                    });
        }
    }


    private PermissionModel getPermissionModel() {
        PermissionModel permissionModel = new PermissionModel();
        permissionModel.setSqlExecuteStatus(SqlExecuteStatus.SUCCESS.getValue());
        return permissionModel;
    }

    private SqlHistory getSqlHistory() {
        SqlHistory sqlHistory = new SqlHistory();
        sqlHistory.setOrigin(1);
        return sqlHistory;
    }

    private SqlRecord getSqlRecord() {
        SqlRecord sqlRecord = new SqlRecord();
        sqlRecord.setSql("xxx");
        return sqlRecord;
    }

    private List<WebSQLResultsUpdateRow> getRows(List<Object> data, Map<String, Object> updateValues, int order) {
        if (data == null) {
            return Collections.emptyList();
        }
        WebSQLResultsUpdateRow row = new WebSQLResultsUpdateRow();
        row.setData(data);
        row.setOrder(order);
        row.setPermissionModel(getPermissionModel());
        row.setSqlRecord(getSqlRecord());
        row.setSql("update xxx");
        row.setOperation("update");
        row.setUpdateValues(updateValues);
        return Collections.singletonList(row);
    }

    private List<WebSQLResultsBaseRow> getRows(List<Object> data, String operation, int order) {
        if (data == null) {
            return Collections.emptyList();
        }
        WebSQLResultsBaseRow row = new WebSQLResultsUpdateRow();
        row.setData(data);
        row.setOrder(order);
        row.setPermissionModel(getPermissionModel());
        row.setSqlRecord(getSqlRecord());
        row.setSql(operation + " xxx");
        row.setOperation(operation);
        return Collections.singletonList(row);
    }

    private SqlDesensitization makeSqlDesensitization(int dbType) {
        SqlDesensitization sqlDesensitization = new SqlDesensitization();

        String tableName = "dc_sql_history_202306";
        String column = "records.rules.auth_user";

        sqlDesensitization.setEnableDesensitizeType(1);

        DataMask dataMask = new DataMask();
        dataMask.setColumnName(column);
        dataMask.setColumnId("c0741df2593e4cfdb4fc38cb0629e965");
        dataMask.setColumnAlias(column);
        dataMask.setTableName(tableName);
        dataMask.setShowTableName(tableName);
        dataMask.setConnectId("123");
        dataMask.setDbType(Long.valueOf(dbType));
        dataMask.setInstanceName("123");
        dataMask.setEnvironment(1L);
        dataMask.setAlgorithmType(99);
        dataMask.setAlgorithmParam("{\"key\":99,\"value\":null}");
        dataMask.setIsSensitive(1);
        dataMask.setIsDefault(1);
        dataMask.setActionMask("cfg");
        dataMask.setAuthLevel(0);
        dataMask.setDesensitizeRuleId("6f353067d33c4d24ab7badb0e7fd61a5");
        dataMask.setDesensitizeRuleName("姓名脱敏规则");
        dataMask.setDistinguishRuleId("20bf1c8aaa596dd11b054805354fbe2b");
        dataMask.setDistinguishRuleName("中文姓名");
        dataMask.setColumnAlias("#FFFFFF");
        dataMask.setSensitiveLevelId("dafe08d4edd813472078c134e427cbff");
        dataMask.setSensitiveLevelName("3级");

        sqlDesensitization.setDataMasks(List.of(dataMask));
        sqlDesensitization.setSymbol("******");

        MaskNodeModel maskNodeModel = new MaskNodeModel();
        maskNodeModel.setTableName(tableName);
//        String column = "records.rules.rule_condition";
        maskNodeModel.setColumnAlias(column);
        maskNodeModel.setColumnName(column);
        maskNodeModel.setColumnScript(column);
        sqlDesensitization.setNodeModels(List.of(maskNodeModel));
        sqlDesensitization.setNeedOriginalRows(false);

        return sqlDesensitization;
    }

}
