package com.dc.summer;

import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.WebAsyncTaskReact;
import com.dc.summer.model.data.message.TaskInfoMessage;
import com.dc.summer.model.data.message.TaskResultMessage;
import com.dc.test.springboot.BaseSpringBootTest;
import com.dc.test.springboot.DataBaseTest;
import com.google.gson.reflect.TypeToken;
import org.springframework.test.util.AssertionErrors;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

public class TaskSpringBootTest extends BaseSpringBootTest {

    protected static final List<WebAsyncTaskInfo> taskInfos = new ArrayList<>();


    protected void taskInfo(int i, Consumer<WebAsyncTaskInfo> consumer) {
        test((dataBaseTest, integer) -> {
            WebAsyncTaskInfo taskInfo;
            do {
                TaskInfoMessage message = new TaskInfoMessage();
                message.setToken(dataBaseTest.getToken());
                message.setTaskId(taskInfos.get(i).getTaskId());
                ResultActions actions = post("/execute/task-info", message);
                taskInfo = fetchResultBody(actions.andReturn(), new TypeToken<Result<WebAsyncTaskInfo>>() {
                });
                Thread.sleep(1000);
            } while (!taskInfo.isOver());
            consumer.accept(taskInfo);
        });
    }

    protected void taskReact(Predicate<DataBaseTest> ignoreFailure) {
        test((dataBaseTest, integer) -> {
            WebAsyncTaskReact taskReact;
            do {
                TaskInfoMessage message = new TaskInfoMessage();
                message.setToken(dataBaseTest.getToken());
                message.setTaskId(taskInfos.get(integer).getTaskId());
                ResultActions actions = post("/execute/task-react", message);
                taskReact = fetchResultBody(actions.andReturn(), new TypeToken<Result<WebAsyncTaskReact>>() {
                });

                if (ignoreFailure != null && ignoreFailure.test(dataBaseTest)) {
                    return;
                }
                WebSQLExecuteInfo webSQLExecuteInfo = taskReact.getExecuteInfo();
                if (webSQLExecuteInfo != null) {
                    List<WebSQLQueryResult> queryResults = webSQLExecuteInfo.getQueryResults();
                    if (queryResults != null) {
                        queryResults.forEach(queryResult -> {
                            AssertionErrors.assertEquals("execute sql error", SqlExecuteStatus.SUCCESS.getValue(), queryResult.getStatus());
                        });
                    }
                }

            } while (!taskReact.getTaskInfo().isOver());
        });
    }

    protected void taskInfo() {
        test((dataBaseTest, integer) -> {
            WebAsyncTaskInfo taskInfo;
            do {
                TaskInfoMessage message = new TaskInfoMessage();
                message.setToken(dataBaseTest.getToken());
                message.setTaskId(taskInfos.get(integer).getTaskId());
                ResultActions actions = post("/execute/task-info", message);
                taskInfo = fetchResultBody(actions.andReturn(), new TypeToken<Result<WebAsyncTaskInfo>>() {
                });
                Thread.sleep(1000);
            } while (!taskInfo.isOver());
        });
    }

    protected void taskResult(int i, Consumer<Result<Object>> consumer) {
        test((dataBaseTest, integer) -> {
            TaskResultMessage message = new TaskResultMessage();
            message.setToken(dataBaseTest.getToken());
            message.setTaskId(taskInfos.get(i).getTaskId());
            message.setStage(1);
            post("/execute/task-result", message)
                    .andExpect(result -> consumer.accept(fetchResult(result)));
        });
    }

    protected void taskResult(Predicate<DataBaseTest> ignoreFailure) {
        test((dataBaseTest, integer) -> {
            TaskResultMessage message = new TaskResultMessage();
            message.setToken(dataBaseTest.getToken());
            message.setTaskId(taskInfos.get(integer).getTaskId());
            post("/execute/task-result", message)
                    .andExpect(result -> {
                        if (ignoreFailure != null && ignoreFailure.test(dataBaseTest)) {
                            return;
                        }
                        WebSQLExecuteInfo webSQLExecuteInfo = fetchResultBody(result, new TypeToken<Result<WebSQLExecuteInfo>>() {
                        });
                        List<WebSQLQueryResult> queryResults = webSQLExecuteInfo.getQueryResults();
                        if (queryResults != null) {
                            queryResults.forEach(queryResult -> {
                                AssertionErrors.assertEquals("execute sql error", SqlExecuteStatus.SUCCESS.getValue(), queryResult.getStatus());
                            });
                        }
                    });
        });
    }



    protected void task(Function<DataBaseTest, ResultActions> function) {
        dataBaseTestMap.values().forEach(dataBaseTest -> {
            ResultActions actions = function.apply(dataBaseTest);
            WebAsyncTaskInfo webAsyncTaskInfo = fetchResultBody(actions.andReturn(), new TypeToken<Result<WebAsyncTaskInfo>>() {
            });
            taskInfos.add(webAsyncTaskInfo);
        });
    }

    protected void task(BiFunction<DataBaseTest, Integer, ResultActions> function) {
        AtomicInteger i = new AtomicInteger(0);
        dataBaseTestMap.values().forEach(dataBaseTest -> {
            try {
                ResultActions actions = function.apply(dataBaseTest, i.getAndIncrement());
                WebAsyncTaskInfo webAsyncTaskInfo = fetchResultBody(actions.andReturn(), new TypeToken<Result<WebAsyncTaskInfo>>() {
                });
                taskInfos.add(webAsyncTaskInfo);
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });
    }

}
