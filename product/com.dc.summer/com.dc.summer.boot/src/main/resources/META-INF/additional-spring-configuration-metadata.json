{"properties": [{"name": "summer.session.maximum-context-size", "type": "java.lang.Integer", "description": "Description for summer.session.maximum-context-size."}, {"name": "summer.session.keep-alive-time", "type": "java.lang.Long", "description": "Description for summer.session.keep-alive-time."}, {"name": "summer.session.query-timeout", "type": "java.lang.Integer", "description": "Description for summer.session.query-timeout."}, {"name": "summer.session.network-timeout", "type": "java.lang.Integer", "description": "Description for summer.session.network-timeout."}, {"name": "spring.datasource.druid.databases.master.url", "type": "java.lang.String", "description": "Description for spring.datasource.druid.databases.master.url."}, {"name": "spring.datasource.druid.databases.master.username", "type": "java.lang.String", "description": "Description for spring.datasource.druid.databases.master.username."}, {"name": "spring.datasource.druid.databases.master.password", "type": "java.lang.String", "description": "Description for spring.datasource.druid.databases.master.password."}, {"name": "spring.redis.cluster.read-model", "type": "java.lang.String", "description": "Description for spring.redis.cluster.read-model."}, {"name": "summer.session.heart-beat-count", "type": "java.lang.Integer", "description": "Description for summer.session.heart-beat-count."}]}