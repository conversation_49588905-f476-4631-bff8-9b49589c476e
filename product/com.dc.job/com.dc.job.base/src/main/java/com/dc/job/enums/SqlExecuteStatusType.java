package com.dc.job.enums;

public enum SqlExecuteStatusType {

    toBeExecuted(0, "待执行"),
    running(1, "执行中"),
    success(2, "执行成功"),
    fail(3, "执行失败"),
    termination(4, "执行终止");

    Integer value;
    String name;

    SqlExecuteStatusType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }


    public Integer getValue() {
        return value;
    }

}
