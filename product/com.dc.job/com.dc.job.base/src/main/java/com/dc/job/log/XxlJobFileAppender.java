package com.dc.job.log;

import com.dc.job.biz.model.LogResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * store trigger log in each log-file
 * <AUTHOR> 2016-3-12 19:25:12
 */
public class XxlJobFileAppender {
	private static Logger logger = LoggerFactory.getLogger(XxlJobFileAppender.class);
	
	// for JobThread (support log for child thread of job handler)
	//public static ThreadLocal<String> contextHolder = new ThreadLocal<String>();
	public static final InheritableThreadLocal<String> contextHolder = new InheritableThreadLocal<String>();


	/**
	 * log base path
	 *
	 * strut like:
	 * 	---/
	 * 	---/gluesource/
	 * 	---/gluesource/10_1514171108000.js
	 * 	---/gluesource/10_1514171108000.js
	 * 	---/2017-12-25/
	 * 	---/2017-12-25/639.log
	 * 	---/2017-12-25/821.log
	 *
	 */
	private static String logBasePath = "/data/applogs/xxl-job/jobhandler";
	private static String glueSrcPath = logBasePath.concat("/gluesource");
	public static void initLogPath(String logPath){
		// init
		if (logPath!=null && logPath.trim().length()>0) {
			logBasePath = logPath;
		}
		// mk base dir
		File logPathDir = new File(logBasePath);
		if (!logPathDir.exists()) {
			logPathDir.mkdirs();
		}
		logBasePath = logPathDir.getPath();

		// mk glue dir
		File glueBaseDir = new File(logPathDir, "gluesource");
		if (!glueBaseDir.exists()) {
			glueBaseDir.mkdirs();
		}
		glueSrcPath = glueBaseDir.getPath();
	}
	public static String getLogPath() {
		return logBasePath;
	}
	public static String getGlueSrcPath() {
		return glueSrcPath;
	}

	/**
	 * log filename, like "logPath/yyyy-MM-dd/9999.log"
	 *
	 * @param triggerDate
	 * @param logId
	 * @return
	 */
	public static String makeLogFileName(Date triggerDate, long logId) {

		// filePath/yyyy-MM-dd
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");	// avoid concurrent problem, can not be static
		File logFilePath = new File(getLogPath(), sdf.format(triggerDate));
		if (!logFilePath.exists()) {
			logFilePath.mkdir();
		}

		// filePath/yyyy-MM-dd/9999.log
		String logFileName = logFilePath.getPath()
				.concat(File.separator)
				.concat(String.valueOf(logId))
				.concat(".log");
		return logFileName;
	}

	/**
	 * append log
	 *
	 * @param logFileName
	 * @param appendLog
	 */
	public static void appendLog(String logFileName, String appendLog) {

		// log file
		if (logFileName==null || logFileName.trim().length()==0) {
			return;
		}
		File logFile = new File(logFileName);

		if (!logFile.exists()) {
			try {
				logFile.createNewFile();
			} catch (IOException e) {
				logger.error(e.getMessage(), e);
				return;
			}
		}

		// log
		if (appendLog == null) {
			appendLog = "";
		}
		appendLog += "\r\n";
		appendLog = logDetailDesense(appendLog);
		// append file content
		FileOutputStream fos = null;
		try {
			fos = new FileOutputStream(logFile, true);
			fos.write(appendLog.getBytes("utf-8"));
			fos.flush();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
		
	}

	/**
	 * support read log-file
	 *
	 * @param logFileName
	 * @return log content
	 */
	public static LogResult readLog(String logFileName, int fromLineNum){

		// valid log file
		if (logFileName==null || logFileName.trim().length()==0) {
            return new LogResult(fromLineNum, 0, "readLog fail, logFile not found", true);
		}
		File logFile = new File(logFileName);

		if (!logFile.exists()) {
            return new LogResult(fromLineNum, 0, "readLog fail, logFile not exists", true);
		}

		// read file
		StringBuffer logContentBuffer = new StringBuffer();
		int toLineNum = 0;
		LineNumberReader reader = null;
		try {
			//reader = new LineNumberReader(new FileReader(logFile));
			reader = new LineNumberReader(new InputStreamReader(new FileInputStream(logFile), "utf-8"));
			String line = null;

			while ((line = reader.readLine())!=null) {
				toLineNum = reader.getLineNumber();		// [from, to], start as 1
				if (toLineNum >= fromLineNum) {
					logContentBuffer.append(line).append("\n");
				}
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}

		// result
		LogResult logResult = new LogResult(fromLineNum, toLineNum, logContentBuffer.toString(), false);
		return logResult;

		/*
        // it will return the number of characters actually skipped
        reader.skip(Long.MAX_VALUE);
        int maxLineNum = reader.getLineNumber();
        maxLineNum++;	// 最大行号
        */
	}

	/**
	 * support read log-file
	 *
	 * @param logFileName
	 * @return log content
	 */
	public static LogResult readLog(String logFileName, int fromLineNum, int toLineNum) {

		// valid log file
		if (logFileName==null || logFileName.trim().length()==0) {
			return new LogResult(fromLineNum, 0, "readLog fail, logFile not found", true);
		}
		File logFile = new File(logFileName);

		if (!logFile.exists()) {
			return new LogResult(fromLineNum, 0, "readLog fail, logFile not exists", true);
		}

		if (toLineNum > 0 && toLineNum < fromLineNum) { // toLineNum大于0时启用,若启用后则需大于等于fromLineNum才有意义
			return new LogResult(fromLineNum, toLineNum, "readLog fail, toLineNum needs to be greater than fromLineNum", true);
		}

		// read file
		StringBuffer logContentBuffer = new StringBuffer();
		int nowLineNum = 0;
		LineNumberReader reader = null;
		try {
			//reader = new LineNumberReader(new FileReader(logFile));
			reader = new LineNumberReader(new InputStreamReader(new FileInputStream(logFile), "utf-8"));
			String line = null;

			while ((line = reader.readLine())!=null) {
				nowLineNum = reader.getLineNumber();		// [from, to], start as 1
				if (toLineNum > 0 && toLineNum < nowLineNum) { // 若当前行数大于想要取出的行数则不再读取
					break;
				}
				if (nowLineNum >= fromLineNum) {
					logContentBuffer.append(line).append("\n");
				}
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}

		int endLineNum = Math.min(toLineNum, nowLineNum);
		if (endLineNum <= 0) {
			endLineNum = nowLineNum;
		}

		// result
		LogResult logResult = new LogResult(fromLineNum, endLineNum, logContentBuffer.toString(), false);
		return logResult;

		/*
        // it will return the number of characters actually skipped
        reader.skip(Long.MAX_VALUE);
        int maxLineNum = reader.getLineNumber();
        maxLineNum++;	// 最大行号
        */
	}

	/**
	 * support read log-file
	 *
	 * @param logFileName
	 * @return log content
	 */
	public static LogResult readLastLog(String logFileName, int toLineNum, int lastLineNum) {

		// valid log file
		if (logFileName==null || logFileName.trim().length()==0) {
			return new LogResult(1, 0, "readLog fail, logFile not found", true);
		}
		File logFile = new File(logFileName);

		if (!logFile.exists()) {
			return new LogResult(1, 0, "readLog fail, logFile not exists", true);
		}

		int maxLineNum = getMaxLineNum(logFile);

		int fromLineNum = maxLineNum - lastLineNum + 1;
		if (fromLineNum < 0) {
			fromLineNum = 0;
		}

		// read file
		StringBuffer logContentBuffer = new StringBuffer();
		int nowLineNum = 0;
		LineNumberReader reader = null;
		try {
			//reader = new LineNumberReader(new FileReader(logFile));
			reader = new LineNumberReader(new InputStreamReader(new FileInputStream(logFile), "utf-8"));
			String line = null;

			while ((line = reader.readLine())!=null && nowLineNum<=maxLineNum) {
				nowLineNum = reader.getLineNumber();		// [from, to], start as 1
				if (nowLineNum >= fromLineNum) {
					logContentBuffer.append(line).append("\n");
				}
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}

		if (maxLineNum == toLineNum) {
			fromLineNum = nowLineNum + 1;
		}

		// result
		LogResult logResult = new LogResult(fromLineNum, nowLineNum, logContentBuffer.toString(), false);
		return logResult;

	}

	/**
	 * support read log-file
	 *
	 * @param logFileName
	 * @return log content
	 */
	public static LogResult logMaxLineNum(String logFileName) {

		// valid log file
		if (logFileName==null || logFileName.trim().length()==0) {
			return new LogResult(0, 0, "readLog fail, logFile not found", true);
		}
		File logFile = new File(logFileName);

		if (!logFile.exists()) {
			return new LogResult(0, 0, "readLog fail, logFile not exists", true);
		}

		int maxLineNum = getMaxLineNum(logFile);
		return new LogResult(0, maxLineNum, "readLog finish", true);

	}

	public static int getMaxLineNum(File logFile) {

		LineNumberReader reader = null;

		try {
			reader = new LineNumberReader(new InputStreamReader(new FileInputStream(logFile), "utf-8"));
			int nowLineNum = 0;
			while (reader.readLine()!=null) {
				nowLineNum = reader.getLineNumber();		// [from, to], start as 1
			}
			return nowLineNum;
//			reader.skip(Long.MAX_VALUE);
//			int maxLineNum = reader.getLineNumber();
//			maxLineNum++;	// 最大行号
//			return maxLineNum;
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
		return 0;
	}

	/**
	 * read log data
	 * @param logFile
	 * @return log line content
	 */
	public static String readLines(File logFile){
		BufferedReader reader = null;
		try {
			reader = new BufferedReader(new InputStreamReader(new FileInputStream(logFile), "utf-8"));
			if (reader != null) {
				StringBuilder sb = new StringBuilder();
				String line = null;
				while ((line = reader.readLine()) != null) {
					sb.append(line).append("\n");
				}
				return sb.toString();
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
		return null;
	}

	/**
	 *  平安日志脱敏需要使用的日志过滤
	 * @param value 日志文本信息
	 * @return
	 */
	private static String logDetailDesense(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        try {
            Class<?> ruleSetDesensTemplateBuilderClazz = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sdmp.sdk.extension.template.RuleSetDesensTemplateBuilder");
            Object ruleSetDesensTemplateBuilderClazzObj = ruleSetDesensTemplateBuilderClazz.newInstance();

            Method ruleSetDesensTemplateBuilderClazzLoad = ruleSetDesensTemplateBuilderClazz.getDeclaredMethod("load", String.class);
            Object desensTemplateBuilderObj = ruleSetDesensTemplateBuilderClazzLoad.invoke(ruleSetDesensTemplateBuilderClazzObj, "sensitive-sdk-system-strategy");

            Class<?> desensTemplateBuilderClazz = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sdmp.sdk.extension.template.DesensTemplateBuilder");
//                Object desensTemplateBuilderObj = desensTemplateBuilderClazz.newInstance();


            Method desensTemplateBuilderRemove = desensTemplateBuilderClazz.getDeclaredMethod("remove", String.class);
            desensTemplateBuilderRemove.invoke(desensTemplateBuilderObj, "SDA0021");

            Method desensTemplateBuilderBuild = desensTemplateBuilderClazz.getDeclaredMethod("build");
            Object desenTemplateObj = desensTemplateBuilderBuild.invoke(desensTemplateBuilderObj);

            Class<?> MsgDesensor = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sensitive.sdk.logdesens.internal.MsgDesensor");

            Class<?> desensTemplateClazz = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sdmp.sdk.extension.template.DesensTemplate");
            Method desensWithPlaceHolderParam = MsgDesensor.getDeclaredMethod("desensWithPlaceHolderParam", String.class, Object[].class, Set.class, desensTemplateClazz);

            value = (String) desensWithPlaceHolderParam.invoke(null, value, null, new HashSet<>(), desenTemplateObj);

        } catch (Exception e) {
            logger.error("调用直接脱敏失败，请检查脱敏方法。", e);
        } catch (Throwable t) {
            logger.error("调用直接脱敏失败，请检查扩展程序：" + t.getMessage());
        }
        return value;
	}

}
