<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dc</groupId>
        <artifactId>summer.product</artifactId>
        <version>1.0</version>
    </parent>
    <modules>
        <module>com.dc.job.base</module>
        <module>com.dc.job.executor</module>
        <module>com.dc.job.boot</module>
        <module>com.dc.job.administrator</module>
        <module>com.dc.job.admin</module>
    </modules>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>com.dc.job</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <netty-all.version>4.1.48.Final</netty-all.version>
        <groovy.version>3.0.3</groovy.version>
    </properties>

</project>