package com.dc.executor.util;

import com.dc.job.log.XxlJobFileAppender;
import com.dc.job.log.XxlJobLogger;
import com.dc.job.util.DateUtil;
import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;

public class DCJobLogger extends XxlJobLogger {

    private static void logDetail(StackTraceElement callInfo, String appendLog) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(DateUtil.formatDateTime(new Date())).append(" ").append(appendLog != null ? appendLog : "");
        String formatAppendLog = stringBuffer.toString();
        String logFileName = (String) XxlJobFileAppender.contextHolder.get();
        if (logFileName != null && logFileName.trim().length() > 0) {
            XxlJobFileAppender.appendLog(logFileName, formatAppendLog);
        } else {

        }

    }

    public static void schedulingSuccessful() {
        DCJobLogger.log("调度成功");
    }

    public static void schedulingFailed() {
        DCJobLogger.log("调度失败");
    }

    public static void schedulingTermination() {
        DCJobLogger.log("调度终止");
    }

    public static void log(String appendLogPattern, Object... appendLogArguments) {
        FormattingTuple ft = MessageFormatter.arrayFormat(appendLogPattern, appendLogArguments);
        String appendLog = ft.getMessage();
        StackTraceElement callInfo = (new Throwable()).getStackTrace()[1];
        logDetail(callInfo, appendLog);
    }

    public static void log(Throwable e) {
        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        String appendLog = stringWriter.toString();
        StackTraceElement callInfo = (new Throwable()).getStackTrace()[1];
        logDetail(callInfo, appendLog);
    }
}
