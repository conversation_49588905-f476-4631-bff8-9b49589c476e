package com.dc.executor.model;

import com.dc.springboot.core.model.database.ConnectionConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SchemaInfo {
    private String connect_id;
    private String schema_id;
    private String schema_name;
    private Integer object_type;
    private Object entity;
    private Integer db_type;
    private Integer pattern;
    private Integer environment;
    private String connection;
    private String instance_name;
    private String connection_desc;
    private String username;
    private Integer executor;
    private String db_name;
    private String charset;

    private String lxfkzmd;
    private String label_id;
    private String label_name;
    private String connect_user;
    private Integer connection_pattern;
    private String connection_pattern_zh;
    private List<Integer> instance_catalog_ids;
    private String instance_catalog;

    private ConnectionConfig connectionConfig;

    @ApiModelProperty(value = "实例是否可用", example = "true")
    private boolean activeInstance;

    @ApiModelProperty(value = "schema是否可用", example = "true")
    private boolean activeSchema = true;
}
