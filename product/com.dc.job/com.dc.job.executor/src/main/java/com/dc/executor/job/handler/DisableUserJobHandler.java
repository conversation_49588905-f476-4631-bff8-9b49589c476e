package com.dc.executor.job.handler;

import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.SystemParamConfigMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.model.SystemParamConfig;

import com.dc.repository.mysql.model.User;
import com.dc.springboot.core.component.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.dc.utils.DateUtil.*;

@Slf4j
@Component
public class DisableUserJobHandler extends AbstractJobHandler {

    // 定义常量，避免硬编码
    private static final String DISABLE_USER_URL_KEY = "long_non_login_disabled";
    private static final Integer DISABLED_VALUE = 0;

    @Resource
    protected SystemParamConfigMapper systemParamConfigMapper;
    @Resource
    protected UserMapper userMapper;
    @XxlJob("DisableUserJobHandler")
    public ReturnT<String> disableUserJobHandler(String param) {
        try {
            log.info("Start disable user!");
            SystemParamConfig systemParamConfig = systemParamConfigMapper.getSystemParamConfigByKey(DISABLE_USER_URL_KEY);
            //判断systemParamConfig的p_value字段 是否为json，是json的话解析json
            if (systemParamConfig != null && systemParamConfig.getP_value().startsWith("{")) {
                // 解析json
                String json = systemParamConfig.getP_value();
                // 获取对应的值
                try {
                    Map<String, Object> map = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
                    });
                    String pValueStr = map.get("value").toString();
                    int pValue = Integer.parseInt(pValueStr);
                    if (Objects.equals(DISABLED_VALUE, pValue)) {
                        DCJobLogger.log("禁用未登录用户阈值为0，不禁用用户");
                        return ReturnT.SUCCESS;
                    }
                    //获取不需要禁用用户
                    Object ignoreUsers = map.get("users");

                    List<String> ignoreList = null;
                    //Object转List
                    if (ignoreUsers instanceof List) {
                       ignoreList = (List<String>) ignoreUsers;

                    }
                    DCJobLogger.log("禁用未登录用户阈值为" + pValue);
                    String expireTime = getPreviousDate(pValue);
                    DCJobLogger.log("禁用未登录用户时间" + expireTime);
                    List<User> userList = userMapper.getAllDisableUserByExpireTime(expireTime);
                    if (userList != null && !userList.isEmpty()) {
                        for (User user : userList) {
                            if (ignoreList != null && !ignoreList.isEmpty()) {
                            //取userList和ignoreList取差集
                                if (ignoreList.contains(user.getUniqueKey())) {
//                                    userList.remove(user);
                                    continue;
                                }
                            }
                            DCJobLogger.log("待禁用用户：" + user.getUsername());
                            userMapper.disableUserById(user.getId());

                        }
                    }

                    DCJobLogger.log("禁用未登录用户结束");
                } catch (Exception e) {
                    DCJobLogger.log("解析配置失败");
                    log.error("call DisableUserJobHandler error!", e);
                }
            } else {
                DCJobLogger.log("未配置禁用未登录用户阈值，不禁用用户");
                return ReturnT.SUCCESS;
            }






        } catch (Exception e) {
            log.error("call DisableUserJobHandler error!", e);
            return ReturnT.FAIL;
        } finally {
            log.info("End disable user!");
        }
        return ReturnT.SUCCESS;
    }

    public static String getPreviousDate(int days) {
        if (days <= 0) {
            throw new IllegalArgumentException("Days must be a positive integer");
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime previousDate = now.minusDays(days);
        return previousDate.format(DateTimeFormatter.ofPattern(ymd_hms_str_1));
    }

}
