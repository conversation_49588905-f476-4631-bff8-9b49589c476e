package com.dc.executor.type;

/**
 * 不同JobHandler
 */
public enum JobHandlerType {

    PwdVerifyJobHandler("pwdVerify"),  //密码验证 暂时删除
    PwdUpdate<PERSON>ob<PERSON>and<PERSON>("pwdUpdate"),  //密码更新 暂时删除
    AlertJobHandler("alert"),  //告警 暂时删除
    Message("message"),
    Mail("mail"),
    SQLImport("sqlImport"),
    SQLExport("sqlExport"),
    Script("script"),
    SQLExecute("sqlExecute"),
    SQLExecuteSuspend("sqlExecuteSuspend"),
    SQLExecuteResume("sqlExecuteResume"),
    TagScan("tagScan"),
    RuleExpiredJobHandler("ruleExpired");   //规则过期提醒
    String value;

    JobHandlerType(String value) {
        // TODO Auto-generated constructor stub
        this.value = value;
    }


    public String getValue() {
        return value;
    }

    public static String getTypeByCode(String code) {
        for (JobHandlerType jh : JobHandlerType.values()) {
            if (jh.getValue().equals(code)) {
                return jh.toString();
            }
        }
        return "";
    }
}
