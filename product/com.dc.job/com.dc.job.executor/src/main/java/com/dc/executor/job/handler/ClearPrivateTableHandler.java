package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.springboot.core.component.JSON;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class ClearPrivateTableHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("ClearPrivateTableHandler")
    public ReturnT<String> clearPrivateTableHandler(String param) {
        try {
            log.info("Start Clear Private Table!");
            String uri = jobConfig.getPath().getDcBackend() +'/' + param;


            String backResult =  HttpClientUtils.doGet(uri, null);
            if (StringUtils.isBlank(backResult)) {
                log.info("call interface:" + uri + ", but get nothing!");
                DCJobLogger.log("call interface:" + uri + ", but get nothing!");
                return ReturnT.FAIL;
            }
            Map<String, Object> map = JSON.parseObject(backResult, Map.class);
            if (map != null) {
                if (!map.get("status").equals(0)) {
                    DCJobLogger.log("call interface:" + uri + ", but get error:" + map.get("msg"));
                    return ReturnT.FAIL;
                } else {
                    DCJobLogger.log("call interface:" + uri + ", success:" + map.get("msg"));
                    log.info("call interface:" + uri + ", success!");
                }
            }
        } catch (Exception e) {
            log.error("call ClearPrivateTableHandler error!", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
