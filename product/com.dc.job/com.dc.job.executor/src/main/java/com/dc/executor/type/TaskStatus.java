package com.dc.executor.type;

import lombok.Getter;

/**
 * 同步数据字典用到的枚举类
 */
@Getter
public enum TaskStatus {

    IN_PROGRESS("1", "进行中"),
    COMPLETED("2", "已结束"),
    ABNORMAL_EXIT("3", "异常退出");

    private final String code;
    private final String description;

    TaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static TaskStatus fromCode(String code) {
        for (TaskStatus status : TaskStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
