package com.dc.executor.type;


public enum ExportType {

    XML("stream.xml"),
    DBUNIT("stream.dbunit"),
    JSON("stream.json"),
    HTML("stream.html"),
    CSV("stream.csv"),
    MARKDOWN_TABLE("stream.markdown.table"),
    SQL("stream.sql"),
    TXT("stream.txt"),
    SOURCE_CODE("stream.source.code"),
    XLSX("stream.xlsx"),

    ;


    ExportType(String id) {
        this.id = id;
    }

    private final String id;

    private static final String NODE_ID = "stream_consumer";

    public String getProcessorFullId() {
        return NODE_ID + ":" + this.id;
    }

}
