package com.dc.executor.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class NoLockChangesModel {

    @SerializedName("log_id")
    private long logId;

    @SerializedName("job_id")
    private Integer jobId;

    @SerializedName("connect_id")
    private String connectId; // 实例 unique_key

    private Integer dbType; // 1:oracle;2:mysql

    private String schema;

    private String tableName;
    /**
     * 工具类型
     * PT-Oline 3.5.4
     */
    private String toolType;
    /**
     * 变更命令
     */
    private String changesCommand;
}
