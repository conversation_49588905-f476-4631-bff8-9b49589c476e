package com.dc.executor.model;

import com.dc.springboot.core.model.recovery.RecoveryDataSql;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CheckModel {

    @JsonProperty("rc_new_ID")
    private String rcNewId;

    @JsonProperty("rc_recoveryDataSql")
    private RecoveryDataSql rcRecoveryDataSql;

    @JsonProperty("rc_recoveryDataSql_other")
    private RecoveryDataSql rcRecoveryDataSqlOther;

    @JsonProperty("ID")
    private String id;

}
