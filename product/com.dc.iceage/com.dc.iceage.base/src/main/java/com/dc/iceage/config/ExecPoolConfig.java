package com.dc.iceage.config;

import com.dc.config.ConfigInstanceType;
import com.dc.summer.exec.pool.config.DruidConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "iceage.exec-pool", ignoreInvalidFields = true)
public class ExecPoolConfig extends DruidConfig {

    @PostConstruct
    public void init() {
        this.setProperties();
    }

    @Override
    public ConfigInstanceType getType() {
        return ConfigInstanceType.MULTIPLE;
    }
}
