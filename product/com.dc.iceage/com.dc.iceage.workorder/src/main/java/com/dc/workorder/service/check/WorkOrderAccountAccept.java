package com.dc.workorder.service.check;

import com.dc.repository.mysql.column.*;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSqlParse;
import com.dc.repository.mysql.model.WorkOrderScript;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ObjectTypeMessage;
import com.dc.springboot.core.model.database.ResourceObject;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.utils.CommonUtils;
import com.dc.workorder.utils.OrderSqlParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class WorkOrderAccountAccept extends WorkOrderCheckSqlPrivilege {

    public WorkOrderAccountAccept(OriginType originType, List<ParserExecuteType> parserExecuteTypes, ConnectionConfig connectionConfig) {
        super(originType, parserExecuteTypes, connectionConfig);
    }

    @Override
    protected void generateOrderSqlParse(List<ResourceContent> resourceContents, BaseInfo baseInfo, AuthGrant authGrant, AuthRevoke authRevoke, Integer orderId, String scriptName, Integer dbType) {

        String username = baseInfo.getUsername();
        String password = baseInfo.getPassword();

        StringBuilder createSql = new StringBuilder(String.format("CREATE USER %s IDENTIFIED BY \"%s\"", username, password));
        if (baseInfo.isExternally()) {
            createSql = new StringBuilder(String.format("CREATE USER %s IDENTIFIED EXTERNALLY", username));
        }

        String defaultTableSpace = baseInfo.getDefault_tablespace();
        if (StringUtils.isNotBlank(defaultTableSpace)) {
            createSql.append(String.format(" DEFAULT TABLESPACE %s", defaultTableSpace));
        }

        String temporaryTablespace = baseInfo.getTemporary_tablespace();
        if (StringUtils.isNotBlank(temporaryTablespace)) {
            createSql.append(String.format(" TEMPORARY TABLESPACE %s", temporaryTablespace));
        }

        if (StringUtils.isNotBlank(baseInfo.getProfile())) {
            if (Character.isDigit(baseInfo.getProfile().charAt(0))) {
                createSql.append(String.format(" PROFILE \"%s\"", baseInfo.getProfile()));
            } else {
                createSql.append(String.format(" PROFILE %s", baseInfo.getProfile()));
            }
        }

        if (baseInfo.isAccount_status()) {
            createSql.append(" ACCOUNT LOCK");
        }

        OrderSqlParse createOrderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, createSql.toString(), lineNumber++, null);
        orderSqlParses.add(createOrderSqlParse);

        for (ResourceContent resource : resourceContents) {
            //获取 字典类型的对象
            List<ResourceObject> objects = authGrant.getObject_auth().stream()
                    .map(o -> new ResourceObject(o.getSchema_name(), o.getObject_name())).distinct().collect(Collectors.toList());

            ObjectTypeMessage objectTypeMessage = new ObjectTypeMessage();
            objectTypeMessage.setObjects(objects);
            objectTypeMessage.setConnectionConfig(connectionConfig);
            List<ResourceObject> directoryObjects = databaseService.getDirectoryObject(objectTypeMessage);

            for (ObjectAuth objectAuth : authGrant.getObject_auth()) {
                //GRANT SELECT ON "schema"."table" TO "user" WITH GRANT OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(objectAuth.getPrivilege())
                        .append(" ON ");

                int cur = 0;
                while (cur < directoryObjects.size()) {
                    ResourceObject currentObj = directoryObjects.get(cur);
                    if (StringUtils.isNotBlank(currentObj.getOwner())) {
                        if (currentObj.getOwner().equals(objectAuth.getSchema_name()) && currentObj.getName().equals(objectAuth.getObject_name())) {
                            break;
                        }
                    } else {
                        if (currentObj.getName().equals(objectAuth.getObject_name())) {
                            break;
                        }
                    }
                    cur++;
                }

                if (cur < directoryObjects.size()) {
                    sqlBuilder.append("DIRECTORY ");
                }

                sqlBuilder.append(objectAuth.getSchemaObject())
                        .append(" TO ")
                        .append(username);

                if (objectAuth.isGrantable()) {
                    sqlBuilder.append(" WITH GRANT OPTION");
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sqlBuilder.toString(), lineNumber++, objectAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }
            StringBuilder roles = new StringBuilder();
            for (RoleAuth roleAuth : authGrant.getRole_auth()) {
                //GRANT "role" TO "user" WITH ADMIN OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(roleAuth.getGranted_role())
                        .append(" TO ")
                        .append(username);
                if (roleAuth.isAdminOption()) {
                    sqlBuilder.append(" WITH ADMIN OPTION");
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sqlBuilder.toString(), lineNumber++, roleAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);

                if (roleAuth.getDefault_role() == 1) {
                    roles.append(roleAuth.getGranted_role()).append(",");
                }
            }
            if (roles.length() > 0) {
                roles.deleteCharAt(roles.length() - 1);

                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, "ALTER USER " + username + " DEFAULT ROLE " + roles, lineNumber++, null);
                orderSqlParses.add(orderSqlParse);
            }

            for (SysAuth sysAuth : authGrant.getSys_auth()) {
                String sql = String.format("GRANT %s TO %s", sysAuth.getPrivilege(), username);
                if (sysAuth.getAdmin_option() == 1) {
                    sql += " WITH ADMIN OPTION";
                }
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, sysAuth.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }

            for (TsQuota tsQuota : authGrant.getTs_quotas()) {
                String sql = String.format("ALTER USER %s QUOTA %s ON %s", username, tsQuota.getQuotaSize(), tsQuota.getTablespace_name());
                OrderSqlParse orderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, sql, lineNumber++, tsQuota.getPrivilege_expire());
                orderSqlParses.add(orderSqlParse);
            }

        }

    }
}
