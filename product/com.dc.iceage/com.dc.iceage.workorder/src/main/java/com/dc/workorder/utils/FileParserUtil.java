package com.dc.workorder.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.tika.detect.AutoDetectReader;
import org.apache.tika.exception.TikaException;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.Charset;

@Slf4j
public class FileParserUtil {

    public static String getFileCharset(String url) {
        InputStream is = null;
        try {
            URL httpUrl = new URL(url);
            is = httpUrl.openStream();
            try (AutoDetectReader autoDetectReader = new AutoDetectReader(is)) {
                Charset charset = autoDetectReader.getCharset();
                return charset.name();
            }
        } catch (IOException | TikaException e) {
            log.error("解析文件字符集失败 ：" + e.getMessage());
            return "utf-8";
        } finally {
            if (is != null) {
                try {
                    is.reset();
                } catch (IOException e) {
                    log.error("文件流重置游标失败： " + e.getMessage());
                } finally {
                    try {
                        is.close();
                    } catch (IOException e) {
                        log.error(e.getMessage());
                    }
                }
            }
        }
    }
}
