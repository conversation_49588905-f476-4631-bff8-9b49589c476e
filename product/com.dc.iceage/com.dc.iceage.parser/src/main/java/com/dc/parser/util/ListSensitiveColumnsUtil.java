package com.dc.parser.util;

import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.parser.utils.model.SqlParseResult;
import com.dc.parser.sensitive.model.ColumnToken;
import com.dc.parser.sensitive.model.MetaDataNode;
import com.dc.parser.sensitive.model.QueryMaskModel;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.summer.parser.utils.SqlParserUtil;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.ESqlClause;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.TInsertSqlStatement;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class ListSensitiveColumnsUtil {

    private final Logger logger = LoggerFactory.getLogger(ListSensitiveColumnsUtil.class);

    private QueryMaskModel sqlParserModel;

    private String TOKEN_IN_CHAIN = ".";
    private final String KEY_SELECT = "SELECT";

    private Set<Integer> columnTokenIdentCodeLists = new HashSet<>();
    private List<ColumnToken> columnTokenList = new ArrayList<>();

    public ListSensitiveColumnsUtil(QueryMaskModel sqlParserModel) {
        this.sqlParserModel = sqlParserModel;
    }

    public List<ColumnToken> doHandle() {

        logger.info("-------------------go-------------------");

        try {
            if (!KEY_SELECT.equals(sqlParserModel.getOperation())) {
                logger.info("skip : operation =>{}  ", sqlParserModel.getOperation());
                return null;
            }

            //
            DCustomSqlStatement tCustomSqlStatement = sqlParserModel.gettCustomSqlStatement();

            if (tCustomSqlStatement != null && tCustomSqlStatement.getResultColumnList() != null) {
                //提取
                this.collectStatement(tCustomSqlStatement);
            }


            // insert into select ....
            // INSERT ALL INTO ...
            // INSERT first ...
            if (tCustomSqlStatement instanceof TInsertSqlStatement) {

                TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) tCustomSqlStatement;
                TSelectSqlStatement subQuery = null;
                if (null != tInsertSqlStatement) {
                    subQuery = tInsertSqlStatement.getSubQuery();
                }
                if (tInsertSqlStatement.getSubQuery() != null) {
                    //深度替换
                    this.collectStatement(subQuery);
                    //
                    tInsertSqlStatement.setSubQuery(subQuery);
                }
            }

            logger.info("done。");

        } catch (Exception e) {
            logger.error("doHandle in ListSensitiveColumnsUtil error!", e);
        } finally {
            logger.info("\r\n");
        }
        return this.columnTokenList;
    }

    private void collectStatement(DCustomSqlStatement tCustomSqlStatement) {

        if (null != tCustomSqlStatement) {

            //1-1 : select ... from tab;
            TResultColumnList tResultColumns = tCustomSqlStatement.getResultColumnList();
            this.collectResultColumnList(tResultColumns);

            //2-1 : select name from tab,tab1;  引导 name同时属于 tab ,tab2:
            TTableList tables = tCustomSqlStatement.getTables();
            this.collectTablesColumnList(tables);

            //3-1  subQuery: select ... from ( select ... from ...) b
            TJoinList joins = tCustomSqlStatement.getJoins();
            this.collectJoinsColumnList(joins);

        }
    }

    private void collectTablesColumnList(TTableList tables) {

        //识别NAME 》 select NAME from tab,tab1
        if (null != tables && tables.size() > 0) {

            for (TTable table : tables) {

                if (null != table) {

                    /*TAliasClause aliasClause = table.getAliasClause();*/
                    TObjectName tableName = table.getTableName();
                    if (null != tableName) {

                        ColumnToken tableToken = new DBOSchemaUtil(this.sqlParserModel.getDbType(), this.sqlParserModel.getCurr3FSchemaName(), tableName).parser();//  this._3fSchemaSource(tableName);
                        String extractedTable = tableToken.getExtractedTable();
                        if (DatabaseType.get3FDatabaseIntegerValueList().contains(this.sqlParserModel.getDbType())) {
                            extractedTable = String.format("%s%s%s", tableToken.getExtractedSchema(), this.TOKEN_IN_CHAIN, tableToken.getExtractedTable());
                        }


                        //----------- step 1--------------

                        TObjectNameList tObjectNameList = null;
                        if (null != table.getObjectNameReferences()) {
                            tObjectNameList = table.getObjectNameReferences();
                        }

                        if (null != tObjectNameList && tObjectNameList.size() > 0) {
                            for (TObjectName tObj : tObjectNameList) {

                                //fix 17171 ： use ESqlClause.selectList 是只允许脱查询列， 避免条件被脱敏掉
                                if (null != tObj && ESqlClause.selectList.equals(tObj.getLocation()) && null != tableToken && null != tableName && null != tableName.getObjectToken() && null != tObj.getDbObjectType() && "column".equals(tObj.getDbObjectType().name())) {
                                    //fix "select a.password , name from (select *  from tab ) a";   tObj.getColumnNameOnly() => password;
                                    String extractedColumn = tObj.getColumnNameOnly();
                                    logger.info("tabs link :column=>{}, schema=>{}, tab=> {}, expr=>{}", extractedColumn, tableToken.getExtractedSchema(), extractedTable, tObj.toString());
                                    //fix : select * from (select password from (select * from (select *  from tab ) a) b) c
                                    this.collectColumnToken(new ColumnToken(extractedColumn, extractedTable, tableToken.getExtractedSchema()));
                                }
                            }
                        }


                        //----------- step 2--------------

                        TObjectNameList linkedColumns = null;
                        if (null != table.getLinkedColumns()) {
                            linkedColumns = table.getLinkedColumns();
                        }
                        if (null != linkedColumns && linkedColumns.size() > 0) {
                            for (TObjectName tObj : linkedColumns) {

                                //fix 17171 ： use ESqlClause.selectList 是只允许脱查询列， 避免条件被脱敏掉
                                if (null != tObj && ESqlClause.selectList.equals(tObj.getLocation()) && null != tableToken && null != tableName && null != tableName.getObjectToken()) {
                                    //fix "select a.password , name from (select *  from tab ) a";   tObj.getColumnNameOnly() => password;
                                    String extractedColumn = tObj.getColumnNameOnly();
                                    logger.info("tabs link :column=>{}, schema=>{}, tab=> {}, expr=>{}", extractedColumn, tableToken.getExtractedSchema(), extractedTable, tObj.toString());
                                    //fix : select * from (select password from (select * from (select *  from tab ) a) b) c
                                    this.collectColumnToken(new ColumnToken(extractedColumn, extractedTable, tableToken.getExtractedSchema()));
                                }
                            }
                        }

                    }


                }
            }
        }


    }

    private void collectJoinsColumnList(TJoinList joins) {

        if (null != joins && joins.size() > 0) {

            for (TJoin join : joins) {

                if (null != join) {

                    TTable table = join.getTable();

                    //----------- step 1--------------
                    if (null != table && null != table.getSubquery()) {
                        TSelectSqlStatement tSelectSqlStatement = table.getSubquery();
                        this.collectStatement(tSelectSqlStatement);
                    }

                }
            }
        }

    }

    private void collectResultColumnList(TResultColumnList tResultColumnList) {

        if (null != tResultColumnList) {

            if (tResultColumnList != null && tResultColumnList.size() > 0) {

                for (TResultColumn tResultColumn : tResultColumnList) {

                    if (null == tResultColumn || null == tResultColumn.getExpr() || null == tResultColumn.getExpr().getExpressionType()) {
                        continue;
                    }

                    //
                    TExpression tExpr = tResultColumn.getExpr();

                    if (null != tExpr) {
                        this.collectExpr(new MetaDataNode(tExpr));
                    }


                }
            }
        }

    }


    private void collectColumnToken(ColumnToken token) {
        Integer code = token.getToken2();
        if (null != token && !columnTokenIdentCodeLists.contains(code)) {
            columnTokenIdentCodeLists.add(code);
            this.columnTokenList.add(token);
        }
    }


    private void collectExpr(MetaDataNode node) {

        //20220715  v1.0  语法之间存在各种嵌套由此采用EExpressionType类型识别局限性太小

        TExpression tExpr = (TExpression) node.getTreeNode();
        //TAliasClause aliasClause = null;
        if (null != tExpr) {

            String extractedAlias = null;
            TObjectName tableName = null;
            TTable sourceTable = null;
            String extractedSchema = this.sqlParserModel.getCurr3FSchemaName();
            String extractedColumn = null;
            //
            String extractedScript = tExpr.toString();

            //
            TObjectName objectOperand = tExpr.getObjectOperand();
            if (null != objectOperand && null != objectOperand.getSourceTable()) {
                tableName = objectOperand.getSourceTable().getTableName();
            }

            if (null != objectOperand && null != objectOperand.getDatabaseToken()) {
                extractedSchema = CommonUtil.replace(objectOperand.getDatabaseToken().toString());
            }

            ColumnToken tableToken = new DBOSchemaUtil(this.sqlParserModel.getDbType(), this.sqlParserModel.getCurr3FSchemaName(), tableName).parser();
            String extractedTable = tableToken.getExtractedTable();
            if (DatabaseType.get3FDatabaseIntegerValueList().contains(this.sqlParserModel.getDbType())) {
                extractedTable = String.format("%s%s%s", tableToken.getExtractedSchema(), this.TOKEN_IN_CHAIN, tableToken.getExtractedTable());
            }

            if (null != objectOperand && null != objectOperand.getSourceTable()) {
                sourceTable = objectOperand.getSourceTable();
                extractedColumn = objectOperand.getColumnNameOnly();
            }


            //simple column
            if (tExpr.getExpressionType().equals(EExpressionType.simple_object_name_t)) {
                logger.info("simple:column=>{}, schema=>{}, tab=> {}, expr=>{}", extractedColumn, extractedSchema, extractedTable, extractedScript);
                //fix : select * from (select password from (select * from (select *  from tab ) a) b) c
                this.collectColumnToken(new ColumnToken(extractedColumn, extractedTable, extractedSchema));
            }

            //simple constant
            if (tExpr.getExpressionType().equals(EExpressionType.simple_constant_t)) {
                //System.out.println(2);
            }

            //subQuery  =>select ({subQuery}) from ...
            /*if (tExpr.getExpressionType().equals(EExpressionType.subquery_t)) {*/
            if (null != tExpr.getSubQuery() && null != tExpr.getSubQuery().getResultColumnList()) {
                logger.info("sub query 1:column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectStatement(tExpr.getSubQuery());
            }

            //subQuery  => select ... from ({subQuery}) a
            if (null != sourceTable && null != sourceTable.getSubquery()) {
                logger.info("sub query 2:column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectStatement(sourceTable.getSubquery());
            }


            // Sample 》 oracle : select col1 || col2 || ...
            // Sample 》 select ({sql}) from ....
            /*if (Arrays.asList(EExpressionType.concatenate_t, EExpressionType.arithmetic_minus_t, EExpressionType.parenthesis_t, EExpressionType.arithmetic_plus_t).contains(tExpr.getExpressionType())) {*/
            if (null != tExpr.getLeftOperand() || null != tExpr.getRightOperand()) {
                logger.info("select (sql):column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectParenthesis(tExpr.getLeftOperand(), tExpr.getRightOperand());

            }


            //cass when
            /*if (tExpr.getExpressionType().equals(EExpressionType.case_t)) {*/
            if (null != tExpr.getCaseExpression()) {
                logger.info("case when :column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectCaseWhenItem(tExpr.getCaseExpression());
            }

            //function column
            /*if (tExpr.getExpressionType().equals(EExpressionType.function_t)) {*/
            if (null != tExpr.getFunctionCall()) {
                logger.info("fun :column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectFunCall(tExpr.getFunctionCall());
            }

        }
    }

    private void collectParenthesis(TExpression... list) {

        if (null != list) {

            for (TExpression tExpr : list) {

                if (null != tExpr) {
                    this.collectExpr(new MetaDataNode(tExpr));
                }
            }
        }
    }

    private void collectCaseWhenItem(TCaseExpression caseExpression) {

        //20220715  v1.0  语法之间存在各种嵌套由此采用EExpressionType类型识别局限性太小

        if (null != caseExpression && null != caseExpression.getWhenClauseItemList()) {

            TWhenClauseItemList whenClauseItemList = caseExpression.getWhenClauseItemList();

            for (TWhenClauseItem item : whenClauseItemList) {

                if (null == item && null != item.getReturn_expr() && null != item.getReturn_expr().getExpressionType()) {
                    continue;
                }

                //v2.0
                if (null != item && null != item.getReturn_expr()) {
                    this.collectExpr(new MetaDataNode(item.getReturn_expr()));
                }

            }
        }
    }

    private void collectFunCall(TFunctionCall functionCall) {

        //20220715  v1.0  语法之间存在各种嵌套由此采用EExpressionType类型识别局限性太小

        if (null != functionCall && null != functionCall.getArgs()) {

            TExpressionList tExprArgsList = functionCall.getArgs();

            for (TExpression tExpr : tExprArgsList) {

                //v2.0
                if (null != tExpr) {
                    this.collectExpr(new MetaDataNode(tExpr));
                }
            }
        }

        //fix : select substring(name from 1 for 3) from student;
        if (null != functionCall && (null != functionCall.getExpr1() || null != functionCall.getExpr2() || null != functionCall.getExpr3())) {
            this.collectParenthesis(functionCall.getExpr1(), functionCall.getExpr2(), functionCall.getExpr3());
        }


        if (null != functionCall && null != functionCall.getGroupConcatParam() && null != functionCall.getGroupConcatParam().getExprList()) {

            //fix 》 SELECT GROUP_CONCAT(pwd,id)
            TExpressionList exprList = functionCall.getGroupConcatParam().getExprList();
            if (null != exprList && exprList.size() > 0) {

                for (TExpression tExpr : exprList) {

                    //v2.0
                    if (null != tExpr) {
                        this.collectExpr(new MetaDataNode(tExpr));
                    }
                }
            }
        }

    }


    public static void main(String[] args) {

        List<String> mySqlLists = Arrays.asList(

//                /*"select * from tab ",*/
//                /*"select '*' from tab ",*/
//
//                "select  password from `tab` ",
//                "select  `password` from `tab` ",
//                "select  `password` from tab ",
//
//                /* alias */
//                "select  password from `tab` ",
//                "select  password  as pwd from `tab` ",
//                "select  a.`password`  as pwd from `tab` a ",
//                "select  b.password  as pwd from `tab1`a , tab b ",
//                "select  a.password  as pwd, a.name, b.password from `tab` a , tab2 b ",
//                /*"select  password  as pwd from `tab1` a , tab2 b ",*/
//                /*"select  `password`  as pwd from `tab1` a , tab2 b ",*/
//                /*"select '123456' as password from `tab` ",*/
//
//                /* function */
//                /*"SELECT date_format(password, '%Y-%m-%d')",*/
//                "select sum(password) from tab",
//                /*"select 1 + 1 from tab",*/
//                "select password +1 from tab",
//                "select (password + 1 )  from tab",
//                "select (1 + password) + password  from tab",
//                "select 1 + password +1 + password  from tab",
//                "select count(password) from tab",
//                "select count(password) as pwd from tab",
//                "select concat(password,'001') as pwd from tab",
//                "select concat(password,password2) as pwd from tab",
//                "select concat(a.password1,a.password) as pwd from tab a",
//                "select avg(IFNULL(id, password)) from tab",
//                "select AVG(ISNULL(DATEDIFF(SECOND, call.password, call.end_time),0)) from tab",
//                "select position('1' in password) from  `tab`",
//                /* subQuery*/
//                /*"select (0) as pwd from tab",*/
//                /*"select id, (0) as temp from tab",*/
//                "select id, (select max(password) from tab) as password from tab2",
//                "select (select `password` from `tab` LIMIT 1)",
//
//                /* case when */
//                "select CASE WHEN `id` IS NOT NULL THEN  `password` ELSE NULL END from tab",
//                /*"select CASE WHEN `id` IS NOT NULL THEN  `password1` ELSE `password2` END from tab",*/
//                /*"select CASE WHEN `id` IS NOT NULL THEN  `password1` ELSE '是' END from tab",*/
//                /*"select CASE WHEN `id` IS NOT NULL THEN  (select password1 from tab2 limit 1) ELSE (select password2 from tab3 limit 1) END from tab",*/
//                "select CASE WHEN `id` IS NOT NULL THEN  (select password from tab limit 1) ELSE ('001') END from tab",
//                /*"select CASE WHEN `id` IS NOT NULL THEN  (select password2 from tab2 limit 1) ELSE (select max(password3) from tab3 limit 1) END from tab",*/
//                /*"select (CASE WHEN `id` IS NOT NULL THEN  `password1` ELSE `password2` END) as tmp from tab",*/
//
//                "select a.password , name from (select *  from tab ) a",
//                "select password , name from (select *  from tab ) a",
                "select * from (select password as pwd from (select a.password , name from (select *  from tab ) a) b) c",
//                "select CONCAT(password, id) from tab",
//                "select c from (select CONCAT(password, id) as c from tab) a",
//
//
//                "select substring(password from 1 for 2) from tab",
//                "select substring(password,1,2) from tab",
//
//                "select adddate(password,interval 5 day) from tab",
//                "select subdate(password,2) from tab",
//                "select subdate(password,interval 5 minute) from tab",
//                "select adddate(password,2) from tab",
//                "select time_FORMAT(password,'%h-%m-%s %r') from tab",
//                "SELECT * FROM tab WHERE password < ANY(SELECT DISTINCT password FROM tab WHERE department = '管理系')AND department != '管理系'",
//                "SELECT *  FROM tab WHERE password < (SELECT DISTINCT password FROM tab WHERE department = '管理系')AND department != '管理系'",

//                "select password from tab where name > any(select password from tab) ",
//                "select password from tab where name > all(select password from tab) ",
                "select 1"
        );

        List<String> oracleLists = Arrays.asList(

                "insert first when id >= 2 then into test3(id, name) when id >= 3 then into test4(id, name) select t.id, t.password from tab t",
                "INSERT ALL INTO t1(object_id, object_name) INTO t2(object_id, object_name) SELECT password, name FROM tab",
                "select (password || age || name) from tab ",
                "select max(password) from tab ",
                "select password || age || name from tab ",
                "select userid,phone,username, concat(password, ' ') || concat(password, ' ') from SCOTT.tab; ",
                "select \"password\" - 9 from tab ",

                "select 1"
        );


        List<String> mssqlLists = Arrays.asList(
//                "SELECT password FROM [tab]",
//                "SELECT password FROM [dbo].[tab]",
//                "SELECT password FROM [lwc_test].[dbo].[tab]",
                "SELECT [lwc_test].[dbo].[tab].password FROM [lwc_test].[dbo].[tab]",
//                "SELECT  [test1].[dbo].[tab].password FROM [test1].[dbo].[tab];",
//                "SELECT datalength(password) FROM [lwc_test].[dbo].[tab]",
//                "SELECT substring(password, 1,2) FROM [lwc_test].[dbo].[tab]",
//                "SELECT right(password, 1) FROM [lwc_test].[dbo].[tab]",
//                "SELECT upper(password) FROM [lwc_test].[dbo].[tab]",
//                "select  upper(password)  from  [lwc_test].[dbo].[tab]",

                "select 1"
        );

        List<String> db2Lists = Arrays.asList(
//                "SELECT password as a FROM tab",
                "SELECT name as a,CAST(password AS int) FROM tab",
                "SELECT name as a,CAST(password AS varchar(10)) FROM tab",
                "SELECT CAST(password AS varchar) FROM TEST2.\"tab\";",
//                "select ASCII(password) from TEST2.\"tab\"",
//                "select SPACE(10) from TEST2.\"tab\"",
//                "select ASCII(password) from tab",
//                "select SPACE(10) from TEST2.tab",

                "select 1"
        );


        List<String> tdPGLists = Arrays.asList(
//                "SELECT (id, name) FROM \"QYY\".\"dbo\".\"tab\"",
                "SELECT d.* FROM \"QYY\".\"dbo\".\"tab\" d;\n",
                "select 1"
        );


//        Integer dbType = DatabaseType.MYSQL.getValue();
//        Integer dbType = DatabaseType.ORACLE.getValue();
        Integer dbType = DatabaseType.SQL_SERVER.getValue();
//        Integer dbType = DatabaseType.DB2.getValue();
//        Integer dbType = DatabaseType.TDPG.getValue();

        Map<Integer, List<String>> data = new HashMap<>();
        data.put(DatabaseType.MYSQL.getValue(), mySqlLists);
        data.put(DatabaseType.ORACLE.getValue(), oracleLists);
        data.put(DatabaseType.SQL_SERVER.getValue(), mssqlLists);
        data.put(DatabaseType.DB2.getValue(), db2Lists);
        data.put(DatabaseType.TDPG.getValue(), tdPGLists);

        List<String> sqls = data.get(dbType);


        for (String sql : sqls) {
            ParserParamDto paramDTO = new ParserParamDto();
            paramDTO.setSql(sql);
            paramDTO.setEnableDesensiteType(1);   //是否开启强制全托.
            paramDTO.setDbType(dbType);

            QueryMaskModel queryMaskModel = new QueryMaskModel();
            List<SqlParseResult> parser = SqlParserUtil.parser(paramDTO.getSql(), paramDTO.getDbType());
            queryMaskModel.setOperation("SELECT");
            queryMaskModel.settCustomSqlStatement(parser.get(0).gettCustomSqlStatement());
            queryMaskModel.setCurr3FSchemaName("dbo");
            queryMaskModel.setDbType(dbType);


            List<ColumnToken> list = new ListSensitiveColumnsUtil(queryMaskModel).doHandle();
            System.out.println(list);

        }

    }

}
