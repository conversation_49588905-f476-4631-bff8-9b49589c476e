package com.dc.parser.model.chain.impl;

import com.dc.parser.component.ParserMapper;
import com.dc.parser.constants.AuthConstant;
import com.dc.parser.constants.DCConstants;
import com.dc.parser.constants.OwnerConstant;
import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.service.UserRuleService;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.parser.util.ParserConstant;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.DBPrivateTable;
import com.dc.repository.mysql.model.InstanceRole;
import com.dc.repository.mysql.model.PermissionRule;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.AuthTraceModel;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.InstanceRoleDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.summer.parser.sql.constants.GrantConstant;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.RedisSqlConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.parser.model.AuthRuleModel;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.sql.type.GrantType;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.parser.service.SqlRuleService;
import com.dc.parser.type.DataNodeType;
import com.dc.type.DatabaseType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.parser.util.ExecuteSqlUtil;
import com.dc.parser.util.GetAuthUtil;
import com.dc.summer.parser.utils.SqlPreparedUtil;
import com.dc.parser.type.AuthLevelType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class SqlAuthVerifySecurityChain extends SqlCheckParserChain {

    private final GroupUserMapper groupUserMapper = Resource.getBeanRequireNonNull(GroupUserMapper.class);
    private final UserRuleService userRuleService = Resource.getBeanRequireNonNull(UserRuleService.class);
    private final SqlRuleService sqlRuleService = Resource.getBeanRequireNonNull(SqlRuleService.class);
    private final SchemaMapper schemaMapper = Resource.getBeanRequireNonNull(SchemaMapper.class);
    private final SecurityRuleDetailsMapper securityRuleDetailsMapper = Resource.getBeanRequireNonNull(SecurityRuleDetailsMapper.class);
    private final ParserMapper parserMapper = ParserMapper.INSTANCE;

    public SqlAuthVerifySecurityChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        try {

            // ===========================白名单操作======================
            if (SqlPreparedUtil.isPassAuth(sqlParseModel, parserParamDto.getDbType())) {
                webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                // 不拦截解析出错的白名单sql
                parserParamDto.setInstanceOwner(true);
                return true;
            }

            // ===========================获取用户权限======================
            this.getAuth(parserParamDto, sqlParseModel);
            Map<String, List<AuthRuleModel>> authRuleKeys = new HashMap<>(); // 权限追溯记录

            // ===========================特殊要求的权限======================
            String specialPermissionMessage = getSpecialPermissionMessage(sqlParseModel);
            if (!specialPermissionMessage.isEmpty() && sqlParseModel.getErrorMessage() == null) {
                if (isInstanceOwner(webSQLParserResult, authRuleKeys)) {
                    return true;
                }

                webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
                // 快捷申请
                webSQLParserResult.setNoPermissionObjects(CommonUtil.buildCannotGrantedMap());
                webSQLParserResult.setMessage(specialPermissionMessage);
                return false;
            }

            // ===========================解析出错的SQL======================
            String parseFailSqlMessage = getParseFailSqlMessage(sqlParseModel);
            if (parseFailSqlMessage != null && isInstanceOwner(webSQLParserResult, authRuleKeys)) {
                return true;
            }

            // 如果解析器有错误信息，但不是大权限用户，则拦截。
            if (parseFailSqlMessage != null && !parserParamDto.isInstanceOwner()) {
                //没有解析出来权限模型，有些语句本来就是不应该有权限模型的，因此是无意义的，如: VALUES current date 、VALUES SQRT(25)
                if (sqlParseModel.getSqlAuthModelList().isEmpty() && isMeaninglessForNoSqlAuthModels(sqlParseModel)) {
                    webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                    return true;
                }
                webSQLParserResult.setStatus(SqlExecuteStatus.EXEC_ERROR.getValue());
                log.debug(ParserConstant.NOT_SUPPORT_MESSAGE + ": {}", parseFailSqlMessage);
                webSQLParserResult.setMessage(ParserConstant.NOT_SUPPORT_MESSAGE);
                return false;
            }

            // ============================验证权限===========================
            List<SqlAuthModel> sqlAuthModels = buildNoAuthModels(parserParamDto, authRuleKeys, sqlParseModel);
            //私有表权限，打标记，为后续summer做准备。这里借助了parserParamDto做中转。
            if (parserParamDto.isNeedRecordInPrivateTable()) {
                webSQLParserResult.setNeedRecordInPrivateTable(true);
            }

            // ============================权限追踪===========================
            List<AuthTraceModel> authTraceModels = sqlRuleService.authRule(authRuleKeys, parserParamDto);
            webSQLParserResult.setRules(authTraceModels);

            // ============================表组权限===========================
            this.tableGroupAuth(parserParamDto, sqlParseModel, sqlAuthModels, authTraceModels);

            // ============================查询总行数权限、导出权限===========================
            webSQLParserResult.setCanCountTotal(SqlPreparedUtil.canCountTotal(sqlAuthModels, sqlParseModel));
            webSQLParserResult.setCanExport(SqlPreparedUtil.canExport(sqlAuthModels, parserParamDto.getDbType()));

            // ============================只有操作和函数权限需要返回快捷申请等信息===========================
            sqlAuthModels = SqlPreparedUtil.getNeedSqlAuthModels(sqlAuthModels, sqlParseModel.getAction().getFunctions());

            if (!sqlAuthModels.isEmpty()) {
                webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
                // 权限便携申请
                webSQLParserResult.setNoPermissionObjects(buildNoAuthMapList(sqlAuthModels, parserParamDto, sqlParseModel));
                // 返回无权限信息
                String noPermissionMessage = SqlPreparedUtil.getNoPermissionMessage(sqlAuthModels, webSQLParserInfo.getPermissionsDictionary(), sqlParseModel, parserParamDto.getSchemaName(), parserParamDto.getDbType());
                webSQLParserResult.setMessage(DatabaseType.ADBMYSQL2.getValue().equals(parserParamDto.getDbType()) ? noPermissionMessage.replaceAll(DCConstants.DC_GROUP + ".", "") : noPermissionMessage);
                return false;
            } else {
                webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
            }


        } catch (Exception e) {
            log.error("权限校验，解析异常：", e);
            webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
            webSQLParserResult.setMessage("权限校验-解析异常! " + e.getMessage());
            return false;
        }

        return true;
    }

    private boolean isInstanceOwner(WebSQLParserResult webSQLParserResult, Map<String, List<AuthRuleModel>> authRuleKeys) {
        for (Map.Entry<String, List<String>> entry : parserParamDto.getRoleAuth().entrySet()) {
            if (entry.getValue().contains(parserParamDto.getConnectId())) {
                InstanceRoleDto instanceRoleDto = parserParamDto.getInstanceRoleMap().get(entry.getKey());
                if (Integer.valueOf(1).equals(instanceRoleDto.getOperate_range()) && Integer.valueOf(2).equals(instanceRoleDto.getOperate_rule())) {
                    webSQLParserResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                    webSQLParserResult.setCanExport(true);
                    webSQLParserResult.setCanCountTotal(true);
                    // 不拦截解析出错的大权限角色的sql
                    parserParamDto.setInstanceOwner(true);
                    GetAuthUtil.buildAuthRule(authRuleKeys, entry.getKey(), parserParamDto.getConnectId(), instanceRoleDto.getRole_name(), "Instance");
                    // 权限追踪
                    webSQLParserResult.setRules(sqlRuleService.authRule(authRuleKeys, parserParamDto));
                    return true;
                }
            }
        }
        return false;
    }

    public void getAuth(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {
        List<String> actionKeys = getOperationAuthActionKey(paramDTO.getPreOperation(), sqlParserModel, false);

        List<InstanceRole> instanceRoleList = getInstanceRole(actionKeys);

        Map<String, Object> map = new HashMap<>();
        map.put("now", System.currentTimeMillis() / 1000);
        map.put("user_id", paramDTO.getUserId());
        map.put("connect_id", paramDTO.getConnectId());

        List<String> schemaIds = new ArrayList<>();
        List<String> objectNames = new ArrayList<>();
        Set<String> functions = sqlParserModel.getAction().getFunctions();

        List<String> allPrivateSchemaIds = null;
        if (paramDTO.getDbType().equals(DatabaseType.ORACLE.getValue())) { //目前仅支持oracle实例
            allPrivateSchemaIds = schemaMapper.getAllPrivateSchemaId(paramDTO.getConnectId());
        }

        for (SqlAuthModel sqlAuthModel : sqlParserModel.getSqlAuthModelList()) {
            String schemaUniqueKey = sqlAuthModel.getSchemaUniqueKey();
            if (allPrivateSchemaIds != null && allPrivateSchemaIds.contains(schemaUniqueKey)) {
                paramDTO.getCurIsPrivateSchemaIdName().put(sqlAuthModel.getSchemaUniqueKey(), sqlAuthModel.getSchemaName());
            }
            schemaIds.add(schemaUniqueKey);
            String fullName = sqlAuthModel.getFullName() != null ? sqlAuthModel.getFullName().toUpperCase(Locale.ROOT) : sqlAuthModel.getFullName();
            objectNames.add(fullName);
            List<String> oidName = getOIDName(paramDTO, sqlParserModel, sqlAuthModel.getOperation(), sqlAuthModel.getFullName(), sqlAuthModel.getSchemaName());
            if (oidName.size() != 0) {
                objectNames.addAll(oidName);
            }

            if (!CollectionUtils.isEmpty(functions)) {
                objectNames.add(sqlAuthModel.getSchemaName());
            }
        }

        //如果是私有schema，那么需要添加一个额外的实例角色。
        if (!CollectionUtils.isEmpty(paramDTO.getCurIsPrivateSchemaIdName()) &&
                Arrays.stream(securityRuleDetailsMapper.getSecurityRuleValue("private_schema", paramDTO.getConnectId()).split(",")).collect(Collectors.toSet())
                        .containsAll(paramDTO.getCurIsPrivateSchemaIdName().values())) {
            instanceRoleList.add(Resource.getBeanRequireNonNull(GroupUserMapper.class).getPrivateTableInstanceRole());
        }

        actionKeys.addAll(instanceRoleList.stream().map(InstanceRole::getUnique_key).collect(Collectors.toList()));
        map.put("action_keys", actionKeys);
        map.put("schema_ids", schemaIds);
        map.put("resource_types", DataNodeType.getIdentCode(DataNodeType.getResourceTypes()));
        map.put("object_names", objectNames);

        List<String> organizations = webSQLParserInfo.getOrganizations();
        map.put("organizations", organizations);

        List<PermissionRule> userAuths = userRuleService.getUserAuth(map);
        paramDTO.setPeUserAuths(parserMapper.toPermissionRuleDtoList(userAuths));

        Map<String, InstanceRole> instanceRoleMap = instanceRoleList.stream()
                .collect(Collectors.toMap(InstanceRole::getUnique_key, instanceRole -> instanceRole));
        Map<String, List<String>> roleAuth = GetAuthUtil.buildRoleAuth(paramDTO.getPeUserAuths());
        Map<String, List<String>> operationAuth = GetAuthUtil.buildOperationAuth(paramDTO.getPeUserAuths());
        Map<String, List<String>> maskAuth = GetAuthUtil.buildMaskAuth(paramDTO.getPeUserAuths());

        paramDTO.setInstanceRoleMap(parserMapper.toInstanceRoleDtoMap(instanceRoleMap));
        paramDTO.setRoleAuth(roleAuth);
        paramDTO.setOperationAuth(operationAuth);
        paramDTO.setMaskAuth(maskAuth);
    }

    private List<PermissionRule> getTableGroupAuth(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {
        List<String> actionKeys = getOperationAuthActionKey(paramDTO.getPreOperation(), sqlParserModel, true);

        Map<String, Object> map = new HashMap<>();
        map.put("now", System.currentTimeMillis() / 1000);
        map.put("user_id", paramDTO.getUserId());
        map.put("connect_id", paramDTO.getConnectId());
        map.put("action_keys", actionKeys);

        List<String> organizations = webSQLParserInfo.getOrganizations();
        map.put("organizations", organizations);

        return groupUserMapper.getUserTableGroupAuth(map);
    }

    private String getParseFailSqlMessage(SqlParseModel sqlParseModel) {
        if (StringUtils.isBlank(sqlParseModel.getOperation()) || sqlParseModel.getErrorMessage() != null) {
            return sqlParseModel.getErrorMessage();
        } else if (DatabaseType.getIdentCode(DatabaseType.useDcSqlParser()).contains(parserParamDto.getDbType()) &&
                sqlParseModel.getCustomSqlStatement() != null &&
                !sqlParseModel.getCustomSqlStatement().isSupport()) {
            return "不支持解析的 SQL。";
        } else {
            return null;
        }
    }

    private String getSpecialPermissionMessage(SqlParseModel sqlParseModel) {
        // getSpecialJudgeMessage
        String message = "";

        // 只允许范围和操作都是不限制的角色执行: 切换、创建、删除、修改数据库的操作,以及执行begin开头的sql
        if (Arrays.asList(SqlConstant.KEY_USE, SqlConstant.KEY_DATABASE).contains(sqlParseModel.getOperation())) {
            message = "没有 " + sqlParseModel.getOperation() + " 的执行权限!";
        } else if (Arrays.asList(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE, SqlConstant.KEY_DO).contains(sqlParseModel.getOperation())) {
            message = "没有 BEGIN END 的执行权限!";
        } else if (Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER).contains(sqlParseModel.getOperation())
                && sqlParseModel.getSqlAuthModelList().size() > 0 && sqlParseModel.getSqlAuthModelList().get(0).getType() != null
                && Arrays.asList(SqlConstant.KEY_USER, SqlConstant.KEY_DATABASE, SqlConstant.KEY_SCHEMA).contains(sqlParseModel.getSqlAuthModelList().get(0).getType().toUpperCase(Locale.ROOT))) {
            message = SqlConstant.KEY_CREATE.equals(sqlParseModel.getOperation()) ? "没有 CREATE 数据库的执行权限!" :
                    SqlConstant.KEY_DROP.equals(sqlParseModel.getOperation()) ? "没有 DROP 数据库的执行权限!" :
                            SqlConstant.KEY_ALTER.equals(sqlParseModel.getOperation()) ? "没有 ALTER 数据库的执行权限!" : "";
        } else if (sqlParseModel.getAction().isNeedCreateSchema()) {
            message = "没有 CREATE 数据库的执行权限!";
        } else if (sqlParseModel.isSwitchDatabase()) {
            message = "没有切换SCHEMA的执行权限!";
        }

        return message;
    }

    private static List<SqlAuthModel> buildNoAuthModels(ParserParamDto paramDTO, Map<String, List<AuthRuleModel>> authRuleKeys,
                                                        SqlParseModel sqlParserModel) {
        sqlParserModel.setAuthModelsNum(sqlParserModel.getSqlAuthModelList().size());
        List<SqlAuthModel> hasNoAuthModels = new ArrayList<>();
        sortSqlAuthModelList(sqlParserModel, paramDTO);

        for (SqlAuthModel sqlAuthModel : sqlParserModel.getSqlAuthModelList()) {
            String operation = sqlAuthModel.getOperation();
            if (Arrays.asList(SqlConstant.KEY_LOAD, SqlConstant.KEY_REPLACE, SqlConstant.KEY_IMPORT).contains(operation)) {
                operation = SqlConstant.KEY_INSERT;
            } else if (Arrays.asList(SqlConstant.KEY_VALUES, SqlConstant.KEY_EXEC, SqlConstant.KEY_EXECUTE).contains(operation)) {
                operation = SqlConstant.KEY_CALL;
            } else if (SqlConstant.KEY_EXPORT.equals(operation)) {
                operation = SqlConstant.KEY_EXPORT_OPERATION;
            }

            boolean hasOperationAuth = hasOperationAuth(paramDTO, sqlAuthModel, authRuleKeys, sqlParserModel, operation);
            boolean hasFunctionAuth = false;
            if (!CollectionUtils.isEmpty(sqlParserModel.getAction().getFunctions())) {
                hasFunctionAuth = hasFunctionAuth(paramDTO, sqlAuthModel, authRuleKeys, sqlParserModel, OperationAuthConstant.call);
                if (!hasFunctionAuth && !CollectionUtils.isEmpty(CommonUtil.getFunctions(sqlParserModel.getAction().getFunctions())) && sqlAuthModel.getOperation().equals(SqlConstant.STATISTICS)) {
                    hasFunctionAuth = hasFunctionAuth(paramDTO, sqlAuthModel, authRuleKeys, sqlParserModel, OperationAuthConstant.statistics);
                }
            }
            boolean hasExportAuth = true;
            if (List.of(SqlConstant.KEY_SELECT, SqlConstant.KEY_DESC, SqlConstant.KEY_SHOW, SqlConstant.KEY_CALL, SqlConstant.STATISTICS).contains(operation.toUpperCase(Locale.ROOT))) {
                hasExportAuth = hasExportAuth(paramDTO, sqlAuthModel, new HashMap<>(), sqlParserModel, OperationAuthConstant.export);
            } else if (List.of(DatabaseType.MONGODB.getValue(), DatabaseType.REDIS.getValue(), DatabaseType.SPARK.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(paramDTO.getDbType())) {
                hasExportAuth = false;
            }
            boolean hasCountTotalAuth = false;
            if (SqlConstant.KEY_SELECT.equalsIgnoreCase(operation) || SqlConstant.STATISTICS.equalsIgnoreCase(operation)) {
                hasCountTotalAuth = hasTotalRowsAuth(paramDTO, sqlAuthModel, new HashMap<>(), sqlParserModel, OperationAuthConstant.statistics);
            }

            if (!hasOperationAuth || !hasFunctionAuth || !hasExportAuth || !hasCountTotalAuth) {
                sqlAuthModel.setHasOperationAuth(hasOperationAuth);
                sqlAuthModel.setHasFunctionAuth(hasFunctionAuth);
                sqlAuthModel.setHasExportAuth(hasExportAuth);
                sqlAuthModel.setHasCountTotalAuth(hasCountTotalAuth);
                hasNoAuthModels.add(sqlAuthModel);
            }
        }

        return hasNoAuthModels;
    }

    private static boolean hasOperationAuth(ParserParamDto paramDTO, SqlAuthModel sqlAuthModel, Map<String, List<AuthRuleModel>> authRuleKeys,
                                            SqlParseModel sqlParserModel, String operation) {

        String instanceUniqueKey = paramDTO.getConnectId();
        String ddlSubdivide = sqlAuthModel.getDdlSubdivideOperation();
        String schemaUniqueKey = sqlAuthModel.getSchemaUniqueKey();
        Integer isSys = sqlAuthModel.isSys() ? 1 : 0;
        Map<String, List<String>> operationAuth = paramDTO.getOperationAuth();

        boolean hasAuth = false;

        // 验证实例级别权限
        hasAuth = hasInstanceAuth(operation, paramDTO, ddlSubdivide, isSys, authRuleKeys, sqlParserModel, sqlAuthModel);

        //处理PG VACUUM 权限，如果表名为空，则需要具有实例级别权限才可以执行
        if (DatabaseType.PG_SQL.getValue().equals(paramDTO.getDbType()) && SqlConstant.KEY_VACUUM.equalsIgnoreCase(operation)) {
            if (StringUtils.isEmpty(sqlAuthModel.getName())) {
                return hasAuth;
            }
        }

        if (!hasAuth && !DatabaseType.ELASTIC_SEARCH.getValue().equals(paramDTO.getDbType())) {
            // 验证schema级别权限
            hasAuth = hasSchemaAuth(operation, ddlSubdivide, operationAuth, schemaUniqueKey, instanceUniqueKey, authRuleKeys, paramDTO, sqlAuthModel);
        }
        if (!hasAuth) {
            // 验证表级别权限
            boolean needCheckTable = (sqlAuthModel.isTable() && StringUtils.isNotBlank(sqlAuthModel.getName())) || !Arrays.asList(SqlConstant.KEY_SELECT, SqlConstant.KEY_CALL).contains(operation.toUpperCase(Locale.ROOT));
            if (needCheckTable) {
                hasAuth = hasTableAuth(operation, ddlSubdivide, operationAuth, schemaUniqueKey, instanceUniqueKey, sqlAuthModel.getFullName(), authRuleKeys,
                        paramDTO, sqlAuthModel, sqlParserModel.getAction().getFunctions());
            }

            // 验证可编程对象级别权限
            boolean needCheckObject = !needCheckTable;
            if (!hasAuth && Arrays.asList(OperationAuthConstant.statistics, OperationAuthConstant.export).contains(operation.toLowerCase(Locale.ROOT))) {
                needCheckObject = true;
            } else if (!hasAuth && DatabaseType.HETU.getValue().equals(paramDTO.getDbType()) && SqlConstant.KEY_SELECT.equals(operation)) {
                needCheckObject = true;
            }
            if (needCheckObject) {
                hasAuth = hasObjectAuth(operation, operationAuth, schemaUniqueKey, instanceUniqueKey, sqlAuthModel.getSchemaName(), sqlAuthModel.getFullName(), authRuleKeys, sqlParserModel, paramDTO, sqlAuthModel);
            }
        }

        return hasAuth;
    }

    private static boolean hasFunctionAuth(ParserParamDto paramDTO, SqlAuthModel sqlAuthModel, Map<String, List<AuthRuleModel>> authRuleKeys,
                                           SqlParseModel sqlParserModel, String operation) {
        return hasOperationAuth(paramDTO, sqlAuthModel, authRuleKeys, sqlParserModel, operation);
    }

    private static boolean hasExportAuth(ParserParamDto paramDTO, SqlAuthModel sqlAuthModel, Map<String, List<AuthRuleModel>> authRuleKeys,
                                         SqlParseModel sqlParserModel, String operation) {
        return hasOperationAuth(paramDTO, sqlAuthModel, authRuleKeys, sqlParserModel, operation);
    }

    private static boolean hasTotalRowsAuth(ParserParamDto paramDTO, SqlAuthModel sqlAuthModel, Map<String, List<AuthRuleModel>> authRuleKeys,
                                            SqlParseModel sqlParserModel, String operation) {
        return hasOperationAuth(paramDTO, sqlAuthModel, authRuleKeys, sqlParserModel, operation);
    }

    //任何人在私有schema下 create_table drop_table alter_table 也需要记录。（其它人，不是走的私有表角色授权）
    private static void doOtherNeedRecordInPT(String ddlSubdivide, ParserParamDto paramDto, SqlAuthModel sqlAuthModel) {
        if (List.of("create_table", "drop_table", "alter_table").contains(ddlSubdivide.toLowerCase(Locale.ROOT)) &&
                paramDto.getCurIsPrivateSchemaIdName().containsKey(sqlAuthModel.getSchemaUniqueKey())) {
            paramDto.setNeedRecordInPrivateTable(true);
        }
    }

    private static boolean canAuthByPrivateTableRole(ParserParamDto paramDto, SqlParseModel sqlParseModel, SqlAuthModel sqlAuthModel,
                                                     String checkOperation, String originalOperation, InstanceRoleDto instanceRole, Map<String, List<AuthRuleModel>> authRuleKeys) {
        //isTableCreator()只调一次，后续复用结果
        boolean isTableCreator = isTableCreator(paramDto.getConnectId(), sqlAuthModel.getCatalogName(), sqlAuthModel.getSchemaName(),
                SqlConstant.STATISTICS.equals(sqlAuthModel.getOperation()) ? sqlAuthModel.getStaFuncForObjectName() : sqlAuthModel.getFullName(), paramDto.getUserId());

        //create table
        if (List.of(SqlConstant.KEY_CREATE, SqlConstant.KEY_CREATE_TABLE).contains(checkOperation.toUpperCase(Locale.ROOT)) &&
                SqlConstant.KEY_TABLE.equalsIgnoreCase(sqlAuthModel.getType())) {
            if (sqlParseModel.getAction().isCreateTableAsSelect()) { //私有表角色，禁止create table as select语句，并且要求强制更改提示信息
                sqlAuthModel.setNeedChangeMessageForPT(true);
                return false;
            }
            if (SqlConstant.KEY_COMMENT.equals(originalOperation) && !isTableCreator) {//如果是因为comment走到这里的，需要额外判断
                return false;
            }
            GetAuthUtil.buildAuthRule(authRuleKeys, AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH, paramDto.getConnectId(), instanceRole.getRole_name(), "Instance");
            paramDto.setNeedRecordInPrivateTable(true);
            return true;

        // other for table
        } else if (getAuthsForTableCreator().contains(checkOperation.toLowerCase(Locale.ROOT)) &&
                isTableCreator &&
                (SqlConstant.KEY_TABLE.equalsIgnoreCase(sqlAuthModel.getType()) || SqlConstant.STATISTICS.equals(checkOperation))) {
            if ((sqlParseModel.getAction().isUpdateSelect() && checkOperation.equalsIgnoreCase(SqlConstant.KEY_UPDATE)) ||
                    (sqlParseModel.getAction().isInsertIntoSelect() && checkOperation.equalsIgnoreCase(SqlConstant.KEY_INSERT))) {//私有表角色，禁止update select、insert into select语句，并且要求强制更改提示信息
                sqlAuthModel.setNeedChangeMessageForPT(true);
                return false;
            }
            GetAuthUtil.buildAuthRule(authRuleKeys, AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH,
//                    SqlConstant.STATISTICS.equals(sqlAuthModel.getOperation()) ? sqlAuthModel.staFuncForObjectName : sqlAuthModel.getFullName(),
                    paramDto.getConnectId(),
                    checkOperation, "Instance");
            paramDto.setNeedRecordInPrivateTable(true);
            return true;
        }

        return false;
    }

    private static boolean hasInstanceAuth(String operation, ParserParamDto paramDTO, String ddlSubdivide, Integer isSys,
                                           Map<String, List<AuthRuleModel>> authRuleKeys, SqlParseModel sqlParseModel, SqlAuthModel sqlAuthModel) {
        List<String> checkOperations = CommonUtil.getCheckOperations(operation, ddlSubdivide);
        for (String checkOperation : checkOperations) {
            if (hasRoleAuth(checkOperation, isSys, authRuleKeys, paramDTO, sqlParseModel, sqlAuthModel, operation)) {
                if (StringUtils.isNotEmpty(ddlSubdivide)) doOtherNeedRecordInPT(ddlSubdivide, paramDTO, sqlAuthModel);
                return true;
            }
            if (hasInstanceOperationAuth(checkOperation, paramDTO.getOperationAuth(), paramDTO.getConnectId(), authRuleKeys)) {
                if (StringUtils.isNotEmpty(ddlSubdivide)) doOtherNeedRecordInPT(ddlSubdivide, paramDTO, sqlAuthModel);
                return true;
            }
            if (hasSpecialAuthForFunctionAndPackage(checkOperation, sqlAuthModel, authRuleKeys, paramDTO.getOperationAuth(), paramDTO.getConnectId(), AuthLevelType.instance)) {
                if (StringUtils.isNotEmpty(ddlSubdivide)) doOtherNeedRecordInPT(ddlSubdivide, paramDTO, sqlAuthModel);
                return true;
            }
        }
        return false;
    }

    private static boolean hasRoleAuth(String checkOperation, Integer isSys, Map<String, List<AuthRuleModel>> authRuleKeys, ParserParamDto paramDTO, SqlParseModel sqlParseModel, SqlAuthModel sqlAuthModel, String originalOperation) {
        Integer operateRange = isSys == 1 ? Integer.valueOf(2) : Integer.valueOf(3);
        for (Map.Entry<String, List<String>> entry : paramDTO.getRoleAuth().entrySet()) {
            if (entry.getValue().contains(paramDTO.getConnectId())) {
                InstanceRoleDto instanceRole = paramDTO.getInstanceRoleMap().get(entry.getKey());
                if (    // 操作范围
                        (Integer.valueOf(1).equals(instanceRole.getOperate_range()) ||
                            (!Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE, SqlConstant.KEY_VACUUM, SqlConstant.KEY_SET).contains(checkOperation) &&
                            operateRange.equals(instanceRole.getOperate_range())))
                        // 操作权限
                        &&
                        (Integer.valueOf(2).equals(instanceRole.getOperate_rule()) ||
                            instanceRole.buildActionKeyList().contains(getAuthToken(checkOperation, AuthLevelType.instance.getValue())))
                ) { //除了私有表角色之外的大权限实例角色。优先级在前面。
                    // 不拦截解析出错的大权限角色的sql
                    paramDTO.setInstanceOwner(true);
                    GetAuthUtil.buildAuthRule(authRuleKeys, entry.getKey(), paramDTO.getConnectId(), instanceRole.getRole_name(), "Instance");
                    return true;
                }
                //私有表实例角色，优先级次之
                if (Integer.valueOf(4).equals(instanceRole.getOperate_range()) &&
                        AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH.equalsIgnoreCase(instanceRole.getUnique_key()) &&
                        paramDTO.getCurIsPrivateSchemaIdName().containsKey(sqlAuthModel.getSchemaUniqueKey())) { //当前sqlAuth是私有schema
                    //说明对该实例，有私有表角色的权利。然后细看能不能授权。（因为不是大权限角色，不能直接授权）
                    boolean ret = canAuthByPrivateTableRole(paramDTO, sqlParseModel, sqlAuthModel, checkOperation, originalOperation, instanceRole, authRuleKeys);
                    if (ret) return true;
                }
            }
        }
        return false;
    }

    private static boolean hasInstanceOperationAuth(String operation, Map<String, List<String>> authMap, String instanceUniqueKey,
                                                    Map<String, List<AuthRuleModel>> authRuleKeys) {
        if (GetAuthUtil.containsActionKey(authMap, getAuthToken(operation, AuthLevelType.instance.getValue()), instanceUniqueKey)) {
            GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(operation, AuthLevelType.instance.getValue()), instanceUniqueKey, operation, "Instance");
            return true;
        }
        return false;
    }

    private static boolean hasInstanceOperationAuthNotBuild(String operation, Map<String, List<String>> authMap, String instanceUniqueKey) {
        //不构建授权记录，只看有没有权限
        return GetAuthUtil.containsActionKey(authMap, getAuthToken(operation, AuthLevelType.instance.getValue()), instanceUniqueKey);
    }

    private static boolean hasSchemaAuth(String operation, String ddlSubdivide, Map<String, List<String>> authMap, String schemaUniqueKey,
                                         String instanceUniqueKey, Map<String, List<AuthRuleModel>> authRuleKeys, ParserParamDto paramDto, SqlAuthModel sqlAuthModel) {
        String instanceSchemaUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey;
        List<String> checkOperations = CommonUtil.getCheckOperations(operation, ddlSubdivide);
        for (String checkOperation : checkOperations) {
            if (hasSchemaAuthSub(checkOperation, authMap, instanceSchemaUniqueKey, authRuleKeys, schemaUniqueKey)) {
                if (StringUtils.isNotEmpty(ddlSubdivide)) doOtherNeedRecordInPT(ddlSubdivide, paramDto, sqlAuthModel);
                return true;
            }
            if (hasSpecialAuthForFunctionAndPackage(checkOperation, sqlAuthModel, authRuleKeys, authMap, paramDto.getConnectId(), AuthLevelType.schema)) {
                if (StringUtils.isNotEmpty(ddlSubdivide)) doOtherNeedRecordInPT(ddlSubdivide, paramDto, sqlAuthModel);
                return true;
            }
        }
        return false;
    }

    private static boolean hasSchemaAuthSub(String operation, Map<String, List<String>> authMap, String instanceSchemaUniqueKey,
                                            Map<String, List<AuthRuleModel>> authRuleKeys, String schemaUniqueKey) {
        if (Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE).contains(operation)) {
            return false; // grant、revoke只有实例级别的权限
        }
        // schema负责人权限
        String leaderRoleSchemaAuth = "leader_role_schema_auth";
        if (GetAuthUtil.containsActionKey(authMap, leaderRoleSchemaAuth, instanceSchemaUniqueKey)) {
            GetAuthUtil.buildAuthRule(authRuleKeys, leaderRoleSchemaAuth, schemaUniqueKey, OwnerConstant.schema_leader, "Schema");
            return true;
        }

        if (GetAuthUtil.containsActionKey(authMap, getAuthToken(operation, AuthLevelType.schema.getValue()), instanceSchemaUniqueKey)) {
            GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(operation, AuthLevelType.schema.getValue()), schemaUniqueKey, operation, "Schema");
            return true;
        }
        return false;
    }

    private static boolean hasTableAuth(String operation, String ddlSubdivide, Map<String, List<String>> authMap, String schemaUniqueKey,
                                        String instanceUniqueKey, String tableName, Map<String, List<AuthRuleModel>> authRuleKeys,
                                        ParserParamDto paramDto, SqlAuthModel sqlAuthModel, Set<String> functions) {
        if (isWhiteTable(paramDto.getDbType(), tableName, functions, operation, sqlAuthModel)) {
            return true;
        }

        String tableUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.TABLE.getValue() + AuthConstant.connector + tableName;
        String tableSubUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.TABLE_SUB.getValue() + AuthConstant.connector + tableName;

        List<String> checkOperations = CommonUtil.getCheckOperations(operation, ddlSubdivide);

        boolean hasTableAuth;
        boolean hasTableSubAuth;
        for (String checkOperation : checkOperations) {
            if ((SqlConstant.STATISTICS.equalsIgnoreCase(checkOperation) || SqlConstant.STATISTICS.equalsIgnoreCase(sqlAuthModel.getOperation())) && !operation.equalsIgnoreCase(SqlConstant.KEY_SELECT)) {
                String statTableName = StringUtils.isNotBlank(sqlAuthModel.getStaFuncForObjectName()) ? sqlAuthModel.getStaFuncForObjectName() : tableName;
                String newTableUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.TABLE.getValue() + AuthConstant.connector + statTableName;
                String newTableSubUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.TABLE_SUB.getValue() + AuthConstant.connector + statTableName;
                hasTableAuth = hasTableAuthSub(checkOperation, authMap, newTableUniqueKey);
                hasTableSubAuth = hasTableAuthSub(checkOperation, authMap, newTableSubUniqueKey);
            } else {
                hasTableAuth = hasTableAuthSub(checkOperation, authMap, tableUniqueKey);
                hasTableSubAuth = hasTableAuthSub(checkOperation, authMap, tableSubUniqueKey);
            }

            if (hasTableAuth || hasTableSubAuth) {
                if (StringUtils.isNotEmpty(ddlSubdivide)) doOtherNeedRecordInPT(ddlSubdivide, paramDto, sqlAuthModel);
                GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(checkOperation, AuthLevelType.table.getValue()), tableName, checkOperation, "Table");
                return true;
            }
        }

        return false;
    }

    private static boolean hasTableAuthSub(String operation, Map<String, List<String>> authMap, String tableUniqueKey) {
        return GetAuthUtil.containsActionKey(authMap, getAuthToken(operation, AuthLevelType.table.getValue()), tableUniqueKey);
    }

    private static boolean hasObjectAuth(String operation, Map<String, List<String>> authMap, String schemaUniqueKey, String instanceUniqueKey, String schemaName,
                                         String objectName, Map<String, List<AuthRuleModel>> authRuleKeys, SqlParseModel sqlParserModel, ParserParamDto paramDTO, SqlAuthModel sqlAuthModel) {

        String type;
        String typeSub;
        String authType;

        if (sqlAuthModel.isBuiltinFunction()) {
            GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(operation, AuthLevelType.object.getValue()), objectName, operation, "Function");
            return true;
        }

        if (sqlParserModel.getAction().isSelectFunction()) {
            type = AuthConstant.connector + DataNodeType.FUNCTION.getValue() + AuthConstant.connector;
            typeSub = AuthConstant.connector + DataNodeType.FUNCTION_SUB.getValue() + AuthConstant.connector;
            authType = "Function";
        } else if (SqlConstant.KEY_CALL.equalsIgnoreCase(operation)) {
            type = AuthConstant.connector + DataNodeType.PROCEDURE.getValue() + AuthConstant.connector;
            typeSub = AuthConstant.connector + DataNodeType.PROCEDURE_SUB.getValue() + AuthConstant.connector;
            authType = "Procedure";
            if (StringUtils.isNotBlank(paramDTO.getObjectType()) && SqlConstant.KEY_MSSQL_WORK.equalsIgnoreCase(paramDTO.getObjectType())) {
                type = AuthConstant.connector + DataNodeType.WORK.getValue() + AuthConstant.connector;
                typeSub = AuthConstant.connector + DataNodeType.WORK_SUB.getValue() + AuthConstant.connector;
                authType = "Work";
            }
        } else if (sqlParserModel.getAction().isSelectSequence()) {
            type = AuthConstant.connector + DataNodeType.SEQUENCE.getValue() + AuthConstant.connector;
            typeSub = AuthConstant.connector + DataNodeType.SEQUENCE_SUB.getValue() + AuthConstant.connector;
            authType = "Sequence";
        } else if (sqlParserModel.getAction().isTemplate()) {
            type = AuthConstant.connector + DataNodeType.TEMPLATE.getValue() + AuthConstant.connector;
            typeSub = AuthConstant.connector + DataNodeType.TEMPLATE_SUB.getValue() + AuthConstant.connector;
            authType = "Template";
        } else {
            type = AuthConstant.connector + DataNodeType.VIEW.getValue() + AuthConstant.connector;
            typeSub = AuthConstant.connector + DataNodeType.VIEW_SUB.getValue() + AuthConstant.connector;
            authType = "View";
        }

        if (sqlAuthModel.getType().equals(SqlConstant.PACKAGE) && !SqlConstant.STATISTICS.equalsIgnoreCase(operation)) {
            type = AuthConstant.connector + DataNodeType.PACKAGE.getValue() + AuthConstant.connector;
            typeSub = AuthConstant.connector + DataNodeType.PACKAGE_SUB.getValue() + AuthConstant.connector;
            authType = "Package";
            sqlAuthModel.setAuthSchemaNameForPackage(sqlAuthModel.getSchemaName());
            sqlAuthModel.setAuthSchemaIdForPackage(sqlAuthModel.getSchemaUniqueKey());
        }
        boolean hasSameFunction = CommonUtil.hasSameFunction(paramDTO.getDbType(), sqlParserModel, operation);

        String objectUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + type + objectName;
        String objectSubUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + typeSub + objectName;

        List<String> checkOperations = CommonUtil.getCheckOperations(operation, "");

        for (String checkOperation : checkOperations) {
            boolean hasObjectAuth, hasObjectSubAuth;
            if (SqlConstant.STATISTICS.equalsIgnoreCase(checkOperation)) {
                String statObjectName = StringUtils.isNotBlank(sqlAuthModel.getStaFuncForObjectName()) ? sqlAuthModel.getStaFuncForObjectName() : objectName;
                String newObjectUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + type + statObjectName;
                String newObjectSubUniqueKey = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + typeSub + statObjectName;
                hasObjectAuth = hasObjectAuth(checkOperation, authMap, newObjectUniqueKey);
                hasObjectSubAuth = hasObjectAuth(checkOperation, authMap, newObjectSubUniqueKey);
            } else if ("Procedure".equals(authType)) { //上面Procedure可能其实是Function
                hasObjectAuth = hasObjectAuth(checkOperation, authMap, instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + type + objectName);
                hasObjectSubAuth = hasObjectAuth(checkOperation, authMap, instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + typeSub + objectName);
                if (!hasObjectAuth && !hasObjectSubAuth && SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType())) { //没有权限再当做Function去鉴权
                    type = AuthConstant.connector + DataNodeType.FUNCTION.getValue() + AuthConstant.connector;
                    typeSub = AuthConstant.connector + DataNodeType.FUNCTION_SUB.getValue() + AuthConstant.connector;
                    authType = "Function";
                    hasObjectAuth = hasObjectAuth(checkOperation, authMap, instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + type + objectName);
                    hasObjectSubAuth = hasObjectAuth(checkOperation, authMap, instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + typeSub + objectName);
                }
            } else { //常规情况
                hasObjectAuth = hasObjectAuth(checkOperation, authMap, objectUniqueKey);
                hasObjectSubAuth = hasObjectAuth(checkOperation, authMap, objectSubUniqueKey);
            }

            if (!hasObjectAuth && !hasObjectSubAuth && "View".equals(authType)) {
                String objectUniqueKeyTemp = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.SYNONYM.getValue() + AuthConstant.connector + objectName;
                String objectSubUniqueKeyTemp = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.SYNONYM_SUB.getValue() + AuthConstant.connector + objectName;
                hasObjectAuth = hasObjectAuth(checkOperation, authMap, objectUniqueKeyTemp);
                hasObjectSubAuth = hasObjectAuth(checkOperation, authMap, objectSubUniqueKeyTemp);
            }
            if (!hasObjectAuth && !hasObjectSubAuth && "Package".equals(authType)) {
                String objectUniqueKeyTemp = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.PACKAGE + AuthConstant.connector + objectName;
                String objectSubUniqueKeyTemp = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.PACKAGE_SUB.getValue() + AuthConstant.connector + objectName;
                hasObjectAuth = hasObjectAuth(checkOperation, authMap, objectUniqueKeyTemp);
                hasObjectSubAuth = hasObjectAuth(checkOperation, authMap, objectSubUniqueKeyTemp);
            }

            //放开自定义函数
            if ("Function".equals(authType) && SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType()) && SqlConstant.KEY_CALL.equalsIgnoreCase(checkOperation)) {
                hasObjectAuth = true;
                hasObjectSubAuth = true;
            }

            String oid = "";
            if (hasSameFunction && !hasObjectAuth && !hasObjectSubAuth) {
                oid = hasSameFunctionAuth(paramDTO, sqlParserModel, checkOperation, authMap, objectName, schemaName, objectUniqueKey, objectSubUniqueKey);
                hasObjectAuth = StringUtils.isNotBlank(oid);
            }
            if (hasObjectAuth || hasObjectSubAuth) {
                GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(checkOperation, AuthLevelType.object.getValue()), objectName, checkOperation, authType, oid);
                return true;
            }
        }

        return false;
    }

    private static boolean hasObjectAuth(String operation, Map<String, List<String>> authMap, String objectUniqueKey) {
        List<String> instanceItem = authMap.get(getAuthToken(operation, AuthLevelType.object.getValue()));
        if (instanceItem != null) {
            for (String item : instanceItem) {
                if (item.equalsIgnoreCase(objectUniqueKey)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static String hasSameFunctionAuth(ParserParamDto paramDTO, SqlParseModel sqlParserModel, String operation, Map<String, List<String>> authMap,
                                              String objectName, String schemaName, String objectUniqueKey, String objectSubUniqueKey) {
        if (SqlConstant.KEY_CALL.equalsIgnoreCase(operation) || sqlParserModel.getAction().isSelectFunction()) {
            if (DatabaseType.getPGSqlIntegerValueList().contains(paramDTO.getDbType())) {
                List<String> postGreSqlFunctionOid = paramDTO.getOidList();
                if (postGreSqlFunctionOid == null || postGreSqlFunctionOid.isEmpty()) {
                    postGreSqlFunctionOid = ExecuteSqlUtil.getPostGreSqlFunctionOid(paramDTO, schemaName, objectName, sqlParserModel.getAction().getFuncArgs());
                }
                return getOidName(postGreSqlFunctionOid, paramDTO, operation, objectUniqueKey, objectSubUniqueKey, authMap);
            } else if (CommonUtil.useColonSplit(paramDTO.getDbType())) {
                List<String> informixOid = paramDTO.getOidList();
                if (informixOid == null || informixOid.isEmpty()) {
                    informixOid = ExecuteSqlUtil.getInformixFunctionOid(paramDTO, schemaName, objectName);
                }
                return getOidName(informixOid, paramDTO, operation, objectUniqueKey, objectSubUniqueKey, authMap);
            } else if (Arrays.asList(DatabaseType.DB2.getValue(), DatabaseType.DB2AS400.getValue()).contains(paramDTO.getDbType())) {
                List<String> db2Oid = paramDTO.getOidList();
                if (db2Oid == null || db2Oid.isEmpty()) {
                    db2Oid = ExecuteSqlUtil.getDB2ProcedureOid(paramDTO, schemaName, objectName, sqlParserModel.getOperation());
                }
                return getOidName(db2Oid, paramDTO, operation, objectUniqueKey, objectSubUniqueKey, authMap);
            }
        }
        return "";
    }
    //放行部分特殊情况，针对for function package
    private static boolean hasSpecialAuthForFunctionAndPackage(String checkOperation, SqlAuthModel sqlAuthModel, Map<String, List<AuthRuleModel>> authRuleKeys, Map<String, List<String>> authMap, String instanceUniqueKey, AuthLevelType authLevelType) {

        //如果不是函数、包，则让这个方法直接无效。或者说是函数，但操作不是call或statistics；或者说是包，但操作不是call或statistics，则都直接作废方法
        if ((!SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType()) && !SqlConstant.PACKAGE.equalsIgnoreCase(sqlAuthModel.getType())) ||
                (SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType()) && !List.of(SqlConstant.KEY_CALL, SqlConstant.STATISTICS).contains(checkOperation.toUpperCase(Locale.ROOT))) ||
                (SqlConstant.PACKAGE.equalsIgnoreCase(sqlAuthModel.getType()) && !List.of(SqlConstant.KEY_CALL, SqlConstant.STATISTICS).contains(checkOperation.toUpperCase(Locale.ROOT)))) {
            return false;
        }

        String schemaUniqueKey = sqlAuthModel.getSchemaUniqueKey();

        switch (authLevelType) {
            case instance: {
                boolean hasSpecialAuth = authRuleKeys.entrySet().stream().anyMatch(entry -> entry.getValue().stream().anyMatch(authRuleModel -> "Instance".equals(authRuleModel.getType())));

                if (SqlConstant.STATISTICS.equals(checkOperation.toUpperCase(Locale.ROOT))) { //由于优先级问题，统计暂时提到最前面。后同
                    hasSpecialAuth = hasInstanceOperationAuthNotBuild(checkOperation, authMap, instanceUniqueKey);
                } else if (SqlConstant.PACKAGE.equalsIgnoreCase(sqlAuthModel.getType()) && ParserConstant.ORACLE_SYS_SCHEMA.equals(sqlAuthModel.getSchemaName())) {
                    hasSpecialAuth = hasInstanceOperationAuthNotBuild(checkOperation, authMap, instanceUniqueKey);
                }
                if (hasSpecialAuth) {
                    GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(checkOperation, authLevelType.getValue()), instanceUniqueKey, checkOperation, "Instance");
                }
                return hasSpecialAuth;
            }
            case schema: {
                boolean hasSpecialAuth = authRuleKeys.entrySet().stream().anyMatch(entry -> entry.getValue().stream().anyMatch(authRuleModel ->
                        "Schema".equals(authRuleModel.getType()) && authRuleModel.getName().equals(schemaUniqueKey))
                );

                String instanceSchemaUniqueKey = instanceUniqueKey + AuthConstant.connector + sqlAuthModel.getSchemaUniqueKey();
                if (SqlConstant.STATISTICS.equals(checkOperation.toUpperCase(Locale.ROOT))) {
                    hasSpecialAuth = hasSchemaAuthSub(checkOperation, authMap, instanceSchemaUniqueKey, authRuleKeys, sqlAuthModel.getSchemaUniqueKey());
                }
                if ((SqlConstant.PACKAGE.equalsIgnoreCase(sqlAuthModel.getType()) && ParserConstant.ORACLE_SYS_SCHEMA.equals(sqlAuthModel.getSchemaName()))) {
                    hasSpecialAuth = hasSchemaAuthSub(checkOperation, authMap, instanceSchemaUniqueKey, authRuleKeys, sqlAuthModel.getSchemaUniqueKey());
                }
                return hasSpecialAuth;
            }
            case table: { //暂时未用
                boolean hasSpecialAuth = true;

                if (SqlConstant.STATISTICS.equals(checkOperation.toUpperCase(Locale.ROOT))) {
                    String statFullName = StringUtils.isNotBlank(sqlAuthModel.getStaFuncForObjectName()) ? sqlAuthModel.getStaFuncForObjectName() : sqlAuthModel.getFullName();
                    String newTableUniqueKey = instanceUniqueKey + AuthConstant.connector + sqlAuthModel.getSchemaUniqueKey() + AuthConstant.connector + DataNodeType.TABLE.getValue() + AuthConstant.connector + statFullName;
                    String newTableSubUniqueKey = instanceUniqueKey + AuthConstant.connector + sqlAuthModel.getSchemaUniqueKey() + AuthConstant.connector + DataNodeType.TABLE_SUB.getValue() + AuthConstant.connector + statFullName;
                    hasSpecialAuth = hasTableAuthSub(checkOperation, authMap, newTableUniqueKey);
                    if (!hasSpecialAuth) {
                        hasSpecialAuth = hasTableAuthSub(checkOperation, authMap, newTableSubUniqueKey);
                    }
                    if (hasSpecialAuth) {
                        GetAuthUtil.buildAuthRule(authRuleKeys, getAuthToken(checkOperation, authLevelType.getValue()), sqlAuthModel.getFullName(), checkOperation, "Table");
                    }
                }
                return hasSpecialAuth;
            }
        }

        return false;
    }

    private static String getOidName(List<String> oidList, ParserParamDto paramDTO, String operation, String objectUniqueKey,
                                     String objectSubUniqueKey, Map<String, List<String>> authMap) {
        if (!oidList.isEmpty()) {
            paramDTO.setOidList(oidList);
            for (String oid : oidList) {
                String objectUniqueKeyNew = objectUniqueKey + "[oid:" + oid + "]";
                String objectSubUniqueKeyNew = objectSubUniqueKey + "[oid:" + oid + "]";
                boolean hasObjectAuth = hasObjectAuth(operation, authMap, objectUniqueKeyNew);
                boolean hasObjectSubAuth = hasObjectAuth(operation, authMap, objectSubUniqueKeyNew);
                if (hasObjectAuth || hasObjectSubAuth) {
                    return "[oid:" + oid + "]";
                }
            }
        }
        return "";
    }

    // -------------------表组相关的权限校验------------------------
    private void tableGroupAuth(ParserParamDto paramDTO, SqlParseModel sqlParserModel, List<SqlAuthModel> sqlAuthModels, List<AuthTraceModel> authTraceModels) {
        if (DatabaseType.ELASTIC_SEARCH.getValue().equals(paramDTO.getDbType()) && !sqlAuthModels.isEmpty()) {
            List<PermissionRule> tableGroupAuth = getTableGroupAuth(paramDTO, sqlParserModel);
            for (PermissionRule permissionRule : tableGroupAuth) {
                if (StringUtils.isBlank(permissionRule.getObject_name()) || permissionRule.getObject_name().length() < 2) {
                    continue;
                }

                // 通配符前缀
                String wildcardPrefix = permissionRule.getObject_name().substring(0, permissionRule.getObject_name().length() - 1);

                for (SqlAuthModel sqlAuthModel : sqlAuthModels) {
                    if (sqlAuthModel.getName().startsWith(wildcardPrefix)) {
                        String authToken = getAuthToken(sqlAuthModel.getOperation(), AuthLevelType.table_group.getValue());
                        if (!sqlAuthModel.isHasOperationAuth() && authToken.equals(permissionRule.getAction_key())) {
                            sqlAuthModel.setHasOperationAuth(true);
                            authTraceModels.add(sqlRuleService.buildSingleAuthTraceModel(permissionRule, "TableGroup", sqlAuthModel.getOperation(), sqlAuthModel.getName()));
                        }

                        if (!sqlAuthModel.isHasOperationAuth() && SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlAuthModel.getOperation())) {
                            authToken = getAuthToken(OperationAuthConstant.statistics, AuthLevelType.table_group.getValue());
                            if (authToken.equals(permissionRule.getAction_key())) {
                                sqlAuthModel.setHasOperationAuth(true);
                                authTraceModels.add(sqlRuleService.buildSingleAuthTraceModel(permissionRule, "TableGroup", sqlAuthModel.getOperation(), sqlAuthModel.getName()));
                            }
                        }

                        if (SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlAuthModel.getOperation())
                                && sqlParserModel.getAction().getFunctions() != null && sqlParserModel.getAction().getFunctions().size() > 0) {
                            authToken = getAuthToken(OperationAuthConstant.statistics, AuthLevelType.table_group.getValue());
                            if (!sqlAuthModel.isHasFunctionAuth() && authToken.equals(permissionRule.getAction_key())) {
                                sqlAuthModel.setHasFunctionAuth(true);
                            }
                        }

                        if (SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlAuthModel.getOperation())) {
                            authToken = getAuthToken(OperationAuthConstant.statistics, AuthLevelType.table_group.getValue());
                            if (!sqlAuthModel.isHasCountTotalAuth() && authToken.equals(permissionRule.getAction_key())) {
                                sqlAuthModel.setHasCountTotalAuth(true);
                            }
                            authToken = getAuthToken(OperationAuthConstant.export, AuthLevelType.table_group.getValue());
                            if (!sqlAuthModel.isHasExportAuth() && authToken.equals(permissionRule.getAction_key())) {
                                sqlAuthModel.setHasExportAuth(true);
                            }
                        }
                    }
                }
            }
        }
    }

    // ------------------构建权限快捷申请对象--------------
    private List<Map<String, Object>> buildNoAuthMapList(List<SqlAuthModel> sqlAuthModels, ParserParamDto paramDTO,
                                                         SqlParseModel sqlParserModel) {
        List<Map<String, Object>> noPermissionObjects = new ArrayList<>();

        for (SqlAuthModel sqlAuthModel : sqlAuthModels) {
            if (!sqlAuthModel.isHasOperationAuth()) {
                String operation = sqlAuthModel.getOperation();
                if (Arrays.asList(SqlConstant.KEY_EXEC, SqlConstant.KEY_EXECUTE).contains(operation)) {
                    operation = SqlConstant.KEY_CALL;
                } else if (SqlConstant.KEY_RENAME.equalsIgnoreCase(operation)) {
                    operation = SqlConstant.KEY_ALTER;
                } else if (Arrays.asList(SqlConstant.KEY_LOAD, SqlConstant.KEY_REPLACE, SqlConstant.KEY_IMPORT).contains(operation)) {
                    operation = SqlConstant.KEY_INSERT;
                }
                noPermissionObjects.addAll(buildNoPermissionMap(sqlAuthModel, paramDTO, operation, sqlParserModel));
            }


        }

        return noPermissionObjects;
    }

    private List<Map<String, Object>> buildNoPermissionMap(SqlAuthModel sqlAuthModel, ParserParamDto paramDTO, String operation,
                                                           SqlParseModel sqlParserModel) {
        // 对象类型转换
        if (RedisSqlConstant.KEY_TABLE_OR_VIEW.equalsIgnoreCase(sqlAuthModel.getType())) {
            if (sqlAuthModel.isTable()) {
                sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            } else {
                sqlAuthModel.setType(SqlConstant.KEY_VIEW);
            }
        }

        int authLevel = AuthLevelType.other.getValue(); // 权限等级(0:实例 1:schema 2:表 3:对象)
        String type = "";

        if (Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE).contains(operation)) {
            authLevel = AuthLevelType.instance.getValue();
        } else if (Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_ALTER, SqlConstant.KEY_COMMENT, SqlConstant.KEY_SHOW, SqlConstant.KEY_VALUES, SqlConstant.KEY_SUBMIT, SqlConstant.KEY_CANCEL).contains(operation)) {
            authLevel = AuthLevelType.schema.getValue();
        } else if (SqlConstant.KEY_DROP.equals(operation) && !SqlConstant.KEY_TABLE.equalsIgnoreCase(sqlAuthModel.getType())) {
            authLevel = AuthLevelType.schema.getValue();
        } else if (sqlParserModel.getAction().isSelectFunction() && !SqlConstant.PACKAGE.equals(sqlAuthModel.getType())) {
            authLevel = AuthLevelType.object.getValue();
            type = AuthConstant.connector + DataNodeType.FUNCTION_SUB.getValue() + AuthConstant.connector; // 函数
        } else if (SqlConstant.KEY_CALL.equals(operation)) {
            authLevel = AuthLevelType.object.getValue();
            if (StringUtils.isNotBlank(paramDTO.getObjectType()) && SqlConstant.KEY_MSSQL_WORK.equalsIgnoreCase(paramDTO.getObjectType())) {
                type = AuthConstant.connector + DataNodeType.WORK.getValue() + AuthConstant.connector; // sqlserver任务
            } else if (SqlConstant.PACKAGE.equals(sqlAuthModel.getType())) {
                type = AuthConstant.connector + DataNodeType.PACKAGE_SUB.getValue() + AuthConstant.connector;
            } else if (SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType())) {
                type = AuthConstant.connector + DataNodeType.FUNCTION_SUB.getValue() + AuthConstant.connector; //函数
            } else {
                type = AuthConstant.connector + DataNodeType.PROCEDURE_SUB.getValue() + AuthConstant.connector; // 存储过程
            }
        } else if (sqlParserModel.getAction().isSelectSequence()) {
            authLevel = AuthLevelType.object.getValue();
            type = AuthConstant.connector + DataNodeType.SEQUENCE_SUB.getValue() + AuthConstant.connector; // 序列
        } else if (sqlParserModel.getAction().isTemplate()) {
            authLevel = AuthLevelType.object.getValue();
            type = AuthConstant.connector + DataNodeType.TEMPLATE_SUB.getValue() + AuthConstant.connector; // 模板
        } else if (sqlParserModel.getAction().isSelectValue() || sqlParserModel.getAction().isDescDatabase()) {
            authLevel = AuthLevelType.schema.getValue();
        } else if (SqlConstant.STATISTICS.equals(operation) && sqlAuthModel.isTable()) {
            authLevel = AuthLevelType.table.getValue();
            type = AuthConstant.connector + DataNodeType.FUNCTION_SUB.getValue() + AuthConstant.connector;
        } else if (Arrays.asList(SqlConstant.KEY_SELECT, SqlConstant.STATISTICS).contains(operation.toUpperCase(Locale.ROOT)) && !sqlAuthModel.isTable()) {
            authLevel = AuthLevelType.object.getValue();
            type = AuthConstant.connector + DataNodeType.VIEW_SUB.getValue() + AuthConstant.connector; // 视图
        } else if (Arrays.asList(SqlConstant.KEY_SELECT, SqlConstant.STATISTICS, SqlConstant.KEY_INSERT,
                SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_DROP, SqlConstant.KEY_TRUNCATE,
                SqlConstant.KEY_EXPORT, SqlConstant.KEY_DESC).contains(operation.toUpperCase(Locale.ROOT))) {
            authLevel = AuthLevelType.table.getValue();
        }

        if (SqlConstant.KEY_VACUUM.equalsIgnoreCase(operation)) {
            if (StringUtils.isEmpty(sqlAuthModel.getName())) {
                authLevel = AuthLevelType.instance.getValue();
            } else {
                authLevel = AuthLevelType.table.getValue();
            }
        }

        if (DatabaseType.REDIS.getValue().equals(paramDTO.getDbType())) {
            authLevel = AuthLevelType.schema.getValue(); // redis最低schema级别
        } else if (DatabaseType.ELASTIC_SEARCH.getValue().equals(paramDTO.getDbType())) {
            if (AuthLevelType.schema.getValue().equals(authLevel) || StringUtils.isBlank(sqlAuthModel.getFullName())) {
                authLevel = AuthLevelType.instance.getValue(); // es没有schema级别
            }
        }

        if (AuthLevelType.other.getValue() == authLevel) {
            return CommonUtil.buildCannotGrantedMap();
        } else {

            if (SqlConstant.KEY_VALUES.equals(operation)) {
                operation = SqlConstant.KEY_SELECT;
                sqlAuthModel.setOperation(operation);
            }

            if (SqlConstant.KEY_COMMENT.equalsIgnoreCase(operation)) {
                operation = SqlConstant.KEY_ALTER; // COMMENT按照ALTER进行快捷申请
                sqlAuthModel.setOperation(operation);
                sqlAuthModel.setDdlSubdivideOperation(GetAuthUtil.needDDLSubdivideAuth(operation, sqlAuthModel.getType()));
            }

            if (StringUtils.isNotBlank(sqlAuthModel.getDdlSubdivideOperation())
                    && CommonUtil.dictionaryContains(webSQLParserInfo.getPermissionsDictionary(), sqlAuthModel.getDdlSubdivideOperation())) {
                operation = sqlAuthModel.getDdlSubdivideOperation(); // 若权限字典有DDL细分,则按照细分权限进行快捷申请
            }

            if (CommonUtil.dictionaryContains(webSQLParserInfo.getPermissionsDictionary(), operation.toLowerCase(Locale.ROOT))) {
                if (DatabaseType.ADBMYSQL2.getValue().equals(paramDTO.getDbType()) && AuthLevelType.object.getValue() == authLevel) {
                    authLevel = AuthLevelType.table.getValue();
                }
                List<Map<String, Object>> noPermissionObjects = new ArrayList<>();
                Map<String, Object> map;
                if (SqlConstant.STATISTICS.equals(operation)) {
                    map = buildActionNameKey(operation, authLevel, paramDTO.getConnectId(), sqlAuthModel.getSchemaUniqueKey(), sqlAuthModel.getStaFuncForObjectName(), type);
                } else if (SqlConstant.PACKAGE.equals(sqlAuthModel.getType()) && !StringUtils.isEmpty(sqlAuthModel.getAuthSchemaIdForPackage()) && !sqlAuthModel.getAuthSchemaIdForPackage().equals(sqlAuthModel.getSchemaUniqueKey())) {
                    map = buildActionNameKey(operation, authLevel, paramDTO.getConnectId(), sqlAuthModel.getAuthSchemaIdForPackage(), sqlAuthModel.getFullName(), type);
                } else {
                    map = buildActionNameKey(operation, authLevel, paramDTO.getConnectId(), sqlAuthModel.getSchemaUniqueKey(), sqlAuthModel.getFullName(), type);
                }
                if (paramDTO.getOidList() != null && !paramDTO.getOidList().isEmpty()) {
                    map.put("oid", paramDTO.getOidList());
                }
                noPermissionObjects.add(map);
                return noPermissionObjects;
            } else {
                return CommonUtil.buildCannotGrantedMap();
            }

        }
    }

    private static Map<String, Object> buildActionNameKey(String operation, int authLevel, String instanceUniqueKey,
                                                          String schemaUniqueKey, String name, String type) {
        Map<String, Object> map = new HashMap<>();

        String actionNameKey = "";
        String uuid = "";

        if (AuthLevelType.instance.getValue() == authLevel) {
            actionNameKey = getAuthToken(operation, AuthLevelType.instance.getValue());
            uuid = instanceUniqueKey;
        } else if (AuthLevelType.schema.getValue() == authLevel) {
            actionNameKey = getAuthToken(operation, AuthLevelType.schema.getValue());
            uuid = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey;
        } else if (AuthLevelType.table.getValue() == authLevel) {
            actionNameKey = getAuthToken(operation, AuthLevelType.table.getValue());
            uuid = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + DataNodeType.TABLE.getValue() + AuthConstant.connector + name;
        } else if (AuthLevelType.object.getValue() == authLevel) {
            actionNameKey = getAuthToken(operation, AuthLevelType.object.getValue());
            uuid = instanceUniqueKey + AuthConstant.connector + schemaUniqueKey + type + name;
        }

        map.put("actionNameKey", actionNameKey);
        map.put("uuid", uuid);

        return map;
    }

    private static String getAuthToken(String operation, Integer level) {
        String authToken = "";
        String authLevelName = "";

        if (AuthLevelType.instance.getValue().equals(level)) {
            authLevelName = AuthLevelType.instance.getName();
        } else if (AuthLevelType.schema.getValue().equals(level)) {
            authLevelName = AuthLevelType.schema.getName();
        } else if (AuthLevelType.table.getValue().equals(level)) {
            authLevelName = AuthLevelType.table.getName();
        } else if (AuthLevelType.object.getValue().equals(level)) {
            authLevelName = AuthLevelType.object.getName();
        } else if (AuthLevelType.table_group.getValue().equals(level)) {
            authLevelName = AuthLevelType.table_group.getName();
        }

        operation = operation.toLowerCase(Locale.ROOT);

        // 权限转换
        if (Arrays.asList("values", "exec", "execute").contains(operation)) {
            operation = "call";
        } else if ("describe".equalsIgnoreCase(operation)) {
            operation = "desc";
        } else if (Arrays.asList("load", "replace", "import").contains(operation)) {
            operation = OperationAuthConstant.insert;
        }

        if (!authLevelName.isEmpty()) {
            authToken = operation + "_" + authLevelName + "_auth";
        }

        return authToken;
    }

    private static List<String> getOperationAuthActionKey(String preOperation, SqlParseModel sqlParserModel, boolean needTableGroup) {
        List<String> authList = new ArrayList<>();

        GetAuthUtil.addMaskAuthList(authList);

        String operation = sqlParserModel.getOperation().toLowerCase(Locale.ROOT);

        // ------- 权限转换 -------
        if (StringUtils.isNotBlank(preOperation) && !"/".equalsIgnoreCase(sqlParserModel.getOperation())) {
            operation = preOperation.toLowerCase(Locale.ROOT); // oracle定时任务job的权限
        }
        if (Arrays.asList("values", "exec", "execute").contains(operation)) {
            operation = OperationAuthConstant.call;
        } else if ("describe".equalsIgnoreCase(operation)) {
            operation = OperationAuthConstant.desc;
        } else if (Arrays.asList("load", "replace", "import").contains(operation)) {
            operation = OperationAuthConstant.insert;
        }

        addOperationAuthList(authList, operation, needTableGroup);

        // schema负责人的权限
        authList.add(getAuthToken(OperationAuthConstant.leader_role, AuthLevelType.schema.getValue()));

        // 如果是select操作，或者是语句中有函数并且是统计范围内的函数，则可以通过statistics授权。（二者并不冲突）（都有 or 有一个 or 都没有）
        Set<String> noFilterFunctions = sqlParserModel.getAction().getFunctions(); //原始函数集合。
        if (OperationAuthConstant.select.equals(operation) ||
                (noFilterFunctions != null && noFilterFunctions.stream()
                        .anyMatch(function -> Arrays.stream(GrantConstant.FunTarget).map(GrantType::getValue).anyMatch(value -> value.equalsIgnoreCase(function))))) {

            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.table.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.object.getValue()));
            if (needTableGroup) {
                authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.table_group.getValue()));
            }
        }

        if (!CollectionUtils.isEmpty(noFilterFunctions)) {
            authList.add(getAuthToken(OperationAuthConstant.call, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.call, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.call, AuthLevelType.object.getValue()));
        }

        // select、show、desc、call需要验证导出权限
        if (Arrays.asList(OperationAuthConstant.select, OperationAuthConstant.show, OperationAuthConstant.desc, OperationAuthConstant.call).contains(operation)) {
            authList.add(getAuthToken(OperationAuthConstant.export, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.export, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.export, AuthLevelType.table.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.export, AuthLevelType.object.getValue()));
            if (needTableGroup) {
                authList.add(getAuthToken(OperationAuthConstant.export, AuthLevelType.table_group.getValue()));
            }
        }

        // comment 需要验证create/alter权限
        if (SqlConstant.KEY_COMMENT.equalsIgnoreCase(operation)) {
            authList.add(getAuthToken(OperationAuthConstant.alter, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.alter, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.create, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.create, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.create_table, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.create_table, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.alter_table, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.alter_table, AuthLevelType.schema.getValue()));
        }

        // 同一个sql需要两种权限判定,如 insert into select...
        if (sqlParserModel.getAction().isSelectInto() || sqlParserModel.getAction().isInsertIntoSelect() || sqlParserModel.getAction().isMerge()) {
            authList.add(getAuthToken(OperationAuthConstant.select, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.select, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.select, AuthLevelType.table.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.select, AuthLevelType.object.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.table.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.statistics, AuthLevelType.object.getValue()));
        }
        //如果是merge into语句，需要查询用户的update delete insert call select statistics...等权限，select statistics字符串在上面已经添加了
        if (sqlParserModel.getAction().isMerge()) {
            //update。无object
            authList.add(getAuthToken(OperationAuthConstant.update, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.update, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.update, AuthLevelType.table.getValue()));
            //delete。无object
            authList.add(getAuthToken(OperationAuthConstant.delete, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.delete, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.delete, AuthLevelType.table.getValue()));
            //insert。无object
            authList.add(getAuthToken(OperationAuthConstant.insert, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.insert, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.insert, AuthLevelType.table.getValue()));
            //call。无table
            authList.add(getAuthToken(OperationAuthConstant.call, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.call, AuthLevelType.schema.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.call, AuthLevelType.object.getValue()));
        }

        //TODO 如果是select into，需要添加create table相关。待优化。
        if (sqlParserModel.getAction().isSelectInto()) {
            authList.add(getAuthToken(OperationAuthConstant.create_table, AuthLevelType.instance.getValue()));
            authList.add(getAuthToken(OperationAuthConstant.create_table, AuthLevelType.schema.getValue()));
        }

        if (sqlParserModel.getAction().getNeedOtherOperations() != null) {
            for (String otherOperation : sqlParserModel.getAction().getNeedOtherOperations()) {
                addOperationAuthList(authList, otherOperation, false);
            }
        }

        return authList;
    }

    private static void addOperationAuthList(List<String> authList, String operation, boolean needTableGroup) {
        List<String> instanceAuth = Arrays.asList(OperationAuthConstant.instance_auth);
        List<String> schemaAuth = Arrays.asList(OperationAuthConstant.schema_auth);
        List<String> tableAuth = Arrays.asList(OperationAuthConstant.table_auth);
        List<String> tableGroupAuth = Arrays.asList(OperationAuthConstant.table_group_auth);
        List<String> objectAuth = Arrays.asList(OperationAuthConstant.object_auth);

        if (instanceAuth.contains(operation)) {
            authList.add(getAuthToken(operation, AuthLevelType.instance.getValue()));
        }
        if (schemaAuth.contains(operation)) {
            authList.add(getAuthToken(operation, AuthLevelType.schema.getValue()));
        }
        if (tableAuth.contains(operation)) {
            authList.add(getAuthToken(operation, AuthLevelType.table.getValue()));
        }
        if (needTableGroup && tableGroupAuth.contains(operation)) {
            authList.add(getAuthToken(operation, AuthLevelType.table_group.getValue()));
        }
        if (objectAuth.contains(operation)) {
            authList.add(getAuthToken(operation, AuthLevelType.object.getValue()));
        }
    }

    private List<InstanceRole> getInstanceRole(List<String> operationActionKeys) {
        return groupUserMapper.getInstanceRole()
                .stream()
                .filter(instanceRole -> instanceRole.getOperate_rule().equals(2)
                        || CollectionUtils.containsAny(operationActionKeys, instanceRole.getActionKeyList())
                        || Arrays.asList(1, 2).contains(instanceRole.getSensitive_rule()))
                .collect(Collectors.toList());
    }

    private static List<String> getOIDName(ParserParamDto paramDTO, SqlParseModel sqlParserModel, String operation,
                                           String objectName, String schemaName) {
        List<String> list = new ArrayList<>();

        try {
            if (Arrays.asList("values", "exec", "execute").contains(operation)) {
                operation = OperationAuthConstant.call;
            }

            if (SqlConstant.KEY_CALL.equalsIgnoreCase(operation) || sqlParserModel.getAction().isSelectFunction()) {
                if (DatabaseType.getPGSqlIntegerValueList().contains(paramDTO.getDbType())) {
                    List<String> postGreSqlFunctionOid = ExecuteSqlUtil.getPostGreSqlFunctionOid(paramDTO, schemaName, objectName, sqlParserModel.getAction().getFuncArgs());
                    if (postGreSqlFunctionOid.size() > 0) {
                        paramDTO.setOidList(postGreSqlFunctionOid);
                        for (String oid : postGreSqlFunctionOid) {
                            list.add((objectName + "[oid:" + oid + "]").toUpperCase(Locale.ROOT));
                        }
                    }
                } else if (CommonUtil.useColonSplit(paramDTO.getDbType())) {
                    List<String> informixOid = ExecuteSqlUtil.getInformixFunctionOid(paramDTO, schemaName, objectName);
                    if (informixOid.size() > 0) {
                        paramDTO.setOidList(informixOid);
                        for (String oid : informixOid) {
                            list.add((objectName + "[oid:" + oid + "]").toUpperCase(Locale.ROOT));
                        }
                    }
                } else if (Arrays.asList(DatabaseType.DB2.getValue(), DatabaseType.DB2AS400.getValue()).contains(paramDTO.getDbType())) {
                    List<String> db2Oid = ExecuteSqlUtil.getDB2ProcedureOid(paramDTO, schemaName, objectName, sqlParserModel.getOperation());
                    if (db2Oid.size() > 0) {
                        paramDTO.setOidList(db2Oid);
                        for (String oid : db2Oid) {
                            list.add((objectName + "[oid:" + oid + "]").toUpperCase(Locale.ROOT));
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("get oid name error!", e);
        }

        return list;
    }

    private static void sortSqlAuthModelList(SqlParseModel sqlParserModel, ParserParamDto paramDTO) {
        if (sqlParserModel == null || CollectionUtils.isEmpty(sqlParserModel.getSqlAuthModelList())) {
            return;
        }
        sqlParserModel.setSqlAuthModelList(new LinkedList<>(sqlParserModel.getSqlAuthModelList()));
        LinkedList<SqlAuthModel> list = (LinkedList<SqlAuthModel>) sqlParserModel.getSqlAuthModelList();
        int size = list.size();
        int lastIndex = size - 1;
        int i = 0; //单指针
        while (i < lastIndex) {
            SqlAuthModel currentModel = list.get(i);
            if (SqlConstant.KEY_CALL.equalsIgnoreCase(currentModel.getOperation()) || SqlConstant.STATISTICS.equalsIgnoreCase(currentModel.getOperation())) {
                // 将符合条件的元素移到链表末尾
                list.addLast(currentModel);
                list.remove(i);
                lastIndex--;
            } else {
                i++;
            }
        }
    }

    private static boolean isWhiteTable(Integer dbType, String tableName, Set<String> functions, String operation, SqlAuthModel sqlAuthModel) {
        boolean dualIsWhite = DatabaseType.getDualWhiteList().contains(DatabaseType.of(dbType));
        if (!dualIsWhite) {
            return false;
        }
        if (SqlConstant.KEY_TABLE_DUAL.equalsIgnoreCase(tableName) && CollectionUtils.isEmpty(functions)) {
            return true; // 白名单表
        }
        if (SqlConstant.KEY_TABLE_DUAL.equalsIgnoreCase(tableName) || tableName.endsWith(CommonUtil.useColonSplit(dbType) ? ":" : ".")) {
            return true;
        }
        if (SqlConstant.STATISTICS.equalsIgnoreCase(operation) && SqlConstant.KEY_TABLE_DUAL.equalsIgnoreCase(sqlAuthModel.getStaFuncForObjectName())) {
            return true;
        }
        return DatabaseType.DB2.getValue().equals(dbType) &&
                ParserConstant.DB2_SYSIBM_SCHEMA.equalsIgnoreCase(sqlAuthModel.getSchemaName()) &&
                (SqlConstant.KEY_TABLE_SYSDUMMY1.equalsIgnoreCase(tableName) || (SqlConstant.STATISTICS.equalsIgnoreCase(operation) && SqlConstant.KEY_TABLE_SYSDUMMY1.equalsIgnoreCase(sqlAuthModel.getStaFuncForObjectName())));
    }

    private static boolean isCallOrStatistics(String operation) {
        return List.of(OperationAuthConstant.call, OperationAuthConstant.statistics).contains(operation.toLowerCase(Locale.ROOT));
    }

    private static List<String> getAuthsForTableCreator() {
        return OperationAuthConstant.authsForTableCreator;
    }

    private static boolean isTableCreator(String instanceUniKey, String catalogName, String schemaName, String tableName, String userId) {
        String tableCreatorUserId = Resource.getBeanRequireNonNull(DBPrivateTableMapper.class)
                .getTableCreatorUserId(new DBPrivateTable(instanceUniKey, catalogName, schemaName, tableName));
        boolean isTableCreator = tableCreatorUserId != null && tableCreatorUserId.equals(userId);
        return isTableCreator;
    }

    private static boolean isMeaninglessForNoSqlAuthModels(SqlParseModel model) {
        SqlActionModel action = model.getAction();
        if (SqlConstant.KEY_SELECT.equalsIgnoreCase(model.getOperation()) && action.isSelectValue()) {
            return true;
        }
        return false;
    }

}
