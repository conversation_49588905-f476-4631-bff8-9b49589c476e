package com.dc.parser.util;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Map;

public class MapValueUtils {

    public static Object getIgnoreCase(Map<String, Object> map, String key) {
        return getIgnoreCase(map, key, true);
    }

    public static Object getIgnoreCase(Map<String, Object> map, String key, boolean ignoreNull) {

        if (MapUtils.isEmpty(map) || StringUtils.isEmpty(key)) {
            return ignoreNull ? "" : null;
        }
        Object upperValue = map.get(key.toUpperCase(Locale.ROOT));
        Object lowerValue = map.get(key.toLowerCase(Locale.ROOT));

        return upperValue != null ? upperValue : lowerValue != null ? lowerValue : ignoreNull ? "" : null;
    }

}
