package com.dc.parser.component;

import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleContent;
import com.dc.parser.sensitive.model.ColumnModel;
import com.dc.repository.mysql.column.SchemaInfo;
import com.dc.repository.mysql.model.*;
import com.dc.repository.redis.model.*;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.parser.ParserCacheMessage;
import com.dc.springboot.core.model.parser.ParserCheckMessage;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.*;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.utils.model.ActionModel;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring")
public interface ParserMapper {

    ParserMapper INSTANCE = Mappers.getMapper(ParserMapper.class);

    ColumnModel copyColumnModel(ColumnModel o);

    DatabaseConnectionDto toDatabaseConnectionDto(DatabaseConnection connection);

    UserDto toUserDto(User user);

    SchemaDto toSchemaDto(Schema schema);

    List<PermissionRuleDto> toPermissionRuleDtoList(List<PermissionRule> permissionRuleList);

    @Mapping(target = "testSql", ignore = true)
    @Mapping(target = "testConnectionConfiguration", ignore = true)
    @Mapping(target = "keepAliveInterval", ignore = true)
    @Mapping(target = "ignore", ignore = true)
    @Mapping(target = "executeEvent", ignore = true)
    @Mapping(target = "download", ignore = true)
    @Mapping(target = "connectionType", ignore = true)
    @Mapping(target = "connectionConfiguration", ignore = true)
    @Mapping(target = "closeIdleInterval", ignore = true)
    @Mapping(target = "charset", ignore = true)
    ConnectionConfig toConnectionConfig(EnvConnection connection);

    ParamsConfigDto toParamsConfigDto(EnvParamsConfig paramsConfig);

    ReviewConfigDto toReviewConfigDto(EnvReviewConfig reviewConfig);

    ParserParamDto copyParserParamDto(ParserParamDto parserParamDto);

    Map<String, InstanceRoleDto> toInstanceRoleDtoMap(Map<String, InstanceRole> instanceRoleMap);

    @Mapping(target = "value", ignore = true)
    @Mapping(target = "sqlRule", ignore = true)
    @Mapping(target = "uniqueKey", source = "nameKey")
    @Mapping(target = "ruleName", source = "name")
    @Mapping(target = "ruleDesc", source = "desc")
    @Mapping(target = "paramsMap", expression = "java(buildParamsMap(sqlAuditRules.getParams()))")
    CheckRuleContent toCheckRuleContents(SqlAuditRules sqlAuditRules);

    default Map<String, String> buildParamsMap(String params) {
        if (StringUtils.isBlank(params)) {
            return new LinkedHashMap<>();
        }
        Map<String, String> resultMap = new LinkedHashMap<>();
        for (Map<String, Object> map : JSON.parseObject(params, new TypeReference<List<Map<String, Object>>>() {})) {
            if (map.get("key") != null && map.get("value") != null) {
                resultMap.put(map.get("key").toString(), map.get("value").toString());
            }
        }
        return resultMap;
    }


    List<CheckResultDto> toCheckResultDto(List<CheckResult> check);

    SqlActionModel copySqlActionModel(SqlActionModel sqlActionModel);

    @Mapping(target = "token", ignore = true)
    @Mapping(target = "tableId", ignore = true)
    @Mapping(target = "schemaName", ignore = true)
    @Mapping(target = "schema", ignore = true)
    @Mapping(target = "peUserAuths", ignore = true)
    @Mapping(target = "oidList", ignore = true)
    @Mapping(target = "instance", ignore = true)
    @Mapping(target = "frameworkName", ignore = true)
    @Mapping(target = "executionContext", ignore = true)
    @Mapping(target = "charset", ignore = true)
    @Mapping(target = "catalogUniqueKey", ignore = true)
    @Mapping(target = "authMap", ignore = true)
    @Mapping(target = "authDtoList", ignore = true)
    ParserParamDto toParserParamDto(ParserCheckMessage preCheckParam);

    @Mapping(target = "userId", source = "env.user.userId")
    @Mapping(target = "tableId", ignore = true)
    @Mapping(target = "symbol", source = "env.paramsConfig.symbol")
    @Mapping(target = "sql", ignore = true)
    @Mapping(target = "schemaName", ignore = true)
    @Mapping(target = "schemaId", ignore = true)
    @Mapping(target = "roleAuth", ignore = true)
    @Mapping(target = "peUserAuths", ignore = true)
    @Mapping(target = "orderMaskLevel", ignore = true)
    @Mapping(target = "operationAuth", ignore = true)
    @Mapping(target = "oidList", ignore = true)
    @Mapping(target = "maskAuthObject", ignore = true)
    @Mapping(target = "maskAuth", ignore = true)
    @Mapping(target = "isAccountConn", source = "env.connection.isAccountConn")
    @Mapping(target = "instanceRoleMap", ignore = true)
    @Mapping(target = "instance", expression = "java(toDatabaseConnectionDto(env.getConnection()))")
    @Mapping(target = "frameworkName", ignore = true)
    @Mapping(target = "executionContext", ignore = true)
    @Mapping(target = "enableDesensiteType", source = "env.paramsConfig.enableDesensiteType")
    @Mapping(target = "editing", ignore = true)
    @Mapping(target = "dblinkMap", ignore = true)
    @Mapping(target = "dbType", source = "env.connection.databaseType")
    @Mapping(target = "currentPattern", source = "env.connection.connectionPattern")
    @Mapping(target = "connectId", source = "env.connection.connectionId")
    @Mapping(target = "charset", source = "env.schema.charset")
    @Mapping(target = "catalogUniqueKey", ignore = true)
    @Mapping(target = "authMap", ignore = true)
    @Mapping(target = "authDtoList", ignore = true)
    @Mapping(target = "auditOrigin", ignore = true)
    @Mapping(target = "accountId", source = "env.connection.accountId")
    @Mapping(target = "account", source = "env.connection.userName")
    @Mapping(target = "catalogName", ignore = true)
    @Mapping(target = "schema", ignore = true)
    ParserParamDto toParserParamDto(ParserCacheMessage message, Env env);

    @Mapping(target = "uniqueKey", source = "userId")
    @Mapping(target = "roleIds", ignore = true)
    @Mapping(target = "platform", ignore = true)
    @Mapping(target = "phone", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "openId", ignore = true)
    @Mapping(target = "isNew", ignore = true)
    @Mapping(target = "isFirstLogin", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    @Mapping(target = "gender", ignore = true)
    @Mapping(target = "extra", ignore = true)
    @Mapping(target = "email", ignore = true)
    @Mapping(target = "createMode", ignore = true)
    UserDto toUserDto(EnvUser user);

    @Mapping(target = "unique_key", source = "schema.schemaId")
    @Mapping(target = "schema_name", source = "schema.schemaName")
    @Mapping(target = "db_type", source = "connection.databaseType")
    @Mapping(target = "table_count", ignore = true)
    @Mapping(target = "is_sys", source = "schema.isSys")
    @Mapping(target = "is_delete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "gmt_modified", ignore = true)
    @Mapping(target = "gmt_create", ignore = true)
    @Mapping(target = "def_dbo_name", source = "schema.defDboName")
    @Mapping(target = "connect_id", source = "connection.connectionId")
    @Mapping(target = "catalog_name", source = "schema.catalogName")
    SchemaDto toSchemaDto(EnvSchema schema, EnvConnection connection);

    @Mapping(target = "version", ignore = true)
    @Mapping(target = "username", source = "userName")
    @Mapping(target = "unique_key", source = "connectionId")
    @Mapping(target = "tenant", ignore = true)
    @Mapping(target = "service_name", source = "serviceName")
    @Mapping(target = "security_rule_set_id", source = "securityRuleSetId")
    @Mapping(target = "port", source = "hostPort")
    @Mapping(target = "password", source = "userPassword")
    @Mapping(target = "kingbase_database_mode", ignore = true)
    @Mapping(target = "jdbc_url", ignore = true)
    @Mapping(target = "is_delete", ignore = true)
    @Mapping(target = "is_dblink", ignore = true)
    @Mapping(target = "is_active", ignore = true)
    @Mapping(target = "ip", source = "hostName")
    @Mapping(target = "instance_name", source = "instanceName")
    @Mapping(target = "id", source = "connectIdHash")
    @Mapping(target = "gmt_modified", ignore = true)
    @Mapping(target = "gmt_create", ignore = true)
    @Mapping(target = "extended_attributes", ignore = true)
    @Mapping(target = "executor", ignore = true)
    @Mapping(target = "entity", ignore = true)
    @Mapping(target = "driver_properties", ignore = true)
    @Mapping(target = "driver_id", ignore = true)
    @Mapping(target = "domain", ignore = true)
    @Mapping(target = "dblink_name", ignore = true)
    @Mapping(target = "db_type", source = "databaseType")
    @Mapping(target = "db_role", ignore = true)
    @Mapping(target = "db_name", ignore = true)
    @Mapping(target = "db_id", ignore = true)
    @Mapping(target = "connection_desc", source = "connectionDesc")
    @Mapping(target = "connection", ignore = true)
    @Mapping(target = "connect_type", ignore = true)
    @Mapping(target = "connect_mode", ignore = true)
    @Mapping(target = "config", ignore = true)
    @Mapping(target = "cluster", ignore = true)
    @Mapping(target = "category_id", source = "categoryId")
    @Mapping(target = "authentication_parameters", ignore = true)
    @Mapping(target = "auth_source", ignore = true)
    DatabaseConnectionDto toDatabaseConnectionDto(EnvConnection connection);

    @Mapping(target = "whereClause", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "useDatabase", ignore = true)
    @Mapping(target = "token", ignore = true)
    @Mapping(target = "tableId", ignore = true)
    @Mapping(target = "symbol", ignore = true)
    @Mapping(target = "sqlList", ignore = true)
    @Mapping(target = "sql", ignore = true)
    @Mapping(target = "schemaName", source = "schema_name")
    @Mapping(target = "schemaId", source = "unique_key")
    @Mapping(target = "schema", ignore = true)
    @Mapping(target = "roleAuth", ignore = true)
    @Mapping(target = "requestTime", ignore = true)
    @Mapping(target = "recycleBinSql", ignore = true)
    @Mapping(target = "preOperation", ignore = true)
    @Mapping(target = "peUserAuths", ignore = true)
    @Mapping(target = "origin", ignore = true)
    @Mapping(target = "orderMaskLevel", ignore = true)
    @Mapping(target = "operationAuth", ignore = true)
    @Mapping(target = "oidList", ignore = true)
    @Mapping(target = "offset", ignore = true)
    @Mapping(target = "objectType", ignore = true)
    @Mapping(target = "maskAuthObject", ignore = true)
    @Mapping(target = "maskAuth", ignore = true)
    @Mapping(target = "limit", ignore = true)
    @Mapping(target = "isVerify", ignore = true)
    @Mapping(target = "isAccountConn", ignore = true)
    @Mapping(target = "instanceRoleMap", ignore = true)
    @Mapping(target = "instance", ignore = true)
    @Mapping(target = "frameworkName", source = "def_dbo_name")
    @Mapping(target = "executionContext", ignore = true)
    @Mapping(target = "enableDesensiteType", ignore = true)
    @Mapping(target = "editing", ignore = true)
    @Mapping(target = "dblinkMap", ignore = true)
    @Mapping(target = "dbType", ignore = true)
    @Mapping(target = "currentPattern", ignore = true)
    @Mapping(target = "connectId", ignore = true)
    @Mapping(target = "catalogUniqueKey", source = "schema.pid")
    @Mapping(target = "catalogName", source = "catalog_name")
    @Mapping(target = "authMap", ignore = true)
    @Mapping(target = "authList", ignore = true)
    @Mapping(target = "authDtoList", ignore = true)
    @Mapping(target = "auditOrigin", ignore = true)
    @Mapping(target = "accountId", ignore = true)
    @Mapping(target = "account", ignore = true)
    void updateParserParamDto(@MappingTarget ParserParamDto dto, SchemaDto schema);

    List<ActionModelDto> toActionModelList(List<ActionModel> actionModels);

    OrderSchemaTask copy(OrderSchemaTask task);

}
