package com.dc.parser.sensitive.model;

import com.dc.type.DatabaseType;

//@Data
public class SchemaModel {

    //3f Schema
    private String extDatabase;
    private String extDboSchema;
    /**
     * @Deprecated
     **/
    private String extTable;
    private String prevTokenInChain;  //点符号内联
    private String nextTokenInChain;  //点符号内联


    public String generate3fSchemaTableName(Integer dbType, String dboSchema, String tokenInChain, String tableName) {
        if (null != dbType && null != tableName && null != dboSchema && DatabaseType.getIdentCode(DatabaseType.get3FDatabaseTypeList()).contains(dbType)) {
            return String.format("%s%s%s", dboSchema, tokenInChain, tableName);
        }
        return tableName;
    }


    public String getExtDatabase() {
        return extDatabase;
    }

    public void setExtDatabase(String extDatabase) {
        this.extDatabase = extDatabase;
    }

    public String getExtDboSchema() {
        return extDboSchema;
    }

    public void setExtDboSchema(String extDboSchema) {
        this.extDboSchema = extDboSchema;
    }

    public String getExtTable() {
        return extTable;
    }

    public void setExtTable(String extTable) {
        this.extTable = extTable;
    }

    public String getPrevTokenInChain() {
        return prevTokenInChain;
    }

    public void setPrevTokenInChain(String prevTokenInChain) {
        this.prevTokenInChain = prevTokenInChain;
    }

    public String getNextTokenInChain() {
        return nextTokenInChain;
    }

    public void setNextTokenInChain(String nextTokenInChain) {
        this.nextTokenInChain = nextTokenInChain;
    }
}
