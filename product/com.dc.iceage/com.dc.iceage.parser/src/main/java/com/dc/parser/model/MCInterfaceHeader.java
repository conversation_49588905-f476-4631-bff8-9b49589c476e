package com.dc.parser.model;

public class MCInterfaceHeader {

    private String api_getway; // 请求mc获取权限的地址
    private String user_token; // 用户token,请求接口使用
    private String request_id; // uuid,请求接口使用
    private String authorization; // 内部网关 basic
    private String identity; // 内部网关 用户信息

    public MCInterfaceHeader(String api_getway, String user_token, String request_id, String authorization, String identity) {
        this.api_getway = api_getway;
        this.user_token = user_token;
        this.request_id = request_id;
        this.authorization = authorization;
        this.identity = identity;
    }

    public String getApi_getway() {
        return api_getway;
    }

    public void setApi_getway(String api_getway) {
        this.api_getway = api_getway;
    }

    public String getUser_token() {
        return user_token;
    }

    public void setUser_token(String user_token) {
        this.user_token = user_token;
    }

    public String getRequest_id() {
        return request_id;
    }

    public void setRequest_id(String request_id) {
        this.request_id = request_id;
    }

    public String getAuthorization() {
        return authorization;
    }

    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }
}
