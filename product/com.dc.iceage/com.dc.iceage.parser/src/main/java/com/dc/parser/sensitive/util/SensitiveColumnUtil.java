package com.dc.parser.sensitive.util;

import com.dc.parser.sensitive.constants.EPSConstants;
import com.dc.parser.sensitive.model.CheckObject;
import com.dc.parser.sensitive.model.ColumnModel;
import com.dc.parser.type.ColAsciiType;
import com.dc.parser.type.SensitiveDataType;
import com.dc.springboot.core.model.sensitive.DataMask;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class SensitiveColumnUtil {

    public static final String SENSITIVE_WORD_REGEX = "(?i)({table}\\.)*{column}$";


    /**
     * 列名特殊符号转义
     *
     * @param str
     * @return
     */
    public static String strEncode(String str) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < str.length(); ++i) {
            char c = str.charAt(i);
            for (ColAsciiType ascii : ColAsciiType.values()) {
                if (ascii.getValue().charAt(0) == c) {
                    sb.append(ColAsciiType.ASCII_5C.getValue());
                    break;
                }
            }
            sb.append(c);
        }
        return sb.toString();
    }

    public static String replace(String name) {
        if (!StringUtils.isEmpty(name)) {
            return name.replaceAll("\"|'|`|\\[|\\]", "");
        }
        return null;
    }

    public static CheckObject check(List<DataMask> list, ColumnModel column) {

        if (null != list && list.size() > 0) {

            List<CheckObject> checkObjectList = new ArrayList<>();

            for (String ext : column.values()) {

                if (StringUtils.isEmpty(ext)) {
                    continue;
                }

                //fix:FROM 子查询 SELECT *
                if ((EPSConstants.KEYWORD_STAR.equals(ext))) {
                    // select NAME as N from (select * from (select *  from tab ) a) b
                    //fix：子查询为 select * 情形 匹配所有脱敏列
                    list.stream().filter(e -> e.getTableName().equals(column.getExtTable())).forEach(e -> {
                        if (e.getTableName().equals(column.getExtTable())) {
                            CheckObject reg = new CheckObject(false);
                            reg.setExtTable(column.getExtTable());
                            //fix1：SqlParserUtil.replace 用于 清除语法符号 例如 `name`
                            reg.setExtColumn(SensitiveColumnUtil.replace(e.getColumnName()));
                            //当子查询为SELECT *: select name from (select * from tab) b  开启使用别名
                            checkObjectList.add(reg);
                        }
                    });
                } else {

                    CheckObject reg = new CheckObject(false);
                    reg.setExtTable(column.getExtTable());
                    //fix1：SqlParserUtil.replace 用于 清除语法符号 例如 `name`
                    reg.setExtColumn(SensitiveColumnUtil.replace(ext));
                    checkObjectList.add(reg);
                }

            }


            for (CheckObject ck : checkObjectList) {

                String regexString = generateRegex(ck.getExtTable(), ck.getExtColumn());
                ck.setRegexString(regexString);

                if (StringUtils.isEmpty(regexString)) {
                    continue;
                }

                for (DataMask cfg : list) {
                    String tx = cfg.getTableName() + EPSConstants.TOKEN_IN_CHAIN + cfg.getColumnName();
                    //脱敏规则不为空
                    if (SensitiveDataType.FULL.getValue() == cfg.getAuthLevel() && null != cfg.getAlgorithmType() && null != cfg.getAlgorithmParam()) {
                        if (Pattern.matches(ck.getRegexString(), SensitiveColumnUtil.replace(tx))) {
                            ck.setMaskColumn(true);
                            return ck;
                        }
                    }
                }
            }
        }
        return new CheckObject(false);
    }

    private static String generateRegex(String table, String column) {
        if (!StringUtils.isEmpty(table) && !StringUtils.isEmpty(column)) {
            //fix:  列可能包含符号 ： name||id  或者  (name)
            return SENSITIVE_WORD_REGEX.replace("{table}", table).replace("{column}", SensitiveColumnUtil.strEncode(column));
        }
        return null;
    }


}
