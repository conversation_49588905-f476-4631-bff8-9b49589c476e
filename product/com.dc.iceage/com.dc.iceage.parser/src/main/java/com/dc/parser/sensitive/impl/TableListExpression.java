package com.dc.parser.sensitive.impl;

import com.dc.parser.sensitive.constants.EPSConstants;
import com.dc.parser.sensitive.model.CheckObject;
import com.dc.parser.sensitive.model.ColumnModel;
import com.dc.parser.sensitive.model.SchemaModel;
import com.dc.parser.sensitive.util.SensitiveColumnUtil;
import com.dc.parser.sensitive.util.TExpressionUtil;
import com.dc.sqlparser.types.ESqlClause;
import com.dc.sqlparser.nodes.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

//@Slf4j
public class TableListExpression implements SQLExpression {

    private static Logger log = LoggerFactory.getLogger(TableListExpression.class);

    private SchemaModel schemaModel = null;

    private StatementContext context;

    @Override
    public void interpret(StatementContext ctx) {

        this.context = ctx;

        List<TParseTreeNode> nodes = ctx.getNodes();

        for (TParseTreeNode treeNode : nodes) {

            TTableList tables = null;
            if (null != treeNode && treeNode instanceof TTableList) {
                tables = (TTableList) treeNode;
            }

            if (null != tables && tables.size() > 0) {

                TObjectNameList linkedColumns = null;
                TObjectNameList objectNameReferences = null;

                for (TTable tab : tables) {

                    if (null == tab) {
                        continue;
                    }

                    this.schemaModel = TExpressionUtil.getSchema(tab.getTableName(),
                            ctx.getConfiguration().getDbType(),
                            ctx.getConfiguration().getDboSchemaName()
                    );

                    //用例1： select name , password from tab t1, tab2 t2; 此处勉强让tab2 公用tab1 的 getObjectNameReferences
                    if (null != tab.getObjectNameReferences() && tab.getObjectNameReferences().size() > 0) {
                        objectNameReferences = tab.getObjectNameReferences();
                    }
                    if (null != tab.getLinkedColumns() && tab.getLinkedColumns().size() > 0) {
                        linkedColumns = tab.getLinkedColumns();
                    }

                    this.handle(objectNameReferences, linkedColumns);
                }
            }
        }
    }


    public void handle(TObjectNameList... list) {

        for (TObjectNameList columns : list) {

            if (null != columns && columns.size() > 0) {

                for (TObjectName object : columns) {

                    //fix bug 17171 ： filter ESqlClause.selectList ，避免脱敏掉条件
                    if (null != object && ESqlClause.selectList.equals(object.getLocation())
                            && null != object.getDbObjectType()
                            && "column".equals(object.getDbObjectType().name())) {

                        ColumnModel column = new ColumnModel();
                        column.setExtColumn(object.getColumnNameOnly());
                        //判断是否已经有别名
                        TAliasClause aliasClauseFromEndToken = TExpressionUtil.getAliasClauseFromEndToken(object.getEndToken());
                        if (null != aliasClauseFromEndToken) {
                            //FiXME 如果将来需要使用这个别名需要过滤一下特殊标点符号，雷同列的取值方式
                            column.setExtAlias(aliasClauseFromEndToken.toString());
                        }
                        column.setExtTable(this.schemaModel.getExtTable());
                        column.setExtScript(object.toString());
                        // log.debug("link (extTable={}, extColumn={}, extAlias={}, extScript={})", column.getExtTable(), column.getExtColumn(), column.getExtAlias(), column.getExtScript());

                        CheckObject check = SensitiveColumnUtil.check(context.getConfiguration().getConfigs(), column);
                        if (check.isMaskColumn() && !EPSConstants.KEYWORD_STAR.equals(column.getExtColumn())) {
                            String style = TExpressionUtil.invokeStr(context.getSymbol());
                            //fix1：name 使用别名：select a.password , name from (select *  from student ) a
                            if (null != object.getSourceColumn() && null == column.getExtAlias() && EPSConstants.KEYWORD_STAR.equals(object.getSourceColumn().toString())) {
                                style += TExpressionUtil.createAliasStr(column.getExtColumn());
                            }
                            TExpressionUtil.convert(object, style);
                        }
                    }
                }
            }
        }
    }
}
