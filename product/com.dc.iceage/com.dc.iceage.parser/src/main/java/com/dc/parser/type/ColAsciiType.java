package com.dc.parser.type;

import com.dc.parser.sensitive.model.ColumnToken;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ColAsciiType {

    ASCII_2A("*"),
    ASCII_2E("."),
    ASCII_3F("?"),
    ASCII_2B("+"),
    ASCII_24("$"),
    ASCII_5E("^"),
    ASCII_5B("["),
    ASCII_5D("]"),
    ASCII_28("("),
    ASCII_29(")"),
    ASCII_7B("{"),
    ASCII_7D("}"),
    ASCII_7C("|"),
    ASCII_5C("\\"),
    ASCII_2F("/"),

    ;

    String value;

    ColAsciiType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static List<String> list() {
        List<String> arr = new ArrayList<>();
        //避免重复替换`\` 将其置放第一个位子
        arr.add(ColAsciiType.ASCII_5C.getValue());
        arr.addAll(Arrays.stream(ColAsciiType.values()).map(ColAsciiType::getValue).collect(
                Collectors.toList()).stream().filter(e -> !ColAsciiType.ASCII_5C.getValue().equals(e)).collect(Collectors.toList())
        );
        return arr;
    }

    public static String strEncode(String str) {
        ColumnToken token = new ColumnToken();
        token.setIdentCode(str);
        ColAsciiType.list().forEach(e -> {
            token.setIdentCode(token.getIdentCode().replace(e, ColAsciiType.ASCII_5C.getValue() + e));
        });
        return token.getIdentCode();
    }


    public static void main(String[] args) {
        final String[] str = {"(name+1)"};
        List<String> list = ColAsciiType.list();
        list.stream().forEach(e -> {
            str[0] = str[0].replace(e, ASCII_5C.getValue() + e);
        });
        System.out.println(str[0]);
    }


}
