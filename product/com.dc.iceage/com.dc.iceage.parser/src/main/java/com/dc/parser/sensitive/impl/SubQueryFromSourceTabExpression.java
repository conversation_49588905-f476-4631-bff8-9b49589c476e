package com.dc.parser.sensitive.impl;

import com.dc.sqlparser.nodes.TExpression;
import com.dc.sqlparser.nodes.TObjectName;
import com.dc.sqlparser.nodes.TTable;

/**
 * 嵌套子查询
 *
 * @description subquery_t:
 */
public class SubQueryFromSourceTabExpression extends AbstractSubQueryExpression {

    @Override
    public void handle(TExpression tExpr) {

        TObjectName objectOperand = null;
        if (null != tExpr.getObjectOperand()) {
            objectOperand = tExpr.getObjectOperand();
        }

        TTable sourceTable = null;
        if (null != objectOperand && objectOperand.getSourceTable() != null) {
            sourceTable = objectOperand.getSourceTable();
        }

        //FROM子查询 : select name from ( select name from tab ) b
        if (null != sourceTable && null != sourceTable.getSubquery()) {

            StatementContext context = super.getContext().createContext();
            context.setUseAlias(false);
            context.addEps(new CustomSqlStatementExpression());
            context.addNode(sourceTable.getSubquery());
            context.search();

        }

    }
}
