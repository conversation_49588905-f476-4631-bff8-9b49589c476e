package com.dc.parser.sensitive.model;

import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.sqlparser.nodes.TParseTreeNode;

import java.util.List;

//@Data
public class EnableConfigProperties {

    ///操作类型
    private String operation;
    //
    private Integer dbType;
    //
    private String origin_sql;
    //parser tree
    private TParseTreeNode treeNode;
    //dbo schema
    private String dboSchemaName;
    //脱敏规则
    private List<DataMask> configs;


    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getDbType() {
        return dbType;
    }

    public void setDbType(Integer dbType) {
        this.dbType = dbType;
    }

    public String getOrigin_sql() {
        return origin_sql;
    }

    public void setOrigin_sql(String origin_sql) {
        this.origin_sql = origin_sql;
    }

    public TParseTreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TParseTreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public String getDboSchemaName() {
        return dboSchemaName;
    }

    public void setDboSchemaName(String dboSchemaName) {
        this.dboSchemaName = dboSchemaName;
    }

    public List<DataMask> getConfigs() {
        return configs;
    }

    public void setConfigs(List<DataMask> configs) {
        this.configs = configs;
    }
}
