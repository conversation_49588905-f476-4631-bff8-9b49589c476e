package com.dc.proxy.model.receiver;

import com.dc.springboot.core.model.data.ResultData;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;

public class MultipleResultDataReceiver extends ResultDataReceiver{

    protected final ResultData resultData = new ResultData();

    public MultipleResultDataReceiver(DBCExecutionContext executionContext) {
        super(executionContext);
    }

    @Override
    public void fetchEnd(DBCSession session, DBCResultSet resultSet) throws DBCException {
        resultData.add(getRowsData());
        clear();
    }

    public ResultData getResultData() {
        ResultData copy = new ResultData(resultData.size());
        boolean b = copy.addAll(resultData);
        return copy;
    }

    @Override
    public void close() {
        super.close();
        resultData.clear();
    }
}
