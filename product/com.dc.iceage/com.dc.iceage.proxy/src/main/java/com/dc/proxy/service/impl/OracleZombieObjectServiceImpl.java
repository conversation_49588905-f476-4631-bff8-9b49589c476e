package com.dc.proxy.service.impl;

import com.dc.iceage.model.jdbc.ExecutionContainer;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.DcMetadata;
import com.dc.repository.mysql.service.DcMetadataService;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.type.DatabaseType;
import com.dc.utils.OracleTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.function.Consumer;

@Slf4j
@Service
public class OracleZombieObjectServiceImpl extends AbstractZombieObjectService {

    @Autowired
    private DcMetadataService dcMetadataService;

    /**
     * 获取过去两天内被使用过的数据库对象（如表、视图、索引等）的信息，
     * 包含对象的所有者、对象名称、对象类型，以及对象最近一次被引用的时间戳
     */
    private final String ZOMBIE_OBJECT_SQL1 = "SELECT\n" +
            "    p.OBJECT_OWNER,\n" +
            "    p.OBJECT_NAME,\n" +
            "    p.OBJECT_TYPE,\n" +
            "    MAX(p.TIMESTAMP) AS LAST_TIMESTAMP\n" +
            "FROM\n" +
            "    V$SQL s\n" +
            "        JOIN V$SQL_PLAN p ON s.SQL_ID = p.SQL_ID\n" +
            "WHERE\n" +
            "    p.OBJECT_OWNER NOT IN (\n" +
            "                           'SYS', 'SYSTEM', 'CTXSYS', 'DBSNMP', 'OUTLN', 'AUDSYS', 'ORDDATA', 'ORDPLUGINS',\n" +
            "                           'XDB', 'MDSYS', 'WMSYS', 'APPQOSSYS', 'LBACSYS', 'DVSYS', 'OLAPSYS', 'GSMADMIN_INTERNAL',\n" +
            "                           'ANONYMOUS', 'SYSMAN'\n" +
            "        )\n" +
            "  AND p.OBJECT_TYPE IS NOT NULL\n" +
            "  AND p.TIMESTAMP >= SYSDATE - INTERVAL '2' DAY\n" +
            "GROUP BY\n" +
            "    p.OBJECT_NAME,\n" +
            "    p.OBJECT_OWNER,\n" +
            "    p.OBJECT_TYPE";

    private final String ZOMBIE_OBJECT_SQL2 = "SELECT\n" +
            "    p.OBJECT_OWNER,\n" +
            "    p.OBJECT_NAME,\n" +
            "    p.OBJECT_TYPE,\n" +
            "    MAX(p.TIMESTAMP) AS LAST_TIMESTAMP\n" +
            "FROM\n" +
            "    DBA_HIST_SQLTEXT s\n" +
            "        JOIN DBA_HIST_SQL_PLAN p ON s.SQL_ID = p.SQL_ID\n" +
            "WHERE\n" +
            "    p.OBJECT_OWNER NOT IN (\n" +
            "                           'SYS', 'SYSTEM', 'CTXSYS', 'DBSNMP', 'OUTLN', 'AUDSYS', 'ORDDATA', 'ORDPLUGINS',\n" +
            "                           'XDB', 'MDSYS', 'WMSYS', 'APPQOSSYS', 'LBACSYS', 'DVSYS', 'OLAPSYS', 'GSMADMIN_INTERNAL',\n" +
            "                           'ANONYMOUS', 'SYSMAN'\n" +
            "        )\n" +
            "  AND p.OBJECT_TYPE IS NOT NULL\n" +
            "  AND p.TIMESTAMP >= SYSDATE - INTERVAL '2' DAY\n" +
            "GROUP BY\n" +
            "    p.OBJECT_NAME,\n" +
            "    p.OBJECT_OWNER,\n" +
            "    p.OBJECT_TYPE";

    /**
     * 获取非系统用户在过去两天内的最后一次活动记录，
     * 包括用户名、会话状态 (SESSION_STATE)，以及该活动的采样时间 (SAMPLE_TIME)
     */
    private final String ZOMBIE_USER_SQL = "SELECT\n" +
            "    u.USERNAME,\n" +
            "    u.ACCOUNT_STATUS,\n" +
            "    ash.SAMPLE_TIME\n" +
            "FROM (\n" +
            "         SELECT\n" +
            "             ash.*,\n" +
            "             ROW_NUMBER() OVER (PARTITION BY ash.user_id ORDER BY ash.sample_time DESC) AS rn\n" +
            "         FROM\n" +
            "             DBA_HIST_ACTIVE_SESS_HISTORY ash\n" +
            "                 JOIN\n" +
            "             DBA_USERS u ON ash.user_id = u.user_id\n" +
            "         WHERE\n" +
            "             ash.sample_time >= SYSDATE - 2    -- 最近两天的记录\n" +
            "           AND u.username NOT IN ('SYS', 'SYSTEM')   -- 过滤掉系统用户\n" +
            "     ) ash\n" +
            "         JOIN\n" +
            "     DBA_USERS u ON ash.user_id = u.user_id\n" +
            "WHERE\n" +
            "    ash.rn = 1\n" +
            "ORDER BY\n" +
            "    ash.sample_time DESC";

    private final String ZOMBIE_USER_V$SESSION_SQL = "SELECT\n" +
            "    vsession.USERNAME,\n" +
            "    users.ACCOUNT_STATUS,\n" +
            "    MAX(vsession.LOGON_TIME) AS LAST_LOGON_TIME\n" +
            "FROM\n" +
            "    V$SESSION vsession join DBA_USERS users on vsession.USERNAME= users.USERNAME\n" +
            "WHERE\n" +
            "    vsession.USERNAME IS NOT NULL\n" +
            "  AND vsession.LOGON_TIME >= SYSDATE - INTERVAL '2' DAY\n" +
            "GROUP BY\n" +
            "    vsession.USERNAME,\n" +
            "    users.ACCOUNT_STATUS";

    private final String[] OBJECT_SQL_ARRAY = {ZOMBIE_OBJECT_SQL1, ZOMBIE_OBJECT_SQL2};

    private final String[] USER_SQL_ARRAY = {ZOMBIE_USER_SQL, ZOMBIE_USER_V$SESSION_SQL};

    public void updateDatabaseMetadataUsers(DatabaseConnection instance, String updateUserjob, DBRProgressMonitor monitor, ExecutionContainer container) {
        Consumer<Object[]> updateUserConsumer = row -> {
            String user = (String) row[0];
            String status = (String) row[1];
            long accessTime = ((Timestamp) row[2]).getTime();
            DcMetadata dcMetadata = new DcMetadata();
            dcMetadata.setConnectIdHash(instance.getId().longValue())
                    .setObjectName(user)
                    .setObjectStatus(status)
                    .setLastActiveTime(accessTime);
            dcMetadataService.updateMetaUser(dcMetadata);
        };

        for (String sql : USER_SQL_ARRAY) {
            executeMetadataUpdateJob(updateUserjob, monitor, container, sql, updateUserConsumer);
        }
    }

    public void updateDatabaseMetadataObjects(DatabaseConnection instance, String updateObjectjob, DBRProgressMonitor monitor, ExecutionContainer container) {
        Consumer<Object[]> updateObjectConsumer = row -> {
            String owner = (String) row[0];
            String objectName = (String) row[1];
            String type = (String) row[2];
            type = OracleTypeUtils.mapToSysAllObjects(type);
            if (OracleTypeUtils.disableType(type)) {
                return;
            }
            long accessTime = ((Timestamp) row[3]).getTime();
            DcMetadata dcMetadata = new DcMetadata();
            dcMetadata.setConnectIdHash(instance.getId().longValue())
                    .setSchemaName(owner)
                    .setObjectName(objectName)
                    .setObjectType(type)
                    .setLastActiveTime(accessTime);
            dcMetadataService.updateMetadataObject(dcMetadata);
        };

        for (String sql : OBJECT_SQL_ARRAY) {
            executeMetadataUpdateJob(updateObjectjob, monitor, container, sql, updateObjectConsumer);
        }
    }


    @Override
    public DatabaseType getDataBaseType() {
        return DatabaseType.ORACLE;
    }
}
