package com.dc.proxy.model.type;

public enum HiveType {
    TBALE("TABLE", new String[]{"TABLE", "INDEX_TABLE"}),
    VIEW("VIEW", new String[]{"VIEW"}),
    FUNCTION("FUNCTION", null),
    MATE<PERSON><PERSON>IZEDVIEW("MATERIALIZED VIEW", null),
    PROC<PERSON><PERSON><PERSON>("PROCEDURE", null),
    TRIG<PERSON>R("TRIGGER", null),
    SYNONY<PERSON>("SYNONYM", null),
    EVENT("EVENT", null),
    JOB("JOB", null),
    WORK("WORK", null),
    PACKAGE("PACKAGE", null),
    PACKAGEBODY("PACKA<PERSON>", null),
    SEQ<PERSON><PERSON><PERSON>("SEQUENCE", null),
    COLUMN("COLUMN", null);

    private String code;

    private String[] name;

    HiveType(String code, String[] name) {
        this.code = code;
        this.name = name;
    }

    public static String[] getNameByCode(String code) {
        for (HiveType hiveType : HiveType.values()) {
            if (hiveType.getCode().equals(code)) {
                return hiveType.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String[] getName() {
        return name;
    }

    public void setName(String[] name) {
        this.name = name;
    }
}
