package com.dc.proxy.model.data.message;

import com.dc.springboot.core.model.database.ConnectionMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("模式消息")
public class SchemaMessage extends ConnectionMessage {

    @ApiModelProperty(value = "目录", example = "demo_catalog")
    private String catalog;

    @ApiModelProperty(value = "模式名", example = "demo_schema")
    private String schemaPattern;

}
