package com.dc.proxy.model.receiver;

import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.struct.DBSDataContainer;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Consumer;

@Slf4j
public class RowDataReceiver implements DBDDataReceiver {

    private DBDAttributeBinding[] metaColumns;

    private Object[] rowValues;

    private final Consumer<Object[]> consumer;

    protected final DBSDataContainer dataContainer;

    public RowDataReceiver(DBSDataContainer dataContainer, Consumer<Object[]> consumer) {
        this.dataContainer = dataContainer;
        this.consumer = consumer;
    }

    @Override
    public void fetchStart(DBCSession session, DBCResultSet resultSet, long offset, long maxRows) throws DBCException {
        // Get columns metadata
        DBCResultSetMetaData metaData = resultSet.getMeta();
        if (metaData == null) {
            throw new DBCException("Null resultset metadata");
        }

        // Extract column info
        metaColumns = DBUtils.getAttributeBindings(session, dataContainer, metaData);
    }

    @Override
    public DBCStatistics getStatistics() {
        return DBDDataReceiver.super.getStatistics();
    }

    @Override
    public void fetchRow(DBCSession session, DBCResultSet resultSet)
            throws DBCException {

        fetchRowValues(session, resultSet);
        consumer.accept(rowValues);
    }

    protected void fetchRowValues(DBCSession session, DBCResultSet resultSet) throws DBCException {
        rowValues = new Object[metaColumns.length];
        for (int i = 0; i < metaColumns.length; i++) {
            final DBDAttributeBinding attr = metaColumns[i];
            DBDValueHandler valueHandler = attr.getValueHandler();
            rowValues[i] = valueHandler.fetchValueObject(session, resultSet, attr, i);
        }
    }

    @Override
    public void fetchEnd(DBCSession session, DBCResultSet resultSet) {

    }

    @Override
    public void close() {
    }

}
