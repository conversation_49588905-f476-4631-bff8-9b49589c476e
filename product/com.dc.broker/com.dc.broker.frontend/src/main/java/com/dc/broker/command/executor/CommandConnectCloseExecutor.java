package com.dc.broker.command.executor;

import com.dc.broker.command.model.CommandConnectMessage;
import com.dc.broker.protocol.MessageBase;
import com.dc.broker.session.BrokerConnectionContext;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.utils.MD5Util;
import com.dc.utils.CipherUtils;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CommandConnectCloseExecutor extends AbstractCommandExecutor {

    private final MessageBase.Message message;

    private final BrokerConnectionContext connectionContext;

    @Override
    public MessageBase.Message execute() {
        connectionContext.close();
        return MessageBase.Message.getDefaultInstance()
                .toBuilder().setCmd(message.getCmd())
                .setRequestId(message.getRequestId()).setContent(gson.toJson(Result.success())).build();
    }

}
