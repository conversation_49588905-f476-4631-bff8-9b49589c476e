package com.dc.broker.command.executor;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.broker.command.model.CommandConnectMessage;
import com.dc.broker.component.BrokerMapper;
import com.dc.broker.model.AccessToken;
import com.dc.broker.model.AccessInfo;
import com.dc.broker.protocol.MessageBase;
import com.dc.broker.session.BrokerConnectionContext;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.Organization;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.model.User;
import com.dc.repository.redis.model.EnvConnection;
import com.dc.repository.redis.model.EnvSchema;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.exception.ResultException;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.type.DatabaseType;
import com.dc.utils.CipherUtils;
import com.dc.utils.StringUtils;
import lombok.RequiredArgsConstructor;

import java.util.*;

@RequiredArgsConstructor
public class CommandConnectExecutor extends AbstractCommandExecutor {

    private final MessageBase.Message message;

    private final BrokerConnectionContext connectionContext;

    @Override
    public MessageBase.Message execute() {
        String content = message.getContent();
        CommandConnectMessage commandConnectMessage = gson.fromJson(content, CommandConnectMessage.class);

        DatabaseConnectionMapper connectionMapper = Resource.getBean(DatabaseConnectionMapper.class);
        SchemaMapper schemaMapper = Resource.getBean(SchemaMapper.class);
        BrokerMapper brokerMapper = Resource.getBean(BrokerMapper.class);

        assert connectionMapper != null;
        assert brokerMapper != null;
        assert schemaMapper != null;

        MessageBase.Message.Builder responseBuilder = MessageBase.Message.getDefaultInstance()
                .toBuilder().setCmd(message.getCmd()).setRequestId(message.getRequestId());

        ConnectionConfig connectionConfig;

        AccessInfo accessInfo;
        try {
            DatabaseConnection connectionByUniqueKey = connectionMapper.getConnectionByUniqueKey(commandConnectMessage.getConnectId());
            if (connectionByUniqueKey == null) {
                throw new ResultException("invalid connect_id, connection not found in DC platform!");
            }
            EnvConnection envConnection = gson.fromJson(gson.toJson(connectionByUniqueKey), EnvConnection.class);
            envConnection.setConnectionId(connectionByUniqueKey.getUnique_key());
            envConnection.setUserName(connectionByUniqueKey.getUsername());
            envConnection.setConnectionPattern(ConnectionPatternType.SECURITY_COLLABORATION.getValue());
            envConnection.setConnectionPatternZh(ConnectionPatternType.SECURITY_COLLABORATION.getDescription());
            connectionContext.setEnvConnection(envConnection);

            connectionConfig = brokerMapper.toDatabaseConnectionDto(connectionByUniqueKey).buildConnectionConfig(commandConnectMessage.getDatabaseName(), commandConnectMessage.getCatalogName());

            accessInfo = getAccessInfo(commandConnectMessage.getConnectToken());
            if (accessInfo == null) {
                throw new ResultException("invalid access_token!");
            }

            if (!StringUtils.isNullOrWhiteSpace(commandConnectMessage.getDatabaseName())) {
                Schema schema = schemaMapper.selectOne(Wrappers.<Schema>lambdaQuery()
                        .eq(Schema::getSchema_name, commandConnectMessage.getDatabaseName())
                        .eq(DatabaseType.get3FDatabaseIntegerValueList().contains(connectionConfig.getDatabaseType()) && !StringUtils.isNullOrWhiteSpace(commandConnectMessage.getCatalogName()),
                                Schema::getCatalog_name,
                                commandConnectMessage.getCatalogName())
                        .eq(Schema::getIs_delete, 0)
                        .eq(Schema::getConnect_id, commandConnectMessage.getConnectId()));
                if (schema != null) {
                    EnvSchema envSchema = gson.fromJson(gson.toJson(schema), EnvSchema.class);
                    envSchema.setSchemaId(schema.getUnique_key());
                    connectionContext.setEnvSchema(envSchema);
                }
            }

            if (!accessInfo.getIgnorePermission()) {
                Boolean aBoolean = this.checkConnectionAuth(commandConnectMessage.getConnectId(), accessInfo.getUserId());
                if (!aBoolean) {
                    throw new ResultException("no permission to connect this database!");
                }
            }
        } catch (Exception exception) {
            responseBuilder.setContent(gson.toJson(Result.fail(exception.getMessage())));
            return responseBuilder.build();
        }
        connectionContext.setAutoCommit(commandConnectMessage.isAutoCommit());
        return this.boolResponse(message, "connect failed!", () -> connectionContext.openSession(connectionConfig, accessInfo));
    }

    private AccessInfo getAccessInfo(String token) {
        AccessInfo accessInfo = null;
        UserMapper userMapper = Resource.getBean(UserMapper.class);
        OrganizationMapper organizationMapper = Resource.getBean(OrganizationMapper.class);
        assert userMapper != null;
        assert organizationMapper != null;
        String decrypt = CipherUtils.decrypt(token);
        AccessToken accessToken = gson.fromJson(decrypt, AccessToken.class);
        User user = userMapper.selectOne(Wrappers.<User>lambdaQuery()
                .eq(User::getUniqueKey, accessToken.getUniqueKey()));
        if (user != null) {
            accessInfo = new AccessInfo();
            accessInfo.setUserId(user.getUniqueKey());
            accessInfo.setUsername(user.getUsername());
            accessInfo.setRealName(user.getRealName());
            accessInfo.setOrgIds(user.getOrgIds());
            accessInfo.setROrgIds(user.getROrgIds());
            accessInfo.setIgnorePermission(accessToken.getIgnorePermission());

            // set organizations
            List<String> organizations = new ArrayList<>(List.of(user.getROrgIds().split("\\,")));
            List<Organization> organizationList = organizationMapper.selectList(Wrappers.<Organization>lambdaQuery()
                    .in(Organization::getUniqueKey, organizations)
                    .orderByAsc(Organization::getGmtCreate)
            );
            Collections.reverse(organizationList); // 翻转组织名称显示顺序
            List<String> organizationNames = new ArrayList<>();
            for (Organization organization : organizationList) {
                String orgNames = organization.getOrgNames();
                List<String> list = gson.fromJson(orgNames, List.class);
                String organizationName = String.join("/", list);
                organizationNames.add(organizationName);
            }
            accessInfo.setOrganizationName(String.join(",", organizationNames));
        }
        return accessInfo;
    }


    private Boolean checkConnectionAuth(String connectId, String userId) {
        GroupUserMapper groupUserMapper = Resource.getBean(GroupUserMapper.class);
        assert groupUserMapper != null;
        Map<String, Object> map = new HashMap<>();
        map.put("connect_id", connectId);
        map.put("user_id", userId);
        map.put("now", System.currentTimeMillis() / 1000);
        return groupUserMapper.hasConnectionAuth(map);
    }

}
