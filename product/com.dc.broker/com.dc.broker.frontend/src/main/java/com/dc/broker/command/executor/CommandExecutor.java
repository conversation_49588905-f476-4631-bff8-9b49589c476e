package com.dc.broker.command.executor;

import com.dc.broker.protocol.MessageBase;
import com.dc.summer.DBException;

import java.sql.SQLException;

/**
 * Command executor.
 */
public interface CommandExecutor {
    
    /**
     * Execute command.
     * @throws DBException
     */
    MessageBase.Message execute() throws DBException;
    
    /**
     * Close command executor.
     * @throws DBException SQL exception
     */
    default void close() throws DBException {
    }
}
