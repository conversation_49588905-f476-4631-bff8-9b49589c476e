package com.dc.broker.config;


import com.dc.springboot.core.config.PathConfig;
import com.dc.springboot.core.model.thread.ThreadConfig;
import com.dc.summer.exec.config.DataSourceConfig;
import com.dc.summer.exec.config.SessionConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@Component
@ConfigurationProperties(prefix = "broker", ignoreInvalidFields = true)
public class BrokerConfig extends ThreadConfig {

    private Path path;

    private SessionConfig session;

    private DataSourceConfig dataSource;

    private String keyPrefix;

    private NettyConfig netty;

    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Data
    public static class Path extends PathConfig {
        private String dcIceage;
    }

    public static void setPathInstance(Path path) {
        PathConfig.setInstance(path);
    }

    public static Path getPathInstance() {
        return (Path) PathConfig.getInstance();
    }


}
