package com.dc.broker.controller;

import com.dc.broker.model.TokenMessage;
import com.dc.broker.service.TokenService;
import com.dc.springboot.auth.AuthIgnored;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.execution.SingleSyncExecuteMessage;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Api(tags = "令牌控制器")
@Slf4j
@RequestMapping("/access-token")
public class TokenController {

    @Resource
    TokenService tokenService;


    @ApiOperation("单一执行SQL - 同步执行SQL，立刻返回结果集")
    @PostMapping(value = "/create")
    @AuthIgnored
    public Result<String> createToken(@RequestBody TokenMessage message) {
        String token = tokenService.createToken(message);
        if (token == null) {
            return Result.fail("用户不存在！");
        }
        return Result.success(token);
    }


}
