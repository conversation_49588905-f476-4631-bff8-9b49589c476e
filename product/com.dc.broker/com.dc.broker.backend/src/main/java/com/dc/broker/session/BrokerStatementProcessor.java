package com.dc.broker.session;

import com.dc.broker.container.BrokerSQLQueryDataContainer;
import com.dc.broker.model.RespCursorExecMessage;
import com.dc.broker.model.CursorExecModel;
import com.dc.broker.model.RespCursorResultMessage;
import com.dc.broker.sql.BrokerSQLCursor;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableParametrized;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.struct.DBSDataContainer;
import lombok.Getter;

import java.lang.reflect.InvocationTargetException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BrokerStatementProcessor {

    private static final Log log = Log.getLog(BrokerStatementProcessor.class);

    LoggingProgressMonitor monitor = new LoggingProgressMonitor();

    @Getter
    private final SQLSyntaxManager syntaxManager;

    @Getter
    private final SQLRuleManager ruleManager;

    @Getter
    private final BrokerConnectionContext contextInfo;

    private final Map<String, BrokerSQLCursor> statements = new ConcurrentHashMap<>();

    BrokerStatementProcessor(BrokerConnectionContext contextInfo, boolean initSyntaxRule) {
        this.contextInfo = contextInfo;
        if (initSyntaxRule) {
            syntaxManager = new SQLSyntaxManager();
            syntaxManager.init(
                    contextInfo.getDataSource().getSQLDialect(), contextInfo.getDataSourceContainer().getPreferenceStore());
            ruleManager = new SQLRuleManager(syntaxManager);
            ruleManager.loadRules(contextInfo.getDataSource(), false);
        } else {
            syntaxManager = null;
            ruleManager = null;
        }
    }

    public String createSQLCursor(Long maxRows) {
        BrokerSQLCursor brokerSQLCursor = new BrokerSQLCursor(contextInfo, maxRows);
        String id = brokerSQLCursor.toString();
        statements.put(id, brokerSQLCursor);
        return id;
    }

    public boolean isStatementClosed(String cursor) {
        return getSQLCursor(cursor).isStatementClosed();
    }

    public boolean isResultSetClosed(String cursor) {
        return getSQLCursor(cursor).isResultSetClosed();
    }

    public boolean closeResultSet(String cursor) {
        BrokerSQLCursor sqlCursor = getSQLCursor(cursor);
        try {
            sqlCursor.closeResultSet();
        } catch (Exception exception) {
            return false;
        }
        return true;
    }

    public boolean closeSQLCursor(String cursor) {
        BrokerSQLCursor sqlCursor = getSQLCursor(cursor);
        try {
            sqlCursor.close();
        } catch (Exception exception) {
            return false;
        } finally {
            statements.remove(cursor);
        }
        return true;
    }

    public BrokerSQLCursor getSQLCursor(String cursor) {
        return statements.get(cursor);
    }

    public RespCursorExecMessage processQuery(String cursor, CursorExecModel executeModel) throws DBException {
        RespCursorExecMessage respCursorExecMessage = new RespCursorExecMessage();
        BrokerSQLCursor brokerSQLCursor = getSQLCursor(cursor);
        DBCExecutionPurpose purpose = DBCExecutionPurpose.USER;
        DBCExecutionContext executionContext;
        String executeSql = executeModel.getSql();
        try (DBCSession session = contextInfo.openSession(monitor, purpose, "Query SQL")) {
            executionContext = contextInfo.getExecutionContext();
            DBSDataContainer dataContainer = new BrokerSQLQueryDataContainer(executionContext.getDataSource(), executionContext, executeSql, brokerSQLCursor.getMaxRows());
            SQLQuery sqlQuery = new SQLQuery(executionContext.getDataSource(), executeSql);

            DBRRunnableParametrized<DBRProgressMonitor> runnableConfirm = param -> {

                    AbstractExecutionSource source = new AbstractExecutionSource(dataContainer, session.getExecutionContext(), BrokerStatementProcessor.this, sqlQuery);
                    DBCStatement dbStat = DBUtils.makeStatement(source, session, DBCStatementType.QUERY, sqlQuery, executeModel.getOffset(), brokerSQLCursor.getMaxRows());
                    DBExecUtils.setStatementFetchSize(dbStat, 0, 0, 1000);
                    brokerSQLCursor.setDbStat(dbStat);
                    List<List<Object>> seqOfParams = executeModel.getSeqOfParams();
                    if (seqOfParams != null && dbStat instanceof JDBCPreparedStatement) {
                        JDBCPreparedStatement preparedStatement = (JDBCPreparedStatement) dbStat;
                        try {
                            for (List<Object> params : seqOfParams) {
                                for (int i = 0; i < params.size(); i++) {
                                    preparedStatement.setObject(i + 1, params.get(i));
                                }
                                preparedStatement.addBatch();
                            }
                            int[] ints = preparedStatement.executeBatch();
                            long sum = 0;
                            for (int number : ints) {
                                sum += number;
                            }
                            respCursorExecMessage.setHasResultSet(false);
                            respCursorExecMessage.setUpdateCount(sum);
                        } catch (SQLException e) {
                            throw new DBException(e.getMessage());
                        }
                    } else {
                        List<Object> params = executeModel.getParams();
                        if (params != null && dbStat instanceof JDBCPreparedStatement) {
                            try {
                                JDBCPreparedStatement preparedStatement = (JDBCPreparedStatement) dbStat;
                                for (int i = 0; i < params.size(); i++) {
                                    preparedStatement.setObject(i + 1, params.get(i));
                                }
                            } catch (SQLException e) {
                                throw new DBException(e.getMessage());
                            }
                        }
                        boolean hasResultSet = dbStat.executeStatement();
                        respCursorExecMessage.setHasResultSet(hasResultSet);
                        if (hasResultSet) {
                            DBCResultSet dbcResultSet = brokerSQLCursor.openResultSet(dataContainer, executeModel.getSqlDesensitization(), executeModel.getSqlFieldDataList());
                            respCursorExecMessage.setResultSetMetadata(brokerSQLCursor.getResultSetDesc());
                            respCursorExecMessage.setResultSet(dbcResultSet.toString());
                        } else {
                            long updateRowCount = dbStat.getUpdateRowCount();
                            respCursorExecMessage.setUpdateCount(updateRowCount);
                        }
                    }
                };
            DBExecUtils.tryExecuteRecover(monitor, executionContext.getDataSource(), runnableConfirm);
        }
        return respCursorExecMessage;
    }

    public RespCursorResultMessage fetchQueryResult(String cursor, Long fetchSize) throws DBException {
        RespCursorResultMessage respCursorResultMessage = new RespCursorResultMessage();
        BrokerSQLCursor brokerSQLCursor = statements.get(cursor);
        WebSQLQueryResultSet fetch = brokerSQLCursor.fetch(fetchSize);
        respCursorResultMessage.setRows(fetch.getRows());
        respCursorResultMessage.setHasMoreData(fetch.isHasMoreData());
        return respCursorResultMessage;
    }

}
