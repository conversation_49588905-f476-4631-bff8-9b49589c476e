### 设计文档：新增 Nacos 配置文件获取方式

#### 概述
本文档描述了在现有项目中新增从 Nacos 配置中心获取配置文件的功能。此功能需要在客户现场实施，并在不影响现有本地配置文件的前提下增加 Nacos 配置中心的支持。

#### 目标
- 增加从 Nacos 配置中心获取配置文件的功能
- 允许用户根据需要启用或禁用 Nacos 配置
- 确保在 Nacos 配置不可用时，应用程序能够回退到本地配置文件

#### 架构图
```
                           +----------------------+
                           |   Nacos Config Server|
                           +----------+-----------+
                                      |
                                      |
                                      v
                           +----------------------+
                           |    Spring Boot App   |
                           |  (bootstrap.yml,     |
                           |   application.yml)   |
                           +----------+-----------+
                                      |
                                      |
                +---------------------+---------------------+
                |                                           |
                v                                           v
  +--------------------------+                  +----------------------------+
  | From Nacos (enabled=true)|                 |  From Local (enabled=false)|
  |  - Retrieves config from |                 |  - Uses local config files |
  |    Nacos server          |                 |    (application.yml,       |
  |  - application.yml       |                 |     application-dev.yml)   |
  |  - application-dev.yml   |                 |     application-env.yml)   |
  |  - application-env.yml   |                 +----------------------------+
  +--------------------------+
```

#### 实现步骤

##### 1. 添加 Maven 依赖
在项目的 `pom.xml` 中添加以下 Maven 依赖：
```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-bootstrap</artifactId>
</dependency>

<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
</dependency>
```

##### 2. 配置 `bootstrap.yml`
在 `src/main/resources` 目录下添加 `bootstrap.yml` 文件，并进行如下配置：
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848              # 指定 Nacos 配置中心的地址和端口号
        enabled: false                              # 是否启用从 Nacos 获取配置文件 (true 为开启，false 为关闭)
        prefix: dc-summer                           # 配置文件的前缀，一般用于区分不同的应用或模块
        file-extension: yaml                        # 配置文件的扩展名，支持 properties 和 yaml
        group: DEFAULT_GROUP                        # 配置文件所在的 Nacos 组，默认是 DEFAULT_GROUP
        extension-configs:                          # 额外的配置文件列表，可以指定多个
          - dataId: dc-job-admin.yaml               # 额外的配置文件的 Data ID
            group: DEFAULT_GROUP                    # 额外的配置文件所在的 Nacos 组
```

##### 3. 适配服务
为四个服务适配 Nacos 配置：
- dc-summer
- dc-iceage
- dc-job
- dc-job-admin

每个服务的 `bootstrap.yml` 文件配置如下：
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848
        enabled: true                                # 根据需要启用或禁用 Nacos
        prefix: <service-name>                       # 替换为具体的服务名，如 dc-summer
        file-extension: yaml
        group: DEFAULT_GROUP
        extension-configs:
          - dataId: <service-name>.yaml          # 替换为具体的 Data ID，如 dc-summer-dev.yaml
            group: DEFAULT_GROUP
```

例如，`dc-summer` 服务的 `bootstrap.yml` 文件应如下：
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848
        enabled: true
        prefix: dc-summer
        file-extension: yaml
        group: DEFAULT_GROUP
        extension-configs:
          - dataId: dc-summer-dev.yaml
            group: DEFAULT_GROUP
```

#### 验证
- 启动每个服务，验证是否正确从 Nacos 配置中心获取配置文件。
- 修改 `enabled` 为 `false`，验证是否回退到本地配置文件。

#### 注意事项
- 确保 Nacos 配置中心的地址和端口正确可达。
- 在本地和 Nacos 配置文件之间切换时，确保配置文件内容的一致性。

#### 总结
通过上述步骤，成功在现有项目中集成了 Nacos 配置中心。此设计确保了在不同环境下的灵活配置管理，提高了配置的可管理性和扩展性。