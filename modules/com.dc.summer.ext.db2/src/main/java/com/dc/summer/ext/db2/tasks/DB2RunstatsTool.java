
package com.dc.summer.ext.db2.tasks;

import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.code.NotNull;
import com.dc.summer.ext.db2.model.DB2TableBase;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

import static com.dc.utils.CommonUtils.getLineSeparator;

public class DB2RunstatsTool extends DB2ToolWithStatus<DB2TableBase, DB2RunstatsToolSettings>{

    @NotNull
    @Override
    public DB2RunstatsToolSettings createToolSettings() {
        return new DB2RunstatsToolSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, DB2RunstatsToolSettings settings, List<DBEPersistAction> queries, DB2TableBase object) throws DBCException {
        String sql = "CALL SYSPROC.ADMIN_CMD('"; //$NON-NLS-1$
        sql += "RUNSTATS ON TABLE "; //$NON-NLS-1$
        sql += object.getFullyQualifiedName(DBPEvaluationContext.DDL);
        sql += getLineSeparator();
        sql += DB2RunstatsOptions.getOption(settings.getColumnStat()).getDdlString();
        if (!DB2RunstatsOptions.getOption(settings.getIndexStat()).getDdlString().equals("")) { //$NON-NLS-1$
            sql += " " + DB2RunstatsOptions.getOption(settings.getIndexStat()).getDdlString(); //$NON-NLS-1$
        }
        if (settings.isTableSampling()) {
            sql += String.format(" TABLESAMPLE SYSTEM(%d)", settings.getSamplePercent()); //$NON-NLS-1$
        }
        sql += "')"; //$NON-NLS-1$
        queries.add(new SQLDatabasePersistAction(sql));
    }
}
