/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.manager;

import com.dc.summer.ext.db2.model.security.DB2Role;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.code.Nullable;
import com.dc.summer.ext.db2.model.DB2DataSource;

/**
 * DB2 Role Manager
 * 
 * <AUTHOR>
 */
public class DB2RoleManager extends DB2AbstractDropOnlyManager<DB2Role, DB2DataSource> {

    private static final String SQL_DROP = "DROP ROLE %s";

    @Override
    public String buildDropStatement(DB2Role db2Role)
    {
        String name = db2Role.getName();
        return String.format(SQL_DROP, name);
    }

    @Nullable
    @Override
    public DBSObjectCache<DB2DataSource, DB2Role> getObjectsCache(DB2Role db2Role)
    {
        return db2Role.getDataSource().getRoleCache();
    }

}
