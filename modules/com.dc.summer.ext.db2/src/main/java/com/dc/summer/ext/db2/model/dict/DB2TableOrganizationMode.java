
package com.dc.summer.ext.db2.model.dict;

import com.dc.summer.model.DBPNamedObject;
import com.dc.code.NotNull;

public enum DB2TableOrganizationMode implements DBPNamedObject {
    C("Column-organized table"),
    R("Row-organized table"),
    N("Not a table");

    private final String name;

    DB2TableOrganizationMode(@NotNull String name) {
        this.name = name;
    }

    @NotNull
    @Override
    public String getName() {
        return name;
    }
}