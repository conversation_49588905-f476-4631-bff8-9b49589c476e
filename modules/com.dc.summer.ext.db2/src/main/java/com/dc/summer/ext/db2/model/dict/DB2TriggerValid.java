/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.model.dict;

import com.dc.summer.model.DBPNamedObject;
import com.dc.code.NotNull;
import com.dc.summer.model.struct.DBSObjectState;

/**
 * DB2 Trigger Valid attribute
 * 
 * <AUTHOR> Forveille
 */
public enum DB2TriggerValid implements DBPNamedObject {
    N("Invalid", DBSObjectState.INVALID),

    X("Inoperative", DBSObjectState.INVALID), // DF: No exact correspondance

    Y("Valid", DBSObjectState.ACTIVE); // DF: No exact correspondance

    private String name;
    private DBSObjectState state;

    // -----------------
    // Constructor
    // -----------------

    private DB2TriggerValid(String name, DBSObjectState state)
    {
        this.name = name;
        this.state = state;
    }

    // -----------------------
    // Display @Property Value
    // -----------------------
    @Override
    public String toString()
    {
        return name;
    }

    // ----------------
    // Standard Getters
    // ----------------

    @NotNull
    @Override
    public String getName()
    {
        return name;
    }

    public DBSObjectState getState()
    {
        return state;
    }
}