
package com.dc.summer.ext.db2.tasks;

import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.code.NotNull;
import com.dc.summer.ext.db2.model.DB2TableBase;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

import static com.dc.utils.CommonUtils.getLineSeparator;

public class DB2ToolTableTruncate extends DB2ToolWithStatus<DB2TableBase, DB2ToolTableTruncateSettings>{

    @NotNull
    @Override
    public DB2ToolTableTruncateSettings createToolSettings() {
        return new DB2ToolTableTruncateSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, DB2ToolTableTruncateSettings settings, List<DBEPersistAction> queries, DB2TableBase object) throws DBCException {
        String sql = "TRUNCATE TABLE"; //$NON-NLS-1$
        sql += " " + object.getFullyQualifiedName(DBPEvaluationContext.DDL); //$NON-NLS-1$
        sql += " " + DB2TableTruncateOptions.getOption(settings.getStorageOption()).getDdlString(); //$NON-NLS-1$
        sql += " " + DB2TableTruncateOptions.getOption(settings.getTriggerOption()).getDdlString(); //$NON-NLS-1$
        sql += getLineSeparator() + "CONTINUE IDENTITY IMMEDIATE"; //$NON-NLS-1$
        queries.add(new SQLDatabasePersistAction(sql));
    }

    public boolean needsRefreshOnFinish() {
        return true;
    }
}
