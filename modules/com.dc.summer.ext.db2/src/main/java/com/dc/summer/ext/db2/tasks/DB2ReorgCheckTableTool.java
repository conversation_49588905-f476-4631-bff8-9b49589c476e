
package com.dc.summer.ext.db2.tasks;

import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.code.NotNull;
import com.dc.summer.ext.db2.model.DB2TableBase;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

public class DB2ReorgCheckTableTool extends DB2ToolWithStatus<DB2TableBase, DB2ReorgCheckTableToolSettings>{

    @NotNull
    @Override
    public DB2ReorgCheckTableToolSettings createToolSettings() {
        return new DB2ReorgCheckTableToolSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, DB2ReorgCheckTableToolSettings settings, List<DBEPersistAction> queries, DB2TableBase object) throws DBCException {
        String sql = "CALL SYSPROC.REORGCHK_TB_STATS('T','"; //$NON-NLS-1$
        sql += object.getFullyQualifiedName(DBPEvaluationContext.DDL);
        sql += "')"; //$NON-NLS-1$
        queries.add(new SQLDatabasePersistAction(sql));
    }
}
