/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.manager;

import com.dc.summer.ext.db2.model.DB2Alias;
import com.dc.summer.ext.db2.model.dict.DB2AliasType;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.code.Nullable;
import com.dc.summer.ext.db2.model.DB2Schema;

import static com.dc.summer.ext.db2.model.dict.DB2AliasType.*;

/**
 * DB2 Alias Manager
 * 
 * <AUTHOR> Forveille
 */
public class DB2AliasManager extends DB2AbstractDropOnlyManager<DB2Alias, DB2Schema> {

    private static final String SQL_DROP_MODULE = "DROP ALIAS %s FOR MODULE";
    private static final String SQL_DROP_SEQUENCE = "DROP ALIAS %s FOR SEQUENCE";
    private static final String SQL_DROP_TABLE = "DROP ALIAS %s FOR TABLE";

    @Override
    public String buildDropStatement(DB2Alias db2Alias)
    {
        String fullyQualifiedName = db2Alias.getFullyQualifiedName(DBPEvaluationContext.DDL);

        switch (db2Alias.getType()) {
        case MODULE:
            return String.format(SQL_DROP_MODULE, fullyQualifiedName);
        case SEQUENCE:
            return String.format(SQL_DROP_SEQUENCE, fullyQualifiedName);
        case TABLE:
            return String.format(SQL_DROP_TABLE, fullyQualifiedName);
        default:
            throw new IllegalArgumentException(db2Alias.getType() + " as DB2AliasType is not supported");
        }
    }

    @Nullable
    @Override
    public DBSObjectCache<DB2Schema, DB2Alias> getObjectsCache(DB2Alias db2Alias)
    {
        return db2Alias.getSchema().getAliasCache();
    }

}
