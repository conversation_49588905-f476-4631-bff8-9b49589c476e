/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.data;

import com.dc.summer.ext.db2.DB2Constants;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.ArrayUtils;

import java.sql.Types;

/**
 * DB2 Data Types provider
 *
 * <AUTHOR> Forveille
 */
public class DB2ValueHandlerProvider implements DBDValueHandlerProvider {

    private static final int[] NUMERIC_TYPES = {

    };

    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
        final String typeName = typedObject.getTypeName();
        if (DB2Constants.TYPE_NAME_DECFLOAT.equals(typeName)) {
            return new DB2DecFloatValueHandler(typedObject, preferences);
        } else if (typeName.contains("TIMESTAMP") || typedObject.getDataKind() == DBPDataKind.DATETIME) {
            return new DB2TimestampValueHandler(preferences);
        }

        switch (typedObject.getTypeID()) {
            case Types.DECIMAL:
            case Types.NUMERIC:
            case Types.REAL:
            case Types.FLOAT:
            case Types.DOUBLE:
//            return new DB2NumericValueHandler(typedObject, preferences.getDataFormatterProfile());
                break;
            case Types.CHAR:
            case Types.VARCHAR:
                return DB2VarcharValueHandler.INSTANCE;
            default:
        }

        return null;
    }
}