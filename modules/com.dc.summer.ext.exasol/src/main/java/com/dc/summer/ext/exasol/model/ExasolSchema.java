/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2016-2016 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.exasol.model;

import com.dc.summer.Log;
import com.dc.summer.ext.exasol.ExasolMessages;
import com.dc.summer.ext.exasol.model.cache.*;
import com.dc.summer.ext.exasol.model.security.ExasolGrantee;
import com.dc.summer.ext.exasol.tools.ExasolUtils;
import com.dc.summer.model.*;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.meta.IPropertyValueListProvider;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSProcedureContainer;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.exasol.ExasolSysTablePrefix;
import com.dc.summer.ext.exasol.model.cache.*;
import com.dc.summer.ext.exasol.tools.ExasolJDBCObjectSimpleCacheLiterals;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.utils.ByteNumberFormat;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


public class ExasolSchema extends ExasolGlobalObject implements DBSSchema, DBPNamedObject2, DBPRefreshableObject, DBPSystemObject, DBSProcedureContainer, DBPScriptObject {

    private static final List<String> SYSTEM_SCHEMA = Arrays.asList("SYS","EXA_STATISTICS");
    private static final Log log = Log.getLog(ExasolSchema.class);
    private String name;
    private String owner;
    private Timestamp createTime;
    private String remarks;
    private long objectId;
    private String tablePrefix;
    private BigDecimal rawObjectSize;
    private BigDecimal memObjectSize;
    private BigDecimal rawObjectSizeLimit;
    private Boolean refreshed = false; 


    // ExasolSchema's children
    public final DBSObjectCache<ExasolSchema, ExasolScript> scriptCache;

    public final DBSObjectCache<ExasolSchema, ExasolFunction> functionCache;
    private ExasolViewCache viewCache = new ExasolViewCache();
    private ExasolTableCache tableCache = new ExasolTableCache();
    
    // ExasolTable's children
    private final ExasolTableUniqueKeyCache constraintCache = new ExasolTableUniqueKeyCache(tableCache);
    private final ExasolTableForeignKeyCache associationCache = new ExasolTableForeignKeyCache(tableCache);
    private final ExasolTableIndexCache indexCache = new ExasolTableIndexCache(tableCache);

    public ExasolSchema(ExasolDataSource exasolDataSource, String name, String owner) {
        super(exasolDataSource, true);
        this.tablePrefix = exasolDataSource.getTablePrefix(ExasolSysTablePrefix.ALL);
        this.name = name;
        this.owner = owner;
        this.scriptCache = new ExasolJDBCObjectSimpleCacheLiterals<>(
        		ExasolScript.class,
        		"/*snapshot execution*/ select "
        		+ "script_name,script_owner,script_language,script_type,script_result_type,script_text,script_comment,b.created "
        		+ "from SYS." + tablePrefix + "_SCRIPTS a inner join SYS." + tablePrefix + "_OBJECTS b "
        		+ "on a.SCRIPT_OBJECT_ID  = b.object_id and b.object_type = 'SCRIPT' where a.script_schema = '%s' "
        		+ "order by script_name",
        		name);

        this.functionCache = new ExasolJDBCObjectSimpleCacheLiterals<>(ExasolFunction.class,
                "/*snapshot execution*/ SELECT\n" + 
                "    F.*,\n" + 
                "    O.CREATED\n" + 
                "FROM\n" + 
                "    SYS." +  tablePrefix + "_FUNCTIONS F\n" + 
                "INNER JOIN SYS." + tablePrefix + "_OBJECTS O ON\n" + 
                "    F.FUNCTION_OBJECT_ID = O.OBJECT_ID\n" + 
                "WHERE\n" + 
                "    F.FUNCTION_SCHEMA = '%s' and O.OBJECT_TYPE = 'FUNCTION' AND o.ROOT_NAME = '%s'\n" + 
                "ORDER BY\n" + 
                "    FUNCTION_NAME\n", 
                name,name);
        
        
        

    }

    public ExasolSchema(ExasolDataSource exasolDataSource, ResultSet dbResult) throws DBException {

        this(
                exasolDataSource, 
                JDBCUtils.safeGetStringTrimmed(dbResult, "OBJECT_NAME"),
                JDBCUtils.safeGetString(dbResult, "OWNER")
            );
        this.createTime = JDBCUtils.safeGetTimestamp(dbResult, "CREATED");
        this.remarks = JDBCUtils.safeGetString(dbResult, "OBJECT_COMMENT");
        this.name = JDBCUtils.safeGetString(dbResult, "OBJECT_NAME");
        this.objectId = JDBCUtils.safeGetLong(dbResult, "SCHEMA_OBJECT_ID");


    }
    
    @NotNull
    @Override
    @Property(viewable = true, editable = false, order = 1)
    public String getName() {
        return this.name;
    }
    
    @Override
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public Collection<ExasolTableBase> getChildren(@NotNull DBRProgressMonitor monitor) throws DBException {
        List<ExasolTableBase> allChildren = new ArrayList<>();
        allChildren.addAll(tableCache.getAllObjects(monitor, this));
        allChildren.addAll(viewCache.getAllObjects(monitor, this));
        return allChildren;
    }

    @Override
    public ExasolTableBase getChild(@NotNull DBRProgressMonitor monitor, @NotNull String childName) throws DBException {

        ExasolTableBase child = tableCache.getObject(monitor, this, childName);
        if (child == null) {
            child = viewCache.getObject(monitor, this, childName);
        }
        return child;
    }

    @NotNull
    @Override
    public Class<ExasolTable> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) throws DBException {
    	return ExasolTable.class;
    }

    @Override
    public synchronized void cacheStructure(@NotNull DBRProgressMonitor monitor, int scope) throws DBException {
    	
        if (((scope & STRUCT_ENTITIES) != 0)) {
            monitor.subTask("Cache tables");
            tableCache.getAllObjects(monitor, this);
            monitor.subTask("Cache Views");
            viewCache.getAllObjects(monitor, this);
        }
        if (((scope & STRUCT_ATTRIBUTES) != 0)) {
            monitor.subTask("Cache table columns");
            tableCache.loadChildren(monitor, this, null);
            monitor.subTask("Cache Views");
            viewCache.loadChildren(monitor, this, null);
        }

        if ((scope & STRUCT_ASSOCIATIONS) != 0) {
            monitor.subTask("Cache table unique keys");
            constraintCache.getObjects(monitor, this, null);
            monitor.subTask("Cache table foreign keys");
            associationCache.getObjects(monitor, this, null);
            monitor.subTask("Cache Indexes");
            indexCache.getObjects(monitor, this, null);

        }


    }

    // -----------------
    // Associations
    // -----------------

    @Association
    public Collection<ExasolTable> getTables(DBRProgressMonitor monitor) throws DBException {
        return tableCache.getTypedObjects(monitor, this, ExasolTable.class);
    }

    public ExasolTable getTable(DBRProgressMonitor monitor, String name) throws DBException {
        return tableCache.getObject(monitor, this, name, ExasolTable.class);
    }

    @Association
    public Collection<ExasolView> getViews(DBRProgressMonitor monitor) throws DBException {
        return viewCache.getTypedObjects(monitor, this, ExasolView.class);
    }

    public ExasolView getView(DBRProgressMonitor monitor, String name) throws DBException {
        return viewCache.getObject(monitor, this, name, ExasolView.class);
    }


    @Override
    public boolean isSystem() {
        // TODO Auto-generated method stub
        return SYSTEM_SCHEMA.contains(name);
    }

    @Override
    public Collection<ExasolScript> getProcedures(DBRProgressMonitor monitor) throws DBException {

        return scriptCache.getAllObjects(monitor, this).stream()
    			.filter(o -> o.getType().equals("SCRIPTING"))
    			.collect(Collectors.toCollection(ArrayList::new));
    }
    
    public Collection<ExasolScript> getUdfs(DBRProgressMonitor monitor) throws DBException {
    	
    	return scriptCache.getAllObjects(monitor, this).stream()
    			.filter(o -> o.getType().equals("UDF"))
    			.collect(Collectors.toCollection(ArrayList::new));
    }

    public ExasolScript getUdf(DBRProgressMonitor monitor, String name) throws DBException {

        return scriptCache.getObject(monitor, this, name);
    }


    public Collection<ExasolScript> getAdapter(DBRProgressMonitor monitor) throws DBException {

        return scriptCache.getAllObjects(monitor, this).stream()
    			.filter(o -> o.getType().equals("ADAPTER"))
    			.collect(Collectors.toCollection(ArrayList::new));

    }

    public ExasolScript getAdapter(DBRProgressMonitor monitor, String name) throws DBException {

        return scriptCache.getObject(monitor, this, name);
    }
    
    
    @Override
    public ExasolScript getProcedure(DBRProgressMonitor monitor, String uniqueName) throws DBException {

        return scriptCache.getObject(monitor, this, uniqueName);
    }

    public Collection<ExasolFunction> getFunctions(DBRProgressMonitor monitor) throws DBException {
        return functionCache.getAllObjects(monitor, this);
    }
    
    public ExasolFunction getFunction(DBRProgressMonitor monitor,String name) throws DBException {
        return functionCache.getObject(monitor, this, name);
    }

    
    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        
        ((ExasolDataSource) getDataSource()).refreshObject(monitor);
        functionCache.clearCache();
        scriptCache.clearCache();
        tableCache.clearCache();
        viewCache.clearCache();
        indexCache.clearCache();


        constraintCache.clearCache();
        associationCache.clearCache();
        refreshed=false;
        return this;
    }

    @Override
    public String toString() {
        return "Schema " + name;
    }
    
    private synchronized void refresh(DBRProgressMonitor monitor) throws DBCException
    {
    	if (!refreshed && this.objectId != 0) {
	    	JDBCSession session = DBUtils.openMetaSession(monitor, this, ExasolMessages.read_schema_details );
	    	try (JDBCPreparedStatement stmt = session.prepareStatement("/*snapshot execution*/ SELECT * FROM SYS."+getDataSource().getTablePrefix(ExasolSysTablePrefix.ALL)+"_OBJECT_SIZES WHERE OBJECT_ID = ?"))
	    	{
	    		stmt.setLong(1, this.objectId);
	    		try (JDBCResultSet dbResult = stmt.executeQuery()) 
	    		{
	    			if (dbResult.next()) {
                        this.createTime = JDBCUtils.safeGetTimestamp(dbResult, "CREATED");
                        this.rawObjectSize = JDBCUtils.safeGetBigDecimal(dbResult, "RAW_OBJECT_SIZE");
                        this.memObjectSize = JDBCUtils.safeGetBigDecimal(dbResult, "MEM_OBJECT_SIZE");
                        this.rawObjectSizeLimit = JDBCUtils.safeGetBigDecimal(dbResult, "RAW_OBJECT_SIZE_LIMIT");
                    }
	    		}
	    		refreshed = true;
	    	} catch (SQLException e) {
	    		throw new DBCException(e, session.getExecutionContext());
			}
    	}
		
	}

    @Property(viewable = true, editable = false, order = 2)
    public Timestamp getCreateTime(DBRProgressMonitor monitor) throws DBCException {
    	refresh(monitor);
        return createTime;
    }

    @Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 3)
    public String getDescription() {
        return remarks;
    }
    
    public void setDescription(String newRemarks)
    {
    	remarks = newRemarks;
    }

    @Property(viewable = true, editable = false, updatable = true,  order = 4)
    public String getOwner() {
        return owner;
    }

    
    
    @Property(viewable = true, editable = false, updatable =  false,  order = 5, formatter = ByteNumberFormat.class)
    public long getRawObjectSize() {
    	if (rawObjectSize == null)
    		return -1;
		return rawObjectSize.longValue();
	}

    @Property(viewable = true, editable = false, updatable =  false,  order = 6, formatter = ByteNumberFormat.class)
	public long getMemObjectSize() {
    	if (memObjectSize == null)
    		return -1;
		return memObjectSize.longValue();
	}

    @Property(viewable = true, editable = true, updatable = true,  order = 7)
	public BigDecimal getRawObjectSizeLimit() {
		return rawObjectSizeLimit;
	}
    
    public void setRawObjectSizeLimit(BigDecimal limit) {
    	this.rawObjectSizeLimit = limit;
    }
    

	public void setOwner(String owner)
    {
        this.owner = owner;
    }

    public ExasolTableCache getTableCache() {
        return tableCache;
    }

    public ExasolViewCache getViewCache() {
        return viewCache;
    }


    public ExasolTableUniqueKeyCache getConstraintCache() {
        return constraintCache;
    }

    public ExasolTableForeignKeyCache getAssociationCache() {
        return associationCache;
    }

	@Override
	public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options)
			throws DBException
	{
		return ExasolUtils.generateDDLforSchema(monitor, this);
	}
	
	
	public static class OwnerListProvider implements IPropertyValueListProvider<ExasolSchema> {
		
		@Override
		public boolean allowCustomValue() {
			return false;
		}
		
		public Object[] getPossibleValues(ExasolSchema object)
		{
			ExasolDataSource dataSource = object.getDataSource();
			try {
				Collection<ExasolGrantee> grantees = dataSource.getAllGrantees(new VoidProgressMonitor());
				return grantees.toArray(new Object[grantees.size()]);
			} catch (DBException e) {
				log.error(e);
				return new  Object[0];
			}
		}
		
	}
	
	public Boolean isPhysicalSchema()
	{
	    return true;
	}

	public ExasolTableIndexCache getIndexCache() {
		return indexCache;
	}
	
	@Association
	public Collection<ExasolTableIndex> getIndexes(DBRProgressMonitor monitor) throws DBException {
		return indexCache.getObjects(monitor, this, null);
	}
	
	
	
	
}
