/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2016-2016 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.exasol.manager;

import com.dc.summer.DBException;
import com.dc.summer.ext.exasol.model.ExasolFunction;
import com.dc.summer.ext.exasol.model.ExasolSchema;
import com.dc.summer.ext.exasol.tools.ExasolUtils;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

public class ExasolFunctionManager extends SQLObjectEditor<ExasolFunction, ExasolSchema> {

    @Override
    public DBSObjectCache<ExasolSchema, ExasolFunction> getObjectsCache(ExasolFunction object) {
        return object.getContainer().functionCache;
    }

    @Override
    public long getMakerOptions(DBPDataSource dataSource) {
        return FEATURE_EDITOR_ON_CREATE;
    }

    @Override
    protected void validateObjectProperties(DBRProgressMonitor monitor, ObjectChangeCommand command, Map<String, Object> options)
            throws DBException {
        if (CommonUtils.isEmpty(command.getObject().getName()))
        {
            throw new DBException("Function name cannot be empty");
        }
    }
    

    @Override
    protected ExasolFunction createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context,
                                                  Object container, Object copyFrom, Map<String, Object> options) throws DBException {
        ExasolFunction newScript =  new ExasolFunction((ExasolSchema) container);
        newScript.setName("function_name");
        newScript.setObjectDefinitionText("FUNCTION function_name() RETURNS INTEGER");
        return newScript;
    }
    
    private void createOrReplaceScriptQuery(List<DBEPersistAction> actions, ExasolFunction script, Boolean replace)
    {
        actions.add(
                new SQLDatabasePersistAction("Create Script", "OPEN SCHEMA " + script.getSchema().getName()));
        if (replace) { 
            actions.add(
                new SQLDatabasePersistAction("Create Script", "CREATE OR REPLACE " + script.getSql()));
        } else {
        actions.add(
                new SQLDatabasePersistAction("Create Script", "CREATE " + script.getSql()));
        }
    }
    
    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions,
                                          ObjectCreateCommand command, Map<String, Object> options) {
        createOrReplaceScriptQuery(actions, command.getObject(), false);
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions,
                                          ObjectDeleteCommand command, Map<String, Object> options) {
        actions.add(
                new SQLDatabasePersistAction("Create Script", "DROP FUNCTION " + command.getObject().getFullyQualifiedName(DBPEvaluationContext.DDL)));
    }
    
    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actionList,
                                          ObjectChangeCommand command, Map<String, Object> options)
    {
        if (command.getProperties().size() > 1 || command.getProperty("description") == null )
        {
            createOrReplaceScriptQuery(actionList, command.getObject(),true);
        }
    }
    
    @Override
    protected void addObjectExtraActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions,
                                         NestedObjectCommand<ExasolFunction, PropertyHandler> command, Map<String, Object> options)
    {
        if (command.getProperty("description") != null) {
            actions.add(new SQLDatabasePersistAction("Comment on Script","COMMENT ON FUNCTION " + 
                            command.getObject().getFullyQualifiedName(DBPEvaluationContext.DDL) + " IS " +
                            SQLUtils.quoteString(command.getObject(), ExasolUtils.quoteString(command.getObject().getDescription()))));
        }
    }

}