package com.dc.summer.model.document.data;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDComposite;
import com.dc.summer.model.data.DBDDocument;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataType;

public abstract class DBAbstractDocument<DATASOURCE extends DBPDataSource, SOURCE_TYPE> implements DBDDocument, DBDComposite {
   @NotNull
   protected final DATASOURCE dataSource;
   protected SOURCE_TYPE rawValue;
   protected DBMapValue<DATASOURCE> rawMap;

   public DBAbstractDocument(@NotNull DATASOURCE dataSource, SOURCE_TYPE rawValue) {
      this.dataSource = dataSource;
      this.rawValue = rawValue;
   }

   public @NotNull DATASOURCE getDataSource() {
      return this.dataSource;
   }

   public @NotNull String getDocumentContentType() {
      return "text/json";
   }

   public @NotNull DBMapValue<DATASOURCE> getRootNode() {
      if (this.rawMap == null) {
         this.rawMap = this.makeRawMap();
      }

      return this.rawMap;
   }

   protected abstract DBMapValue<DATASOURCE> makeRawMap();

   public SOURCE_TYPE getRawValue() {
      return this.rawValue;
   }

   public boolean isNull() {
      return this.rawValue == null;
   }

   public boolean isModified() {
      return this.getRootNode().isModified();
   }

   protected void markModified() {
      this.rawMap.setModified(true);
   }

   public void release() {
   }

   public DBSDataType getDataType() {
      return this.getRootNode();
   }

   public @NotNull DBSAttributeBase[] getAttributes() {
      return this.getRootNode().getAttributes();
   }

   public @Nullable Object getAttributeValue(@NotNull DBSAttributeBase attribute) throws DBCException {
      return this.getRootNode().getAttributeValue(attribute);
   }

   public @Nullable Object getAttributeValue(@NotNull String attributeName) throws DBCException {
      return this.getRootNode().getAttributeValue(attributeName);
   }

   public void setAttributeValue(@NotNull DBSAttributeBase attribute, Object value) throws DBCException {
      this.getRootNode().setAttributeValue(attribute, value);
      this.markModified();
   }

   public String toString() {
      return DBDocumentUtils.convertValueToJson(this.dataSource, this.getRawValue());
   }
}
