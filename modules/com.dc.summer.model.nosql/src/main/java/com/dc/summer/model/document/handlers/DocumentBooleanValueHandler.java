package com.dc.summer.model.document.handlers;

import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.CommonUtils;

public class DocumentBooleanV<PERSON>ueHandler extends DocumentBaseValueHandler {
   public static final DocumentBooleanValueHandler INSTANCE = new DocumentBooleanValueHandler();

   public @NotNull Class<Boolean> getValueObjectType(@NotNull DBSTypedObject attribute) {
      return Boolean.class;
   }

   public Boolean getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
      return CommonUtils.toBoolean(object);
   }
}
