package com.dc.summer.model.mq;

import com.google.gson.Gson;
import java.util.Map;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

public class MQMessage {
   private static final Gson gson = new Gson();
   @NotNull
   private final String queueId;
   @NotNull
   private final String value;
   @Nullable
   private final String key;
   @NotNull
   private final Map<String, String> headers;

   public MQMessage(@NotNull String queueId, @NotNull Object value, @Nullable String key) {
      this(queueId, value, key, Map.of());
   }

   public MQMessage(@NotNull String queueId, @NotNull Object value) {
      this(queueId, value, (String)null, Map.of());
   }

   public MQMessage(@NotNull String queueId, @NotNull Object value, @Nullable String key, @NotNull Map<String, String> headers) {
      this.queueId = queueId;
      this.value = value instanceof String ? (String)value : gson.toJson(value);
      this.key = key;
      this.headers = headers;
   }

   public @NotNull String getQueueId() {
      return this.queueId;
   }

   public @NotNull String getValue() {
      return this.value;
   }

   public @Nullable String getKey() {
      return this.key;
   }

   public @NotNull Map<String, String> getHeaders() {
      return this.headers;
   }
}
