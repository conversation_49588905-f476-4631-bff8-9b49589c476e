package com.dc.summer.model.document.data;

import java.time.Instant;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDComposite;
import com.dc.summer.model.data.DBDValueCloneable;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.struct.AbstractDataType;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSAttributeDynamic;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.utils.CommonUtils;

public class DBMapValue<DATASOURCE extends DBPDataSource> extends AbstractDataType<DATASOURCE> implements DBDComposite, DBDValueCloneable {
   private static final Log log = Log.getLog(DBMapValue.class);
   @Nullable
   private final Object parent;
   @Nullable
   private Map<String, Object> contents;
   private boolean modified;

   public DBMapValue(@NotNull DATASOURCE dataSource, @Nullable Object parent, @Nullable Map<String, Object> sourceData) {
      super(dataSource);
      this.parent = parent;
      this.contents = sourceData;
   }

   public DBMapValue(@NotNull DATASOURCE dataSource, @Nullable Map<String, Object> sourceData) {
      this(dataSource, null, sourceData);
   }

   public DBMapValue(@NotNull DBMapValue<DATASOURCE> source, @Nullable Object parent) {
      super(source.getDataSource());
      this.parent = parent;
      this.contents = source.contents == null ? null : new LinkedHashMap(source.contents);
   }

   public Map<String, Object> getRawValue() {
      return this.contents;
   }

   public boolean isNull() {
      return this.contents == null;
   }

   public boolean isModified() {
      return this.modified;
   }

   public void setModified(boolean modified) {
      this.modified = modified;
   }

   public void release() {
      this.contents = null;
   }

   public @NotNull String getTypeName() {
      return "##map";
   }

   public int getTypeID() {
      return 2;
   }

   public DBPDataKind getDataKind() {
      return DBPDataKind.STRUCT;
   }

   public DBSDataType getDataType() {
      return this;
   }

   public @Nullable DBSDataType getComponentType(@NotNull DBRProgressMonitor monitor) throws DBException {
      return DBDocumentUtils.getDocumentDataType(this.getDataSource(), 11);
   }

   public @NotNull DBSAttributeBase[] getAttributes() {
      if (this.contents != null && !this.contents.isEmpty()) {
         Set<String> keySet = this.contents.keySet();
         MapAttribute[] attrs = new MapAttribute[keySet.size()];
         int index = 0;

         for(Iterator var5 = keySet.iterator(); var5.hasNext(); ++index) {
            String key = (String)var5.next();
            attrs[index] = new MapAttribute(this, index, key, this.contents.get(key));
         }

         return attrs;
      } else {
         return new DBSAttributeBase[0];
      }
   }

   public @Nullable Object getAttributeValue(@NotNull String attributeName) {
      return this.contents == null ? null : this.contents.get(attributeName);
   }

   public @Nullable Object getAttributeValue(@NotNull DBSAttributeBase attribute) {
      return this.contents == null ? null : this.contents.get(attribute.getName());
   }

   public void setAttributeValue(@NotNull DBSAttributeBase attribute, @Nullable Object value) throws DBCException {
      if (this.contents == null) {
         this.contents = new LinkedHashMap();
      }

      this.contents.put(attribute.getName(), value);
      this.modified = true;
   }

   public DBDValueCloneable cloneValue(DBRProgressMonitor monitor) throws DBCException {
      return new DBMapValue(this, (Object)null);
   }

   public @Nullable Object getParent() {
      return this.parent;
   }

   public String toString() {
      return CommonUtils.toString(this.getRawValue());
   }

   public String toJson() {
      return DBDocumentUtils.convertValueToJson(this.getDataSource(), this.contents);
   }

   private static MapValueType getMapValueTypeFromValue(Object value) {
      if (value == null) {
         return DBMapValue.MapValueType.NULL;
      } else if (value instanceof CharSequence) {
         return DBMapValue.MapValueType.STRING;
      } else if (value instanceof Boolean) {
         return DBMapValue.MapValueType.BOOLEAN;
      } else if (value instanceof Number) {
         return DBMapValue.MapValueType.NUMBER;
      } else if (!(value instanceof Date) && !(value instanceof Instant)) {
         if (value instanceof Collection) {
            return DBMapValue.MapValueType.ARRAY;
         } else {
            return !(value instanceof Map) && !(value instanceof DBMapValue) ? DBMapValue.MapValueType.OBJECT : DBMapValue.MapValueType.MAP;
         }
      } else {
         return DBMapValue.MapValueType.DATETIME;
      }
   }

   public static class MapAttribute<DATASOURCE extends DBPDataSource> implements DBSAttributeDynamic, DBMapElement<DATASOURCE> {
      private final DBMapValue<DATASOURCE> mapValue;
      String name;
      int position;
      MapValueType valueType;

      MapAttribute(DBMapValue<DATASOURCE> mapValue, int index, String name, Object value) {
         this.mapValue = mapValue;
         this.position = index;
         this.name = name;
         this.valueType = DBMapValue.getMapValueTypeFromValue(value);
      }

      @Property(
         viewable = true,
         order = 1
      )
      public @NotNull String getName() {
         return this.name;
      }

      @Property(
         viewable = true,
         order = 2
      )
      public int getOrdinalPosition() {
         return this.position;
      }

      @Property(
         viewable = true,
         order = 3
      )
      public String getTypeName() {
         return this.valueType.name();
      }

      public String getFullTypeName() {
         return this.getTypeName();
      }

      public int getTypeID() {
         return this.valueType.ordinal();
      }

      public DBPDataKind getDataKind() {
         return this.valueType.getDataKind();
      }

      public Integer getScale() {
         return null;
      }

      public Integer getPrecision() {
         return null;
      }

      public long getMaxLength() {
         return 0L;
      }

      public long getTypeModifiers() {
         return 0L;
      }

      public boolean equals(Object obj) {
         if (!(obj instanceof MapAttribute)) {
            return false;
         } else {
            MapAttribute attr = (MapAttribute)obj;
            return CommonUtils.equalObjects(this.name, attr.name) && this.valueType == attr.valueType && this.position == attr.position;
         }
      }

      public int hashCode() {
         return this.name.hashCode() + this.valueType.ordinal() + this.position;
      }

      public String toString() {
         return this.name + "; " + this.position;
      }

      public boolean isRequired() {
         return false;
      }

      public boolean isAutoGenerated() {
         return false;
      }

      public DBMapValue<DATASOURCE> getContainer() {
         return this.mapValue;
      }

      public boolean isDynamicAttribute() {
         return true;
      }
   }

   public static enum MapValueType {
      NULL(DBPDataKind.ANY),
      STRING(DBPDataKind.STRING),
      NUMBER(DBPDataKind.NUMERIC),
      BOOLEAN(DBPDataKind.BOOLEAN),
      DATETIME(DBPDataKind.DATETIME),
      ARRAY(DBPDataKind.ARRAY),
      MAP(DBPDataKind.STRUCT),
      OBJECT(DBPDataKind.OBJECT);

      private final DBPDataKind dataKind;

      private MapValueType(DBPDataKind dataKind) {
         this.dataKind = dataKind;
      }

      public DBPDataKind getDataKind() {
         return this.dataKind;
      }
   }
}
