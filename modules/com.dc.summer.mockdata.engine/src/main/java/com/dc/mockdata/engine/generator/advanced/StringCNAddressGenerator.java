package com.dc.mockdata.engine.generator.advanced;

import com.dc.mockdata.engine.generator.AbstractMockValueGenerator;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.io.IOException;
import java.util.Random;

public class StringCNAddressGenerator extends AbstractMockValueGenerator {

    private static final String[] PROVINCES = {
            "华夏省", "龙腾省", "锦绣省", "光明省", "祥瑞省"
    };

    private static final String[] CITIES = {
            "星辉市", "苍穹市", "晨曦市", "流云市", "碧波市"
    };

    private static final String[] DISTRICTS = {
            "天府区", "云霞区", "虹桥区", "金陵区", "玉泉区"
    };

    private static final String[] STREETS = {
            "和平街", "长安街", "建设街", "光华街", "文昌街"
    };

    // 获取随机数组元素
    private static String getRandomElement(String[] array) {
        Random random = new Random();
        int index = random.nextInt(array.length);
        return array[index];
    }

    // 生成随机家庭住址
    public static String generateRandomAddress() {
        Random random = new Random();

        String province = getRandomElement(PROVINCES);
        String city = getRandomElement(CITIES);
        String district = getRandomElement(DISTRICTS);
        String street = getRandomElement(STREETS);
        int buildingNumber = random.nextInt(100) + 1;
        int unitNumber = random.nextInt(20) + 1;
        int roomNumber = random.nextInt(500) + 1;

        return province + city + district + street + buildingNumber + "号" + unitNumber + "单元" + roomNumber + "室";
    }

    @Override
    protected Object generateOneValue(DBRProgressMonitor var1) throws DBException, IOException {
        return generateRandomAddress();
    }
}
