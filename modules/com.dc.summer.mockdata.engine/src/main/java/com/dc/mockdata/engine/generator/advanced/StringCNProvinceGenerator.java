package com.dc.mockdata.engine.generator.advanced;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * 中国省份
 * <AUTHOR>
 */
@Slf4j
public class StringCNProvinceGenerator extends AdvancedStringValueGenerator {

    private static List<String> PROVINCES;

    private static int provinces;

    public StringCNProvinceGenerator() {
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (provinces == 0) {
            PROVINCES = this.readDict("Chinese_province.txt");
            provinces = PROVINCES.size();
        }

        return this.isGenerateNULL() ? null : PROVINCES.get(this.random.nextInt(provinces));
    }
}
