package com.dc.mockdata.engine.generator.advanced;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * 中文姓名
 * <AUTHOR>
 */
@Slf4j
public class StringCNNameCorpusGenerator extends AdvancedStringValueGenerator {

    private static List<String> CORPUSNAMES;

    private static int corpusnames;

    public StringCNNameCorpusGenerator() {
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (corpusnames == 0) {
            CORPUSNAMES = this.readDict("Chinese_Names_Corpus.txt");
            corpusnames = CORPUSNAMES.size();
        }

        return this.isGenerateNULL() ? null : CORPUSNAMES.get(this.random.nextInt(corpusnames));
    }
}
