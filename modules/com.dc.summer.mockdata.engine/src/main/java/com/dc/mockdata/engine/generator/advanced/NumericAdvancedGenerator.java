package com.dc.mockdata.engine.generator.advanced;

import com.dc.mockdata.engine.MockDataUtils;
import com.dc.mockdata.engine.generator.AbstractMockValueGenerator;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.util.Map;

public class NumericAdvancedGenerator extends AbstractMockValueGenerator {

    private Double min;
    private Double max;
    private Integer precision;
    private Integer scale;

    public NumericAdvancedGenerator() {
    }

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);
        Double d = this.getDoubleProperty(properties, "minimum");
        if (d != null) {
            this.min = d;
        }

        d = this.getDoubleProperty(properties, "maximum");
        if (d != null) {
            this.max = d;
        }

        Object p = properties.get("precision");
        if (p != null) {
            this.precision = CommonUtils.toInt(p);
        }

        Object s = properties.get("scale");
        if (s != null) {
            this.scale = CommonUtils.toInt(s);
        }
    }

    @Override
    protected Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (this.isGenerateNULL()) {
            return null;
        } else {
            Integer attrScale = null;
            if (attribute != null) {
                attrScale = this.attribute.getScale();
            }
            if (this.scale != null) {
                if (attrScale == null) {
                    attrScale = this.scale;
                } else if (attrScale > this.scale) {
                    attrScale = this.scale;
                }
            }

            Integer attrPrecision = null;
            if (attribute != null) {
                attrPrecision = this.attribute.getPrecision();
            }
            if (this.precision != null) {
                if (attrPrecision == null) {
                    attrPrecision = this.precision;
                } else if (attrPrecision > this.precision) {
                    attrPrecision = this.precision;
                }
            }

            return MockDataUtils.generateNumeric(attrPrecision, attrScale, this.min, this.max, this.random);
        }
    }
}
