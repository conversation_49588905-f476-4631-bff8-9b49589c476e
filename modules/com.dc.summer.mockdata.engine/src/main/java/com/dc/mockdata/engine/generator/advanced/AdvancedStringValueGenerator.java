package com.dc.mockdata.engine.generator.advanced;

import com.dc.mockdata.engine.generator.AbstractStringValueGenerator;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public abstract class AdvancedStringValueGenerator extends AbstractStringValueGenerator {

    public AdvancedStringValueGenerator() {
    }

    protected List<String> readDict(String fileName) throws IOException {
        List<String> list = new ArrayList<>();
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(this.getClass().getClassLoader().getResourceAsStream("dictionaries/" + fileName))
        );
        String line = reader.readLine();

        while ((line = reader.readLine()) != null) {
            list.add(line);
        }

        return list;
    }
}
