
package com.dc.parser.ext.oracle.statement.dml;

import com.dc.parser.ext.oracle.segment.HintSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.ext.oracle.statement.OracleStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Oracle delete statement.
 */
@Getter
@Setter
public final class OracleDeleteStatement extends DeleteStatement implements OracleStatement {
    private HintSegment hint;
}
