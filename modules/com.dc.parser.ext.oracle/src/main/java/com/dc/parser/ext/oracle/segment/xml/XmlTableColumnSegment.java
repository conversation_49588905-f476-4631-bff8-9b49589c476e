
package com.dc.parser.ext.oracle.segment.xml;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.complex.ComplexExpressionSegment;

/**
 * Xml table column segment.
 */
@RequiredArgsConstructor
@Getter
public final class XmlTableColumnSegment implements ComplexExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String columnName;
    
    private final String dataType;
    
    private final String path;
    
    private final ExpressionSegment defaultExpr;
    
    private final String text;
}
