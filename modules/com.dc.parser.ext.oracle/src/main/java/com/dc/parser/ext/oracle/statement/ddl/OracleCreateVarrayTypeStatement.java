
package com.dc.parser.ext.oracle.statement.ddl;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.type.TypeSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.statement.ddl.CreateTypeStatement;
import com.dc.parser.ext.oracle.statement.OracleStatement;

@Getter
@Setter
@RequiredArgsConstructor
public final class OracleCreateVarrayTypeStatement extends CreateTypeStatement implements OracleStatement {
    
    private final boolean isReplace;
    
    private final boolean editionable;
    
    /** default -1 means that the size is not specified. */
    private final int size;
    
    private final boolean notNull;
    
    private final boolean isPersistable;
    
    private final TypeSegment typeSegment;
    
    private final DataTypeSegment dataType;
    
}
