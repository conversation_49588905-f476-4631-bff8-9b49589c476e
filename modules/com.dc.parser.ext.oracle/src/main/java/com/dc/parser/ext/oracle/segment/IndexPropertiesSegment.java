package com.dc.parser.ext.oracle.segment;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class IndexPropertiesSegment implements SQLSegment {
    private final int startIndex;

    private final int stopIndex;
    // 事务槽数量
    private int initrans;
    // 表空间
    private TablespaceSegment tablespaceSegment;
    // 默认表空间
    private boolean defaultTablespace;
}
