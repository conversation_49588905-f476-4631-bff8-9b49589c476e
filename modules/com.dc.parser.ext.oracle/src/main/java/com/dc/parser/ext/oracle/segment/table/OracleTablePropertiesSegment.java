package com.dc.parser.ext.oracle.segment.table;

import com.dc.parser.model.segment.ddl.table.TablePropertiesSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class OracleTablePropertiesSegment implements TablePropertiesSegment {
    private final int startIndex;

    private final int stopIndex;

    private PartitionPropertiesSegment partitionProperties;

    private SelectStatement selectStatement;
}
