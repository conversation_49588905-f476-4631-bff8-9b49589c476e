package com.dc.parser.ext.oracle.segment.dcl;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dcl.PrivilegeSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

@RequiredArgsConstructor
@Getter
public class OraclePrivilegesSegment implements SQLSegment {

    private final int startIndex;

    private final int stopIndex;

    @Setter
    private String objectName;

    private final Collection<PrivilegeSegment> privileges = new LinkedList<>();
}
