package com.dc.parser.exec.engine.statement.ddl;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.type.ColumnSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Create index statement binder.
 */
public final class CreateIndexStatementBinder implements SQLStatementBinder<CreateIndexStatement> {

    @Override
    public CreateIndexStatement bind(final CreateIndexStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        CreateIndexStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        binderContext.setOperation(SqlConstant.KEY_ALTER);
        result.setTable(SimpleTableSegmentBinder.bind(sqlStatement.getTable(), binderContext, tableBinderContexts));
        sqlStatement.getColumns()
                .forEach(each -> result.getColumns().add(ColumnSegmentBinder.bind(each, SegmentType.DEFINITION_COLUMNS, binderContext, tableBinderContexts, LinkedHashMultimap.create())));
        return result;
    }

    public void extractSqlAuthModel(final CreateIndexStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(SqlConstant.KEY_INDEX);
        IndexSegment indexSegment = sqlStatement.getIndex();
        sqlAuthModel.setName(indexSegment.getIndexName().getIdentifier().getValue());
        String schemaName = indexSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(binderContext.getCurrentDatabaseName());
        sqlAuthModel.setSchemaName(schemaName);
        binderContext.addSqlAuthModel(sqlAuthModel);
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static CreateIndexStatement copy(final CreateIndexStatement sqlStatement) {
        CreateIndexStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.setIndex(sqlStatement.getIndex());
        sqlStatement.getGeneratedIndexStartIndex().ifPresent(result::setGeneratedIndexStartIndex);
        result.setIfNotExists(sqlStatement.isIfNotExists());
        sqlStatement.getAlgorithmType().ifPresent(result::setAlgorithmType);
        sqlStatement.getLockTable().ifPresent(result::setLockTable);
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
