package com.dc.parser.exec.engine.statement;

import com.dc.parser.model.statement.SQLStatement;

/**
 * SQL statement binder.
 *
 * @param <T> type of SQL statement
 */
public interface SQLStatementBinder<T extends SQLStatement> {

    default T bindAndCollectAuthModel(T sqlStatement, SQLStatementBinderContext binderContext) {
        T boundStatement = bind(sqlStatement, binderContext);

        // 构造鉴权模型
        extractSqlAuthModel(boundStatement, binderContext);
        return boundStatement;
    }

    /**
     * Bind SQL statement.
     *
     * @param sqlStatement  SQL statement
     * @param binderContext SQL statement binder context
     * @return bound SQL statement
     */
    T bind(final T sqlStatement, final SQLStatementBinderContext binderContext);

    /**
     * Extract SQL authorization model.
     *
     * @param sqlStatement  SQL statement
     * @param binderContext SQL statement binder context
     */
    default void extractSqlAuthModel(final T sqlStatement, final SQLStatementBinderContext binderContext) {
    }
}
