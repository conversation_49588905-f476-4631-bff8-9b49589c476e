
package com.dc.parser.exec;

import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.SimpleSQLStatement;

/**
 * Simple SQL parser engine.
 */
public final class SimpleSQLParserEngine implements SQLParserEngine {
    
    @Override
    public SQLStatement parse(final String sql, final boolean useCache) {
        return new SimpleSQLStatement();
    }

    public static void main(String[] args) {
        System.out.println("ok");
    }

}
