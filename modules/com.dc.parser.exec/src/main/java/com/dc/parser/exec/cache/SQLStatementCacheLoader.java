
package com.dc.parser.exec.cache;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.exec.sql.SQLStatementParserExecutor;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.statement.SQLStatement;
import com.github.benmanes.caffeine.cache.CacheLoader;

import javax.annotation.ParametersAreNonnullByDefault;

/**
 * SQL statement cache loader.
 */
public final class SQLStatementCacheLoader implements CacheLoader<String, SQLStatement> {
    
    private final SQLStatementParserExecutor sqlStatementParserExecutor;
    
    public SQLStatementCacheLoader(final DatabaseType databaseType, final CacheOption parseTreeCacheOption) {
        sqlStatementParserExecutor = new SQLStatementParserExecutor(databaseType, parseTreeCacheOption);
    }
    
    @ParametersAreNonnullByDefault
    @Override
    public SQLStatement load(final String sql) {
        return sqlStatementParserExecutor.parse(sql);
    }
}
