package com.dc.summer.exec.model.observer;


import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCExecutionContext;

public interface ContextObserver {

    default void closeSpecifiedContext(String token) {
        // nothing to do
    }

    default void fillingConfigurationPassword(DBPConnectionConfiguration connectionConfiguration) {
        // nothing to do
    }

    default void printLogCloseLifeCycle(DBCExecutionContext context, String token, String scene, String errorMessage) {
        // nothing to do
    }

}
