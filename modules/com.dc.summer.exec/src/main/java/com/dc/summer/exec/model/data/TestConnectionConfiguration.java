package com.dc.summer.exec.model.data;

import com.dc.annotation.NotNull;
import com.dc.type.DatabaseType;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class TestConnectionConfiguration extends DBPConnectionConfiguration {

    @NotNull
    private DatabaseType databaseType;

    private String driverId;

    private String testSql;

}
