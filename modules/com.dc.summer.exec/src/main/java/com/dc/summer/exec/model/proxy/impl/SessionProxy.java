package com.dc.summer.exec.model.proxy.impl;

import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.proxy.DBPHandlerProxy;
import com.dc.utils.LoggerUtils;
import com.dc.utils.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.Statement;
import java.util.Set;

public class SessionProxy extends DBPHandlerProxy<DBCSession> {

    protected boolean activate;

    protected final DBPConnectionConfiguration configuration;

    public SessionProxy(DBCSession jdbcSession, DBPConnectionConfiguration configuration) {
        super(jdbcSession);
        this.configuration = configuration;
        this.activate = true;
    }

    private static final Set<Class<?>> CLASS_SET = Set.of(DBCSession.class, Connection.class);
    private static final Set<String> STATEMENT_SET = Set.of("prepareStatement", "createStatement", "prepareCall");

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {

        String methodName = method.getName();
        logging(methodName, args);

        try {
            Object result = method.invoke(getT(), args);

            if (STATEMENT_SET.contains(methodName)) {
                StatementProxy<?> statementProxy = new StatementProxy<>(result, this, activate);
                if (statementProxy.isMatching()) {
                    result = statementProxy.getInstance();
                }
                Integer queryTimeout = configuration.getQueryTimeout();
                if (getT().getPurpose() == DBCExecutionPurpose.USER_SCRIPT || queryTimeout == null || queryTimeout <= 0) {
                    queryTimeout = 0;
                }
                try {
                    if (result instanceof DBCStatement) {
                        ((DBCStatement) result).setStatementTimeout(queryTimeout);
                    } else if (result instanceof Statement) {
                        ((Statement) result).setQueryTimeout(queryTimeout);
                    }
                } catch (Exception e) {
                    LoggerUtils.print(HandlerCounter.getMarker(), "Can't set statement timeout:{}", e.getMessage());
                }
            }
            if (args != null) {
                for (Object arg : args) {
                    if (arg instanceof String) {
                        HandlerCounter.setSql((String) arg);
                    }
                }
            }
            return result;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw e.getCause();
        }
    }

    private void logging(String methodName, Object[] args) {
        try {
            boolean hasArgs = args != null && args.length > 0;
            boolean isPrepare = methodName.startsWith("prepare") && hasArgs;
            boolean isNative = methodName.equals("nativeSQL") && hasArgs;
            if (isNative || isPrepare) {
                for (Object arg : args) {
                    if (arg instanceof String) {
                        LoggerUtils.print(HandlerCounter.getMarker(), "\n==> {}:\n{}", StringUtils.firstUpper(methodName), arg);
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public Set<Class<?>> getClassSet() {
        return CLASS_SET;
    }
}
