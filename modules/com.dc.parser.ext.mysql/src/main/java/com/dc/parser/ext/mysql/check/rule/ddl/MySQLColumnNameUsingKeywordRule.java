package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.segment.ChangeColumnDefinitionSegment;
import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.ColumnNameUsingKeywordRule;
import com.dc.parser.model.statement.SQLStatement;

import java.util.Locale;

public class MySQLColumnNameUsingKeywordRule extends ColumnNameUsingKeywordRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof MySQLAlterTableStatement) {
            String[] objectName = parameter.getCheckRuleContent().getValue().toUpperCase(Locale.ROOT).split(",");

            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;
            boolean isValid = alterTableStatement.getChangeColumnDefinitions()
                    .stream()
                    .map(ChangeColumnDefinitionSegment::getColumnDefinition)
                    .anyMatch(columnDefinitionSegment -> buildFailResult(columnDefinitionSegment.getColumnName(), objectName));

            if (isValid) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return super.check(sqlStatement, parameter);
    }
}
