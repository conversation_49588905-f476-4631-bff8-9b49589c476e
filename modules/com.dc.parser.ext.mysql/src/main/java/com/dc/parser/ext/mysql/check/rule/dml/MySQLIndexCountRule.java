package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.ext.mysql.statement.ddl.MySQLCreateTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.IndexCountRule;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

public class MySQLIndexCountRule extends IndexCountRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        int count = Integer.parseInt(parameter.getCheckRuleContent().getValue());

        boolean isValid = true;

        if (sqlStatement instanceof MySQLCreateTableStatement) {

            MySQLCreateTableStatement createTableStatement = (MySQLCreateTableStatement) sqlStatement;

            isValid = createTableStatement.getRelationalTable()
                    .map(RelationalTableSegment::getConstraintDefinitions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(constraintDefinitionSegment -> constraintDefinitionSegment.getIndexName().isPresent())
                    .limit(count + 1)  // 提前停止流操作，如果超过 count 个元素
                    .count() <= count;

        } else if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            long addIndexCount = alterTableStatement.getAddConstraintDefinitions()
                    .stream()
                    .map(AddConstraintDefinitionSegment::getConstraintDefinition)
                    .filter(constraintDefinitionSegment -> constraintDefinitionSegment.getIndexName().isPresent())
                    .count();

            isValid = parameter.getIndexCount() + addIndexCount <= count;
        }

        if (!isValid) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return super.check(sqlStatement, parameter);
    }

    @Override
    public void prepare(SQLStatement sqlStatement, CheckRuleParameter parameter, Function<String, List<Map<String, Object>>> function) {
        if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            boolean existsAddIndex = alterTableStatement.getAddConstraintDefinitions()
                    .stream()
                    .map(AddConstraintDefinitionSegment::getConstraintDefinition)
                    .map(ConstraintDefinitionSegment::getIndexName)
                    .anyMatch(Optional::isPresent);

            if (existsAddIndex) {
                String schema = alterTableStatement.getTable().getOwner()
                        .flatMap(OwnerSegment::getOwner)
                        .map(OwnerSegment::getIdentifier)
                        .map(IdentifierValue::getValue)
                        .orElse(parameter.getDefaultSchemaName());

                String table = alterTableStatement.getTable().getTableName().getIdentifier().getValue();

                List<Map<String, Object>> result = function.apply(getQueryIndexCountSql(schema, table));
                if (!result.isEmpty() && result.get(0).get("COUNT") != null) {
                    int count = Integer.parseInt(result.get(0).get("COUNT").toString()) ;
                    parameter.setIndexCount(count);
                }
            }
        }
        super.prepare(sqlStatement, parameter, function);
    }

    @Override
    public String getQueryIndexCountSql(String schemaName, String tableName) {
        return String.format("SELECT COUNT(DISTINCT INDEX_NAME) AS COUNT\n" +
                "FROM information_schema.STATISTICS\n" +
                "WHERE TABLE_SCHEMA = '%s'\n" +
                "  AND TABLE_NAME = '%s'", schemaName, tableName);
    }
}
