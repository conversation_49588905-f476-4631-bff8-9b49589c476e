
package com.dc.parser.ext.mysql.parser;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.parser.SQLLexer;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.spi.DialectSQLParserFacade;

/**
 * SQL parser facade for MySQL.
 */
public final class MySQLParserFacade implements DialectSQLParserFacade {
    
    @Override
    public Class<? extends SQLLexer> getLexerClass() {
        return MySQLLexer.class;
    }
    
    @Override
    public Class<? extends SQLParser> getParserClass() {
        return MySQLParser.class;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.MYSQL;
    }
}
