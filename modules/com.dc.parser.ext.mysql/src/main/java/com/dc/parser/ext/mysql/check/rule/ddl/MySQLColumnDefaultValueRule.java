package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.ext.mysql.utils.MySQLAlterTableHelper;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Optional;
import java.util.Set;

import static com.dc.parser.ext.mysql.utils.MySQLCreateTableHelper.getColumnDefinitionsFromAlterTableStatement;

public class MySQLColumnDefaultValueRule implements SQLRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_DEFAULT_VALUE.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        boolean hasDefaultValue = true;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            hasDefaultValue = getColumnDefinitionsFromAlterTableStatement(createTableStatement)
                    .filter(columnDefinitionSegment -> !columnDefinitionSegment.isAutoIncrement())
                    .filter(columnDefinitionSegment -> !filterType().contains(columnDefinitionSegment.getDataType().getDataTypeName().toUpperCase()))
                    .map(ColumnDefinitionSegment::getDefaultSegment)
                    .allMatch(Optional::isPresent);

        } else if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            hasDefaultValue = MySQLAlterTableHelper.getColumnDefinitionsFromAlterTableStatement(alterTableStatement)
                    .filter(columnDefinitionSegment -> !columnDefinitionSegment.isAutoIncrement())
                    .filter(columnDefinitionSegment -> !filterType().contains(columnDefinitionSegment.getDataType().getDataTypeName().toUpperCase()))
                    .map(ColumnDefinitionSegment::getDefaultSegment)
                    .allMatch(Optional::isPresent);
        }

        if (!hasDefaultValue) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    /**
     * 过滤二进制类型字段
     *
     * @return filter type set
     */
    protected Set<String> filterType() {
        return Set.of("TINYBLOB", "BLOB", "MEDIUMBLOB", "LONGBLOB", "BINARY", "VARBINARY");
    }
}
