package com.dc.parser.ext.mysql.segment;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class DataTypeCharsetSegment implements SQLSegment {
    private final int startIndex;

    private final int stopIndex;

    private CharsetNameSegment charsetName;
}
