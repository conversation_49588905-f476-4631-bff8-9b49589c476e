package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.statement.ddl.MySQLCreateTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.table.CreateTableOptionSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.literal.impl.NumberLiteralValue;

public class MySQLAutoIncrementInitRule implements SQLRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_INITIAL_VALUE.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof MySQLCreateTableStatement) {
            MySQLCreateTableStatement createTableStatement = (MySQLCreateTableStatement) sqlStatement;

            int initValue = Integer.parseInt(parameter.getCheckRuleContent().getValue());

            boolean isRuleSatisfied = createTableStatement.getCreateTableOption()
                    .flatMap(CreateTableOptionSegment::getAutoIncrement)
                    .map(NumberLiteralValue::getValue)
                    .stream()
                    .allMatch(number -> number.intValue() == initValue);

            if (!isRuleSatisfied) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
