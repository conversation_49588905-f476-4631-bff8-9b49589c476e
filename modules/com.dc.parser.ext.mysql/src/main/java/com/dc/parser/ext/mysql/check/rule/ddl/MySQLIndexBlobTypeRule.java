package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dc.parser.ext.mysql.utils.MySQLCreateTableHelper.getColumnDefinitionsFromAlterTableStatement;

public class MySQLIndexBlobTypeRule extends IndexTypeRule {

    public final Set<String> BLOB_TYPES = Set.of("BLOB", "TINYBLOB", "MEDIUMBLOB", "LONGBLOB");

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_BLOB_INDEX.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (parameter.isExistsIndexBlobType()) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        } else if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            //index column set
            Set<String> columnSet = createTableStatement.getRelationalTable()
                    .map(RelationalTableSegment::getConstraintDefinitions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(ConstraintDefinitionSegment::getIndexColumns)
                    .flatMap(Collection::stream)
                    .map(ColumnSegment::getText)
                    .collect(Collectors.toSet());

            if (!columnSet.isEmpty()) {

                boolean existsBlob = getColumnDefinitionsFromAlterTableStatement(createTableStatement)
                        .filter(columnDefinitionSegment -> columnSet.contains(columnDefinitionSegment.getColumnName().getText()))
                        .map(columnDefinitionSegment -> columnDefinitionSegment.getDataType().getDataTypeName())
                        .anyMatch(name -> BLOB_TYPES.contains(name.toUpperCase()));

                if (existsBlob) {
                    return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                }
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public String buildSqlQuery(String tableName, String schema, List<String> fields) {
        // 将 List<String> 转换为一个逗号分隔的字符串，每个元素用单引号包裹
        String fieldList = fields.stream()
                .map(field -> "'" + field + "'")
                .collect(Collectors.joining(", "));

        // 构建最终的 SQL 查询

        return String.format(
                "SELECT *\n" +
                        "FROM information_schema.columns\n" +
                        "WHERE TABLE_NAME = '%s'\n" +
                        "  AND TABLE_SCHEMA = '%s'\n" +
                        "  AND COLUMN_NAME IN (%s)\n" +
                        "AND DATA_TYPE IN ('blob', 'tinyblob', 'mediumblob', 'longblob')",
                tableName, schema, fieldList
        );
    }

    @Override
    protected void processResultSet(CheckRuleParameter parameter, List<String> fields, List<Map<String, Object>> resultSet) {
        parameter.setExistsIndexBlobType(CollectionUtils.isNotEmpty(resultSet));
    }
}
