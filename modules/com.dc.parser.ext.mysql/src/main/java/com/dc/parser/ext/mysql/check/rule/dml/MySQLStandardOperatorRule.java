package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.check.rule.listener.MySQLStandardOperatorListener;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class MySQLStandardOperatorRule implements SQLRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {
        if (sqlStatement.getParseTree() != null) {
            MySQLStandardOperatorListener listener = new MySQLStandardOperatorListener();
            parseTreeWalker.walk(listener, sqlStatement.getParseTree());
            if (listener.isExistsNoStandardOperator()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DML_CHECK_STANDARD_OPERATOR.getValue();
    }
}
