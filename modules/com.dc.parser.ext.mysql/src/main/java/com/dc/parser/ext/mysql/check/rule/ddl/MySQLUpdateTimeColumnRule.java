package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.ddl.UpdateTimeColumnRule;

public class MySQLUpdateTimeColumnRule extends UpdateTimeColumnRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public String getDefaultValue() {
        return "CURRENT_TIMESTAMP";
    }
}
