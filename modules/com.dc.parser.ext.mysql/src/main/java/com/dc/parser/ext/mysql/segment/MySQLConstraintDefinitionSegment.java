package com.dc.parser.ext.mysql.segment;

import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Getter
@Setter
public class MySQLConstraintDefinitionSegment extends ConstraintDefinitionSegment {

    private IndexSpecificationEnum indexSpecification;

    public MySQLConstraintDefinitionSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }

    public Optional<IndexSpecificationEnum> getIndexSpecification() {
        return Optional.ofNullable(indexSpecification);
    }
}
