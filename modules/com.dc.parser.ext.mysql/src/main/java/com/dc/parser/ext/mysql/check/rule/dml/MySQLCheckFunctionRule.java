package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.check.rule.listener.MySQLCheckFunctionListener;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.DMLStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class MySQLCheckFunctionRule implements SQLRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_FUNCTION_SYSDATE.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof DMLStatement && sqlStatement.getParseTree() != null) {
            MySQLCheckFunctionListener listener = new MySQLCheckFunctionListener();
            parseTreeWalker.walk(listener, sqlStatement.getParseTree());
            if (listener.isExistsFuction()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
