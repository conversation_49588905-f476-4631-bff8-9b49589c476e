package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.segment.ChangeColumnDefinitionSegment;
import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.ColumnNameLengthRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public class MySQLColumnNameLengthRule extends ColumnNameLengthRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {
        if (sqlStatement instanceof MySQLAlterTableStatement) {

            int reqLen = Integer.parseInt(parameter.getCheckRuleContent().getValue());

            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            boolean isValid = alterTableStatement.getChangeColumnDefinitions()
                    .stream()
                    .map(ChangeColumnDefinitionSegment::getColumnDefinition)
                    .map(ColumnDefinitionSegment::getColumnName)
                    .map(ColumnSegment::getIdentifier)
                    .map(IdentifierValue::getValue)
                    .anyMatch(columnName -> columnName.length() > reqLen);

            if (isValid) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return super.check(sqlStatement, parameter);
    }
}
