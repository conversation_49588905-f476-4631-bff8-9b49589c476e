package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.ddl.NumberOfColumnsRule;

public class MySQLNumberOfColumnsRule extends NumberOfColumnsRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public String getQueryColumnCountSql(String schemaName, String tableName) {
        return String.format("SELECT COUNT(*) AS COUNT\n" +
                "FROM information_schema.COLUMNS\n" +
                "WHERE TABLE_SCHEMA = '%s'\n" +
                "  AND TABLE_NAME = '%s'", schemaName, tableName);
    }
}
