package com.dc.parser.ext.mysql.statement.dml;

import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * MySQL update statement.
 */
@Setter
public final class MySQLUpdateStatement extends UpdateStatement implements MySQLStatement {

    private WithSegment withSegment;
    
    private OrderBySegment orderBy;
    
    private LimitSegment limit;

    @Override
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get order by segment.
     *
     * @return order by segment
     */
    public Optional<OrderBySegment> getOrderBy() {
        return Optional.ofNullable(orderBy);
    }
    
    /**
     * Get order by segment.
     *
     * @return order by segment
     */
    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }
}
