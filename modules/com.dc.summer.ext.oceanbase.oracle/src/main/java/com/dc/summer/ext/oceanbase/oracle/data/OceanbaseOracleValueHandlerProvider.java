package com.dc.summer.ext.oceanbase.oracle.data;

import com.dc.summer.ext.oceanbase.oracle.model.OceanbaseOracleConstants;
import com.dc.summer.ext.oracle.data.OracleTimestampValueHandler;
import com.dc.summer.ext.oracle.data.OracleValueHandlerProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.impl.jdbc.JDBCConstants;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStandardValueHandlerProvider;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCTemporalAccessorValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.Types;

public class OceanbaseOracleValueHandlerProvider extends OracleValueHandlerProvider {

    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {

        if (JDBCConstants.TYPE_RAW.equalsIgnoreCase(typedObject.getTypeName())) {
            return new OceanbaseOracleRawValueHandler();
        }
        if (OceanbaseOracleConstants.TYPE_ROWID.equalsIgnoreCase(typedObject.getTypeName())) {
            return new OceanbaseOracleRowidValueHandler();
        }

        if (OceanbaseOracleConstants.TYPE_BINARY_FLOAT.equalsIgnoreCase(typedObject.getTypeName())
                || OceanbaseOracleConstants.TYPE_BINARY_DOUBLE.equalsIgnoreCase(typedObject.getTypeName())
                || OceanbaseOracleConstants.TYPE_CURSOR.equalsIgnoreCase(typedObject.getTypeName())) {
            return new OceanbaseOracleNumberValueHandler(typedObject, null);
        }

        if (OceanbaseOracleConstants.TYPE_TIMESTAMPTZ.equalsIgnoreCase(typedObject.getTypeName())) {
            return new OceanBaseOracleDateTimeValueHandler(preferences);
        }

        if (OceanbaseOracleConstants.TYPE_TIMESTAMPLTZ.equalsIgnoreCase(typedObject.getTypeName())) {
            return new OracleTimestampValueHandler(preferences);
        }


        return super.getValueHandler(dataSource, preferences, typedObject);
    }
}
