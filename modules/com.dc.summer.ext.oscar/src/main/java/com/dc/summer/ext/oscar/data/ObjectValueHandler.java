package com.dc.summer.ext.oscar.data;

import com.dc.code.NotNull;
import com.dc.summer.ext.oscar.internal.OscarMessages;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCAbstractValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class ObjectValueHandler extends JDBCAbstractValueHandler {
   public static final ObjectValueHandler INSTANCE = new ObjectValueHandler();

   @NotNull
   public String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
      return value != null ? "[OBJECT]" : super.getValueDisplayString(column, value, format);
   }

   protected ObjectValue fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
      Object object = resultSet.getObject(index);
      return this.getValueFromObject(session, type, object, false, false);
   }

   protected void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws DBCException, SQLException {
      throw new DBCException(OscarMessages.ObjectValueHandler_parameter_bind_is_not_implemented);
   }

   @NotNull
   public Class getValueObjectType(@NotNull DBSTypedObject attribute) {
      return Object.class;
   }

   @Override
   public ObjectValue getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
      if (object == null) {
         return new ObjectValue((Object)null);
      } else if (object instanceof ObjectValue) {
         return copy ? new ObjectValue(((ObjectValue)object).getValue()) : (ObjectValue)object;
      } else {
         return new ObjectValue(object);
      }
   }
}
