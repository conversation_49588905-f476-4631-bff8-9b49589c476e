package com.dc.summer.struct.compare.impl.liquibase.compare;

import liquibase.diff.DiffResult;
import liquibase.diff.core.StandardDiffGenerator;
import liquibase.snapshot.DatabaseSnapshot;
import liquibase.structure.AbstractDatabaseObject;
import liquibase.structure.DatabaseObject;
import liquibase.structure.DatabaseObjectCollection;
import liquibase.structure.core.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class LBDataDiffGenerator extends StandardDiffGenerator {


    @Override
    public int getPriority() {
        return PRIORITY_DATABASE;
    }

    @Override
    protected <T extends DatabaseObject> void compareObjectType(Class<T> type, DatabaseSnapshot referenceSnapshot, DatabaseSnapshot comparisonSnapshot, DiffResult diffResult) {

        if (diffResult.getCompareControl() instanceof LBDataCompareControl) {
            LBDataCompareControl control = (LBDataCompareControl) diffResult.getCompareControl();

            DatabaseObject sourceDatabaseObject = control.getOptions().getSourceDatabaseObject();
            DatabaseObject targetDatabaseObject = control.getOptions().getTargetDatabaseObject();

            if (sourceDatabaseObject != null && sourceDatabaseObject.getName() != null &&
                    targetDatabaseObject != null && targetDatabaseObject.getName() != null &&
                    !sourceDatabaseObject.getName().equalsIgnoreCase(targetDatabaseObject.getName())) {

                if (sourceDatabaseObject instanceof Table && targetDatabaseObject instanceof Table) {

                    Optional<Table> referenceOptional = referenceSnapshot.get(Table.class).stream().filter(t -> t.getName().equalsIgnoreCase(sourceDatabaseObject.getName())).findFirst()
                            .or(() -> referenceSnapshot.get(Table.class).stream().filter(t -> t.getName().equalsIgnoreCase(targetDatabaseObject.getName())).findFirst());
                    Optional<Table> comparisonOptional = comparisonSnapshot.get(Table.class).stream().filter(t -> t.getName().equalsIgnoreCase(targetDatabaseObject.getName())).findFirst();

                    if (referenceOptional.isPresent() && comparisonOptional.isPresent()) {
                        // 需要刷新快照的缓存
                        DatabaseObjectCollection allFound = (DatabaseObjectCollection) referenceSnapshot.getSerializableFieldValue("objects");

                        if (type == Table.class) {
                            referenceOptional.get().setName(comparisonOptional.get().getName());
                            allFound.add(referenceOptional.get());

                        } else {
                            // 引用的表 全部改成 比较的表
                            for (T referenceObject : referenceSnapshot.get(type)) {
                                if (referenceObject instanceof AbstractDatabaseObject) {

                                    Table table = referenceObject.getAttribute("table", Table.class);
                                    if (table != null && table.getName().equalsIgnoreCase(referenceOptional.get().getName())) {
                                        table.setName(comparisonOptional.get().getName());
                                    } else {
                                        Relation relation = referenceObject.getAttribute("relation", Relation.class);
                                        if (relation instanceof Table && relation.getName().equalsIgnoreCase(referenceOptional.get().getName())) {
                                            relation.setName(comparisonOptional.get().getName());
                                        }
                                    }
                                    allFound.add(referenceObject);
                                }
                            }
                        }
                    }

                } else if (sourceDatabaseObject instanceof View && targetDatabaseObject instanceof View) {
                    // nothing to do here
                }
            }
        }

        super.compareObjectType(type, referenceSnapshot, comparisonSnapshot, diffResult);
    }
}
