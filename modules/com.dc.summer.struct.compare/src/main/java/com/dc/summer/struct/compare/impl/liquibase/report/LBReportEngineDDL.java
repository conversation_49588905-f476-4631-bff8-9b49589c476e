package com.dc.summer.struct.compare.impl.liquibase.report;

import com.dc.summer.struct.compare.impl.liquibase.LBResultChangeItem;
import com.dc.summer.struct.compare.impl.liquibase.LBResultChangeSet;
import com.dc.summer.struct.compare.model.CMPException;
import com.dc.summer.struct.compare.model.CMPQueryReportEngine;
import com.dc.summer.struct.compare.model.CMPResult;
import com.dc.summer.struct.compare.model.CMPResultQuery;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import liquibase.change.Change;
import liquibase.changelog.ChangeSet;
import liquibase.database.Database;
import liquibase.sql.Sql;
import liquibase.sqlgenerator.SqlGeneratorFactory;
import liquibase.statement.SqlStatement;
import com.dc.summer.Log;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;

public class LBReportEngineDDL extends LBReportEngineAbstract implements CMPQueryReportEngine {
   private static final Log log = Log.getLog(LBReportEngineDDL.class);

   public void generateDiffReport(DBRProgressMonitor monitor, CMPResult result, OutputStream stream) throws IOException, CMPException {
      LBResultChangeSet changeSet = (LBResultChangeSet)result.getChangeSet();
      List<ChangeSet> lbChangeSets = changeSet.getSourceDiff();
      Database targetDatabase = changeSet.getTargetSnapshot().getDatabase();
      if (targetDatabase == null) {
         targetDatabase = changeSet.getSourceSnapshot().getDatabase();
      }

      Writer writer = new OutputStreamWriter(stream, GeneralUtils.getDefaultFileEncoding());
      int changeCount = this.countChanges(result);
      monitor.beginTask("Generate DDL for changeset", changeCount);

      for (ChangeSet changeset : lbChangeSets) {
         List<Change> changes = changeset.getChanges();

         for (Change change : changes) {
            monitor.subTask("Process change " + change.getDescription());
            if (changeSet.isChangeEnabled(change)) {
               try {
                  targetDatabase.saveStatements(change, null, writer);
               } catch (Exception var15) {
                  log.debug("Error generating change SQL: " + var15.getMessage());
               }

               monitor.worked(1);
            }
         }

         monitor.done();
      }

      writer.flush();
   }

   public CMPResultQuery[] generateDiffQueries(DBRProgressMonitor monitor, CMPResult result) throws IOException, CMPException {
      List<CMPResultQuery> queries = new ArrayList<>();
      LBResultChangeSet changeSet = (LBResultChangeSet)result.getChangeSet();
      Database targetDatabase = changeSet.getTargetSnapshot().getDatabase();
      int changeCount = this.countChanges(result);
      monitor.beginTask("Generate DDL for changeset", changeCount);
      Iterator<LBResultChangeItem> iterator = changeSet.getChangeItems().iterator();

      while(true) {
         LBResultChangeItem changeItem;
         do {
            if (!iterator.hasNext()) {
               return queries.toArray(new CMPResultQuery[0]);
            }

            changeItem = iterator.next();
            monitor.subTask("Process change " + changeItem.getChange().getDescription());
         } while(!changeItem.isEnabled());

         try {
            SqlStatement[] statements = changeItem.getChange().generateStatements(targetDatabase);

            for (SqlStatement statement : statements) {
               Sql[] sqls = SqlGeneratorFactory.getInstance().generateSql(statement, targetDatabase);

               for (int i = 0; i < sqls.length; ++i) {
                  Sql sql = sqls[i];
                  queries.add(new CMPResultQuery(changeItem, sql.toSql() + sql.getEndDelimiter()));
               }
            }
         } catch (Exception var18) {
            log.debug("Error generating change SQL: " + var18.getMessage());
         }

         monitor.worked(1);
      }
   }
}
