package com.dc.summer.struct.compare.impl.liquibase;

import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.summer.struct.compare.model.CMPOptions;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import liquibase.database.Database;
import liquibase.database.core.OracleDatabase;
import liquibase.diff.ObjectDifferences;
import liquibase.diff.output.ObjectChangeFilter;
import liquibase.structure.DatabaseObject;
import liquibase.structure.core.*;
import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;

public class LBDiffObjectChangeFilter implements ObjectChangeFilter {
    private final CMPOptions options;
    private final DBPDataSource sqlDataSource;
    private final Database lbDatabase;
    private final Map<String, List<DBSObject>> inputObjects = new HashMap<>();
    private final Map<String, DBSObject> outputObjects = new HashMap<>();

    LBDiffObjectChangeFilter(@NotNull CMPOptions options, @NotNull Database lbDatabase) {
        this.options = options;
        this.sqlDataSource = options.getSourceDataSourceContainer().getDataSource();
        this.lbDatabase = lbDatabase;
        Iterator<DBSObject> iterator = options.getSourceInputObjects().iterator();

        DBSObject object;
        String containerName;
        while (iterator.hasNext()) {
            object = iterator.next();
            if (!(object instanceof DBSObjectContainer) && !(object instanceof DBPDataSourceContainer)) {
                if (object != null) {
                    DBSObjectContainer objectContainer = DBUtils.getParentOfType(DBSObjectContainer.class, object);
                    List<DBSObject> objects = this.inputObjects.computeIfAbsent(this.getContainerName(objectContainer), dbsObjectContainer -> new ArrayList<>());
                    objects.add(object);
                }
            } else {
                containerName = "";
                if (object instanceof DBSObjectContainer) {
                    containerName = this.getContainerName((DBSObjectContainer) object);
                }

                this.inputObjects.computeIfAbsent(containerName, dbsObjectContainer -> new ArrayList<>());
            }
        }

        if (!options.getTargetInputObjects().isEmpty()) {
            iterator = options.getTargetInputObjects().iterator();

            while (true) {
                do {
                    if (!iterator.hasNext()) {
                        return;
                    }

                    object = iterator.next();
                } while (!(object instanceof DBSObjectContainer) && !(object instanceof DBPDataSourceContainer));

                containerName = "";
                if (object instanceof DBSObjectContainer) {
                    containerName = this.getContainerName((DBSObjectContainer) object);
                }

                this.outputObjects.put(containerName, object);
            }
        }

    }

    private String getContainerName(DBSObjectContainer object) {
        if (object instanceof DBPDataSource) {
            return "";
        } else {
            List<String> fqNames = new ArrayList<>();
            for (DBSObject co = object; co != null && !(co instanceof DBPDataSource) && !(co instanceof DBPDataSourceContainer); co = co.getParentObject()) {
                if (co instanceof DBSSchema && this.lbDatabase.supportsSchemas()) {
                    fqNames.add(0, DBUtils.getQuotedIdentifier(this.sqlDataSource, co.getName(), true, true));
                }
                if (co instanceof DBSCatalog && this.lbDatabase.supportsCatalogs()) {
                    fqNames.add(0, DBUtils.getQuotedIdentifier(this.sqlDataSource, co.getName(), true, true));
                }
            }

            return String.join(this.sqlDataSource.getSQLDialect().getCatalogSeparator(), fqNames);
        }
    }

    private String getContainerName(DatabaseObject object) {
        Schema schema = object.getSchema();
        Catalog catalog = schema == null ? null : schema.getCatalog();
        if (schema == null) {
            return "";
        } else {
            List<String> fqNames = new ArrayList<>();
            if (catalog != null && catalog.getName() != null && this.lbDatabase.supportsCatalogs()) {
                fqNames.add(DBUtils.getQuotedIdentifier(this.sqlDataSource, catalog.getName(), true, true));
            }
            if (schema.getName() != null && this.lbDatabase.supportsSchemas()) {
                fqNames.add(DBUtils.getQuotedIdentifier(this.sqlDataSource, schema.getName(), true, true));
            }
            return String.join(this.sqlDataSource.getSQLDialect().getCatalogSeparator(), fqNames);
        }
    }

    @Override
    public boolean includeMissing(DatabaseObject object, Database referenceDatabase, Database comparisionDatabase) {
        return this.options.isDoCreate() && this.containsIndex(object);
    }

    @Override
    public boolean includeUnexpected(DatabaseObject object, Database referenceDatabase, Database comparisionDatabase) {
        if (!this.options.isDoDrop() || !this.containsIndex(object)) {
            return false;
        } else {
            String targetContainerName = this.getContainerName(object);
            DBSObject outContainer = this.outputObjects.get(targetContainerName);
            if (outContainer == null) {
                return false;
            } else {
                DBSObjectContainer sourceContainer = this.options.getSourceContainer(outContainer.getName());
                if (sourceContainer == null) {
                    return false;
                } else {
                    String sourceContainerName = this.getContainerName(sourceContainer);
                    List<DBSObject> sourceObjects = this.inputObjects.get(sourceContainerName);
                    if (sourceObjects == null) {
                        return false;
                    } else {
                        String parentName = LBUtils.getParentName(object);
                        return CommonUtils.isNotEmpty(parentName) && !sourceObjects.isEmpty() ?
                                sourceObjects.stream().anyMatch(e -> parentName.equals(e.getName())) :
                                sourceObjects.isEmpty();
                    }
                }
            }
        }
    }

    @Override
    public boolean includeChanged(DatabaseObject object, ObjectDifferences differences, Database referenceDatabase, Database comparisionDatabase) {
        return this.options.isDoChange() && this.containsObject(object) && this.containsIndex(object);
    }

    @Override
    public boolean include(DatabaseObject object) {
        return this.containsObject(object);
    }

    private boolean containsIndex(DatabaseObject object) {
        if (object instanceof Index) {
            Relation relation = ((Index) object).getRelation();
            if (relation instanceof Table) {
                Table table = (Table) relation;
                PrimaryKey primaryKey = table.getPrimaryKey();
                if (primaryKey != null && object.equals(primaryKey.getBackingIndex())) {
                    return false;
                }
                List<UniqueConstraint> uniqueConstraints = relation.getUniqueConstraints();
                if (CollectionUtils.isNotEmpty(uniqueConstraints)) {
                    for (UniqueConstraint uniqueConstraint : uniqueConstraints) {
                        if (object.equals(uniqueConstraint.getBackingIndex())) {
                            return false;
                        }
                    }
                }
                List<ExcludeConstraint> excludeConstraints = table.getAttribute("excludeConstraints", List.class);
                if (CollectionUtils.isNotEmpty(excludeConstraints)) {
                    for (ExcludeConstraint excludeConstraint : excludeConstraints) {
                        if (object.equals(excludeConstraint.getBackingIndex())) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    private boolean containsObject(DatabaseObject object) {
        if (object == null) {
            return false;
        } else if (LBUtils.isTableAttributes(object)) {
            if (object instanceof ForeignKey) {
                ForeignKey fk = (ForeignKey) object;
                return this.containsObject(fk.getForeignKeyTable());
            } else {
                DatabaseObject[] containingObjects = object.getContainingObjects();
                return containingObjects != null && object.getContainingObjects().length != 0 && this.containsObject(object.getContainingObjects()[0]);
            }
        } else {
            String containerName = this.getContainerName(object);
            List<DBSObject> objectList = this.inputObjects.get(containerName);
            DBSObject incObject;
            if (objectList == null) {
                if (object instanceof View && this.lbDatabase instanceof OracleDatabase) {
                    incObject = this.outputObjects.get(containerName);
                    if (incObject != null) {
                        return true;
                    }
                }

                DatabaseObject[] containingObjects = object.getContainingObjects();
                if (containingObjects == null) {
                    return false;
                } else {
                    DatabaseObject[] var8 = containingObjects;
                    int var7 = containingObjects.length;

                    for (int var6 = 0; var6 < var7; ++var6) {
                        DatabaseObject co = var8[var6];
                        if (!this.containsObject(co)) {
                            return false;
                        }
                    }

                    return true;
                }
            } else if (objectList.isEmpty()) {
                return true;
            } else {

                for (DBSObject dbsObject : objectList) {
                    if (CommonUtils.equalObjects(dbsObject.getName(), object.getName())) {
                        return true;
                    }
                }

                return false;
            }
        }
    }

}
