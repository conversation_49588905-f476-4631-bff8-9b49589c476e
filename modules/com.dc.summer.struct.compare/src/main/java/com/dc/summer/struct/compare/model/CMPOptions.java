package com.dc.summer.struct.compare.model;

import com.dc.summer.struct.compare.SCMPConstants;
import com.dc.summer.struct.compare.impl.liquibase.LBUtils;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import liquibase.structure.DatabaseObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.summer.model.task.DBTTaskSettingsInput;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.utils.GeneralUtils;
import com.dc.summer.utils.PrefUtils;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.utils.CommonUtils;
import lombok.Getter;
import lombok.Setter;

public class CMPOptions implements DBTTaskSettingsInput<DBSObject> {
   private static final Log log = Log.getLog(CMPOptions.class);
   private static final String TAG_DO_CREATE = "doCreate";
   private static final String TAG_DO_DROP = "doDrop";
   private static final String TAG_DO_CHANGE = "doChange";
   private static final String TAG_QUOTE_ALL_OBJECTS_NAMES = "quoteAllObjectsNames";
   private static final String TAG_CASE_INSENSITIVE_COMPARE = "caseInsensitiveCompare";
   private static final String TAG_EXPORT_FILE = "exportFile";
   private static final String TAG_OUTPUT_FOLDER_PATH = "outputFolderPath";
   private static final String TAG_OUTPUT_FILE_PATTERN = "outputFilePattern";
   private static final String TAG_REPORT_ENGINE = "reportEngineId";
   private static final String TAG_LOG_LEVEL = "logLevel";
   private static final String TAG_PROJECT = "project";
   private static final String TAG_CONTAINERS_MAPPING = "containersMapping";
   private static final String TAG_SOURCE_CONTAINER_ID = "sourceContainerId";
   private static final String TAG_TARGET_CONTAINER_ID = "targetContainerId";
   private static final String TAG_SOURCE_OBJECTS = "sourceObjects";
   private static final String TAG_TARGET_OBJECTS = "targetObjects";
   private static final String TAG_ONLY_CHANGELOG = "onlyChangelog";
   private static final String TAG_EXCLUDED_OBJECTS = "excludedObjects";
   private static final String TAG_HAS_LICENSE = "hasLiquibaseLicense";
   private static final String DEFAULT_REPORT_DDL = "ddl";
   private final Map<DBSObjectContainer, DBSObjectContainer> containerMapping = new HashMap();
   private final List<DBSObjectType> objectTypes = new ArrayList();
   private Set<Class<? extends DatabaseObject>> excludedObjectTypes = new HashSet();
   private DBPDataSourceContainer sourceDataSourceContainer;
   private DBPDataSourceContainer targetDataSourceContainer;
   private List<DBSObject> sourceInputObjects = new ArrayList();
   private List<DBSObject> targetInputObjects = new ArrayList();
   private boolean onlyChangelog;
   private String diffEngineId;
   private String reportEngineId = "ddl";
   private boolean doCreate;
   private boolean doDrop;
   private boolean doChange;
   private boolean quoteAllObjectsNames;
   private boolean hasLiquibaseLicense;
   private boolean caseInsensitiveCompare;
   private SCMPConstants.LogLevels logLevel;
   private Path changeLog;
   private boolean exportFile;
   private String outputFolderPath;
   private String outputFilePattern;
   private Path outputFolder;

   private CMPResultChangeSet sourceChangeSet;

   private CMPResultChangeSet targetChangeSet;

   @Getter
   @Setter
   private DatabaseObject filterDatabaseObject;

   @Getter
   @Setter
   private DatabaseObject sourceDatabaseObject;

   @Getter
   @Setter
   private DatabaseObject targetDatabaseObject;

   @Getter
   @Setter
   private Class<? extends DatabaseObject>[] snapshotTypes;

   @Getter
   @Setter
   private String taskName;

   public CMPOptions(@NotNull DBPPreferenceStore globalPreferences, boolean onlyChangelog, Class<? extends DatabaseObject>[] snapshotTypes) {
      this.diffEngineId = "liquibase";
      this.logLevel = SCMPConstants.LogLevels.OFF;
      this.onlyChangelog = onlyChangelog;
      this.iniDefaultPreferences(globalPreferences);
      this.doCreate = onlyChangelog || globalPreferences.getBoolean("@dbeaver-create-missing-objects@");
      this.doChange = globalPreferences.getBoolean("@dbeaver-alter-existing-objects@");
      this.doDrop = globalPreferences.getBoolean("@dbeaver-drop-unexpected-objects@");
      this.caseInsensitiveCompare = globalPreferences.getBoolean("case-insensitive-compare");
      this.quoteAllObjectsNames = globalPreferences.getBoolean("quote-all-objects");
      this.exportFile = globalPreferences.getBoolean("export-result-file");
      this.outputFolderPath = globalPreferences.getString("scmp.output-folder-path");
      this.outputFilePattern = globalPreferences.getString("scmp.output-file-pattern");
      if (CommonUtils.isEmpty(this.outputFilePattern) || this.outputFilePattern.startsWith(".")) {
         this.outputFilePattern = "compare-${database}-${timestamp}.sql";
      }

      this.reportEngineId = globalPreferences.getString("report-engine");

      this.snapshotTypes = snapshotTypes;
   }

   public CMPOptions(boolean isChangeLogOnly) {
      this.diffEngineId = "liquibase";
      this.logLevel = SCMPConstants.LogLevels.OFF;
      this.onlyChangelog = isChangeLogOnly;
   }

   public CMPOptions(boolean isChangeLogOnly, @NotNull DBRRunnableContext runnableContext, @NotNull Map<String, Object> properties) {
      this.diffEngineId = "liquibase";
      this.logLevel = SCMPConstants.LogLevels.OFF;
      this.onlyChangelog = isChangeLogOnly;
      this.loadConfiguration(runnableContext, properties);
   }

   public DBPDataSourceContainer getSourceDataSourceContainer() {
      return this.sourceDataSourceContainer;
   }

   public void setSourceDataSourceContainer(DBPDataSourceContainer sourceDataSourceContainer) {
      this.sourceDataSourceContainer = sourceDataSourceContainer;
   }

   public DBPDataSourceContainer getTargetDataSourceContainer() {
      return this.targetDataSourceContainer;
   }

   public void setTargetDataSourceContainer(DBPDataSourceContainer targetDataSourceContainer) {
      this.targetDataSourceContainer = targetDataSourceContainer;
   }

   public List<DBSObject> getSourceInputObjects() {
      return this.sourceInputObjects;
   }

   public void setSourceInputObjects(List<DBSObject> sourceInputObjects) {
      this.sourceInputObjects = sourceInputObjects;
   }

   public List<DBSObject> getTargetInputObjects() {
      return this.targetInputObjects;
   }

   public void setTargetInputObjects(List<DBSObject> targetInputObjects) {
      this.targetInputObjects = targetInputObjects;
   }

   public boolean isOnlyChangelog() {
      return this.onlyChangelog;
   }

   @NotNull
   public List<DBSObjectType> getObjectTypes() {
      return this.objectTypes;
   }

   public String getDiffEngineId() {
      return this.diffEngineId;
   }

   public void setDiffEngineId(String diffEngineId) {
      this.diffEngineId = diffEngineId;
   }

   public String getReportEngineId() {
      return CommonUtils.isEmpty(this.reportEngineId) ? "ddl" : this.reportEngineId;
   }

   public void setReportEngineId(String reportEngineId) {
      this.reportEngineId = reportEngineId;
   }

   public boolean isDoCreate() {
      return this.doCreate;
   }

   public void setDoCreate(boolean doCreate) {
      this.doCreate = doCreate;
   }

   public boolean isDoDrop() {
      return this.doDrop;
   }

   public void setDoDrop(boolean doDrop) {
      this.doDrop = doDrop;
   }

   public boolean isDoChange() {
      return this.doChange;
   }

   public void setDoChange(boolean doChange) {
      this.doChange = doChange;
   }

   public boolean isCaseInsensitiveCompare() {
      return this.caseInsensitiveCompare;
   }

   public void setCaseInsensitiveCompare(boolean caseInsensitiveCompare) {
      this.caseInsensitiveCompare = caseInsensitiveCompare;
   }

   public boolean isQuoteAllObjectsNames() {
      return this.quoteAllObjectsNames;
   }

   @Nullable
   public Set<Class<? extends DatabaseObject>> getExcludedObjectTypes() {
      return this.excludedObjectTypes;
   }

   public void setExcludedObjectTypes(@NotNull Set<Class<? extends DatabaseObject>> excludedObjectTypes) {
      this.excludedObjectTypes = excludedObjectTypes;
   }

   public boolean isHasLiquibaseLicense() {
      return this.hasLiquibaseLicense;
   }

   public void setHasLiquibaseLicense(boolean hasLiquibaseLicense) {
      this.hasLiquibaseLicense = hasLiquibaseLicense;
   }

   @NotNull
   public SCMPConstants.LogLevels getLogLevel() {
      return this.logLevel;
   }

   public void setLogLevel(SCMPConstants.LogLevels logLevel) {
      this.logLevel = logLevel;
   }

   @NotNull
   public Path getChangeLogFile() {
      return this.changeLog;
   }

   public void setChangeLog(Path changeLog) {
      this.changeLog = changeLog;
   }

   public boolean isExportFile() {
      return this.exportFile;
   }

   public void setExportFile(boolean exportFile) {
      this.exportFile = exportFile;
   }

   @Nullable
   public String getOutputFolderPath() {
      return this.outputFolderPath;
   }

   public void setOutputFolderPath(String outputFolderPath) {
      this.outputFolderPath = outputFolderPath;
   }

   public String getOutputFilePattern() {
      return this.outputFilePattern;
   }

   public void setOutputFilePattern(String outputFilePattern) {
      this.outputFilePattern = outputFilePattern;
   }

   public void refreshSchemaMappings(boolean force) {
      if (force) {
         this.containerMapping.clear();
      }

      List<DBSObjectContainer> sourceContainers = this.getSourceContainers();
      List<DBSObjectContainer> targetContainers = this.getTargetContainers();

      for(int i = 0; i < sourceContainers.size() && i < targetContainers.size(); ++i) {
         DBSObjectContainer targetContainer = this.containerMapping.get(sourceContainers.get(i));
         if (targetContainer == null || !targetContainers.contains(targetContainer)) {
            this.containerMapping.put(sourceContainers.get(i), targetContainers.get(i));
         }
      }

   }

   @NotNull
   public List<DBSObjectContainer> getTargetContainers() {
      return CMPUtils.getContainers(this.targetInputObjects, DBSObjectContainer.class, false);
   }

   @NotNull
   public List<DBSObjectContainer> getSourceContainers() {
      return CMPUtils.getContainers(this.sourceInputObjects, DBSObjectContainer.class, false);
   }

   public void mapContainers(@NotNull DBSObjectContainer source, @Nullable DBSObjectContainer target) {
      if (target == null) {
         this.containerMapping.remove(source);
      } else {
         this.containerMapping.put(source, target);
      }

   }

   @Nullable
   public DBSObjectContainer getTargetContainer(@NotNull DBSObjectContainer sourceContainer) {
      return this.containerMapping.get(sourceContainer);
   }

   @Nullable
   public DBSObjectContainer getSourceContainer(@NotNull DBSObjectContainer targetContainer) {
      Iterator var3 = this.containerMapping.entrySet().iterator();

      while(var3.hasNext()) {
         Map.Entry<DBSObjectContainer, DBSObjectContainer> entry = (Map.Entry)var3.next();
         if (entry.getValue() == targetContainer) {
            return entry.getKey();
         }
      }

      return null;
   }

   public CMPResultChangeSet getSourceChangeSet() {
      return sourceChangeSet;
   }

   public void setSourceChangeSet(CMPResultChangeSet sourceChangeSet) {
      this.sourceChangeSet = sourceChangeSet;
   }

   public CMPResultChangeSet getTargetChangeSet() {
      return targetChangeSet;
   }

   public void setTargetChangeSet(CMPResultChangeSet targetChangeSet) {
      this.targetChangeSet = targetChangeSet;
   }

   @Nullable
   public DBSObjectContainer getSourceContainer(String containerName) {
      Iterator var3 = this.containerMapping.entrySet().iterator();

      while(var3.hasNext()) {
         Map.Entry<DBSObjectContainer, DBSObjectContainer> entry = (Map.Entry)var3.next();
         if (containerName.equalsIgnoreCase(entry.getValue().getName())) {
            return entry.getKey();
         }
      }

      return null;
   }

   private void iniDefaultPreferences(DBPPreferenceStore globalPreferences) {
      PrefUtils.setDefaultPreferenceValue(globalPreferences, "@dbeaver-create-missing-objects@", true);
      PrefUtils.setDefaultPreferenceValue(globalPreferences, "@dbeaver-alter-existing-objects@", true);
      PrefUtils.setDefaultPreferenceValue(globalPreferences, "@dbeaver-drop-unexpected-objects@", true);
      PrefUtils.setDefaultPreferenceValue(globalPreferences, "case-insensitive-compare", false);
      PrefUtils.setDefaultPreferenceValue(globalPreferences, "quote-all-objects", true);
      PrefUtils.setDefaultPreferenceValue(globalPreferences, "export-result-file", false);
   }

   public void loadSettingsFromInput(List<DBSObject> inputObjects) {
   }

   public void loadConfiguration(@NotNull DBRRunnableContext runnableContext, @NotNull Map<String, Object> properties) {
      try {
         runnableContext.run(true, true, (monitor) -> {
            if (properties.containsKey("project")) {
               String projectName = CommonUtils.toString(properties.get("project"));
               DBPProject project = CommonUtils.isEmpty(projectName) ? null : DBWorkbench.getPlatform().getWorkspace().getProject(projectName);
               if (project == null) {
                  log.error("Project '" + projectName + "' not found");
               } else {
                  this.onlyChangelog = CommonUtils.getBoolean(properties.get("onlyChangelog"), false);
                  String logLevel = JSONUtils.getString(properties, "sourceContainerId");
                  DBPDataSourceContainer dataSource = project.getDataSourceRegistry().getDataSource(logLevel);
                  if (dataSource == null) {
                     log.error("Datasource '" + logLevel + "' not found");
                  } else {
                     this.sourceDataSourceContainer = dataSource;
                     if (!this.onlyChangelog) {
                        logLevel = JSONUtils.getString(properties, "targetContainerId");
                        dataSource = project.getDataSourceRegistry().getDataSource(logLevel);
                        if (dataSource == null) {
                           log.error("Datasource '" + logLevel + "' not found");
                        }

                        this.targetDataSourceContainer = dataSource;
                     }

                     List<String> targetObjects = JSONUtils.getStringList(properties, "sourceObjects");
                     Iterator var7 = targetObjects.iterator();

                     DBSObject object;
                     String targetObjectId;
                     while(var7.hasNext()) {
                        targetObjectId = (String)var7.next();
                        object = this.loadObject(monitor, project, targetObjectId);
                        if (object != null) {
                           this.sourceInputObjects.add(object);
                        }
                     }

                     if (!this.onlyChangelog) {
                        targetObjects = JSONUtils.getStringList(properties, "targetObjects");
                        var7 = targetObjects.iterator();

                        while(var7.hasNext()) {
                           targetObjectId = (String)var7.next();
                           object = this.loadObject(monitor, project, targetObjectId);
                           if (object != null) {
                              this.targetInputObjects.add(object);
                           }
                        }
                     }

                     this.doCreate = CommonUtils.getBoolean(properties.get("doCreate"), true);
                     this.doDrop = CommonUtils.getBoolean(properties.get("doDrop"), true);
                     this.doChange = CommonUtils.getBoolean(properties.get("doChange"), true);
                     this.hasLiquibaseLicense = CommonUtils.getBoolean(properties.get("hasLiquibaseLicense"), false);
                     this.quoteAllObjectsNames = CommonUtils.getBoolean(properties.get("quoteAllObjectsNames"), true);
                     this.caseInsensitiveCompare = CommonUtils.getBoolean(properties.get("caseInsensitiveCompare"), false);
                     this.exportFile = CommonUtils.getBoolean(properties.get("exportFile"), false);
                     this.outputFolderPath = CommonUtils.toString(properties.get("outputFolderPath"), null);
                     if (CommonUtils.isEmpty(this.outputFolderPath)) {
                        this.outputFolderPath = RuntimeUtils.getUserHomeDir().getAbsolutePath();
                     }

                     this.outputFilePattern = CommonUtils.toString(properties.get("outputFilePattern"), null);
                     if (CommonUtils.isEmpty(this.outputFilePattern) || this.outputFilePattern.startsWith(".")) {
                        this.outputFilePattern = "compare-${database}-${timestamp}.sql";
                     }

                     this.reportEngineId = CommonUtils.toString(properties.get("reportEngineId"), "ddl");
                     logLevel = CommonUtils.toString(properties.get("logLevel"), null);
                     if (CommonUtils.isNotEmpty(logLevel)) {
                        this.logLevel = SCMPConstants.LogLevels.valueOf(logLevel);
                     }

                     List<String> objectsList = JSONUtils.getStringList(properties, "excludedObjects");
                     Iterator var18 = objectsList.iterator();

                     while(var18.hasNext()) {
                        String name = (String)var18.next();

                        try {
                           Class<?> aClass = Class.forName(name);
                           if (DatabaseObject.class.isAssignableFrom(aClass)) {
                              this.excludedObjectTypes.add((Class<? extends DatabaseObject>) aClass);
                           }
                        } catch (ClassNotFoundException var12) {
                           log.debug("Can't find class " + name);
                        }
                     }

                     Map<String, Object> objectMap = JSONUtils.getObjectOrNull(properties, "containersMapping");
                     if (!CommonUtils.isEmpty(objectMap)) {
                        Iterator var20 = objectMap.entrySet().iterator();

                        while(var20.hasNext()) {
                           Map.Entry<String, Object> entry = (Map.Entry)var20.next();
                           DBSObject keyObject = this.loadObject(monitor, project, entry.getKey());
                           DBSObject valueObject = this.loadObject(monitor, project, entry.getValue().toString());
                           if (keyObject instanceof DBSObjectContainer && valueObject instanceof DBSObjectContainer) {
                              this.containerMapping.put((DBSObjectContainer)keyObject, (DBSObjectContainer)valueObject);
                           }
                        }
                     }

                  }
               }
            }
         });
      } catch (InvocationTargetException var4) {
         log.error(var4.getTargetException());
      } catch (InterruptedException var5) {
         log.debug("Canceled by user", var5);
      }

   }

   public void saveConfiguration(@NotNull Map<String, Object> state) {
      state.put("project", this.sourceDataSourceContainer.getProject().getName());
      state.put("onlyChangelog", this.onlyChangelog);
      state.put("sourceContainerId", DBUtils.getObjectFullId(this.sourceDataSourceContainer));
      List<String> inputObjectsIds = new ArrayList();
      Iterator var4 = this.sourceInputObjects.iterator();

      while(var4.hasNext()) {
         DBSObject inputObject = (DBSObject)var4.next();
         inputObjectsIds.add(DBUtils.getObjectFullId(inputObject));
      }

      state.put("sourceObjects", inputObjectsIds);
      if (!this.onlyChangelog) {
         if (this.targetDataSourceContainer != null) {
            state.put("targetContainerId", DBUtils.getObjectFullId(this.targetDataSourceContainer));
         }

         if (!this.targetInputObjects.isEmpty()) {
            List<String> targetObjectsIds = this.targetInputObjects.stream().map(DBUtils::getObjectFullId).collect(Collectors.toList());
            state.put("targetObjects", targetObjectsIds);
         }
      }

      state.put("doCreate", this.doCreate);
      state.put("doDrop", this.doDrop);
      state.put("doChange", this.doChange);
      state.put("quoteAllObjectsNames", this.quoteAllObjectsNames);
      state.put("caseInsensitiveCompare", this.caseInsensitiveCompare);
      state.put("exportFile", this.exportFile);
      if (!CommonUtils.isEmptyTrimmed(this.outputFolderPath)) {
         state.put("outputFolderPath", this.outputFolderPath);
      }

      if (!CommonUtils.isEmptyTrimmed(this.outputFilePattern)) {
         state.put("outputFilePattern", this.outputFilePattern);
      }

      state.put("reportEngineId", this.reportEngineId);
      state.put("logLevel", this.logLevel.name());
      if (!this.excludedObjectTypes.isEmpty()) {
         state.put("excludedObjects", this.excludedObjectTypes.stream().map(Class::getName).collect(Collectors.toList()));
      }

      if (!CommonUtils.isEmpty(this.containerMapping)) {
         Map<String, String> containersIDs = new HashMap();
         Iterator var5 = this.containerMapping.entrySet().iterator();

         while(var5.hasNext()) {
            Map.Entry<DBSObjectContainer, DBSObjectContainer> entry = (Map.Entry)var5.next();
            containersIDs.put(DBUtils.getObjectFullId(entry.getKey()), DBUtils.getObjectFullId(entry.getValue()));
         }

         state.put("containersMapping", containersIDs);
      }

      state.put("hasLiquibaseLicense", this.hasLiquibaseLicense);
   }

   @Nullable
   private DBSObject loadObject(@NotNull DBRProgressMonitor monitor, @NotNull DBPProject project, @NotNull String objectId) {
      monitor.beginTask("Load object '" + objectId + "'", 1);

      try {
         DBSObject var6 = DBUtils.findObjectById(monitor, project, objectId);
         return var6;
      } catch (DBException var11) {
         try {
            DBWorkbench.getPlatform().getNavigatorModel().getNodeByPath(monitor, project, objectId);
         } catch (DBException var10) {
            log.error("Can't find node", var11);
         }

         log.error("Can't find object '" + objectId + "' in project '" + project.getName() + "'", var11);
      } finally {
         monitor.done();
      }

      return null;
   }

   @NotNull
   private Path getOutputFolder(DBRProgressMonitor monitor, DBPProject project) throws DBException {
      if (this.outputFolder == null) {
         String folderPathStr = this.resolveVars(this.sourceDataSourceContainer, this.sourceInputObjects.isEmpty() ? null : this.sourceInputObjects.get(0), this.outputFolderPath);
         // TODO SCMP
//         this.outputFolder = DBFUtils.resolvePathFromString(monitor, project, folderPathStr);
      }

      return this.outputFolder;
   }

   @NotNull
   public Path getOutputFile(DBRProgressMonitor monitor, DBPProject project) throws DBException {
      String outFileName = this.resolveVars(this.sourceDataSourceContainer, this.sourceInputObjects.isEmpty() ? null : this.sourceInputObjects.get(0), this.getOutputFilePattern());
      return this.getOutputFolder(monitor, project).resolve(outFileName);
   }

   private String resolveVars(@NotNull DBPDataSourceContainer container, DBSObject object, String pattern) {
      return GeneralUtils.replaceVariables(pattern, (name) -> {
         switch (name) {
            case "datasource":
               return container.getDataSource() != null ? container.getDataSource().getName() : "";
            case "schema":
               if (object instanceof DBSSchema) {
                  return object.getName();
               }

               DBSSchema schema = DBUtils.getParentOfType(DBSSchema.class, object);
               return schema != null ? schema.getName() : "";
            case "project":
               return container.getProject().getName();
            case "host":
               return container.getConnectionConfiguration().getHostName();
            case "database":
               if (object instanceof DBSCatalog) {
                  return object.getName();
               }

               DBSCatalog catalog = DBUtils.getParentOfType(DBSCatalog.class, object);
               return catalog != null ? catalog.getName() : container.getConnectionConfiguration().getDatabaseName();
         }

         return LBUtils.replaceVariables(name);
      });
   }
}
