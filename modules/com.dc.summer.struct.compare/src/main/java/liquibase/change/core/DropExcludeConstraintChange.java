package liquibase.change.core;

import com.datical.liquibase.ext.util.RefactoringUtils;
import liquibase.change.AbstractChange;
import liquibase.change.DatabaseChange;
import liquibase.change.DatabaseChangeProperty;
import liquibase.database.Database;
import liquibase.database.core.AbstractDb2Database;
import liquibase.database.core.MSSQLDatabase;
import liquibase.database.core.MySQLDatabase;
import liquibase.database.core.OracleDatabase;
import liquibase.database.core.PostgresDatabase;
import liquibase.exception.ValidationErrors;
import liquibase.statement.SqlStatement;

@DatabaseChange(
        name = "dropExcludeConstraint",
        description = "Drops an existing exclude constraint",
        priority = 1,
        appliesTo = {"excludeConstraint"}
)
public class DropExcludeConstraintChange extends AbstractChange {
    private String catalogName;
    private String schemaName;
    private String tableName;
    private String constraintName;

    @Override
    public ValidationErrors validate(Database database) {
        return !(database instanceof OracleDatabase) && !(database instanceof MSSQLDatabase) && (!(database instanceof MySQLDatabase) || !((MySQLDatabase)database).isMinimumMySQLVersion("8.0.16")) && !(database instanceof AbstractDb2Database) && !(database instanceof PostgresDatabase) ? RefactoringUtils.createValidationErrors(database, this) : super.validate(database);
    }

    @DatabaseChangeProperty(
            mustEqualExisting = "excludeConstraint.table.catalog",
            since = "3.0"
    )
    public String getCatalogName() {
        return this.catalogName;
    }

    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }

    @DatabaseChangeProperty(
            mustEqualExisting = "excludeConstraint.table.schema"
    )
    public String getSchemaName() {
        return this.schemaName;
    }

    public DropExcludeConstraintChange setSchemaName(String schemaName) {
        this.schemaName = schemaName;
        return this;
    }

    @DatabaseChangeProperty(
            mustEqualExisting = "excludeConstraint.table",
            description = "Name of the table to drop the exclude constraint from"
    )
    public String getTableName() {
        return this.tableName;
    }

    public DropExcludeConstraintChange setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    @DatabaseChangeProperty(
            mustEqualExisting = "excludeConstraint",
            description = "Name of exclude constraint to drop"
    )
    public String getConstraintName() {
        return this.constraintName;
    }

    public DropExcludeConstraintChange setConstraintName(String constraintName) {
        this.constraintName = constraintName;
        return this;
    }

    public SqlStatement[] generateStatements(Database database) {
        return new SqlStatement[]{new DropExcludeConstraintStatement(this.getCatalogName(), this.getSchemaName(), this.getTableName(), this.getConstraintName())};
    }

    public String getConfirmationMessage() {
        return "Exclude constraint " + this.getConstraintName() + " dropped from " + this.getTableName();
    }

}
