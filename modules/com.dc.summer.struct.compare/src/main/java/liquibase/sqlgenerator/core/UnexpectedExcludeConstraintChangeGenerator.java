package liquibase.sqlgenerator.core;

import liquibase.change.Change;
import liquibase.change.core.DropExcludeConstraintChange;
import liquibase.database.Database;
import liquibase.diff.output.DiffOutputControl;
import liquibase.diff.output.changelog.AbstractChangeGenerator;
import liquibase.diff.output.changelog.ChangeGeneratorChain;
import liquibase.diff.output.changelog.UnexpectedObjectChangeGenerator;
import liquibase.structure.DatabaseObject;
import liquibase.structure.core.ExcludeConstraint;
import liquibase.structure.core.Table;

public class UnexpectedExcludeConstraintChangeGenerator extends AbstractChangeGenerator implements UnexpectedObjectChangeGenerator {

    public int getPriority(Class<? extends DatabaseObject> var1, Database var2) {
        return ExcludeConstraint.class.isAssignableFrom(var1) ? 1 : -1;
    }

    public Class<? extends DatabaseObject>[] runAfterTypes() {
        return new Class[]{Table.class};
    }

    public Class<? extends DatabaseObject>[] runBeforeTypes() {
        return null;
    }

    public Change[] fixUnexpected(DatabaseObject var1, DiffOutputControl var2, Database var3, Database var4, ChangeGeneratorChain var5) {
        ExcludeConstraint excludeConstraint;
        if ((excludeConstraint = (ExcludeConstraint)var1).getTable() == null) {
            return null;
        } else {
            DropExcludeConstraintChange change = new DropExcludeConstraintChange();
            change.setTableName(excludeConstraint.getTable().getName());
            if (var2.getIncludeCatalog()) {
                change.setCatalogName(excludeConstraint.getTable().getSchema().getCatalogName());
            }

            if (var2.getIncludeSchema()) {
                change.setSchemaName(excludeConstraint.getTable().getSchema().getName());
            }

            change.setConstraintName(excludeConstraint.getName());
            return new Change[]{change};
        }
    }
}
