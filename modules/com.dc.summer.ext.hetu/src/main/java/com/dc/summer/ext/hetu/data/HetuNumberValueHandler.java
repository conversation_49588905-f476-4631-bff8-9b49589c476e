package com.dc.summer.ext.hetu.data;

import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCNumberValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class HetuNumberV<PERSON>ue<PERSON>andler extends JDBCNumberValueHandler {


    public HetuNumberValueHandler(DBSTypedObject type, DBDFormatSettings formatSettings) {
        super(type, formatSettings);
    }

    @Override
    public synchronized String getValueDisplayString(DBSTypedObject column, Object value, DBDDisplayFormat format) {

        if (DBDDisplayFormat.SQL_FORMAT == format) {
            String valueDisplayString = super.getValueDisplayString(column, value, DBDDisplayFormat.NATIVE);

            String columnType = column.getTypeName();
            if (columnType.contains("(")) {
                columnType = columnType.split("\\(")[0];
            }
            valueDisplayString = String.format("%s '%s'", columnType, valueDisplayString);
            return valueDisplayString;
        } else {
            return super.getValueDisplayString(column, value, format);
        }


    }
}
