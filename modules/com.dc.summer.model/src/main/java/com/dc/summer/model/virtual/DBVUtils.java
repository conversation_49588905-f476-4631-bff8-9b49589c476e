
package com.dc.summer.model.virtual;

import com.dc.summer.registry.center.BeanRegistryCenter;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.*;
import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.navigator.DBNDatabaseNode;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.summer.registry.expressions.ExpressionNamespaceDescriptor;
import com.dc.summer.registry.expressions.ExpressionRegistry;
import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlContext;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlExpression;
import org.eclipse.core.runtime.IAdaptable;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.navigator.DBNUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.CommonUtils;

import java.util.*;

/**
 * Virtual model serialize
 */
public abstract class DBVUtils {

    private static final Log log = Log.getLog(DBVUtils.class);

    // Entities for unmapped attributes (custom queries, pseudo attributes, etc)
    private static final Map<String, DBVEntity> orphanVirtualEntities = new HashMap<>();

    @Nullable
    public static DBVTransformSettings getTransformSettings(@NotNull DBDAttributeBinding binding, boolean create) {
        if (DBUtils.isDynamicAttribute(binding.getAttribute()) && (binding.getParentObject() == null || binding.getParentObject().getDataKind() != DBPDataKind.DOCUMENT)) {
            return null;
        }
        DBVEntity vEntity = getVirtualEntity(binding, create);
        if (vEntity != null) {
            DBVEntityAttribute vAttr = vEntity.getVirtualAttribute(binding, create);
            if (vAttr != null) {
                return getTransformSettings(vAttr, create);
            }
        }
        return null;
    }

    public static DBVEntity getVirtualEntity(@NotNull DBDAttributeBinding binding, boolean create) {
        DBSEntityAttribute entityAttribute = binding.getEntityAttribute();
        DBVEntity vEntity;
        if (entityAttribute != null) {
            vEntity = getVirtualEntity(entityAttribute.getParentObject(), create);
        } else {
            vEntity = getVirtualEntity(binding.getDataContainer(), create);

        }
        return vEntity;
    }

    @Nullable
    public static DBVEntity getVirtualEntity(@NotNull DBSEntity source, boolean create)
    {
        if (source instanceof DBVEntity) {
            return (DBVEntity) source;
        }
        DBPDataSource dataSource = source.getDataSource();
        return dataSource == null ? null : dataSource.getContainer().getVirtualModel().findEntity(source, create);
    }

    public static DBVEntity getVirtualEntity(@NotNull DBSDataContainer dataContainer, boolean create) {
        if (dataContainer instanceof IAdaptable) {
            // Data container can be a wrapper around another data container (e.g. ResultSetDataContainer). Virtual entity is linked to the nested one.
            DBSDataContainer nestedDC = ((IAdaptable) dataContainer).getAdapter(DBSDataContainer.class);
            if (nestedDC != null) {
                dataContainer = nestedDC;
            }
        }
        if (dataContainer instanceof DBSEntity) {
            return getVirtualEntity((DBSEntity)dataContainer, create);
        }
        // Not an entity. Most likely a custom query. Use local cache for such attributes.
        // There shouldn't be too many such settings as they are defined by user manually
        // so we shouldn't eay too much memory for that
        String attrKey = DBUtils.getObjectFullId(dataContainer);
        synchronized (orphanVirtualEntities) {
            DBPDataSource dataSource = dataContainer.getDataSource();
            if (dataSource == null) {
                return null;
            }
            DBVEntity vEntity = orphanVirtualEntities.get(attrKey);
            if (vEntity == null && create) {
                vEntity = new DBVEntity(
                    dataSource.getContainer().getVirtualModel(),
                    dataContainer.getName(),
                    "");
                orphanVirtualEntities.put(attrKey, vEntity);
            }
            return vEntity;
        }
    }

    @Nullable
    public static DBVTransformSettings getTransformSettings(@NotNull DBVEntityAttribute attribute, boolean create) {
        if (attribute.getTransformSettings() != null) {
            return attribute.getTransformSettings();
        } else if (create) {
            attribute.setTransformSettings(new DBVTransformSettings());
            return attribute.getTransformSettings();
        }
        for (DBVObject object = attribute.getParentObject(); object != null; object = object.getParentObject()) {
            if (object.getTransformSettings() != null) {
                return object.getTransformSettings();
            }
        }
        return null;
    }

    @NotNull
    public static Map<String, Object> getAttributeTransformersOptions(@NotNull DBDAttributeBinding binding) {
        if (DBUtils.isDynamicAttribute(binding.getAttribute())) {
            return Collections.emptyMap();
        }
        Map<String, Object> options = null;
        final DBVTransformSettings transformSettings = getTransformSettings(binding, false);
        if (transformSettings != null) {
            options = transformSettings.getTransformOptions();
        }
        if (options != null) {
            return options;
        }
        return Collections.emptyMap();
    }

    @Nullable
    public static DBDAttributeTransformer[] findAttributeTransformers(@NotNull DBDAttributeBinding binding, @Nullable Boolean custom)
    {
        DBPDataSource dataSource = binding.getDataSource();
        DBDRegistry dataTypeProviderRegistry = (DBDRegistry) BeanRegistryCenter.getInstance().getByClassName("com.dc.summer.registry.datatype.DataTypeProviderRegistry");
        List<? extends DBDAttributeTransformerDescriptor> tdList =
                dataTypeProviderRegistry.findTransformers(dataSource, binding.getAttribute(), custom);
        if (tdList == null || tdList.isEmpty()) {
            return null;
        }
        {
            boolean filtered = false;
            final DBVTransformSettings transformSettings = getTransformSettings(binding, false);
            if (transformSettings != null) {
                filtered = transformSettings.filterTransformers(tdList);
            }

            if (!filtered) {
                // Leave only default transformers
                for (int i = 0; i < tdList.size(); ) {
                    if (tdList.get(i).isCustom() || !tdList.get(i).isApplicableByDefault()) {
                        tdList.remove(i);
                    } else {
                        i++;
                    }
                }
            }
            if (tdList.isEmpty()) {
                return null;
            }
        }
        DBDAttributeTransformer[] result = new DBDAttributeTransformer[tdList.size()];
        for (int i = 0; i < tdList.size(); i++) {
            result[i] = tdList.get(i).getInstance();
        }
        return result;
    }

    public static String getDictionaryDescriptionColumns(DBRProgressMonitor monitor, DBSEntityAttribute attribute) throws DBException {
        DBVEntity dictionary = getVirtualEntity(attribute.getParentObject(), false);
        String descColumns = null;
        if (dictionary != null) {
            descColumns = dictionary.getDescriptionColumnNames();
        }
        if (descColumns == null) {
            descColumns = DBVEntity.getDefaultDescriptionColumn(monitor, attribute);
        }
        return descColumns;
    }

    @NotNull
    public static List<DBDLabelValuePair> readDictionaryRows(
        @NotNull DBCSession session,
        @NotNull DBSEntityAttribute valueAttribute,
        @NotNull DBDValueHandler valueHandler,
        @NotNull DBCResultSet dbResult,
        boolean formatValues,
        boolean containsCount) throws DBCException
    {
        List<DBDLabelValuePair> values = new ArrayList<>();
        List<DBCAttributeMetaData> metaColumns = dbResult.getMeta().getAttributes();
        List<DBDValueHandler> colHandlers = new ArrayList<>(metaColumns.size());
        for (DBCAttributeMetaData col : metaColumns) {
            colHandlers.add(DBUtils.findValueHandler(session, col));
        }
        boolean hasNulls = false;

        String columnDivider = session.getDataSource().getContainer().getPreferenceStore().getString(ModelPreferences.DICTIONARY_COLUMN_DIVIDER);

        // Extract enumeration values and (optionally) their descriptions
        while (dbResult.nextRow()) {
            // Check monitor
            if (session.getProgressMonitor().isCanceled()) {
                break;
            }
            // Get value and description
            Object keyValue = valueHandler.fetchValueObject(session, dbResult, valueAttribute, 0);
            if (DBUtils.isNullValue(keyValue)) {
                if (hasNulls) {
                    continue;
                }
                hasNulls = true;
            }
            if (formatValues && keyValue instanceof Date) {
                // Convert dates into string to avoid collisions
                keyValue = valueHandler.getValueDisplayString(valueAttribute, keyValue, DBDDisplayFormat.UI);
            }
            String keyLabel;
            long keyCount = 0;
            if (metaColumns.size() > 1) {
                StringBuilder keyLabel2 = new StringBuilder();
                for (int i = 1; i < colHandlers.size(); i++) {
                    Object descValue = colHandlers.get(i).fetchValueObject(session, dbResult, metaColumns.get(i), i);
                    if (containsCount && i == colHandlers.size() - 1) {
                        // The last one column is the `count(*)`
                        keyCount = CommonUtils.toLong(descValue);
                        break;
                    }
                    if (keyLabel2.length() > 0) {
                        keyLabel2.append(columnDivider);
                    }
                    keyLabel2.append(colHandlers.get(i).getValueDisplayString(metaColumns.get(i), descValue, DBDDisplayFormat.NATIVE));
                }
                keyLabel = keyLabel2.toString();
            } else {
                keyLabel = valueHandler.getValueDisplayString(valueAttribute, keyValue, DBDDisplayFormat.NATIVE);
            }
            if (containsCount && keyCount > 0) {
                values.add(new DBDLabelValuePairExt(keyLabel, keyValue, keyCount));
            } else {
                values.add(new DBDLabelValuePair(keyLabel, keyValue));
            }
        }
        return values;
    }

    @NotNull
    public static List<DBVEntityAttribute> getCustomAttributes(@NotNull DBSEntity entity) {
        DBVEntity vEntity = getVirtualEntity(entity, false);
        if (vEntity != null) {
            return vEntity.getCustomAttributes();
        }
        return Collections.emptyList();
    }

    @NotNull
    public static List<DBSEntityAttribute> getAllAttributes(@NotNull DBRProgressMonitor monitor, @NotNull DBSEntity entity) throws DBException {
        List<DBSEntityAttribute> result = new ArrayList<>();
        final Collection<? extends DBSEntityAttribute> realAttributes = entity.getAttributes(monitor);
        if (!CommonUtils.isEmpty(realAttributes)) {
            result.addAll(realAttributes);
        }
        DBVEntity vEntity = getVirtualEntity(entity, false);
        if (vEntity != null) {
            List<DBVEntityAttribute> vAttributes = vEntity.getEntityAttributes();
            if (!CommonUtils.isEmpty(vAttributes)) {
                for (DBVEntityAttribute attr : vAttributes) {
                    if (attr.isCustom()) {
                        result.add(attr);
                    }
                }
            }
        }

        return result;
    }

    @NotNull
    public static List<DBSEntityConstraint> getAllConstraints(@NotNull DBRProgressMonitor monitor, @NotNull DBSEntity entity) throws DBException {
        List<DBSEntityConstraint> result = new ArrayList<>();
        final Collection<? extends DBSEntityConstraint> realConstraints = entity.getConstraints(monitor);
        if (!CommonUtils.isEmpty(realConstraints)) {
            result.addAll(realConstraints);
        }
        DBVEntity vEntity = getVirtualEntity(entity, false);
        if (vEntity != null) {
            List<DBVEntityConstraint> vConstraints = vEntity.getConstraints();
            if (!CommonUtils.isEmpty(vConstraints)) {
                result.addAll(vConstraints);
            }
        }

        return result;
    }

    @NotNull
    public static List<DBSEntityAssociation> getAllAssociations(@NotNull DBRProgressMonitor monitor, @NotNull DBSEntity entity) {
        List<DBSEntityAssociation> result = new ArrayList<>();
        try {
            final Collection<? extends DBSEntityAssociation> realConstraints = entity.getAssociations(monitor);
            if (!CommonUtils.isEmpty(realConstraints)) {
                result.addAll(realConstraints);
            }
        } catch (DBException e) {
            log.debug("Error reading entity associations", e);
        }
        if (!(entity instanceof DBVEntity)) {
            DBVEntity vEntity = getVirtualEntity(entity, false);
            if (vEntity != null) {
                List<DBVEntityForeignKey> vFKs = vEntity.getForeignKeys();
                if (!CommonUtils.isEmpty(vFKs)) {
                    result.addAll(vFKs);
                }
            }
        }

        return result;
    }

    @NotNull
    public static List<DBSEntityAssociation> getAllReferences(@NotNull DBRProgressMonitor monitor, @NotNull DBSEntity onEntity) {
        List<DBSEntityAssociation> result = new ArrayList<>();
        try {
            final Collection<? extends DBSEntityAssociation> realConstraints = onEntity.getReferences(monitor);
            if (!CommonUtils.isEmpty(realConstraints)) {
                result.addAll(realConstraints);
            }
        } catch (DBException e) {
            log.debug("Error reading entity references", e);
        }

        result.addAll(getVirtualReferences(onEntity));

        return result;
    }

    @NotNull
    public static List<DBVEntityForeignKey> getVirtualReferences(@NotNull DBSEntity onEntity) {
        DBNDatabaseNode entityNode = DBNUtils.getNodeByObject(onEntity);
        if (entityNode != null) {
            List<DBVEntityForeignKey> globalRefs = DBVModel.getGlobalReferences(entityNode);
            if (!CommonUtils.isEmpty(globalRefs)) {
                return globalRefs;
            }
        }
        return Collections.emptyList();
    }

    @NotNull
    public static DBSEntity getRealEntity(@NotNull DBRProgressMonitor monitor, @NotNull DBSEntity entity) throws DBException {
        if (entity instanceof DBVEntity) {
            DBSEntity realEntity = ((DBVEntity) entity).getRealEntity(monitor);
            if (realEntity == null) {
                throw new DBException("Can't locate real entity for " + DBUtils.getObjectFullId(entity));
            }
            return realEntity;
        }
        return entity;
    }

    @NotNull
    public static DBSEntity tryGetRealEntity(@NotNull DBSEntity entity) {
        if (entity instanceof DBVEntity) {
            try {
                return ((DBVEntity) entity).getRealEntity(new VoidProgressMonitor());
            } catch (DBException e) {
                log.error("Can't get real entity fro mvirtual entity", e);
            }
        }
        return entity;
    }

    public static DBVObject getVirtualObject(DBSObject source, boolean create) {
        if (source == null) {
            return null;
        }
        if (source instanceof DBVObject) {
            return (DBVObject) source;
        }
        return source.getDataSource().getContainer().getVirtualModel().findObject(source, create);
    }

    public static Object executeExpression(DBVEntityAttribute attribute, DBDAttributeBinding[] allAttributes, Object[] row) {
        String exprString = attribute.getExpression();
        if (CommonUtils.isEmpty(exprString)) {
            return null;
        }
        JexlExpression expression = attribute.getParsedExpression();
        if (expression == null) {
            return null;
        }

        return evaluateDataExpression(allAttributes, row, expression, attribute.getName());
    }

    public static Object evaluateDataExpression(DBDAttributeBinding[] allAttributes, Object[] row, JexlExpression expression, String attributeName) {
        Map<String, Object> nsList = getExpressionNamespaces();

        JexlContext context = new JexlContext() {
            @Override
            public Object get(String s) {
                Object ns = nsList.get(s);
                if (ns != null) {
                    return ns;
                }
                if (s.equals(attributeName)) {
                    return null;
                }
                for (DBDAttributeBinding attr : allAttributes) {
                    if (s.equals(attr.getLabel())) {
                        return DBUtils.getAttributeValue(attr, allAttributes, row);
                    }
                }
                return null;
            }

            @Override
            public void set(String s, Object o) {

            }

            @Override
            public boolean has(String s) {
                return get(s) != null;
            }
        };
        try {
            return expression.evaluate(context);
        } catch (Exception e) {
            return GeneralUtils.getExpressionParseMessage(e);
        }
    }

    @NotNull
    private static Map<String, Object> getExpressionNamespaces() {
        Map<String, Object> nsList = new HashMap<>();

        for (ExpressionNamespaceDescriptor ns : ExpressionRegistry.getInstance().getExpressionNamespaces()) {
            Class<?> implClass = ns.getImplClass();
            if (implClass != null) {
                nsList.put(ns.getId(), implClass);
            }
        }
        return nsList;
    }

    public static JexlExpression parseExpression(String expression) {
        Map<String, Object> nsList = getExpressionNamespaces();

        JexlBuilder jexlBuilder = new JexlBuilder();
        jexlBuilder.cache(100);
        jexlBuilder.namespaces(nsList);

        JexlEngine jexlEngine = jexlBuilder.create();
        return jexlEngine.createExpression(expression);
    }

    public static boolean isIdentifyingAttributes(@NotNull DBRProgressMonitor monitor, @NotNull List<DBSEntityAttribute> attributes) throws DBException {
        if (attributes.isEmpty()) {
            return false;
        }
        DBSEntity table = attributes.get(0).getParentObject();

        {
            // Check constraints
            Collection<? extends DBSEntityConstraint> constraints = getAllConstraints(monitor, table);
            if (constraints != null) {
                for (DBSEntityConstraint constraint : constraints) {
                    if (DBUtils.isIdentifierConstraint(monitor, constraint)) {
                        List<? extends DBSEntityAttributeRef> attrRefs = ((DBSEntityReferrer) constraint).getAttributeReferences(monitor);
                        if (attrRefs == null) {
                            continue;
                        }
                        if (attributes.size() != attrRefs.size()) {
                            continue;
                        }
                        boolean matches = true;
                        for (int i = 0; i < attributes.size(); i++) {
                            if (attributes.get(i) != attrRefs.get(i).getAttribute()) {
                                matches = false;
                                break;
                            }
                        }
                        if (matches) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
}
