

package com.dc.summer.runtime.ui;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.access.DBAPasswordChangeInfo;
import com.dc.summer.model.connection.DBPAuthInfo;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.navigator.DBNNode;
import com.dc.summer.model.runtime.DBRProcessDescriptor;
import com.dc.summer.model.runtime.RunJob;
import com.dc.summer.model.runtime.load.ILoadVisualizer;
import org.eclipse.core.runtime.IStatus;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.connection.DBPDriverDependencies;
import com.dc.summer.model.runtime.DBRRunnableWithProgress;
import com.dc.summer.model.runtime.load.ILoadService;
import com.dc.summer.model.struct.DBSObject;

import java.lang.reflect.InvocationTargetException;

/**
 * User interface interactions
 */
public interface DBPPlatformUI {

    /**
     * Error boxes
     */

    enum UserResponse {
        OK,
        CANCEL,
        IGNORE,
        IGNORE_ALL,
        STOP,
        RETRY,
    }

    UserResponse showError(@NotNull final String title, @Nullable final String message, @NotNull final IStatus status);
    UserResponse showError(@NotNull final String title, @Nullable final String message, @NotNull final Throwable e);
    UserResponse showError(@NotNull final String title, @Nullable final String message);
    void showNotification(@NotNull final String title, @Nullable final String message, boolean error);
    void showWarningNotification(@NotNull final String title, @Nullable final String message);
    void showMessageBox(@NotNull final String title, @Nullable final String message, boolean error);
    void showWarningMessageBox(@NotNull final String title, @Nullable final String message);
    boolean confirmAction(String title, String message);
    boolean confirmAction(String title, String message, boolean isWarning);

    UserResponse showErrorStopRetryIgnore(String task, Throwable error, boolean queue);

    /**
     * Notification agent
     */
    long getLongOperationTimeout();
    void notifyAgent(String message, int status);

    /**
     * Asks for user credentials. Returns null if user canceled this action.
     */
    DBPAuthInfo promptUserCredentials(String prompt, String userName, String userPassword, boolean passwordOnly, boolean showSavePassword);

    DBPAuthInfo promptUserCredentials(String prompt, String userNameLabel, String userName, String passwordLabel, String userPassword, boolean passwordOnly, boolean showSavePassword);

    /**
     * Asks for password change. Returns null if user canceled this action.
     */
    DBAPasswordChangeInfo promptUserPasswordChange(String prompt, @Nullable String userName, @Nullable String oldPassword, boolean userEditable, boolean oldPasswordVisible);

    /**
     * Ask user to accept license agreement
     */
    boolean acceptLicense(String message, String licenseText);

    boolean downloadDriverFiles(DBPDriver driverDescriptor, DBPDriverDependencies dependencies);

    /**
     * UI utilities
     */
    DBNNode selectObject(@NotNull Object parentShell, String title, DBNNode rootNode, DBNNode selectedNode, Class<?>[] allowedTypes, Class<?>[] resultTypes, Class<?>[] leafTypes);

    void openEntityEditor(@NotNull DBSObject object);
    void openEntityEditor(@NotNull DBNNode selectedNode, @Nullable String defaultPageId);

    void openConnectionEditor(@NotNull DBPDataSourceContainer dataSourceContainer);

    // Process execution
    void executeProcess(@NotNull DBRProcessDescriptor processDescriptor);

    // Execute some action in UI thread
    void executeWithProgress(@NotNull Runnable runnable);

    void executeWithProgress(@NotNull DBRRunnableWithProgress runnable) throws InvocationTargetException, InterruptedException;

    @NotNull
    <RESULT> RunJob createLoadingService(
        ILoadService<RESULT> loadingService,
        ILoadVisualizer<RESULT> visualizer);

    /**
     * FIXME: this is a hack. We need to call platform (workbench) to refresh part's contexts (enabled commands).
     * There is no such thing as part in abstract UI. Need some better solution.
     */
    void refreshPartState(Object part);

    void copyTextToClipboard(String text, boolean htmlFormat);

    void executeShellProgram(String shellCommand);

    void showInSystemExplorer(@NotNull String path);

    boolean readAndDispatchEvents();

}