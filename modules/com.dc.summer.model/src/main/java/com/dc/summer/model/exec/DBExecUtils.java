
package com.dc.summer.model.exec;

import com.dc.annotation.SQL;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.function.ConsumerFunction;
import com.dc.function.FunctionWrapper;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.*;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.*;
import com.dc.summer.model.edit.DBECommand;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.edit.SQLDatabasePersistActionComment;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.model.net.DBWForwarder;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.net.DBWHandlerType;
import com.dc.summer.model.net.DBWNetworkHandler;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableParametrized;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.sql.*;
import com.dc.summer.model.struct.*;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.summer.model.struct.rdb.DBSTableIndex;
import com.dc.summer.model.virtual.DBVEntity;
import com.dc.summer.model.virtual.DBVEntityConstraint;
import com.dc.summer.model.virtual.DBVUtils;
import com.dc.summer.runtime.jobs.InvalidateJob;
import com.dc.summer.runtime.net.GlobalProxyAuthenticator;
import com.dc.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;

import java.lang.reflect.InvocationTargetException;
import java.net.Authenticator;
import java.util.*;

/**
 * Execution utils
 */
public class DBExecUtils {

    public static final int DEFAULT_READ_FETCH_SIZE = 10000;

    private static final Log log = Log.getLog(DBExecUtils.class);

    /**
     * Current execution context. Used by global authenticators and network handlers
     */
    private static final ThreadLocal<DBPDataSourceContainer> ACTIVE_CONTEXT = new ThreadLocal<>();
    private static final List<DBPDataSourceContainer> ACTIVE_CONTEXTS = new ArrayList<>();
    public static final boolean BROWSE_LAZY_ASSOCIATIONS = false;

    public static DBPDataSourceContainer getCurrentThreadContext() {
        return ACTIVE_CONTEXT.get();
    }

    public static List<DBPDataSourceContainer> getActiveContexts() {
        synchronized (ACTIVE_CONTEXTS) {
            return new ArrayList<>(ACTIVE_CONTEXTS);
        }
    }

    public static void startContextInitiation(DBPDataSourceContainer context) {
        // 设置并添加到活跃的上下文中
        ACTIVE_CONTEXT.set(context);
        synchronized (ACTIVE_CONTEXTS) {
            ACTIVE_CONTEXTS.add(context);
        }
        // 设置代理身份验证（如果需要）。验证器可能在启动时或以后由Eclipse frameword更改。这就是为什么我们在连接启动时设置新的默认验证器。
        // Set proxy auth (if required)
        // Note: authenticator may be changed by Eclipse frameword on startup or later.
        // That's why we set new default authenticator on connection initiation
        boolean hasProxy = false;
        for (DBWHandlerConfiguration handler : context.getConnectionConfiguration().getHandlers()) {
            if (handler.isEnabled() && handler.getType() == DBWHandlerType.PROXY) {
                hasProxy = true;
                break;
            }
        }
        if (hasProxy) {
            Authenticator.setDefault(new GlobalProxyAuthenticator());
        }
    }

    public static void finishContextInitiation(DBPDataSourceContainer context) {
        ACTIVE_CONTEXT.remove();
        synchronized (ACTIVE_CONTEXTS) {
            ACTIVE_CONTEXTS.remove(context);
        }
    }

    public static DBPDataSourceContainer findConnectionContext(String host, int port, String path) {
        DBPDataSourceContainer curContext = getCurrentThreadContext();
        if (curContext != null) {
            return contextMatches(host, port, curContext) ? curContext : null;
        }
        synchronized (ACTIVE_CONTEXTS) {
            for (DBPDataSourceContainer ctx : ACTIVE_CONTEXTS) {
                if (contextMatches(host, port, ctx)) {
                    return ctx;
                }
            }
        }
        return null;
    }

    private static boolean contextMatches(String host, int port, DBPDataSourceContainer ctx) {
        DBPConnectionConfiguration cfg = ctx.getConnectionConfiguration();
        if (CommonUtils.equalObjects(cfg.getHostName(), host) && String.valueOf(port).equals(cfg.getHostPort())) {
            return true;
        }
        for (DBWNetworkHandler networkHandler : ctx.getActiveNetworkHandlers()) {
            if (networkHandler instanceof DBWForwarder && ((DBWForwarder) networkHandler).matchesParameters(host, port)) {
                return true;
            }
        }
        return false;
    }

    @NotNull
    public static DBPErrorAssistant.ErrorType discoverErrorType(@NotNull DBPDataSource dataSource, @NotNull Throwable error) {
        DBPErrorAssistant errorAssistant = DBUtils.getAdapter(DBPErrorAssistant.class, dataSource);

        return errorAssistant != null ?
                ((DBPErrorAssistant) dataSource).discoverErrorType(error) :
                DBPErrorAssistant.ErrorType.NORMAL;
    }

    @NotNull
    public static DBPErrorAssistant.ErrorType discoverErrorType(@NotNull DBPDataSource dataSource, @NotNull String message) {
        DBPErrorAssistant errorAssistant = DBUtils.getAdapter(DBPErrorAssistant.class, dataSource);

        DBPErrorAssistant.ErrorType errorType = errorAssistant != null && StringUtils.isNotBlank(message) ?
                ((DBPErrorAssistant) dataSource).discoverErrorType(message) :
                DBPErrorAssistant.ErrorType.NORMAL;

        log.error("--> ErrorType: " + errorType);

        return errorType;
    }

    /**
     *
     * @param param DBRProgressProgress monitor or DBCSession or DBCExecutionContext
     * @param dataSource
     * @param runnable
     * @return true 恢复成功（没有断开的，直接成功是false）
     * @param <T>
     * @throws DBException
     */
    public static <T> boolean tryExecuteRecover(@NotNull T param, @NotNull DBPDataSource dataSource, @NotNull DBRRunnableParametrized<T> runnable) throws DBException {
        return tryExecuteRecover(param, dataSource, runnable, null, null);
    }

    public static <T> boolean tryExecuteRecover(@NotNull T param, @NotNull DBPDataSource dataSource, @NotNull DBRRunnableParametrized<T> runnable, Runnable recoverBefore, Runnable recoverAfter) throws DBException {
        int tryCount = 1;
        boolean recoverEnabled = dataSource.getContainer().getPreferenceStore().getBoolean(ModelPreferences.EXECUTE_RECOVER_ENABLED);
        if (recoverEnabled) {
            // tryCount + 2
            tryCount += dataSource.getContainer().getPreferenceStore().getInt(ModelPreferences.EXECUTE_RECOVER_RETRY_COUNT);
        }
        Throwable lastError = null;
        int i = 0;
        t:
        for (; i < tryCount; i++) {
            try {
                runnable.run(param);
                lastError = null;
                break;
            } catch (InvocationTargetException e) {
                lastError = e.getTargetException();
                log.error("--> ErrorMessage: " + lastError.getMessage());
                if (!recoverEnabled) {
                    // Can't recover
                    break;
                }

                DBRProgressMonitor monitor;
                if (param instanceof DBRProgressMonitor) {
                    monitor = (DBRProgressMonitor) param;
                } else if (param instanceof DBCSession) {
                    monitor = ((DBCSession) param).getProgressMonitor();
                } else {
                    monitor = new VoidProgressMonitor();
                }

//                if (!monitor.isCanceled()) {
                DBPErrorAssistant.ErrorType errorType = discoverErrorType(dataSource, lastError);
                log.error("--> ErrorType: " + errorType);
                log.error("--> ErrorState: " + SQLState.getStateFromException(lastError));
                log.error("--> ErrorCode: " + SQLState.getCodeFromException(lastError));
                DBCExecutionContext executionContext = null;
                if (lastError instanceof DBCException) {
                    executionContext = ((DBCException) lastError).getExecutionContext();
                }
                switch (errorType) {
                    case TRANSACTION_ABORTED:
                        InvalidateJob.invalidateTransaction(monitor, dataSource, executionContext);
                        logReExecuteMessage(tryCount, lastError, i);
                        continue;
                    case CONNECTION_LOST:
                        boolean executeRecoverConnectionLostEnabled;
                        if (param instanceof DBCSession) {
                            executeRecoverConnectionLostEnabled = ((DBCSession) param).getExecutionContext().getPreferenceStore().getBoolean(ModelPreferences.EXECUTE_RECOVER_CONNECTION_LOST_ENABLED);
                        } else if (param instanceof DBCExecutionContext) {
                            executeRecoverConnectionLostEnabled = ((DBCExecutionContext) param).getPreferenceStore().getBoolean(ModelPreferences.EXECUTE_RECOVER_CONNECTION_LOST_ENABLED);
                        } else {
                            executeRecoverConnectionLostEnabled = dataSource.getContainer().getPreferenceStore().getBoolean(ModelPreferences.EXECUTE_RECOVER_CONNECTION_LOST_ENABLED);
                        }
                        if (executeRecoverConnectionLostEnabled) {
                            if (i == 0 && recoverBefore != null) {
                                recoverBefore.run();
                            }
                            InvalidateJob.invalidateDataSource(
                                    monitor,
                                    dataSource,
                                    false,
                                    true,
                                    executionContext,
                                    null);
                            if (i == 0 && recoverAfter != null) {
                                recoverAfter.run();
                            }
                            logReExecuteMessage(tryCount, lastError, i);
                            continue;
                        } else {
                            break t;
                        }
                    case IGNORE:
                        lastError = null;
                        break t;
                    default:
                        // 不重新执行
                        break t;
                }
//                }
            } catch (InterruptedException e) {
                log.error("Operation interrupted");
                lastError = e;
                break;
            }
        }
        if (lastError != null) {
            if (lastError instanceof DBException) {
                throw (DBException) lastError;
            } else {
                throw new DBException(lastError, dataSource);
            }
        }
        return i > 0;
    }

    private static void logReExecuteMessage(int tryCount, Throwable lastError, int i) {
        if (i < tryCount - 1) {
            log.error(String.format("Operation failed. Retry count remains = %1$s. %2$s", tryCount - i - 1, ExceptionUtils.getRootCause(lastError).toString()));
            log.debug(lastError);
        }
    }

    public static void setStatementFetchSize(DBCStatement dbStat, long firstRow, long maxRows, int fetchSize) {
        boolean useFetchSize = fetchSize > 0 || dbStat.getSession().getDataSource().getContainer().getPreferenceStore().getBoolean(ModelPreferences.RESULT_SET_USE_FETCH_SIZE);
        if (useFetchSize) {
            if (fetchSize <= 0) {
                fetchSize = DEFAULT_READ_FETCH_SIZE;
            }
            if (firstRow >= 0 && maxRows > 0 && fetchSize > firstRow + maxRows) {
                fetchSize = (int) (firstRow + maxRows);
            }
            try {
                dbStat.setResultsFetchSize(fetchSize);
            } catch (Exception e) {
                log.warn(e);
            }
        }
    }

    public static void execute(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query) throws DBException {
        log.info(String.format("execute(%s): %s", jobName, query));
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            try (DBCStatement dbcStatement = DBUtils.createStatement(session, query, true)) {
                dbcStatement.executeStatement();
            }
        }
    }

    public static List<Map<String, Object>> executeQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query) throws DBException {

        List<Map<String, Object>> results = new ArrayList<>();

        executeQuery(monitor, executionContext, jobName, query, new DBDDataReceiver() {

            private DBDAttributeBinding[] bindings;

            @Override
            public void fetchStart(DBCSession session, DBCResultSet resultSet, long offset, long maxRows) throws DBCException {
                DBCResultSetMetaData meta = resultSet.getMeta();
                List<DBCAttributeMetaData> attributes = meta.getAttributes();
                bindings = new DBDAttributeBindingMeta[attributes.size()];
                for (int i = 0; i < attributes.size(); i++) {
                    DBCAttributeMetaData attrMeta = attributes.get(i);
                    bindings[i] = new DBDAttributeBindingMeta(new SimpleContainer(executionContext.getDataSource()), resultSet.getSession(), attrMeta);
                }
            }

            @Override
            public void fetchRow(DBCSession session, DBCResultSet resultSet) throws DBCException {
                Map<String, Object> result = new HashMap<>();
                for (int i = 0; i < bindings.length; i++) {
                    DBDAttributeBinding binding = bindings[i];
                    Object value = binding.getValueHandler().fetchValueObject(
                            resultSet.getSession(),
                            resultSet,
                            binding.getMetaAttribute(),
                            i);
                    result.put(binding.getLabel(), value);
                }
                results.add(result);
            }

            @Override
            public void fetchEnd(DBCSession session, DBCResultSet resultSet) throws DBCException {
            }

            @Override
            public void close() {
            }
        }, false);

        return results;
    }

    public static void executeQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, ConsumerFunction<DBCResultSet> consumer) throws DBException {
        executeQuery(monitor, executionContext, jobName, query, new DBDDataReceiver() {
            @Override
            public void fetchStart(DBCSession session, DBCResultSet resultSet, long offset, long maxRows) throws DBCException {
                try {
                    consumer.accept(resultSet);
                } catch (Throwable e) {
                    throw new DBCException(e.getMessage(), e);
                }
            }

            @Override
            public void fetchRow(DBCSession session, DBCResultSet resultSet) throws DBCException {
            }

            @Override
            public void fetchEnd(DBCSession session, DBCResultSet resultSet) throws DBCException {
            }

            @Override
            public void close() {
            }
        }, false);
    }


    public static <T> List<T> executeQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, FunctionWrapper.Wrapper<DBCResultSet, T> function) throws DBException {
        log.info(String.format("executeQuery(%s): %s", jobName, query));
        List<T> list = new ArrayList<>();
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            try (DBCStatement dbcStatement = DBUtils.createStatement(session, query, true)) {
                if (dbcStatement.executeStatement()) {
                    try (DBCResultSet resultSet = dbcStatement.openResultSet()) {
                        while (resultSet.nextRow()) {
                            list.add(function.apply(resultSet));
                        }
                    }
                }
            }
        } catch (Throwable e) {
            throw new DBException(e.getMessage(), e);
        }
        return list;
    }

    public static void executeQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, DBDDataReceiver dbdDataReceiver, boolean useNativeFormatTemporary) throws DBException {
        log.info(String.format("executeQuery(%s): %s", jobName, query));
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            try (DBCStatement dbcStatement = DBUtils.createStatement(session, query, true)) {
                if (dbcStatement.executeStatement()) {
                    boolean useNativeFormatOriginal = executionContext.getDataSource().getContainer().isUseNativeDateTimeFormat();
                    try (DBCResultSet resultSet = dbcStatement.openResultSet()) {
                        executionContext.getDataSource().getContainer().setUseNativeDateTimeFormat(useNativeFormatTemporary);
                        dbdDataReceiver.fetchStart(session, resultSet, -1, -1);
                        while (resultSet.nextRow()) {
                            dbdDataReceiver.fetchRow(session, resultSet);
                        }
                        dbdDataReceiver.fetchEnd(session, resultSet);
                    } finally {
                        executionContext.getDataSource().getContainer().setUseNativeDateTimeFormat(useNativeFormatOriginal);
                    }
                }
            }
        } catch (Throwable e) {
            throw new DBException(e.getMessage(), e);
        }
    }

    // streaming fetch
    public static void executeQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, DBDDataReceiver dbdDataReceiver) throws DBException {
        log.info(String.format("executeQuery(%s): %s", jobName, query));
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            try (DBCStatement dbcStatement = DBUtils.createStatement(session, query, false)) {
                setStatementFetchSize(dbcStatement, 0, 0, DBConstants.METADATA_FETCH_SIZE);
                if (dbcStatement.executeStatement()) {
                    try (DBCResultSet resultSet = dbcStatement.openResultSet()) {
                        dbdDataReceiver.fetchStart(session, resultSet, -1, -1);
                        while (resultSet.nextRow()) {
                            dbdDataReceiver.fetchRow(session, resultSet);
                        }
                        dbdDataReceiver.fetchEnd(session, resultSet);
                    }
                }
            }
        } catch (Throwable e) {
            throw new DBException(e.getMessage(), e);
        }
    }

    public static int countMetaData(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName,
                                    FunctionWrapper.Wrapper<JDBCDatabaseMetaData, DBCResultSet> metaDataFunction) throws DBException {
        log.info(String.format("countMetaData(%s): %s", jobName, metaDataFunction));
        int count = 0;
        try (JDBCSession session = (JDBCSession) executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            try (DBCResultSet resultSet = metaDataFunction.apply(session.getMetaData())) {
                while (resultSet.nextRow()) {
                    count++;
                }
            }
        } catch (Throwable e) {
            throw new DBException(e.getMessage(), e);
        }
        return count;
    }

    public static int executeMetaData(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName,
                                      FunctionWrapper.Wrapper<JDBCDatabaseMetaData, DBCResultSet> metaDataFunction,
                                      DBDDataReceiver dbdDataReceiver, Integer limit, Integer offset, int rowIndex) throws DBException {

        log.info(String.format("executeMetaData(%s): %s", jobName, metaDataFunction));
        try (JDBCSession session = (JDBCSession) executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            try (DBCResultSet resultSet = metaDataFunction.apply(session.getMetaData())) {
                dbdDataReceiver.fetchStart(session, resultSet, -1, -1);
                while (resultSet.nextRow()) {
                    if (limit != null && offset != null) {
                        if (rowIndex < offset) {
                            rowIndex++;
                            continue;
                        }
                        if (rowIndex < offset + limit) {
                            try {
                                dbdDataReceiver.fetchRow(session, resultSet);
                            } catch (Exception e) {
                                if ("不是分级管理员".equals(e.getMessage())) {
                                    continue;
                                }
                            }
                        }
                    } else {
                        try {
                            dbdDataReceiver.fetchRow(session, resultSet);
                        } catch (Exception e) {
                            if ("不是分级管理员".equals(e.getMessage())) {
                                continue;
                            }
                        }
                    }
                    rowIndex++;
                }
                dbdDataReceiver.fetchEnd(session, resultSet);
            }
        } catch (Throwable e) {
            throw new DBException(e.getMessage(), e);
        }
        return rowIndex;
    }

    public static <T> List<T> executeMetaData(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName,
                                              FunctionWrapper.Wrapper<JDBCDatabaseMetaData, JDBCResultSet> metaDataFunction,
                                              FunctionWrapper.Wrapper<JDBCResultSet, T> resultSetFunction) throws DBException {
        List<T> list = new ArrayList<>();
        log.info(String.format("executeMetaData(%s): %s", jobName, metaDataFunction));
        if (executionContext instanceof JDBCExecutionContext) {
            try (JDBCSession session = (JDBCSession) executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
                try (JDBCResultSet resultSet = metaDataFunction.apply(session.getMetaData())) {
                    while (resultSet.next()) {
                        list.add(resultSetFunction.apply(resultSet));
                    }
                }
            } catch (Throwable e) {
                throw new DBException(e.getMessage(), e);
            }
        }

        return list;
    }

    public static long executeUpdate(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, ConsumerFunction<JDBCPreparedStatement> consumer) throws DBException {
        log.debug(String.format("executeUpdate(%s): %s", jobName, query));
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            boolean supportsPrepareStatement = session.getDataSource().getInfo().isSupportsPrepareStatement(session.getDataSource());
            if (supportsPrepareStatement) {
                try (DBCStatement dbcStatement = DBUtils.makeStatement(session, query, true)) {
                    if (dbcStatement instanceof JDBCPreparedStatement) {
                        consumer.accept((JDBCPreparedStatement) dbcStatement);
                        dbcStatement.executeStatement();
                        return dbcStatement.getUpdateRowCount();
                    }
                }
            } else {
                try (DBCStatement dbcStatement = DBUtils.createStatement(session, query, true)) {
                    if (dbcStatement instanceof JDBCPreparedStatement) {
                        consumer.accept((JDBCPreparedStatement) dbcStatement);
                    }
                    dbcStatement.executeStatement();
                    return dbcStatement.getUpdateRowCount();
                }
            }

        } catch (Throwable e) {
            throw new DBException(e.getMessage(), e);
        }
        return 0L;
    }

    public static void executeScript(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, DBDDataReceiver dbdDataReceiver, int fetchSize) throws DBException {

        log.info(String.format("execute(%s): %s", jobName, query));
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            SimpleContainer simpleContainer = new SimpleContainer(session.getDataSource());
            AbstractExecutionSource source = new AbstractExecutionSource(simpleContainer, executionContext, null, query);
            try (DBCStatement dbcStatement = DBUtils.makeStatement(source, session, DBCStatementType.SCRIPT, query, 0L, 0L)) {
//                DBExecUtils.setStatementFetchSize(dbcStatement, 0, 0, fetchSize);
                if (dbcStatement.executeStatement()) {
                    try (DBCResultSet resultSet = dbcStatement.openResultSet()) {
                        dbdDataReceiver.fetchStart(session, resultSet, -1, -1);
                        while (resultSet.nextRow()) {
                            dbdDataReceiver.fetchRow(session, resultSet);
                        }
                        dbdDataReceiver.fetchEnd(session, resultSet);
                    }
                }
            }
        }
    }

    public static void executeWithMultipleResultSet(DBRProgressMonitor monitor, DBCExecutionContext executionContext, String jobName, @SQL String query, DBDDataReceiver dbdDataReceiver, int fetchSize) throws DBException {

        log.info(String.format("executeWithMultipleResultSet(%s): %s", jobName, query));
        try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, jobName)) {
            SimpleContainer simpleContainer = new SimpleContainer(session.getDataSource());
            AbstractExecutionSource source = new AbstractExecutionSource(simpleContainer, executionContext, null, query);
            try (DBCStatement dbcStatement = DBUtils.makeStatement(source, session, DBCStatementType.SCRIPT, query, 0L, 0L)) {
//                DBExecUtils.setStatementFetchSize(dbcStatement, 0, 0, fetchSize);
                int maxResultsCount = executionContext.getDataSource().getInfo().supportsMultipleResults() ? 100 : 1;
                boolean hasResultSet = dbcStatement.executeStatement();
                for (int i = 0; i < maxResultsCount && hasResultSet; i++) {
                    try (DBCResultSet resultSet = dbcStatement.openResultSet()) {
                        dbdDataReceiver.fetchStart(session, resultSet, -1, -1);
                        while (resultSet.nextRow()) {
                            dbdDataReceiver.fetchRow(session, resultSet);
                        }
                        dbdDataReceiver.fetchEnd(session, resultSet);
                    }
                    hasResultSet = dbcStatement.nextResults();
                }
            }
        }
    }

    public static void executePersistActions(DBCSession session, DBEPersistAction[] persistActions) throws DBCException {
        DBRProgressMonitor monitor = session.getProgressMonitor();
        monitor.beginTask(session.getTaskTitle(), persistActions.length);
        try {
            for (DBEPersistAction action : persistActions) {
                if (monitor.isCanceled()) {
                    break;
                }
                if (!CommonUtils.isEmpty(action.getTitle())) {
                    monitor.subTask(action.getTitle());
                }
                executePersistAction(session, action);
            }
        } finally {
            monitor.done();
        }
    }

    public static void executePersistAction(DBCSession session, DBEPersistAction action) throws DBCException {
        if (action instanceof SQLDatabasePersistActionComment) {
            return;
        }
        String script = action.getScript();
        if (script == null) {
            action.afterExecute(session, null);
        } else {
            DBCStatement dbStat = DBUtils.createStatement(session, script, false);
            try {
                action.beforeExecute(session);
                dbStat.executeStatement();
                if (action instanceof SQLDatabasePersistAction) {
                    ((SQLDatabasePersistAction) action).afterExecute(session, dbStat, null);
                } else {
                    action.afterExecute(session, null);
                }
            } catch (Exception e) {
                action.afterExecute(session, e);
                throw e;
            } finally {
                dbStat.close();
            }
        }
    }

    public static void checkSmartAutoCommit(DBCSession session, String queryText) {
        DBCTransactionManager txnManager = DBUtils.getTransactionManager(session.getExecutionContext());
        if (txnManager != null) {
            try {
                if (!txnManager.isAutoCommit()) {
                    return;
                }

                SQLDialect sqlDialect = SQLUtils.getDialectFromDataSource(session.getDataSource());
                if (!sqlDialect.isTransactionModifyingQuery(queryText)) {
                    return;
                }

                if (txnManager.isAutoCommit()) {
                    txnManager.setAutoCommit(session.getProgressMonitor(), false);
                }
            } catch (DBCException e) {
                log.warn(e);
            }
        }
    }

    public static String getOldCatalogName(DBCExecutionContext context) {
        DBCExecutionContextDefaults<?, ?> contextDefaults = context.getContextDefaults();

        String oldCatalogName = null;

        if (contextDefaults != null) {
            DBSCatalog catalog = contextDefaults.getDefaultCatalog();
            if (catalog != null) {
                oldCatalogName = catalog.getName();
            }
        }

        return oldCatalogName;
    }

    public static void setExecutionContextDefaults(DBRProgressMonitor monitor,
                                                   DBPDataSource dataSource,
                                                   DBCExecutionContext executionContext,
                                                   @Nullable String newInstanceName,
                                                   @Nullable String curInstanceName,
                                                   @Nullable String newObjectName) throws DBException {
        setExecutionContextDefaults(monitor,
                dataSource,
                executionContext,
                newInstanceName,
                curInstanceName,
                newObjectName,
                false);
    }

    public static void setExecutionContextDefaults(DBRProgressMonitor monitor,
                                                   DBPDataSource dataSource,
                                                   DBCExecutionContext executionContext,
                                                   @Nullable String newInstanceName,
                                                   @Nullable String curInstanceName,
                                                   @Nullable String newObjectName,
                                                   boolean force) throws DBException {
        DBSObjectContainer rootContainer = DBUtils.getAdapter(DBSObjectContainer.class, dataSource);
        if (rootContainer == null) {
            return;
        }

        DBCExecutionContextDefaults<?, ?> contextDefaults = null;
        if (executionContext != null) {
            contextDefaults = executionContext.getContextDefaults();
        }
        if (contextDefaults != null && (contextDefaults.supportsSchemaChange() || contextDefaults.supportsCatalogChange())) {
            changeDefaultObject(monitor, rootContainer, contextDefaults, newInstanceName, curInstanceName, newObjectName, force);
        }
    }

    @SuppressWarnings("all")
    public static void changeDefaultObject(
            @NotNull DBRProgressMonitor monitor,
            @NotNull DBSObjectContainer rootContainer,
            @NotNull DBCExecutionContextDefaults contextDefaults,
            @Nullable String newCatalogName,
            @Nullable String curCatalogName,
            @Nullable String newObjectName,
            boolean force) throws DBException {
        DBSCatalog newCatalog = null;
        DBSSchema newSchema = null;

        if (StringUtils.isNotBlank(newCatalogName)) {
            try {
                DBSObject newInstance = rootContainer.getChild(monitor, newCatalogName);
                if (newInstance instanceof DBSCatalog) {
                    newCatalog = (DBSCatalog) newInstance;
                }
            } catch (DBException e) {
                log.error(e.getMessage());
            }
        }
        DBSObject newObject = null;
        if (newObjectName != null) {
            if (newCatalog == null) {
                try {
                    newObject = rootContainer.getChild(monitor, newObjectName);
                } catch (DBException e) {
                    log.error(e.getMessage());
                }
            } else {
                try {
                    newObject = newCatalog.getChild(monitor, newObjectName);
                } catch (DBException e) {
                    log.error(e.getMessage());
                }
            }
            if (newObject instanceof DBSSchema) {
                newSchema = (DBSSchema) newObject;
            } else if (newObject instanceof DBSCatalog) {
                newCatalog = (DBSCatalog) newObject;
                newCatalogName = newCatalog.getName();
            }
        }

        boolean changeCatalog = (curCatalogName != null ? !CommonUtils.equalObjects(curCatalogName, newCatalogName) : newCatalog != null);
        boolean changeSchema = (curCatalogName != null ? !curCatalogName.equals(newCatalogName) : newCatalogName != null);

        if (newCatalog != null && newSchema != null && changeCatalog && contextDefaults.supportsCatalogChange()) {
            monitor.subTask("Catalog Change");
            contextDefaults.setDefaultCatalog(monitor, newCatalog, contextDefaults.supportsSchemaChange() ? newSchema : null, force);
        } else if (newSchema != null && contextDefaults.supportsSchemaChange()) {
            monitor.subTask("Schema Change");
            contextDefaults.setDefaultSchema(monitor, newSchema, force);
        } else if (newCatalog != null && changeCatalog) {
            monitor.subTask("Catalog Change");
            contextDefaults.setDefaultCatalog(monitor, newCatalog, null, force);
        } else if (changeSchema && contextDefaults.supportsSchemaChange() && !contextDefaults.supportsCatalogChange()) {
            throw new DBException("没有找到指定的数据库！");
        }
    }

    public static void recoverSmartCommit(DBCExecutionContext executionContext) {
        DBPPreferenceStore preferenceStore = executionContext.getDataSource().getContainer().getPreferenceStore();
        if (preferenceStore.getBoolean(ModelPreferences.TRANSACTIONS_SMART_COMMIT) && preferenceStore.getBoolean(ModelPreferences.TRANSACTIONS_SMART_COMMIT_RECOVER)) {
            DBCTransactionManager transactionManager = DBUtils.getTransactionManager(executionContext);
            if (transactionManager != null) {
                new AbstractJob("Recover smart commit mode") {
                    @Override
                    protected IStatus run(DBRProgressMonitor monitor) {
                        if (!executionContext.isConnected()) {
                            return Status.OK_STATUS;
                        }
                        try {
                            monitor.beginTask("Switch to auto-commit mode", 1);
                            if (!transactionManager.isAutoCommit()) {
                                transactionManager.setAutoCommit(monitor, true);
                            }
                        } catch (DBCException e) {
                            log.debug("Error recovering smart commit mode: " + e.getMessage());
                        } finally {
                            monitor.done();
                        }
                        return Status.OK_STATUS;
                    }
                }.schedule();
            }
        }
    }

    public static DBSEntityConstraint getBestIdentifier(@NotNull DBRProgressMonitor monitor, @NotNull DBSEntity table, DBDAttributeBinding[] bindings, boolean readMetaData)
            throws DBException {
        if (table instanceof DBSDocumentContainer) {
            return new DBSDocumentConstraint((DBSDocumentContainer) table);
        }
        List<DBSEntityConstraint> identifiers = new ArrayList<>(2);
        //List<DBSEntityConstraint> nonIdentifyingConstraints = null;

        if (readMetaData) {
            if (table instanceof DBSTable && ((DBSTable) table).isView()) {
                // Skip physical identifiers for views. There are nothing anyway

            } else {
                // Check indexes first.
                if (table instanceof DBSTable) {
                    try {
                        Collection<? extends DBSTableIndex> indexes = ((DBSTable) table).getIndexes(monitor);
                        if (!CommonUtils.isEmpty(indexes)) {
                            // First search for primary index
                            for (DBSTableIndex index : indexes) {
                                if (index.isPrimary() && DBUtils.isIdentifierIndex(monitor, index)) {
                                    identifiers.add(index);
                                    break;
                                }
                            }
                            // Then search for unique index
                            for (DBSTableIndex index : indexes) {
                                if (DBUtils.isIdentifierIndex(monitor, index) && !identifiers.contains(index)) {
                                    identifiers.add(index);
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        // Indexes are not supported or not available
                        // Just skip them
                        log.debug(e);
                    }
                }
                {
                    // Check constraints
                    Collection<? extends DBSEntityConstraint> constraints = table.getConstraints(monitor);
                    if (constraints != null) {
                        for (DBSEntityConstraint constraint : constraints) {
                            if (DBUtils.isIdentifierConstraint(monitor, constraint)) {
                                identifiers.add(constraint);
                            }/* else {
                                if (nonIdentifyingConstraints == null) nonIdentifyingConstraints = new ArrayList<>();
                                nonIdentifyingConstraints.add(constraint);
                            }*/
                        }
                    }
                }

            }
        }

        if (!CommonUtils.isEmpty(identifiers)) {
            // Find PK or unique key
            DBSEntityConstraint uniqueId = null;
            for (DBSEntityConstraint constraint : identifiers) {
                if (constraint instanceof DBSEntityReferrer) {
                    DBSEntityReferrer referrer = (DBSEntityReferrer) constraint;
                    if (isGoodReferrer(monitor, bindings, referrer)) {
                        if (referrer.getConstraintType() == DBSEntityConstraintType.PRIMARY_KEY) {
                            return referrer;
                        } else if (uniqueId == null &&
                                (referrer.getConstraintType().isUnique() ||
                                        (referrer instanceof DBSTableIndex && ((DBSTableIndex) referrer).isUnique()))) {
                            uniqueId = referrer;
                        }
                    }
                } else {
                    uniqueId = constraint;
                }
            }
            if (uniqueId != null) {
                return uniqueId;
            }
        }

        {
            // Check for pseudo attrs (ROWID)
            // Do this after natural identifiers search (see #3829)
            for (DBDAttributeBinding column : bindings) {
                DBDPseudoAttribute pseudoAttribute = column instanceof DBDAttributeBindingMeta ? ((DBDAttributeBindingMeta) column).getPseudoAttribute() : null;
                if (pseudoAttribute != null && pseudoAttribute.getType() == DBDPseudoAttributeType.ROWID) {
                    return new DBDPseudoReferrer(table, column);
                }
            }
        }

        // No physical identifiers or row ids
        // Make new or use existing virtual identifier
        DBVEntity virtualEntity = DBVUtils.getVirtualEntity(table, true);
        return virtualEntity.getBestIdentifier();
    }

    private static boolean isGoodReferrer(DBRProgressMonitor monitor, DBDAttributeBinding[] bindings, DBSEntityReferrer referrer) throws DBException {
        if (referrer instanceof DBDPseudoReferrer) {
            return true;
        }
        Collection<? extends DBSEntityAttributeRef> references = referrer.getAttributeReferences(monitor);
        if (references == null || references.isEmpty()) {
            return referrer instanceof DBVEntityConstraint;
        }
        for (DBSEntityAttributeRef ref : references) {
            boolean refMatches = false;
            for (DBDAttributeBinding binding : bindings) {
                if (binding.matches(ref.getAttribute(), false)) {
                    refMatches = true;
                    break;
                }
            }
            if (!refMatches) {
                return false;
            }
        }
        return true;
    }

    public static DBSEntityAssociation getAssociationByAttribute(DBDAttributeBinding attr) throws DBException {
        List<DBSEntityReferrer> referrers = attr.getReferrers();
        if (referrers != null) {
            for (final DBSEntityReferrer referrer : referrers) {
                if (referrer instanceof DBSEntityAssociation) {
                    return (DBSEntityAssociation) referrer;
                }
            }
        }
        throw new DBException("Association not found in attribute [" + attr.getName() + "]");
    }

    public static boolean equalAttributes(DBCAttributeMetaData attr1, DBCAttributeMetaData attr2) {
        return
                attr1 != null && attr2 != null &&
                        SQLUtils.compareAliases(attr1.getLabel(), attr2.getLabel()) &&
                        SQLUtils.compareAliases(attr1.getName(), attr2.getName()) &&
                        CommonUtils.equalObjects(attr1.getEntityMetaData(), attr2.getEntityMetaData()) &&
                        attr1.getOrdinalPosition() == attr2.getOrdinalPosition() &&
                        attr1.isRequired() == attr2.isRequired() &&
                        attr1.getMaxLength() == attr2.getMaxLength() &&
                        CommonUtils.equalObjects(attr1.getPrecision(), attr2.getPrecision()) &&
                        CommonUtils.equalObjects(attr1.getScale(), attr2.getScale()) &&
                        attr1.getTypeID() == attr2.getTypeID() &&
                        CommonUtils.equalObjects(attr1.getTypeName(), attr2.getTypeName());
    }

    public static double makeNumericValue(Object value) {
        if (value == null) {
            return 0;
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof Date) {
            return ((Date) value).getTime();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        } else {
            return 0;
        }
    }

    public static void bindAttributes(
            @NotNull DBCSession session,
            @Nullable DBSEntity sourceEntity,
            @Nullable DBCResultSet resultSet,
            @NotNull DBDAttributeBinding[] bindings,
            @Nullable List<Object[]> rows) throws DBException {
        final DBRProgressMonitor monitor = session.getProgressMonitor();
        final DBPDataSource dataSource = session.getDataSource();
        boolean readMetaData = dataSource.getContainer().getPreferenceStore().getBoolean(ModelPreferences.RESULT_SET_READ_METADATA);
        if (!readMetaData && sourceEntity == null) {
            // Do not read metadata if source entity is not known
            return;
        }
        boolean readReferences = dataSource.getContainer().getPreferenceStore().getBoolean(ModelPreferences.RESULT_SET_READ_REFERENCES);

        final Map<DBCEntityMetaData, DBSEntity> entityBindingMap = new IdentityHashMap<>();

        monitor.beginTask("Discover resultset metadata", 3);
        try {
            SQLQuery sqlQuery = null;
            DBSEntity entity = null;
            int queryEntityMetaScore = -1;
            if (sourceEntity != null) {
                entity = sourceEntity;
            } else if (resultSet != null) {
                DBCStatement sourceStatement = resultSet.getSourceStatement();
                if (sourceStatement != null && sourceStatement.getStatementSource() != null) {
                    DBCExecutionSource executionSource = sourceStatement.getStatementSource();

                    monitor.subTask("Discover owner entity");
                    DBSDataContainer dataContainer = executionSource.getDataContainer();
                    if (dataContainer instanceof DBSEntity) {
                        entity = (DBSEntity) dataContainer;
                    }
                    DBCEntityMetaData entityMeta = null;
                    if (entity == null) {
                        // Discover from entity metadata
                        Object sourceDescriptor = executionSource.getSourceDescriptor();
                        if (sourceDescriptor instanceof SQLQuery) {
                            sqlQuery = (SQLQuery) sourceDescriptor;
                            entityMeta = sqlQuery.getEntityMetadata(false);
                        }
                        if (entityMeta != null) {
                            entity = DBUtils.getEntityFromMetaData(monitor, session.getExecutionContext(), entityMeta);
                            if (entity != null) {
                                queryEntityMetaScore = entityMeta.getCompleteScore();
                                entityBindingMap.put(entityMeta, entity);
                            }
                        }
                    }
                }
            }

            boolean needsTableMetaForColumnResolution = dataSource.getInfo().needsTableMetaForColumnResolution();

            final Map<DBSEntity, DBDRowIdentifier> locatorMap = new IdentityHashMap<>();

            monitor.subTask("Discover attributes");
            for (DBDAttributeBinding binding : bindings) {
                monitor.subTask("Discover attribute '" + binding.getName() + "'");
                DBCAttributeMetaData attrMeta = binding.getMetaAttribute();
                if (attrMeta == null) {
                    continue;
                }
                // We got table name and column name
                // To be editable we need this resultset contain set of columns from the same table
                // which construct any unique key
                DBSEntity attrEntity = null;
                if (sourceEntity == null) {
                    DBCEntityMetaData attrEntityMeta = attrMeta.getEntityMetaData();
                    if (attrEntityMeta == null && sqlQuery != null) {
                        SQLSelectItem selectItem = sqlQuery.getSelectItem(attrMeta.getOrdinalPosition());
                        if (selectItem != null && selectItem.isPlainColumn()) {
                            attrEntityMeta = selectItem.getEntityMetaData();
                        }
                    }
                    if (attrEntityMeta != null) {
                        attrEntity = entityBindingMap.get(attrEntityMeta);
                        if (attrEntity == null) {
                            if (entity != null &&
                                    (queryEntityMetaScore > attrEntityMeta.getCompleteScore() || DBUtils.isView(entity))) {
                                // If query entity score is greater than database provided entity meta score then use base entity (from SQL query)

                                // If this is a view then don't try to detect entity for each attribute
                                // MySQL returns source table name instead of view name. That's crazy.
                                attrEntity = entity;
                            } else {
                                attrEntity = DBUtils.getEntityFromMetaData(monitor, session.getExecutionContext(), attrEntityMeta);

                                if (attrEntity == null) {
                                    log.debug("Table '" + DBUtils.getSimpleQualifiedName(attrEntityMeta.getCatalogName(), attrEntityMeta.getSchemaName(), attrEntityMeta.getEntityName()) + "' not found in metadata catalog");
                                }
                            }
                        }
                        if (attrEntity != null) {
                            entityBindingMap.put(attrEntityMeta, attrEntity);
                        }
                    }
                }
                if (attrEntity == null) {
                    attrEntity = entity;
                }
                if (attrEntity != null && binding instanceof DBDAttributeBindingMeta) {
                    DBDAttributeBindingMeta bindingMeta = (DBDAttributeBindingMeta) binding;

                    // Table column can be found from results metadata or from SQL query parser
                    // If datasource supports table names in result metadata then table name must present in results metadata.
                    // Otherwise it is an expression.

                    // It is a real table columns if:
                    //  - We use some explicit entity (e.g. table data editor)
                    //  - Table metadata was specified for column
                    //  - Database doesn't support column name collisions (default)
                    boolean updateColumnMeta = sourceEntity != null ||
                            bindingMeta.getMetaAttribute().getEntityMetaData() != null ||
                            !needsTableMetaForColumnResolution;

                    // Fix of #11194. If column name and alias are equals we could try to get real column name
                    // from parsed query because driver doesn't return it.
                    String columnName = attrMeta.getName();
                    if (sqlQuery != null &&
                            updateColumnMeta &&
                            CommonUtils.equalObjects(columnName, attrMeta.getLabel())) {
                        int asteriskIndex = sqlQuery.getSelectItemAsteriskIndex();
                        if ((asteriskIndex < 0 || asteriskIndex > attrMeta.getOrdinalPosition()) &&
                                attrMeta.getOrdinalPosition() < sqlQuery.getSelectItemCount() &&
                                bindings.length == sqlQuery.getSelectItemCount()) {
                            SQLSelectItem selectItem = sqlQuery.getSelectItem(attrMeta.getOrdinalPosition());
                            if (selectItem.isPlainColumn()) {
                                String realColumnName = selectItem.getName();
                                if (!CommonUtils.equalObjects(realColumnName, columnName)) {
                                    if (DBUtils.isQuotedIdentifier(dataSource, realColumnName)) {
                                        columnName = DBUtils.getUnQuotedIdentifier(dataSource, realColumnName);
                                    } else {
                                        // #12008
                                        columnName = DBObjectNameCaseTransformer.transformName(dataSource, realColumnName);
                                    }
                                }
                            }
                        }
                    }

                    // Test pseudo attributes
                    DBDPseudoAttribute pseudoAttribute = DBUtils.getPseudoAttribute(attrEntity, columnName);
                    if (pseudoAttribute != null) {
                        bindingMeta.setPseudoAttribute(pseudoAttribute);
                    }

                    DBSEntityAttribute tableColumn = null;
                    if (bindingMeta.getPseudoAttribute() != null) {
                        tableColumn = bindingMeta.getPseudoAttribute().createFakeAttribute(attrEntity, attrMeta);
                    } else if (columnName != null) {
                        tableColumn = attrEntity.getAttribute(monitor, columnName);
                    }

                    if (tableColumn != null) {
                        boolean updateColumnHandler = updateColumnMeta && rows != null
                                && (sqlQuery == null || !DBDAttributeBindingMeta.haveEqualsTypes(tableColumn, attrMeta));

                        if ((!updateColumnHandler && bindingMeta.getDataKind() != tableColumn.getDataKind())
                                || (resultSet != null
                                && !isSameDataTypes(tableColumn, resultSet.getMeta().getAttributes().get(attrMeta.getOrdinalPosition())))) {
                            // Different data kind. Probably it is an alias which conflicts with column name
                            // Do not update entity attribute.
                            // It is a silly workaround for PG-like databases
                        } else if (bindingMeta.setEntityAttribute(tableColumn, updateColumnHandler) && rows != null) {
                            // We have new type and new value handler.
                            // We have to fix already fetched values.
                            // E.g. we fetched strings and found out that we should handle them as LOBs or enums.
                            try {
                                int pos = attrMeta.getOrdinalPosition();
                                for (Object[] row : rows) {
                                    row[pos] = binding.getValueHandler().getValueFromObject(session, tableColumn, row[pos], false, false);
                                }
                            } catch (DBCException e) {
                                log.warn("Error resolving attribute '" + binding.getName() + "' values", e);
                            }
                        }
                    }
                }
            }
            monitor.worked(1);

            {
                // Init row identifiers
                monitor.subTask("Detect unique identifiers");
                for (DBDAttributeBinding binding : bindings) {
                    if (!(binding instanceof DBDAttributeBindingMeta)) {
                        continue;
                    }
                    DBDAttributeBindingMeta bindingMeta = (DBDAttributeBindingMeta) binding;
                    //monitor.subTask("Find attribute '" + binding.getName() + "' identifier");
                    DBSEntityAttribute attr = binding.getEntityAttribute();
                    if (attr == null) {
                        bindingMeta.setRowIdentifierStatus("No corresponding table column");
                        continue;
                    }
                    DBSEntity attrEntity = attr.getParentObject();
                    if (attrEntity != null) {
                        DBDRowIdentifier rowIdentifier = locatorMap.get(attrEntity);
                        if (rowIdentifier == null) {
                            DBSEntityConstraint entityIdentifier = getBestIdentifier(monitor, attrEntity, bindings, readMetaData);
                            if (entityIdentifier != null) {
                                rowIdentifier = new DBDRowIdentifier(attrEntity, entityIdentifier);
                                locatorMap.put(attrEntity, rowIdentifier);
                            } else {
                                bindingMeta.setRowIdentifierStatus("Cannot determine unique row identifier");
                            }
                        }
                        bindingMeta.setRowIdentifier(rowIdentifier);
                    }
                }
                monitor.worked(1);
            }

            if (readMetaData && readReferences && rows != null) {
                monitor.subTask("Read results metadata");
                // Read nested bindings
                for (DBDAttributeBinding binding : bindings) {
                    binding.lateBinding(session, rows);
                }
            }

/*
            monitor.subTask("Load transformers");
            // Load transformers
            for (DBDAttributeBinding binding : bindings) {
                binding.loadTransformers(session, rows);
            }
*/

            monitor.subTask("Complete metadata load");
            // Reload attributes in row identifiers
            for (DBDRowIdentifier rowIdentifier : locatorMap.values()) {
                rowIdentifier.reloadAttributes(monitor, bindings);
            }
        } finally {
            monitor.done();
        }
    }

    private static boolean isSameDataTypes(@NotNull DBSEntityAttribute tableColumn, @NotNull DBCAttributeMetaData resultSetAttributeMeta) {
        if (tableColumn instanceof DBSTypedObjectEx) {
            DBSDataType columnDataType = ((DBSTypedObjectEx) tableColumn).getDataType();
            return columnDataType != null && columnDataType.isStructurallyConsistentTypeWith(resultSetAttributeMeta);
        }
        return tableColumn.getDataKind().isComplex() == resultSetAttributeMeta.getDataKind().isComplex();
    }

    public static boolean isAttributeReadOnly(@Nullable DBDAttributeBinding attribute) {
        if (attribute == null || attribute.getMetaAttribute() == null || attribute.getMetaAttribute().isReadOnly()) {
            return true;
        }
        DBDRowIdentifier rowIdentifier = attribute.getRowIdentifier();
        if (rowIdentifier == null || !(rowIdentifier.getEntity() instanceof DBSDataManipulator)) {
            return true;
        }
        DBSDataManipulator dataContainer = (DBSDataManipulator) rowIdentifier.getEntity();
        return !dataContainer.isFeatureSupported(DBSDataManipulator.FEATURE_DATA_UPDATE);
    }

    public static String getAttributeReadOnlyStatus(@NotNull DBDAttributeBinding attribute) {
        if (attribute == null || attribute.getMetaAttribute() == null) {
            return "Null meta attribute";
        }
        if (attribute.getMetaAttribute().isReadOnly()) {
            return "Attribute is read-only";
        }
        DBDRowIdentifier rowIdentifier = attribute.getRowIdentifier();
        if (rowIdentifier == null) {
            String status = attribute.getRowIdentifierStatus();
            return status != null ? status : "No row identifier found";
        }
        DBSEntity dataContainer = rowIdentifier.getEntity();
        if (!(dataContainer instanceof DBSDataManipulator)) {
            return "Underlying entity doesn't support data modification";
        }
        if (!((DBSDataManipulator) dataContainer).isFeatureSupported(DBSDataManipulator.FEATURE_DATA_UPDATE)) {
            return "Underlying entity doesn't support data update";
        }
        return null;
    }

    public static List<DBEPersistAction> getActionsListFromCommandContext(@NotNull DBRProgressMonitor monitor, DBECommandContext commandContext, DBCExecutionContext executionContext, Map<String, Object> options, @Nullable List<DBEPersistAction> actions) throws DBException {
        if (actions == null) {
            actions = new ArrayList<>();
        }
        for (DBECommand cmd : commandContext.getFinalCommands()) {
            DBEPersistAction[] persistActions = cmd.getPersistActions(monitor, executionContext, options);
            if (persistActions != null) {
                Collections.addAll(actions, persistActions);
            }
        }
        return actions;
    }

    @Nullable
    public static DBSEntity detectSingleSourceTable(DBDAttributeBinding... attributes) {
        // Check single source flag
        DBSEntity sourceTable = null;
        for (DBDAttributeBinding attribute : attributes) {
            if (attribute.isPseudoAttribute()) {
                continue;
            }
            DBDRowIdentifier rowIdentifier = attribute.getRowIdentifier();
            if (rowIdentifier != null) {
                if (sourceTable == null) {
                    sourceTable = rowIdentifier.getEntity();
                } else if (sourceTable != rowIdentifier.getEntity()) {
                    return null;
                }
            }
        }
        return sourceTable;
    }
}