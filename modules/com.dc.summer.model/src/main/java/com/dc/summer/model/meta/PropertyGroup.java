

package com.dc.summer.model.meta;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Property group
 */
@Target(value = {ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PropertyGroup
{

    String id() default "";

    String name() default "";

    String category() default "";

    String description() default "";

    int order() default Integer.MAX_VALUE;

}
