

package com.dc.summer.model.meta;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Category
 */
@Target(value = {ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Category
{
    /**
     * Category ID (unique within class).
     * Properties refers categories with ID.
     * @return id
     */
    String id() default ""; //NON-NLS-1

    /**
     * Category human readable name
     */
    String name() default "";

    /**
     * Category description
     */
    String description() default "";

    boolean primary() default false;

}
