
package com.dc.summer.model.impl.jdbc.data;

import com.dc.code.NotNull;
import com.dc.summer.registry.center.Global;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.data.DBDContentCached;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.storage.ExternalContentStorage;
import com.dc.summer.model.data.storage.StringContentStorage;
import com.dc.summer.model.data.storage.TemporaryContentStorage;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.MimeTypes;
import com.dc.utils.CommonUtils;

import java.io.*;
import java.sql.Clob;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;

/**
 * JDBCContentCLOB
 *
 * <AUTHOR> Rider
 */
public class JDBCContentCLOB extends JDBCContentLOB implements DBDContent {

    private static final Log log = Log.getLog(JDBCContentCLOB.class);

    private Clob clob;
    private Reader tmpReader;

    public JDBCContentCLOB(DBCExecutionContext executionContext, Clob clob) {
        super(executionContext);
        this.clob = clob;
    }

    @Override
    public long getLOBLength() throws DBCException {
        if (clob == null) {
            return 0;
        }
        try {
            return clob.length();
        } catch (Throwable e) {
            throw new DBCException(e, executionContext);
        }
    }

    @NotNull
    @Override
    public String getContentType()
    {
        return MimeTypes.TEXT_PLAIN;
    }

    public Clob getClob() {
        return clob;
    }

    @Override
    public DBDContentStorage getContents(DBRProgressMonitor monitor)
        throws DBCException
    {
        if (storage == null && clob != null) {
            long contentLength = getContentLength();
            if (contentLength < ModelPreferences.getPreferences().getInt(ModelPreferences.MEMORY_CONTENT_MAX_SIZE)) {
                try {
                    String subString = clob.getSubString(1, (int) contentLength);
                    storage = new JDBCContentChars(executionContext, subString);
                } catch (Exception e) {
                    log.debug("Can't get CLOB as substring", e);
                    try {
                        storage = StringContentStorage.createFromReader(clob.getCharacterStream(), contentLength);
                    } catch (IOException e1) {
                        throw new DBCException("IO error while reading content", e);
                    } catch (Throwable e1) {
                        throw new DBCException(e, executionContext);
                    }
                }
            } else {
                // Create new local storage
                File tempFile;
//                                    tempFile = ContentUtils.createTempContentFile(monitor, platform, "clob" + clob.hashCode());
                tempFile = new File(Global.getEXPORT() + "clob-" + System.currentTimeMillis() + ".data");
                try (Writer os = new OutputStreamWriter(new FileOutputStream(tempFile), getDefaultEncoding())) {
                    ContentUtils.copyStreams(clob.getCharacterStream(), contentLength, os, monitor);
                } catch (IOException e) {
                    ContentUtils.deleteTempFile(tempFile);
                    throw new DBCException("IO error while copying content", e);
                } catch (Throwable e) {
                    ContentUtils.deleteTempFile(tempFile);
                    throw new DBCException(e, executionContext);
                }
                this.storage = new TemporaryContentStorage(tempFile, getDefaultEncoding(), true);
            }
            // Free lob - we don't need it anymore
            releaseClob();
        }
        return storage;
    }

    @Override
    public void release()
    {
        releaseTempStream();
        releaseClob();
        super.release();
    }

    private void releaseClob() {
        if (clob != null) {
            try {
                clob.free();
            } catch (Throwable e) {
                // Log as warning only if it is an exception.
                log.debug("Error freeing CLOB: " + e.getClass().getName() + ": " + e.getMessage());
            }
            clob = null;
        }
    }

    private void releaseTempStream() {
        if (tmpReader != null) {
            ContentUtils.close(tmpReader);
            tmpReader = null;
        }
    }

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement preparedStatement,
                              DBSTypedObject columnType, int paramIndex)
        throws DBCException
    {
        try {
            if (storage != null) {
//                String stringValue = ContentUtils.getContentStringValue(session.getProgressMonitor(), this);
//                preparedStatement.setString(paramIndex, stringValue);
                // Try 3 jdbc methods to set character stream
                releaseTempStream();
                tmpReader = storage.getContentReader();
                try {
                    preparedStatement.setNCharacterStream(
                        paramIndex,
                        tmpReader);
                }
                catch (Throwable e) {
                    // 有些数据库不是报的SQLFeatureNotSupportedException这个异常，所以第一层不直接报错了
//                    if (e instanceof SQLException && !(e instanceof SQLFeatureNotSupportedException)) {
//                        throw (SQLException)e;
//                    }
                    long streamLength = ContentUtils.calculateContentLength(storage.getContentReader());
                    try {
                        preparedStatement.setCharacterStream(
                                paramIndex,
                                tmpReader,
                                streamLength);
                    }
                    catch (Throwable e1) {
                        if (e1 instanceof SQLException && !(e instanceof SQLFeatureNotSupportedException)) {
                            throw (SQLException)e1;
                        } else {
                            preparedStatement.setCharacterStream(
                                    paramIndex,
                                    tmpReader,
                                    (int)streamLength);
                        }
                    }
                }
            } else if (clob != null) {
                preparedStatement.setClob(paramIndex, clob);
            } else {
                preparedStatement.setNull(paramIndex, java.sql.Types.CLOB);
            }
        }
        catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
        catch (Throwable e) {
            throw new DBCException("IO error while binding content", e);
        }
    }

    @Override
    public Object getRawValue() {
        return clob;
    }

    @Override
    public boolean isNull()
    {
        return clob == null && storage == null;
    }

    @Override
    protected JDBCContentLOB createNewContent()
    {
        return new JDBCContentCLOB(executionContext, null);
    }

    @Override
    public String getDisplayString(DBDDisplayFormat format)
    {
        if (clob == null && storage == null) {
            return null;
        }
        if (storage != null) {
            if (storage instanceof DBDContentCached) {
                return CommonUtils.toString(((DBDContentCached) storage).getCachedValue());
            } else {
                if (storage instanceof ExternalContentStorage) {
                    return "[" + ((ExternalContentStorage) storage).getFile().getName() + "]";
                }
            }
        } else {
            try {
                return ContentUtils.clobToString(clob.getCharacterStream());
            } catch (SQLException e) {
                return e.getMessage();
            }
        }
        return "[CLOB]";
    }
}
