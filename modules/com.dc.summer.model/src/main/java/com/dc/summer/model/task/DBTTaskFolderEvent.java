
package com.dc.summer.model.task;

public class DBTTaskFolderEvent {

    public enum Action
    {
        TASK_FOLDER_ADD,
        TASK_FOLDER_UPDATE,
        TASK_FOLDER_REMOVE
    }

    private DBTTaskFolder taskFolder;
    private Action action;

    public DBTTaskFolderEvent(DBTTaskFolder taskFolder, Action action) {
        this.taskFolder = taskFolder;
        this.action = action;
    }

    public Action getAction()
    {
        return action;
    }

    public DBTTaskFolder getTaskFolder() {
        return taskFolder;
    }

    @Override
    public String toString() {
        return action + " " + taskFolder;
    }
}
