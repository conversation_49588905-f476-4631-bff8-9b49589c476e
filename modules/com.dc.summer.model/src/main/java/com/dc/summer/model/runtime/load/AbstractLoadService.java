
package com.dc.summer.model.runtime.load;

import com.dc.summer.model.runtime.BlockCanceler;
import com.dc.summer.model.runtime.DBRBlockingObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.utils.CommonUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * Lazy loading service
 * @param <RESULT> result type
 */
public abstract class AbstractLoadService<RESULT> implements ILoadService<RESULT> {
    private String serviceName;
    private DBRProgressMonitor progressMonitor;
    private AbstractJob ownerJob;

    protected AbstractLoadService(String serviceName)
    {
        this.serviceName = serviceName;
    }

    protected AbstractLoadService()
    {
        this("Loading");
    }

    @Override
    public String getServiceName()
    {
        return serviceName;
    }

    public void initService(DBRProgressMonitor monitor, AbstractJob ownerJob)
    {
        this.progressMonitor = monitor;
        this.ownerJob = ownerJob;
    }

    @Override
    public boolean cancel() throws InvocationTargetException
    {
        if (this.ownerJob != null) {
            return this.ownerJob.cancel();
        } else if (progressMonitor != null) {
            try {
                List<DBRBlockingObject> activeBlocks = progressMonitor.getActiveBlocks();
                if (!CommonUtils.isEmpty(activeBlocks)) {
                    BlockCanceler.cancelBlock(progressMonitor, activeBlocks.get(activeBlocks.size() - 1), null);
                }
                return true;
            } catch (DBException e) {
                throw new InvocationTargetException(e);
            }
        }
        return false;
    }

}