

package com.dc.summer.model.auth;

import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;

/**
 * Session provider service
 */
public interface SMSessionProviderService {

    @Nullable
    SMSession acquireSession(
        @NotNull DBRProgressMonitor monitor,
        @NotNull SMSessionContext context,
        @NotNull SMAuthSpace space) throws DBException;

}