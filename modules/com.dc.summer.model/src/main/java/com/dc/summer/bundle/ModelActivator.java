
package com.dc.summer.bundle;

import org.eclipse.core.runtime.Plugin;
import org.osgi.framework.BundleContext;

/**
 * The activator class controls the plug-in life cycle
 */
public class ModelActivator extends Plugin
{

    // The shared instance
    private static ModelActivator instance;

    /**
     * The constructor
     */
    public ModelActivator()
    {
    }

    public static ModelActivator getInstance()
    {
        return instance;
    }

    @Override
    public void start(BundleContext context)
        throws Exception
    {
        super.start(context);
        instance = this;
    }

    @Override
    public void stop(BundleContext context)
        throws Exception
    {
        instance = null;

        super.stop(context);
    }

/*
    public synchronized PrintStream getDebugWriter()
    {
        if (debugWriter == null) {
            File logPath = GeneralUtils.getMetadataFolder();
            File debugLogFile = new File(logPath, "dbeaver-debug.log"); //$NON-NLS-1$
            if (debugLogFile.exists()) {
                if (!debugLogFile.delete()) {
                    System.err.println("Can't delete debug log file"); //$NON-NLS-1$
                }
            }
            try {
                debugWriter = new PrintStream(debugLogFile);
            } catch (FileNotFoundException e) {
                e.printStackTrace(System.err);
            }
        }
        return debugWriter;
    }
*/

}
