
package com.dc.summer.model.struct;

import com.dc.summer.model.DBPObject;
import com.dc.summer.DBException;

/**
 * DBSTypedObjectExt2
 */
public interface DBSTypedObjectExt2 extends DBPObject {
    /**
     * Sets database-specific type name (without modifiers). E.g. {@code varchar}, {@code number}
     *
     * @throws DBException on any DB error or if the type could not be resolved
     */
    void setTypeName(String typeName) throws DBException;

    void setMaxLength(long maxLength);

    void setScale(Integer scale);

    void setPrecision(Integer precision);

    void setRequired(boolean required);

}
