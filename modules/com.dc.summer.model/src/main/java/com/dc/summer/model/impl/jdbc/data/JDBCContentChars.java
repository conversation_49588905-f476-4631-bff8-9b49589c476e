
package com.dc.summer.model.impl.jdbc.data;

import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.MimeTypes;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDContentCached;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.CommonUtils;

import java.io.*;
import java.sql.SQLException;

/**
 * JDBCContentChars
 *
 * <AUTHOR> Rider
 */
public class JDBCContentChars extends JDBCContentAbstract implements DBDContentStorage, DBDContentCached {

    private String originalData;
    protected String data;

    public JDBCContentChars(DBCExecutionContext executionContext, String data) {
        super(executionContext);
        this.data = this.originalData = data;
    }

    public JDBCContentChars(JDBCContentChars copyFrom) {
        super(copyFrom);
        this.originalData = copyFrom.originalData;
        this.data = copyFrom.data;
    }

    @Override
    public InputStream getContentStream()
        throws IOException
    {
        if (data == null) {
            // Empty content
            return new ByteArrayInputStream(new byte[0]);
        } else {
            return new ByteArrayInputStream(data.getBytes(getCharset()));
        }
    }

    @Override
    public Reader getContentReader()
        throws IOException
    {
        if (data == null) {
            // Empty content
            return new StringReader(""); //$NON-NLS-1$
        } else {
            return new StringReader(data);
        }
    }

    @Override
    public long getContentLength() {
        if (data == null) {
            return 0;
        }
        return data.length();
    }

    @NotNull
    @Override
    public String getContentType()
    {
        return MimeTypes.TEXT_PLAIN;
    }

    @Override
    public DBDContentStorage getContents(DBRProgressMonitor monitor)
        throws DBCException
    {
        return this;
    }

    @Override
    public boolean updateContents(
        DBRProgressMonitor monitor,
        DBDContentStorage storage)
        throws DBException
    {
        if (storage == null) {
            data = null;
        } else {
            try {
                Reader reader = storage.getContentReader();
                try {
                    StringWriter sw = new StringWriter((int)storage.getContentLength());
                    ContentUtils.copyStreams(reader, storage.getContentLength(), sw, monitor);
                    data = sw.toString();
                }
                finally {
                    ContentUtils.close(reader);
                }
            }
            catch (IOException e) {
                throw new DBCException("IO error while reading content", e);
            }
        }
        this.modified = true;
        return false;
    }

    @Override
    public void resetContents()
    {
        this.data = this.originalData;
        this.modified = false;
    }

    @Override
    public String getCharset()
    {
        return DBValueFormatting.getDefaultBinaryFileEncoding(executionContext.getDataSource());
    }

    @Override
    public JDBCContentChars cloneStorage(DBRProgressMonitor monitor)
    {
        return cloneValue(monitor);
    }

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement preparedStatement,
                              DBSTypedObject columnType, int paramIndex)
        throws DBCException
    {
        try {
            if (data != null) {
                preparedStatement.setString(paramIndex, data);
            } else {
                preparedStatement.setNull(paramIndex, columnType.getTypeID());
            }
        }
        catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
    }

    @Override
    public Object getRawValue() {
        return data;
    }

    @Override
    public boolean isNull()
    {
        return data == null;
    }

    @Override
    public void release()
    {
        this.data = this.originalData;
    }

    @Override
    public boolean equals(Object obj)
    {
        return
            obj instanceof JDBCContentChars &&
            CommonUtils.equalObjects(data, ((JDBCContentChars) obj).data);
    }

    @Override
    public int hashCode() {
        return data == null ? 0 : data.hashCode();
    }

    @Override
    public String getDisplayString(DBDDisplayFormat format) {
        return data;
    }

    @Override
    public JDBCContentChars cloneValue(DBRProgressMonitor monitor)
    {
        return new JDBCContentChars(this);
    }

    @Override
    public Object getCachedValue()
    {
        return data;
    }

}
