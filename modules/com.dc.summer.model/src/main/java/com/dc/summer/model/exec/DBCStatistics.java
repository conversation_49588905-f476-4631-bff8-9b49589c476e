
package com.dc.summer.model.exec;

import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

import java.util.*;

/**
 * Execution statistics
 */
public class DBCStatistics implements DBCExecutionResult {

    private final long startTime;
    private long rowsUpdated = -1;
    private long rowsFetched = -1;
    private long executeTime;
    private long fetchTime;
    private int statementsCount;
    private String queryText;
    private Map<String, Object> infoMap;
    private List<String> messages;
    private Throwable error;
    private List<Throwable> warnings;
    private boolean isExportLimit;

    public DBCStatistics() {
        this.startTime = System.currentTimeMillis();
    }

    public long getRowsUpdated() {
        return rowsUpdated;
    }

    public void setRowsUpdated(long rowsUpdated) {
        this.rowsUpdated = rowsUpdated;
    }

    public void addRowsUpdated(long rowsUpdated) {
        if (rowsUpdated < 0) {
            return;
        }
        if (this.rowsUpdated == -1) {
            this.rowsUpdated = 0;
        }
        this.rowsUpdated += rowsUpdated;
    }

    public long getRowsFetched() {
        return rowsFetched;
    }

    public void setRowsFetched(long rowsFetched) {
        this.rowsFetched = rowsFetched;
    }

    public long getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(long executeTime) {
        this.executeTime = executeTime;
    }

    public void addExecuteTime(long executeTime) {
        this.executeTime += executeTime;
    }

    public void addExecuteTime() {
        this.executeTime += (System.currentTimeMillis() - startTime);
    }

    public long getFetchTime() {
        return fetchTime;
    }

    public void setFetchTime(long fetchTime) {
        this.fetchTime = fetchTime;
    }

    public void addFetchTime(long fetchTime) {
        this.fetchTime += fetchTime;
    }

    public long getTotalTime() {
        return executeTime + fetchTime;
    }

    public long getEndTime() {
        return startTime + getTotalTime();
    }

    public int getStatementsCount() {
        return statementsCount;
    }

    public void setStatementsCount(int statementsCount) {
        this.statementsCount = statementsCount;
    }

    public void addStatementsCount() {
        this.statementsCount++;
    }


    public String getQueryText() {
        return queryText;
    }

    public void setQueryText(String queryText) {
        this.queryText = queryText;
    }

    public List<String> getMessages() {
        return messages;
    }

    public boolean isExportLimit() {
        return isExportLimit;
    }

    public void setExportLimit(boolean exportLimit) {
        isExportLimit = exportLimit;
    }

    public void addMessage(String message) {
        if (messages == null) {
            messages = new ArrayList<>();
        }
        messages.add(message);
    }

    public Map<String, Object> getInfo() {
        if (infoMap == null) {
            return Collections.emptyMap();
        }
        return infoMap;
    }

    public void addInfo(String name, Object value) {
        if (infoMap == null) {
            infoMap = new LinkedHashMap<>();
        }
        infoMap.put(name, value);
    }

    public boolean isEmpty() {
        return executeTime <= 0 && fetchTime <= 0 && statementsCount == 0;
    }

    public void accumulate(@Nullable DBCStatistics stat) {
        if (stat == null) {
            return;
        }
        if (stat.rowsUpdated >= 0) {
            if (rowsUpdated < 0) rowsUpdated = 0;
            rowsUpdated += stat.rowsUpdated;
        }
        if (stat.rowsFetched > 0) {
            if (rowsFetched < 0) rowsFetched = 0;
            rowsFetched += stat.rowsFetched;
        }
        executeTime += stat.executeTime;
        fetchTime += stat.fetchTime;
        statementsCount += stat.statementsCount;
        if (!CommonUtils.isEmpty(stat.messages)) {
            for (String message : stat.messages) {
                addMessage(message);
            }
        }
        if (!CommonUtils.isEmpty(stat.infoMap)) {
            for (Map.Entry<String, Object> info : stat.infoMap.entrySet()) {
                addInfo(info.getKey(), info.getValue());
            }
        }
        this.setExportLimit(stat.isExportLimit);
    }

    public void reset() {
        rowsUpdated = -1;
        rowsFetched = -1;
        executeTime = 0;
        fetchTime = 0;
        statementsCount = 0;
        messages = null;
        infoMap = null;
    }

    @Nullable
    @Override
    public Throwable getError() {
        return error;
    }

    public void setError(Throwable error) {
        this.error = error;
    }

    @Nullable
    @Override
    public List<Throwable> getWarnings() {
        return warnings;
    }

    public void addWarning(Throwable warning) {
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        warnings.add(warning);
    }
}