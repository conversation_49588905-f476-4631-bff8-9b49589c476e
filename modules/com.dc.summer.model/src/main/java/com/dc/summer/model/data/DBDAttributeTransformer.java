

package com.dc.summer.model.data;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;
import java.util.Map;

/**
 * DBD binding transformer.
 * Transforms attribute to another attribute
 */
public interface DBDAttributeTransformer
{
    /**
     * Transforms attribute
     */
    void transformAttribute(
        @NotNull DBCSession session,
        @NotNull DBDAttributeBinding attribute,
        @NotNull List<Object[]> rows,
        @NotNull Map<String, Object> options)
        throws DBException;

}
