

package com.dc.summer.model.connection;

import com.dc.summer.model.messages.ModelMessages;

/**
* Connection event type
*/
public enum DBPConnectionEventType {
    BEFORE_CONNECT(ModelMessages.model_connection_events_event_before_connect),
    AFTER_CONNECT(ModelMessages.model_connection_events_event_after_connect),
    BEFORE_DISCONNECT(ModelMessages.model_connection_events_event_before_disconnect),
    AFTER_DISCONNECT(ModelMessages.model_connection_events_event_after_disconnect);

    private final String title;

    DBPConnectionEventType(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
}
