
package com.dc.summer.model.runtime;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.summer.utils.GeneralUtils;

import java.lang.reflect.InvocationTargetException;

/**
 * Abstract Database Job
 */
public class SystemJob extends AbstractJob
{
    private final DBRRunnableWithProgress runnable;
    public SystemJob(String name, DBRRunnableWithProgress runnable) {
        super(name);
        setSystem(true);
        setUser(false);
        this.runnable = runnable;
    }

    @Override
    protected IStatus run(DBRProgressMonitor monitor) {
        try {
            runnable.run(monitor);
        } catch (InvocationTargetException e) {
            return GeneralUtils.makeExceptionStatus(e.getTargetException());
        } catch (InterruptedException e) {
            return Status.CANCEL_STATUS;
        }
        return Status.OK_STATUS;
    }

}