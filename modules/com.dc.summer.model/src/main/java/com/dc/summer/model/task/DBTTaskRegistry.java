
package com.dc.summer.model.task;

import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * Task registry
 */
public interface DBTTaskRegistry {

    String EVENT_TASK_EXECUTE = "taskExecuted";
    String EVENT_PARAM_PROJECT = "project";
    String EVENT_PARAM_TASK = "taskId";
    String EVENT_BEFORE_PROJECT_DELETE = "beforeProjectDelete";

    @NotNull
    DBTTaskType[] getAllTaskTypes();

    @Nullable
    DBTTaskType getTaskType(String id);

    @NotNull
    DBTTaskCategory[] getAllCategories();

    @NotNull
    DBTTaskCategory[] getRootCategories();

    @NotNull
    DBTSchedulerDescriptor[] getAllSchedulers();

    DBTSchedulerDescriptor getActiveScheduler();

    void addTaskListener(DBTTaskListener listener);

    void removeTaskListener(DBTTaskListener listener);

}
