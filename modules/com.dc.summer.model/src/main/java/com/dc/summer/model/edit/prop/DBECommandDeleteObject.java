

package com.dc.summer.model.edit.prop;

import com.dc.summer.model.edit.DBECommand;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.DBPObject;

import java.util.Map;

/**
 * Delete object command
 */
public abstract class DBECommandDeleteObject<OBJECT_TYPE extends DBPObject> extends DB<PERSON>ommandAbstract<OBJECT_TYPE> {

    //public static final String PROP_COMPOSITE_COMMAND = ".composite";

    public DBECommandDeleteObject(OBJECT_TYPE object, String title)
    {
        super(object, title);
    }

    @Override
    public DBECommand<?> merge(DBECommand<?> prevCommand, Map<Object, Object> userParams)
    {
        if (prevCommand != null && prevCommand.getObject() == getObject()) {
            return null;
        }
        return this;
    }

}