
package com.dc.summer.model.impl.jdbc.struct;

import com.dc.summer.model.*;
import com.dc.code.Nullable;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.struct.AbstractAttribute;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSObject;

/**
 * JDBC abstract column
 */
public abstract class JDBCAttribute extends AbstractAttribute implements DBSObject {

    protected JDBCAttribute()
    {
    }

    protected JDBCAttribute(String name, String typeName, int valueType, int ordinalPosition, long maxLength, Integer scale,
                            Integer precision, boolean required, boolean sequence)
    {
        super(name, typeName, valueType, ordinalPosition, maxLength, scale, precision, required, sequence);
    }

    // Copy constructor
    protected JDBCAttribute(DBSAttributeBase source)
    {
        super(source);
    }

    @Nullable
    protected JDBCColumnKeyType getKeyType()
    {
        return null;
    }

    @Override
    public DBPDataKind getDataKind()
    {
        return JDBCUtils.resolveDataKind(getDataSource(), typeName, valueType);
    }

}
