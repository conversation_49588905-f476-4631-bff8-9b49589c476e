
package com.dc.summer.model.impl.struct;

import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.summer.model.struct.rdb.DBSTableIndex;
import com.dc.code.NotNull;

/**
 * AbstractTableIndex
 */
public abstract class AbstractTableIndex implements DBSTableIndex
{
    @Override
    public boolean isPersisted()
    {
        return true;
    }

    @NotNull
    @Override
    public DBSEntityConstraintType getConstraintType()
    {
        return DBSEntityConstraintType.INDEX;
    }

    @Override
    public boolean isPrimary() {
        return false;
    }
}
