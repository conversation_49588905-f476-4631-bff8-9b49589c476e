
package com.dc.summer.model.connection;

/**
 * Auth info
 */
public class DBPAuthInfo {
    private String userName;
    private String userPassword;
    private boolean savePassword;

    public DBPAuthInfo() {
    }

    public DBPAuthInfo(String userName, String userPassword, boolean savePassword) {
        this.userName = userName;
        this.userPassword = userPassword;
        this.savePassword = savePassword;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public boolean isSavePassword() {
        return savePassword;
    }

    public void setSavePassword(boolean savePassword) {
        this.savePassword = savePassword;
    }
}
