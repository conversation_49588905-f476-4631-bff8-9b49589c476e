

package com.dc.summer.model.connection;

import com.dc.utils.CommonUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Configuration profile.
 */
public class DBPConfigurationProfile {

    private String profileId;
    private String profileName;
    private String profileDescription;

    // Properties. Basically JSON
    private Map<String, String> properties = new LinkedHashMap<>();

    public DBPConfigurationProfile() {
    }

    public DBPConfigurationProfile(DBPConfigurationProfile source) {
        this.profileId = source.profileId;
        this.profileName = source.profileName;
        this.profileDescription = source.profileDescription;
        if (!CommonUtils.isEmpty(source.properties)) {
            this.properties = new LinkedHashMap<>(source.properties);
        }
    }

    public String getProfileId() {
        if (CommonUtils.isEmpty(profileId)) {
            return profileName;
        }
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    public String getProfileDescription() {
        return profileDescription;
    }

    public void setProfileDescription(String profileDescription) {
        this.profileDescription = profileDescription;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    @Override
    public String toString() {
        return profileName;
    }

}
