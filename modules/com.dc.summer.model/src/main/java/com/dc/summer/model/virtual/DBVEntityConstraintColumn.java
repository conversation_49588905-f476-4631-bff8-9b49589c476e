
package com.dc.summer.model.virtual;

import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.code.NotNull;
import com.dc.summer.model.struct.DBSEntityAttributeRef;

/**
 * Constraint column
 */
public class DBVEntityConstraintColumn implements DBSEntityAttributeRef {

    private final DBVEntityConstraint constraint;
    private final String attributeName;

    public DBVEntityConstraintColumn(DBVEntityConstraint constraint, String attributeName)
    {
        this.constraint = constraint;
        this.attributeName = attributeName;
    }

    public DBVEntityConstraintColumn(DBVEntityConstraint constraint, DBVEntityConstraintColumn copy) {
        this.constraint = constraint;
        this.attributeName = copy.attributeName;
    }

    @NotNull
    @Override
    public DBSEntityAttribute getAttribute()
    {
        // Here we use void monitor.
        // In real life entity columns SHOULD be already read so it doesn't matter
        // But I'm afraid that in some very special cases it does. Thant's too bad.
        return constraint.getEntity().getAttribute(new VoidProgressMonitor(), attributeName);
    }

    public String getAttributeName()
    {
        return attributeName;
    }
}
