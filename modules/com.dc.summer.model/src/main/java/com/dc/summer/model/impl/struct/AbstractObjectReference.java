
package com.dc.summer.model.impl.struct;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.code.NotNull;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectReference;
import com.dc.summer.model.struct.DBSObjectType;

/**
 * Abstract object reference
 */
public abstract class AbstractObjectReference<CONTAINER extends DBSObject> implements DBSObjectReference {

    private final String name;
    private final CONTAINER container;
    private final String description;
    private final Class<?> objectClass;
    private final DBSObjectType type;
    private final String extraInfo;

    protected AbstractObjectReference(String name, CONTAINER container, String description, Class<?> objectClass, DBSObjectType type) {
        this(name, container, description, objectClass, type, null);
    }

    protected AbstractObjectReference(String name, CONTAINER container, String description,
                                      Class<?> objectClass, DBSObjectType type, String extraInfo) {
        this.name = name;
        this.container = container;
        this.description = description;
        this.objectClass = objectClass;
        this.type = type;
        this.extraInfo = extraInfo;
    }

    @NotNull
    @Override
    public String getName()
    {
        return name;
    }

    public CONTAINER getContainer() {
        return container;
    }

    @Override
    public Class<?> getObjectClass() {
        return objectClass;
    }

    @Override
    public String getObjectDescription()
    {
        return description;
    }

    @Override
    public DBSObjectType getObjectType()
    {
        return type;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context)
    {
        if (extraInfo != null) {
            return extraInfo;
        }
        String fqName;
        DBPDataSource dataSource = container.getDataSource();
        if (container == dataSource) {
            // In case if there are no schemas/catalogs supported
            // and data source is a root container
            fqName = DBUtils.getQuotedIdentifier(dataSource, name);
        } else {
            fqName = DBUtils.getFullQualifiedName(dataSource, container, this);
        }
        return fqName;
    }

    @Override
    public String toString() {
        return getFullyQualifiedName(DBPEvaluationContext.UI);
    }
}
