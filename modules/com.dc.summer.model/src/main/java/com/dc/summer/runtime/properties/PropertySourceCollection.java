
package com.dc.summer.runtime.properties;

import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Simple property source which store properties in map
 */
public class PropertySourceCollection implements DBPPropertySource {

    private List<DBPPropertyDescriptor> props = new ArrayList<>();

    private List<Object> items;

    public PropertySourceCollection(Collection<?> collection)
    {
        items = new ArrayList<>(collection);
        for (int i = 0; i < items.size(); i++) {
            //props.addAll(ObjectPropertyDescriptor.extractAnnotations(this, item.getClass(), null));
            props.add(new ItemPropertyDescriptor(String.valueOf(i), items.get(i)));
        }
    }

    @Override
    public Object getEditableValue()
    {
        return this;
    }

    @Override
    public DBPPropertyDescriptor[] getProperties() {
        return props.toArray(new DBPPropertyDescriptor[0]);
    }

    @Override
    public Object getPropertyValue(@Nullable DBRProgressMonitor monitor, String id)
    {
        return items.get(CommonUtils.toInt(id));
    }

    @Override
    public boolean isPropertySet(String id)
    {
        return false;
    }

    @Override
    public boolean isPropertyResettable(String id) {
        return false;
    }

    @Override
    public void resetPropertyValue(@Nullable DBRProgressMonitor monitor, String id)
    {

    }

    @Override
    public void resetPropertyValueToDefault(String id) {

    }

    @Override
    public void setPropertyValue(@Nullable DBRProgressMonitor monitor, String id, Object value)
    {
    }

    @Override
    public String toString() {
        return "[...]";
    }

    private static class ItemPropertyDescriptor implements DBPPropertyDescriptor {
        private final String id;
        private final Object item;

        private ItemPropertyDescriptor(String  id, Object item) {
            this.id = id;
            this.item = item;
        }

        @Override
        public String getCategory() {
            return null;
        }

        @Override
        public String getDescription() {
            return null;
        }

        @Override
        public Class<?> getDataType() {
            return Object.class;
        }

        @Override
        public boolean isRequired() {
            return false;
        }

        @Override
        public Object getDefaultValue() {
            return null;
        }

        @Override
        public boolean isEditable(Object object) {
            return false;
        }

        @NotNull
        @Override
        public PropertyLength getLength() {
            return PropertyLength.LONG;
        }

        @Nullable
        @Override
        public String[] getFeatures() {
            return null;
        }

        @Override
        public boolean hasFeature(@NotNull String feature) {
            return false;
        }

        @NotNull
        @Override
        public String getDisplayName() {
            return DBUtils.getObjectShortName(item);
        }

        @NotNull
        @Override
        public String getId() {
            return id;
        }
    }
}
