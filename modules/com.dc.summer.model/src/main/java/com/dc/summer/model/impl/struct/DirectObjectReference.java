
package com.dc.summer.model.impl.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.summer.model.struct.DBSObject;

/**
 * Direct object reference
 */
public class DirectObjectReference extends AbstractObjectReference<DBSObject> {

    private final DBSObject object;

    public DirectObjectReference(DBSObject container, DBSObjectType type, DBSObject object) {
        super(object.getName(), container, object.getDescription(), object.getClass(), type);
        this.object = object;
    }

    @Override
    public DBSObject resolveObject(DBRProgressMonitor monitor) throws DBException {
        return object;
    }
}
