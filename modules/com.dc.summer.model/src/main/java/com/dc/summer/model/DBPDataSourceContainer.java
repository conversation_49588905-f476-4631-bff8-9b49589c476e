
package com.dc.summer.model;

import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.sql.SQLDialectMetadata;
import com.dc.summer.model.struct.DBSObjectFilter;
import com.dc.summer.model.virtual.DBVModel;
import org.eclipse.equinox.security.storage.ISecurePreferences;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.app.DBPDataSourceRegistry;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.connection.DBPNativeClientLocation;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.navigator.DBNBrowseSettings;
import com.dc.summer.model.net.DBWNetworkHandler;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.runtime.IVariableResolver;

import java.util.Collection;
import java.util.Date;
import java.util.function.BiPredicate;

/**
 * DBPDataSourceContainer
 */
public interface DBPDataSourceContainer extends DBSObject, DBDFormatSettings, DBPNamedObject2, DBPDataSourcePermissionOwner
{
    /**
     * Container unique ID
     * @return id
     */
    @NotNull
    String getId();

    /**
     * Associated driver
     * @return driver descriptor reference
     */
    @NotNull
    DBPDriver getDriver();

    @NotNull
    DBPDataSourceConfigurationStorage getConfigurationStorage();

    @NotNull
    DBPDataSourceOrigin getOrigin();

    /**
     * Connection configuration.
     * @return connection details
     */
    @NotNull
    DBPConnectionConfiguration getConnectionConfiguration();

    /**
     * Actual connection configuration. Contains actual parameters used to connect to this datasource.
     * Differs from getConnectionConfiguration() in case if tunnel or proxy was used.
     * @return actual connection configuration.
     */
    @NotNull
    DBPConnectionConfiguration getActualConnectionConfiguration();

    @NotNull
    DBNBrowseSettings getNavigatorSettings();

    boolean isProvided();

    /*
     * Returns true if datasource can be managed (edited or deleted).
     * Datasource is manageable if it belongs to its owner registry.
     */
    boolean isManageable();

    boolean isAccessCheckRequired();

    /**
     * @return true if datasource is provided by some dynamic DS provider. E.g. cloud configuration.
     */
    boolean isExternallyProvided();

    boolean isTemplate();

    boolean isTemporary();

    // We do not implement DBPHiddenObject because it is not really hidden.
    // This flag means that datasource shouldn't be included in the primary connection list.
    // Also hidden connections are excluded from persistence
    boolean isHidden();

    boolean isConnectionReadOnly();

    boolean isSavePassword();

    void setSavePassword(boolean savePassword);

    void setDescription(String description);

    boolean isDefaultAutoCommit();

    void setDefaultAutoCommit(boolean autoCommit);

    boolean isAutoCloseTransactions();

    @Nullable
    DBPTransactionIsolation getActiveTransactionsIsolation();

    @Nullable
    Integer getDefaultTransactionsIsolation();

    void setDefaultTransactionsIsolation(DBPTransactionIsolation isolationLevel);

    void setLocalTransactional(BiPredicate<DBRProgressMonitor, DBCExecutionContext> runLocalTransactional);

    Boolean getLocalTransactional(DBRProgressMonitor dbrProgressMonitor, DBCExecutionContext dbcExecutionContext);

    /**
     * Search for object filter which corresponds specified object type and parent object.
     * Search filter which match any super class or interface implemented by specified type.
     * @param type object type
     * @param parentObject parent object (in DBS objects hierarchy)
     * @return object filter or null if not filter was set for specified type
     */
    @Nullable
    DBSObjectFilter getObjectFilter(Class<?> type, @Nullable DBSObject parentObject, boolean firstMatch);

    void setObjectFilter(Class<?> type, DBSObject parentObject, DBSObjectFilter filter);

    DBVModel getVirtualModel();

    DBPNativeClientLocation getClientHome();

    DBWNetworkHandler[] getActiveNetworkHandlers();

    /**
     * Checks this data source is connected.
     * Do not check whether underlying connection is alive or not.
     */
    boolean isConnected();

    /**
     * Connects to datasource.
     * This is sync method and returns after actual connection establishment.
     * @param monitor progress monitor
     * @param initialize initialize datasource after connect (call DBPDataSource.initialize)
     * @param reflect notify UI about connection state change
     * @throws DBException on error
     */
    boolean connect(DBRProgressMonitor monitor, boolean initialize, boolean reflect) throws DBException;

    /**
     * Disconnects from datasource.
     * This is async method and returns immediately.
     * Connection will be closed in separate job, so no progress monitor is required.
     * @param monitor progress monitor
     * @throws DBException on error
     * @return true on disconnect, false if disconnect action was canceled
     */
    boolean disconnect(DBRProgressMonitor monitor) throws DBException;

    /**
     * Reconnects datasource.
     * @param monitor progress monitor
     * @return true on reconnect, false if reconnect action was canceled
     * @throws DBException on any DB error
     */
    boolean reconnect(DBRProgressMonitor monitor) throws DBException;

    @Nullable
    DBPDataSource getDataSource();

    @Nullable
    DBPDataSourceFolder getFolder();

    void setFolder(@Nullable DBPDataSourceFolder folder);

    Collection<DBPDataSourceTask> getTasks();

    void acquire(DBPDataSourceTask user);

    void release(DBPDataSourceTask user);

    void fireEvent(DBPEvent event);

    /**
     * Preference store associated with this datasource
     * @return preference store
     */
    @NotNull
    DBPPreferenceStore getPreferenceStore();

    @NotNull
    DBPDataSourceRegistry getRegistry();

    @NotNull
    DBPProject getProject();

    /**
     * @return false on any error. Actual error can be read in registry.
     */
    boolean persistConfiguration();

    @NotNull
    ISecurePreferences getSecurePreferences();

    Date getConnectTime();

    @NotNull
    SQLDialectMetadata getScriptDialect();

    /**
     * Make variable resolver for datasource properties.
     * @param actualConfig if true then actual connection config will be used (e.g. with preprocessed host/port values).
     */
    IVariableResolver getVariablesResolver(boolean actualConfig);

    DBPDataSourceContainer createCopy(DBPDataSourceRegistry forRegistry);

    DBPExclusiveResource getExclusiveLock();
    
    boolean isForceUseSingleConnection();
    
    void setForceUseSingleConnection(boolean value);
}
