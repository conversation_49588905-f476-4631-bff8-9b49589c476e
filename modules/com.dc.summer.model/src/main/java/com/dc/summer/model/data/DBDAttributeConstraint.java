

package com.dc.summer.model.data;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.utils.CommonUtils;

import java.util.Arrays;

/**
 * Attribute constraint
 */
public class DBDAttributeConstraint extends DBDAttributeConstraintBase {

//    public static final String FEATURE_HIDDEN = "hidden";

    @Nullable
    private DBSAttributeBase attribute;
    private String attributeLabel;
    private String attributeName;
    private int originalVisualPosition;
    private boolean plainNameReference; // Disables ordering by column index

    public DBDAttributeConstraint(@NotNull DBDAttributeBinding attribute) {
        setAttribute(attribute);
        setVisualPosition(attribute.getOrdinalPosition());
    }

    public DBDAttributeConstraint(@NotNull DBDAttributeBinding attribute, int visualPosition, int originalVisualPosition) {
        setAttribute(attribute);
        setVisualPosition(visualPosition);
        this.originalVisualPosition = originalVisualPosition; // Can be very important for nested attributes without through ordering
    }

    public DBDAttributeConstraint(@NotNull DBSAttributeBase attribute, int visualPosition) {
        setAttribute(attribute);
        setVisualPosition(visualPosition);
    }

    public DBDAttributeConstraint(@NotNull String attributeName, int originalVisualPosition) {
        this.attribute = null;
        this.attributeName = attributeName;
        this.attributeLabel = attributeName;
        this.originalVisualPosition = originalVisualPosition;
    }

    public DBDAttributeConstraint(@NotNull DBDAttributeConstraint source) {
        super(source);
        this.attribute = source.attribute;
        this.attributeName = source.attributeName;
        this.attributeLabel = source.attributeLabel;
        this.originalVisualPosition = source.originalVisualPosition;
    }

    public static boolean isVisibleByDefault(DBDAttributeBinding binding) {
        return !binding.isPseudoAttribute();
    }

    @Nullable
    public DBSAttributeBase getAttribute() {
        return attribute;
    }

    void setAttribute(@NotNull DBSAttributeBase binding) {
        this.attribute = binding;
        this.attributeName = this.attribute.getName();
        if (this.attribute instanceof DBDAttributeBindingMeta) {
            DBSEntityAttribute entityAttribute = ((DBDAttributeBindingMeta) this.attribute).getEntityAttribute();
            if (entityAttribute != null) {
                this.attributeName = entityAttribute.getName();
            } else {
                this.attributeName = this.attribute.getName();
            }
            this.attributeLabel = ((DBDAttributeBindingMeta) this.attribute).getLabel();
            if (CommonUtils.isEmpty(this.attributeLabel)) {
                this.attributeLabel = this.attributeName;
            }
        }
        this.originalVisualPosition = attribute.getOrdinalPosition();
    }

    @NotNull
    public String getAttributeName() {
        return attributeName;
    }

    @NotNull
    public String getAttributeLabel() {
        return attributeLabel;
    }

    @NotNull
    public String getFullAttributeName() {
        return attribute == null ? attributeName : DBUtils.getObjectFullName(attribute, DBPEvaluationContext.DML);
    }

    public int getOriginalVisualPosition() {
        return originalVisualPosition;
    }

    // Disables ordering by column index
    public boolean isPlainNameReference() {
        return plainNameReference;
    }

    public void setPlainNameReference(boolean plainNameReference) {
        this.plainNameReference = plainNameReference;
    }

    @Override
    public boolean hasFilter() {
        return super.hasFilter() || // compare visual position only if it explicitly set
            (getVisualPosition() != NULL_VISUAL_POSITION && originalVisualPosition != getVisualPosition());
    }

    public void reset() {
        super.reset();
        setVisualPosition(originalVisualPosition);
    }

    public boolean equalFilters(DBDAttributeConstraintBase obj, boolean compareOrders) {
        return
            obj instanceof DBDAttributeConstraint &&
                CommonUtils.equalObjects(this.attribute, ((DBDAttributeConstraint) obj).attribute) &&
                super.equalFilters(obj, compareOrders);
    }

    @Override
    public int hashCode() {
        return this.attributeName.hashCode() + getVisualPosition();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof DBDAttributeConstraint) {
            DBDAttributeConstraint source = (DBDAttributeConstraint) obj;
            return
                CommonUtils.equalObjects(this.attribute, source.attribute) &&
                    super.equals(obj);
        } else {
            return false;
        }
    }

    @Override
    public String toString() {
        String clause = getOperator() == null ?
            (getCriteria() == null ? "" : getCriteria()) :
            (isReverseOperator() ? "NOT " : "") + getOperator().getExpression() + " " + getValue();
        return attributeName + " " + clause;
    }

    public boolean matches(DBSAttributeBase attr, boolean matchByName) {
        return attribute == attr ||
            (attribute instanceof DBDAttributeBinding && ((DBDAttributeBinding) attribute).matches(attr, matchByName));
    }

    public boolean equalVisibility(DBDAttributeConstraint constraint) {
        return isVisible() == constraint.isVisible() && getVisualPosition() == constraint.getVisualPosition() &&
            Arrays.equals(getOptions(), constraint.getOptions());
    }

}
