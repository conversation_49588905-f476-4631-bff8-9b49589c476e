package com.dc.summer.registry.statement;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.ArrayList;
import java.util.List;

public class BindStatementProviderRegistry {

    public static final String EXTENSION_ID = "com.dc.summer.bindingStatement"; //$NON-NLS-1$

    public static BindStatementProviderRegistry instance = null;

    private final List<BindStatementProviderDescriptor> bindings = new ArrayList<>();


    public synchronized static BindStatementProviderRegistry getInstance() {
        if (instance == null) {
            instance = new BindStatementProviderRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private BindStatementProviderRegistry(IExtensionRegistry registry) {
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement element : extElements) {
            if (element.getName().equals("binding")) {
                this.bindings.add(new BindStatementProviderDescriptor(element));
            }
        }
    }

    public List<BindStatementProviderDescriptor> getBindings() {
        return new ArrayList<>(bindings);
    }

    public BindStatementProviderDescriptor getBinding(String id) {
        for (BindStatementProviderDescriptor poolDescriptor : bindings) {
            if (poolDescriptor.getId().equalsIgnoreCase(id)) {
                return poolDescriptor;
            }
        }
        return null;
    }


}
