
package com.dc.summer.model.data.aggregate;

/**
 * FunctionSum
 */
public class FunctionMin implements IAggregateFunction {

    Comparable<?> result = null;

    @Override
    public boolean accumulate(Object value, boolean aggregateAsStrings) {
        value = FunctionNumeric.getComparable(value, aggregateAsStrings);
        if (value != null) {
            if (result == null || AggregateUtils.compareValues((Comparable<?>) value, result) < 0) {
                result = (Comparable<?>) value;
            }
            return true;
        }
        return false;
    }

    @Override
    public Object getResult(int valueCount) {
        return result;
    }
}
