

package com.dc.summer.model.impl.jdbc;

import java.sql.Array;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Map;

/**
 * JDBCArrayImpl
 */
public class JDBCArrayImpl implements Array {

    private final String typeName;
    private final int baseType;
    private final Object[] items;

    public JDBCArrayImpl(String typeName, int baseType, Object[] items) {
        this.typeName = typeName;
        this.baseType = baseType;
        this.items = items;
    }

    @Override
    public String getBaseTypeName() throws SQLException {
        return typeName;
    }

    @Override
    public int getBaseType() throws SQLException {
        return baseType;
    }

    @Override
    public Object getArray() throws SQLException {
        return items;
    }

    @Override
    public Object getArray(Map<String, Class<?>> map) throws SQLException {
        return items;
    }

    @Override
    public Object getArray(long index, int count) throws SQLException {
        return Arrays.copyOfRange(items, (int) index, count);
    }

    @Override
    public Object getArray(long index, int count, Map<String, Class<?>> map) throws SQLException {
        return Arrays.copyOfRange(items, (int) index, count);
    }

    @Override
    public ResultSet getResultSet() throws SQLException {
        return null;
    }

    @Override
    public ResultSet getResultSet(Map<String, Class<?>> map) throws SQLException {
        return null;
    }

    @Override
    public ResultSet getResultSet(long index, int count) throws SQLException {
        return null;
    }

    @Override
    public ResultSet getResultSet(long index, int count, Map<String, Class<?>> map) throws SQLException {
        return null;
    }

    @Override
    public void free() throws SQLException {

    }
}
