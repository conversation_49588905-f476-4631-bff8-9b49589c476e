
package com.dc.summer.model.impl.sql.edit.struct;

import com.dc.summer.model.*;
import com.dc.summer.model.struct.*;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectWithDependencies;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.edit.prop.DBECommandComposite;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.summer.model.struct.rdb.DBSTableColumn;
import com.dc.summer.model.struct.rdb.DBSTableIndex;
import com.dc.summer.model.struct.rdb.DBSTableIndexColumn;
import com.dc.utils.CommonUtils;

import java.util.*;

/**
 * Table column manager. Fits for all composite entities including NoSQL.
 */
public abstract class SQLTableColumnManager<OBJECT_TYPE extends DBSEntityAttribute, TABLE_TYPE extends DBSEntity>
    extends SQLObjectEditor<OBJECT_TYPE, TABLE_TYPE> implements DBEObjectWithDependencies
{
    public static final long DDL_FEATURE_OMIT_COLUMN_CLAUSE_IN_DROP = 1;
    public static final long DDL_FEATURE_USER_BRACKETS_IN_DROP = 2;
    public static final long FEATURE_ALTER_TABLE_ADD_COLUMN = 4;

    public static final String QUOTE = "'";

    protected interface ColumnModifier<OBJECT_TYPE extends DBPObject> {
        void appendModifier(DBRProgressMonitor monitor, OBJECT_TYPE column, StringBuilder sql, DBECommandAbstract<OBJECT_TYPE> command);
    }

    protected final ColumnModifier<OBJECT_TYPE> DataTypeModifier = (monitor, column, sql, command) -> {
        final String typeName = column.getTypeName();
        DBPDataKind dataKind = column.getDataKind();
        final DBSDataType dataType = findDataType(column, typeName);
        sql.append(' ').append(typeName);
        if (dataType == null) {
            log.debug("Type name '" + typeName + "' is not supported by driver"); //$NON-NLS-1$ //$NON-NLS-2$
        } else {
            dataKind = dataType.getDataKind();
        }
        String modifiers = SQLUtils.getColumnTypeModifiers(column.getDataSource(), column, typeName, dataKind);
        if (modifiers != null) {
            sql.append(modifiers);
        }
    };

    protected final ColumnModifier<OBJECT_TYPE> NotNullModifier = (monitor, column, sql, command) -> {
        if (column.isRequired()) {
            sql.append(" NOT NULL"); //$NON-NLS-1$
        }
    };

    protected final ColumnModifier<OBJECT_TYPE> NullNotNullModifier = (monitor, column, sql, command) ->
        sql.append(column.isRequired() ? " NOT NULL" : " NULL");

    protected final ColumnModifier<OBJECT_TYPE> NullNotNullModifierConditional = (monitor, column, sql, command) -> {
        if (command instanceof DBECommandComposite) {
            if (((DBECommandComposite) command).getProperty("required") == null) {
                // Do not set NULL/NOT NULL if it wasn't changed
                return;
            }
        }
        NullNotNullModifier.appendModifier(monitor, column, sql, command);
    };

    protected final ColumnModifier<OBJECT_TYPE> DefaultModifier = (monitor, column, sql, command) -> {
        String defaultValue = CommonUtils.toString(column.getDefaultValue());
        if (!CommonUtils.isEmpty(defaultValue)) {
            DBPDataKind dataKind = column.getDataKind();
            boolean useQuotes = false;//dataKind == DBPDataKind.STRING;
            if (!defaultValue.startsWith(QUOTE) && !defaultValue.endsWith(QUOTE)) {
                if (useQuotes && defaultValue.trim().startsWith(QUOTE)) {
                    useQuotes = false;
                }
                if (dataKind == DBPDataKind.DATETIME) {
                    final char firstChar = defaultValue.trim().charAt(0);
                    if (!Character.isLetter(firstChar) && firstChar != '(' && firstChar != '[') {
                        useQuotes = true;
                    }
                }
            }

            sql.append(" DEFAULT "); //$NON-NLS-1$
            if (useQuotes) sql.append(QUOTE);
            sql.append(defaultValue);
            if (useQuotes) sql.append(QUOTE);
        }
    };

    protected ColumnModifier[] getSupportedModifiers(OBJECT_TYPE column, Map<String, Object> options)
    {
        return new ColumnModifier[] {DataTypeModifier, NotNullModifier, DefaultModifier};
    }

    @Override
    public boolean canEditObject(OBJECT_TYPE object)
    {
        DBSEntity table = object.getParentObject();
        return table != null && !DBUtils.isView(table);
    }

    @Override
    public boolean canCreateObject(Object container)
    {
        return container instanceof DBSTable && !((DBSTable) container).isView();
    }

    @Override
    public boolean canDeleteObject(OBJECT_TYPE object)
    {
        return canEditObject(object);
    }

    @Override
    public long getMakerOptions(DBPDataSource dataSource)
    {
        return FEATURE_EDITOR_ON_CREATE;
    }

    protected long getDDLFeatures(OBJECT_TYPE object)
    {
        return 0;
    }

    private boolean hasDDLFeature(OBJECT_TYPE object, long feature)
    {
        return (getDDLFeatures(object) & feature) != 0;
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options)
    {
        final TABLE_TYPE table = (TABLE_TYPE) command.getObject().getParentObject();
        StringBuilder sql = new StringBuilder(256);
        sql.append("ALTER TABLE ").append(DBUtils.getObjectFullName(table, DBPEvaluationContext.DDL)).append(" ADD ");
        if (hasDDLFeature(command.getObject(), FEATURE_ALTER_TABLE_ADD_COLUMN)) {
            sql.append("COLUMN ");
        }
        sql.append(getNestedDeclaration(monitor, table, command, options));
        actions.add(
            new SQLDatabasePersistAction(
                ModelMessages.model_jdbc_create_new_table_column,
                sql.toString()) );
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options) throws DBException
    {
        boolean useBrackets = hasDDLFeature(command.getObject(), DDL_FEATURE_USER_BRACKETS_IN_DROP);
        StringBuilder ddl = new StringBuilder();
        ddl.append("ALTER TABLE ").append(DBUtils.getObjectFullName(command.getObject().getParentObject(), DBPEvaluationContext.DDL));
        ddl.append(" DROP ");
        if (useBrackets) ddl.append('(');
        if (!hasDDLFeature(command.getObject(), DDL_FEATURE_OMIT_COLUMN_CLAUSE_IN_DROP)) {
            ddl.append("COLUMN ");
        }
        ddl.append(DBUtils.getQuotedIdentifier(command.getObject()));
        if (useBrackets) ddl.append(')');
        actions.add(
            new SQLDatabasePersistAction( ModelMessages.model_jdbc_drop_table_column, ddl.toString())
        );
    }

    @NotNull
    protected String getNewColumnName(@NotNull DBRProgressMonitor monitor, @NotNull DBECommandContext context, @NotNull TABLE_TYPE table) {
        return DBUtils.makeNewObjectName(monitor, "Column{0}", table, DBSEntityAttribute.class, DBSEntity::getAttribute, context);
    }

    @Override
    protected StringBuilder getNestedDeclaration(DBRProgressMonitor monitor, TABLE_TYPE owner, DBECommandAbstract<OBJECT_TYPE> command, Map<String, Object> options)
    {
        OBJECT_TYPE column = command.getObject();

        // Create column
        String columnName = DBUtils.getQuotedIdentifier(column.getDataSource(), column.getName());

        if (command instanceof SQLObjectEditor.ObjectRenameCommand) {
            columnName = DBUtils.getQuotedIdentifier(column.getDataSource(), ((ObjectRenameCommand) command).getNewName());
        }

        StringBuilder decl = new StringBuilder(40);
        decl.append(columnName);
        for (ColumnModifier<OBJECT_TYPE> modifier : getSupportedModifiers(column, options)) {
            modifier.appendModifier(monitor, column, decl, command);
        }

        return decl;
    }

    @Override
    protected void validateObjectProperties(DBRProgressMonitor monitor, ObjectChangeCommand command, Map<String, Object> options)
        throws DBException
    {
        if (CommonUtils.isEmpty(command.getObject().getName())) {
            throw new DBException("Column name cannot be empty");
        }
        if (CommonUtils.isEmpty(command.getObject().getTypeName())) {
            throw new DBException("Column type name cannot be empty");
        }
    }

    private static DBSDataType findDataType(DBSObject object, String typeName)
    {
        DBPDataTypeProvider dataTypeProvider = DBUtils.getParentOfType(DBPDataTypeProvider.class, object);
        if (dataTypeProvider != null) {
            return dataTypeProvider.getLocalDataType(typeName);
        }
        return null;
    }

    protected static DBSDataType findBestDataType(DBSObject object, String ... typeNames)
    {
        DBPDataTypeProvider dataTypeProvider = DBUtils.getParentOfType(DBPDataTypeProvider.class, object);
        if (dataTypeProvider != null) {
            return DBUtils.findBestDataType(dataTypeProvider.getLocalDataTypes(), typeNames);
        }
        return null;
    }

    @Override
    public List<? extends DBSObject> getDependentObjectsList(DBRProgressMonitor monitor, DBSObject object) throws DBException {
        DBSObject dbsObject = object.getParentObject();
        Set<DBSObject> dependentObjectsList = new HashSet<>();
        if (dbsObject instanceof DBSEntity && object instanceof DBSEntityAttribute) {
            DBSEntity parentObject = (DBSEntity) dbsObject;

            Collection<? extends DBSEntityConstraint> constraints = parentObject.getConstraints(monitor);
            if (!CommonUtils.isEmpty(constraints)) {
                for (DBSEntityConstraint constraint : constraints) {
                    addDependentConstraints(monitor, (DBSEntityAttribute) object, dependentObjectsList, constraint);
                }
            }

            Collection<? extends DBSEntityAssociation> associations = parentObject.getAssociations(monitor);
            if (!CommonUtils.isEmpty(associations)) {
                for (DBSEntityAssociation association : associations) {
                    addDependentConstraints(monitor, (DBSEntityAttribute) object, dependentObjectsList, association);
                }
            }
        }

        if (dbsObject instanceof DBSTable) {
            Collection<? extends DBSTableIndex> indexes = ((DBSTable) dbsObject).getIndexes(monitor);
            if (!CommonUtils.isEmpty(indexes)) {
                for (DBSTableIndex index : indexes) {
                    List<? extends DBSTableIndexColumn> attributeReferences = index.getAttributeReferences(monitor);
                    if (!CommonUtils.isEmpty(attributeReferences)) {
                        for (DBSTableIndexColumn indexColumn : attributeReferences) {
                            DBSTableColumn tableColumn = indexColumn.getTableColumn();
                            if (tableColumn == object) {
                                dependentObjectsList.add(index);
                                break;
                            }
                        }
                    }
                }
            }

        }
        return new ArrayList<>(dependentObjectsList);
    }

    private void addDependentConstraints(DBRProgressMonitor monitor, DBSEntityAttribute object, Set<DBSObject> dependentObjectsList, DBSObject constraint) throws DBException {
        if (constraint instanceof DBSEntityReferrer) {
            List<? extends DBSEntityAttributeRef> attributeReferences = ((DBSEntityReferrer) constraint).getAttributeReferences(monitor);
            if (!CommonUtils.isEmpty(attributeReferences)) {
                for (DBSEntityAttributeRef attributeRef : attributeReferences) {
                    if (attributeRef.getAttribute() == object) {
                        dependentObjectsList.add(constraint);
                        break;
                    }
                }
            }
        }
    }

    public static void addColumnCommentAction(List<DBEPersistAction> actionList, DBSEntityAttribute column, DBSEntity table) {
        actionList.add(new SQLDatabasePersistAction(
            "Comment column",
            "COMMENT ON COLUMN " + DBUtils.getObjectFullName(table, DBPEvaluationContext.DDL) + "." + DBUtils.getQuotedIdentifier(column) +
                " IS " + SQLUtils.quoteString(column.getDataSource(), CommonUtils.notEmpty(column.getDescription()))));
    }
}

