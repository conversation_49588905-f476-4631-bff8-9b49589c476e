

package com.dc.summer.runtime.properties;

import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.Nullable;

/**
 * Property source which allows editing of multiple objects.
 */
public interface IPropertySourceMulti extends DBPPropertySource {

    boolean isPropertySet(Object object, ObjectPropertyDescriptor id);

    Object getPropertyValue(@Nullable DBRProgressMonitor monitor, Object object, ObjectPropertyDescriptor prop, boolean formatValue);

    boolean isPropertyResettable(Object object, ObjectPropertyDescriptor prop);

    void resetPropertyValue(@Nullable DBRProgressMonitor monitor, Object object, ObjectPropertyDescriptor prop);

    void setPropertyValue(@Nullable DBRProgressMonitor monitor, Object object, ObjectPropertyDescriptor prop, Object value)
        throws IllegalArgumentException;

}
