
package com.dc.summer.model.impl.jdbc.data.handlers;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.utils.GeneralUtils;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;

import java.sql.SQLException;
import java.util.UUID;

import static com.dc.summer.model.DBPDataKind.BINARY;
import static com.dc.summer.model.DBPDataKind.STRING;

/**
 * UUID type support
 */
public class JDBCUUIDValueHandler extends JDBCObjectValueHandler {

    public static final JDBCUUIDValueHandler INSTANCE = new JDBCUUIDValueHandler();

    @NotNull
    @Override
    public Class<UUID> getValueObjectType(@NotNull DBSTypedObject attribute) {
        return UUID.class;
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException
    {
        if (object == null) {
            return null;
        } else if (object instanceof UUID) {
            return object;
        } else if (object instanceof byte[]) {
            return GeneralUtils.getUUIDFromBytes((byte[]) object);
        } else {
            final String str = object.toString();
            if (str.isEmpty()) {
                return null;
            } else {
                return UUID.fromString(str);
            }
        }
    }

    @Override
    protected void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws DBCException, SQLException {
        if (value instanceof String && paramType.getDataKind() != STRING) {
            value = UUID.fromString((String) value);
        }
        if (value instanceof UUID) {
            switch (paramType.getDataKind()) {
                case BINARY:
                    value = GeneralUtils.getBytesFromUUID((UUID) value);
                    break;
                case STRING:
                    value = value.toString();
                    break;
            }
        }

        super.bindParameter(session, statement, paramType, paramIndex, value);
    }
}
