
package com.dc.summer.runtime.jobs;

import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.runtime.ui.UIServiceConnections;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * EndIdleTransactionsJob
 */
class EndIdleTransactionsJob extends DataSourceUpdaterJob {
    private static final Log log = Log.getLog(EndIdleTransactionsJob.class);

    private static final Set<String> activeDataSources = new HashSet<>();
    private static final Object CONFIRM_SYNC = new Object();

    private final DBPDataSource dataSource;
    private final Map<DBCExecutionContext, DBCTransactionManager> txnToEnd;

    EndIdleTransactionsJob(DBPDataSource dataSource, Map<DBCExecutionContext, DBCTransactionManager> txnToEnd) {
        super("Connection ping (" + dataSource.getContainer().getName() + ")");
        setUser(false);
        setSystem(true);
        this.dataSource = dataSource;
        this.txnToEnd = txnToEnd;
    }

    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @Override
    protected IStatus updateDataSource(DBRProgressMonitor monitor) {
        UIServiceConnections serviceConnections = DBWorkbench.getService(UIServiceConnections.class);
        if (serviceConnections != null) {
            synchronized (CONFIRM_SYNC) {
                if (!serviceConnections.confirmTransactionsClose(txnToEnd.keySet().toArray(new DBCExecutionContext[0]))) {
                    return Status.CANCEL_STATUS;
                }
            }
        }
        log.debug("End idle " + txnToEnd.size() + " transactions for " + dataSource.getContainer().getId());
        for (Map.Entry<DBCExecutionContext, DBCTransactionManager> tee : txnToEnd.entrySet()) {
            try (DBCSession session = tee.getKey().openSession(monitor, DBCExecutionPurpose.UTIL, "End idle transaction")) {
                try {
                    tee.getValue().rollback();
                } catch (DBCException e) {
                    log.error("Error ending idle transaction", e);
                }
            }
        }

        return Status.OK_STATUS;
    }
/*
    class EndTransactionConfirmationJob extends UIJob {

        public EndTransactionConfirmationJob() {
            super("Show end transaction confirmation for " + dataSource.getContainer().getName());
            setUser(false);
            setSystem(true);
        }

        @Override
        public IStatus runInUIThread(IProgressMonitor monitor) {

            return Status.OK_STATUS;
        }
    }

    class EndTransactionConfirmationDialog extends ConfirmationDialog {

    }
*/

}
