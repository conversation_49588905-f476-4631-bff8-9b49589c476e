

package com.dc.summer.model.fs;

import com.dc.summer.model.DBPObject;
import com.dc.summer.model.auth.SMSessionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;

/**
 * Virtual file system provider
 */
public interface DBFFileSystemProvider extends DBPObject {

    DBFVirtualFileSystem[] getAvailableFileSystems(
        @NotNull DBRProgressMonitor monitor,
        @NotNull SMSessionContext sessionContext);

}
