
package com.dc.summer.model.sql.parser;

import com.dc.code.NotNull;

import java.util.Deque;
import java.util.Set;

/**
 * A set of connection-specific dialect features which require special handling during SQL parsing
 */
public interface SQLTokenPredicateSet {
    /**
     * @return maximum statement prefix length handled by predicates
     */
    int getMaxPrefixLength();
    /**
     * @return maximum statement suffix length handled by predicates
     */
    int getMaxSuffixLength();

    /**
     * Checks for a presence of predicates matching given prefix and suffix
     * @param prefix
     * @param suffix
     * @return true if there are any corresponding conditions
     */
    boolean anyMatches(Deque<TokenEntry> prefix, Deque<TokenEntry> suffix);

    /**
     * @return root node of the tree containing all prefixes of all predicates
     */
    @NotNull
    TrieNode<TokenEntry, SQLTokenPredicate> getPrefixTreeRoot();

    /**
     * Searches for all the predicates matching given suffix in the text
     * @param suffix
     * @return set of successfully matched predicates
     */
    @NotNull
    Set<SQLTokenPredicate> matchSuffix(Deque<TokenEntry> suffix);
}
