

package com.dc.summer.registry.expressions;

import com.dc.summer.model.impl.AbstractContextDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;

/**
 * ExpressionNamespaceDescriptor
 */
public class ExpressionNamespaceDescriptor extends AbstractContextDescriptor {

    public static final String EXP_EXTENSION_ID = "com.dc.summer.expressions"; //$NON-NLS-1$

    private final String id;
    private final String description;
    private final ObjectType implClass;

    public ExpressionNamespaceDescriptor(IConfigurationElement config) {
        super(config);
        this.id = config.getAttribute("id");
        this.description = config.getAttribute("description");
        this.implClass = new ObjectType(config.getAttribute("class"));
    }

    public String getId() {
        return id;
    }

    public String getDescription() {
        return description;
    }

    public Class<?> getImplClass() {
        return implClass.getObjectClass();
    }

}
