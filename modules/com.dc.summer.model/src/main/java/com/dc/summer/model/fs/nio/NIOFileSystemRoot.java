
package com.dc.summer.model.fs.nio;

import com.dc.summer.model.fs.DBFVirtualFileSystemRoot;
import org.eclipse.core.resources.IProject;

/**
 * NIOFileSystemRoot
 */
public class NIOFileSystemRoot {

    private final IProject project;
    private final DBFVirtualFileSystemRoot fsRoot;
    private final String fsPrefix;

    public NIOFileSystemRoot(IProject project, DBFVirtualFileSystemRoot fsRoot, String fsPrefix) {
        this.project = project;
        this.fsRoot = fsRoot;
        this.fsPrefix = fsPrefix;
    }

    public IProject getProject() {
        return project;
    }

    public DBFVirtualFileSystemRoot getRoot() {
        return fsRoot;
    }

    public String getPrefix() {
        return fsPrefix;
    }

}
