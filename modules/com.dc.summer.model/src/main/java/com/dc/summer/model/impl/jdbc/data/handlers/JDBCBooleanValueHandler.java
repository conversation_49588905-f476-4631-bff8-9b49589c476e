
package com.dc.summer.model.impl.jdbc.data.handlers;

import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.data.DBDValueDefaultGenerator;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

/**
 * JDBC number value handler
 */
public class JDBCBooleanValueHandler extends JDBCAbstractValueHandler implements DBDValueDefaultGenerator {

    public static final JDBCBooleanValueHandler INSTANCE = new JDBCBooleanValueHandler();

    private static final Log log = Log.getLog(JDBCBooleanValueHandler.class);

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index)
        throws SQLException
    {
        boolean value = resultSet.getBoolean(index);
        return resultSet.wasNull() ? null : value;
    }

    @Override
    protected void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType,
                                 int paramIndex, Object value) throws SQLException
    {
        if (value == null) {
            statement.setNull(paramIndex, paramType.getTypeID());
        } else if (value instanceof Boolean) {
            statement.setBoolean(paramIndex, (Boolean)value);
        } else if (value instanceof Number) {
            statement.setBoolean(paramIndex, ((Number)value).byteValue() != 0);
        } else {
            statement.setBoolean(paramIndex, Boolean.valueOf(value.toString()));
        }
    }

    @NotNull
    @Override
    public Class<Boolean> getValueObjectType(@NotNull DBSTypedObject attribute)
    {
        return Boolean.class;
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException
    {
        if (object == null) {
            return null;
        } else if (object instanceof Boolean) {
            return object;
        } else if (object instanceof String) {
            String strValue = (String) object;
            if (strValue.isEmpty()) {
                // Empty string means NULL value
                return null;
            }
            return Boolean.valueOf((String)object);
        } else if (object instanceof Number) {
            return ((Number) object).byteValue() != 0;
        } else {
            log.warn("Unrecognized type '" + object.getClass().getName() + "' - can't convert to boolean");
            return null;
        }
    }

    @Override
    public String getDefaultValueLabel() {
        return "False";
    }

    @Override
    public Object generateDefaultValue(DBCSession session, DBSTypedObject type) {
        return false;
    }

}