
package com.dc.summer.model.impl.sql.edit.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEObjectMaker;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.jdbc.struct.JDBCTable;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableIndex;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.summer.model.struct.rdb.DBSTableIndexColumn;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * JDBC constraint manager
 */
public abstract class SQLIndexManager<OBJECT_TYPE extends JDBCTableIndex<? extends DBSObjectContainer, TABLE_TYPE>, TABLE_TYPE extends JDBCTable>
    extends SQLObjectEditor<OBJECT_TYPE, TABLE_TYPE>
{

    @Override
    public long getMakerOptions(DBPDataSource dataSource)
    {
        return DBEObjectMaker.FEATURE_EDITOR_ON_CREATE;
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options)
    {
        final TABLE_TYPE table = command.getObject().getTable();
        final OBJECT_TYPE index = command.getObject();

        // Create index
        final String indexName = DBUtils.getQuotedIdentifier(index.getDataSource(), index.getName());
        index.setName(indexName);

        final String tableName = DBUtils.getEntityScriptName(table, options);

        StringBuilder decl = new StringBuilder(40);
        decl.append("CREATE");
        appendIndexModifiers(index, decl);
        decl.append(" INDEX ").append(indexName); //$NON-NLS-1$
        appendIndexType(index, decl);
        decl.append(" ON ").append(tableName) //$NON-NLS-1$
            .append(" ("); //$NON-NLS-1$
        try {
            // Get columns using void monitor
            boolean firstColumn = true;
            for (DBSTableIndexColumn indexColumn : CommonUtils.safeCollection(command.getObject().getAttributeReferences(new VoidProgressMonitor()))) {
                if (!firstColumn) decl.append(","); //$NON-NLS-1$
                firstColumn = false;
                decl.append(DBUtils.getQuotedIdentifier(indexColumn));
                appendIndexColumnModifiers(monitor, decl, indexColumn);
            }
        } catch (DBException e) {
            log.error(e);
        }
        decl.append(")"); //$NON-NLS-1$

        actions.add(
            new SQLDatabasePersistAction(ModelMessages.model_jdbc_create_new_index, decl.toString())
        );
    }

    protected void appendIndexType(OBJECT_TYPE index, StringBuilder decl) {

    }

    protected void appendIndexModifiers(OBJECT_TYPE index, StringBuilder decl) {
        if (index.isUnique()) {
            decl.append(" UNIQUE");
        }
    }

    protected void appendIndexColumnModifiers(DBRProgressMonitor monitor, StringBuilder decl, DBSTableIndexColumn indexColumn) {
        if (!indexColumn.isAscending()) {
            decl.append(" DESC"); //$NON-NLS-1$
        }
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options)
    {
        actions.add(
            new SQLDatabasePersistAction(
                ModelMessages.model_jdbc_drop_index,
                getDropIndexPattern(command.getObject())
                    .replace(PATTERN_ITEM_TABLE, command.getObject().getTable().getFullyQualifiedName(DBPEvaluationContext.DDL))
                    .replace(PATTERN_ITEM_INDEX, command.getObject().getFullyQualifiedName(DBPEvaluationContext.DDL))
                    .replace(PATTERN_ITEM_INDEX_SHORT, DBUtils.getQuotedIdentifier(command.getObject())))
        );
    }

    protected String getDropIndexPattern(OBJECT_TYPE index)
    {
        return "DROP INDEX " + PATTERN_ITEM_INDEX; //$NON-NLS-1$
    }


}

