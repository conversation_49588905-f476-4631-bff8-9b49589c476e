
package com.dc.summer.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.Map;

/**
 * Configuration origin.
 * It can be local configuration or some cloud provider.
 */
public interface DBPDataSourceOrigin extends DBPObjectWithDetails<DBPDataSourceContainer> {

    /**
     * Origin type. Unique
     */
    @NotNull
    String getType();

    /**
     * Origin sub type
     */
    @Nullable
    String getSubType();

    @NotNull
    String getDisplayName();

    boolean isDynamic();

    @NotNull
    Map<String, Object> getDataSourceConfiguration();

}
