

package com.dc.summer.model.edit;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;

/**
 * Database persist action
 */
public interface DBEPersistAction {

    enum ActionType {
        INITIALIZER,
        NORMAL,
        OPTIONAL,
        FINAL<PERSON>ZER,
        COMMENT
    }

    String getTitle();

    String getScript();

    void beforeExecute(DBCSession session)
        throws DBCException;

    void afterExecute(DBCSession session, Throwable error)
        throws DBCException;

    ActionType getType();

    boolean isComplex();

}
