
package com.dc.summer.model.impl.sql.edit.struct;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.impl.jdbc.struct.JDBCTable;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableConstraint;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.DBSEntityAttributeRef;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * JDBC constraint manager
 */
public abstract class SQLConstraintManager<OBJECT_TYPE extends JDBCTableConstraint<TABLE_TYPE>, TABLE_TYPE extends JDBCTable>
    extends SQLObjectEditor<OBJECT_TYPE, TABLE_TYPE>
{

    @Override
    public long getMakerOptions(DBPDataSource dataSource)
    {
        return FEATURE_EDITOR_ON_CREATE;
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options)
    {
        final TABLE_TYPE table = command.getObject().getTable();

        actions.add(
            new SQLDatabasePersistAction(
                ModelMessages.model_jdbc_create_new_constraint,
                "ALTER TABLE " + table.getFullyQualifiedName(DBPEvaluationContext.DDL) + " ADD " + getNestedDeclaration(monitor, table, command, options)));
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options)
    {
        actions.add(
            new SQLDatabasePersistAction(
                ModelMessages.model_jdbc_drop_constraint,
                getDropConstraintPattern(command.getObject())
                    .replace(PATTERN_ITEM_TABLE, command.getObject().getTable().getFullyQualifiedName(DBPEvaluationContext.DDL))
                    .replace(PATTERN_ITEM_CONSTRAINT, DBUtils.getQuotedIdentifier(command.getObject())))
        );
    }

    @Override
    public StringBuilder getNestedDeclaration(DBRProgressMonitor monitor, TABLE_TYPE owner, DBECommandAbstract<OBJECT_TYPE> command, Map<String, Object> options)
    {
        OBJECT_TYPE constraint = command.getObject();

        // Create column
        String constraintName = DBUtils.getQuotedIdentifier(constraint.getDataSource(), constraint.getName());

        boolean legacySyntax = isLegacyConstraintsSyntax(owner);
        boolean shortNotation = isShortNotation(owner);
        StringBuilder decl = new StringBuilder(40);
        if ((!legacySyntax || !constraint.isPersisted()) && !shortNotation) {
            decl.append("CONSTRAINT "); //$NON-NLS-1$
        }
        if (!legacySyntax && !shortNotation) {
            decl.append(constraintName).append(" ");
        }
        decl.append(getAddConstraintTypeClause(constraint));

        appendConstraintDefinition(decl, command);

        if (legacySyntax) {
            decl.append(" CONSTRAINT ").append(constraintName); //$NON-NLS-1$
        }
        return decl;
    }

    protected void appendConstraintDefinition(StringBuilder decl, DBECommandAbstract<OBJECT_TYPE> command) {
        decl.append(" ("); //$NON-NLS-1$
        // Get columns using void monitor
        try {
            List<? extends DBSEntityAttributeRef> attrs = command.getObject().getAttributeReferences(new VoidProgressMonitor());
            if (attrs != null) {
                boolean firstColumn = true;
                for (DBSEntityAttributeRef constraintColumn : attrs) {
                    final DBSEntityAttribute attribute = constraintColumn.getAttribute();
                    if (attribute == null) {
                        continue;
                    }
                    if (!firstColumn) decl.append(","); //$NON-NLS-1$
                    firstColumn = false;
                    decl.append(DBUtils.getQuotedIdentifier(attribute));
                }
            }
        } catch (DBException e) {
            log.warn("Can't obtain attribute references", e);
        }
        decl.append(")"); //$NON-NLS-1$
    }

    @NotNull
    protected String getAddConstraintTypeClause(OBJECT_TYPE constraint) {
        if (constraint.getConstraintType() == DBSEntityConstraintType.UNIQUE_KEY) {
            return "UNIQUE"; //$NON-NLS-1$
        }
        return constraint.getConstraintType().getName().toUpperCase(Locale.ENGLISH);
    }

    protected String getDropConstraintPattern(OBJECT_TYPE constraint)
    {
        return "ALTER TABLE " + PATTERN_ITEM_TABLE + " DROP CONSTRAINT " + PATTERN_ITEM_CONSTRAINT; //$NON-NLS-1$ //$NON-NLS-2$
    }

    protected boolean isLegacyConstraintsSyntax(TABLE_TYPE owner) {
        return false;
    }

    protected boolean isShortNotation(TABLE_TYPE owner) {
        return false;
    }
}

