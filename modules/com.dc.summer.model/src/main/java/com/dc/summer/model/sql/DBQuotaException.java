

package com.dc.summer.model.sql;

import com.dc.summer.model.exec.DBCException;

/**
 * Quota exceeded exception
 */
public class DBQuotaException extends DBCException {

    private final String quotaId;
    private final Object quotaValue;
    private final Object exceededValue;

    public DBQuotaException(String message, String quotaId, Object quotaValue, Object exceededValue) {
        super(message + " (>" + quotaValue + ")");
        this.quotaId = quotaId;
        this.quotaValue = quotaValue;
        this.exceededValue = exceededValue;
    }

    public String getQuotaId() {
        return quotaId;
    }

    public Object getQuotaValue() {
        return quotaValue;
    }

    public Object getExceededValue() {
        return exceededValue;
    }
}
