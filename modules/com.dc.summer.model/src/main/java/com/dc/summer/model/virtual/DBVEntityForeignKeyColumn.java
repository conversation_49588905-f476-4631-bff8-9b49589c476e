
package com.dc.summer.model.virtual;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.rdb.DBSTableForeignKeyColumn;
import com.dc.code.NotNull;
import com.dc.summer.model.struct.DBSEntity;

/**
 * Virtual FK column
 */
public class DBVEntityForeignKeyColumn implements DBSTableForeignKeyColumn {

    private static final Log log = Log.getLog(DBVEntityForeignKeyColumn.class);

    private final DBVEntityForeignKey foreignKey;
    private final String attributeName;
    private final String refAttributeName;

    public DBVEntityForeignKeyColumn(DBVEntityForeignKey foreignKey, String attributeName, String refAttributeName) {
        this.foreignKey = foreignKey;
        this.attributeName = attributeName;
        this.refAttributeName = refAttributeName;
    }

    public DBVEntityForeignKeyColumn(DBVEntityForeignKey foreignKey, DBVEntityForeignKeyColumn copy) {
        this.foreignKey = foreignKey;
        this.attributeName = copy.attributeName;
        this.refAttributeName = copy.refAttributeName;
    }

    @NotNull
    @Override
    public String getName() {
        return attributeName;
    }

    @Override
    public DBSEntityAttribute getAttribute() {
        return foreignKey.getEntity().getAttribute(new VoidProgressMonitor(), attributeName);
    }

    public String getAttributeName() {
        return attributeName;
    }

    public String getRefAttributeName() {
        return refAttributeName;
    }

    @Override
    public DBSEntityAttribute getReferencedColumn() {
        DBSEntity associatedEntity = foreignKey.getAssociatedEntity();
        try {
            return associatedEntity == null ? null : associatedEntity.getAttribute(new VoidProgressMonitor(), refAttributeName);
        } catch (DBException e) {
            log.error("Error getting virtual FK referenced column", e);
            return null;
        }
    }

    @Override
    public String toString() {
        return attributeName + ":" + refAttributeName;
    }
}
