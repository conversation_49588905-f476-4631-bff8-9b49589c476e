
package com.dc.summer.model.struct.rdb;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntity;

import java.util.Collection;
import java.util.List;

/**
 * Table
 */
public interface DBSTable extends DBSEntity, DBPQualifiedObject
{

    boolean isView();

    /**
     * Table indices
     * @return list of indices
     * @throws DBException  on any DB error
     * @param monitor progress monitor
     */
    Collection<? extends DBSTableIndex> getIndexes(DBRProgressMonitor monitor) throws DBException;

    /**
     * Keys are: primary keys and unique keys.
     * Foreign keys can be obtained with {@link #getReferences(DBRProgressMonitor)}
     * @return list of constraints
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
    @Nullable
    @Override
    Collection<? extends DBSTableConstraint> getConstraints(@NotNull DBRProgressMonitor monitor) throws DBException;

    /**
     * Gets this table foreign keys
     * @return foreign keys list
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
//    @Override
//    Collection<? extends DBSTableForeignKey> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException;

    /**
     * Gets foreign keys which refers this table
     * @return foreign keys list
     * @throws DBException on any DB error
     * @param monitor progress monitor
     */
//    @Override
//    Collection<? extends DBSTableForeignKey> getReferences(@NotNull DBRProgressMonitor monitor) throws DBException;

    @Nullable
    List<? extends DBSTrigger> getTriggers(@NotNull DBRProgressMonitor monitor) throws DBException;

}
