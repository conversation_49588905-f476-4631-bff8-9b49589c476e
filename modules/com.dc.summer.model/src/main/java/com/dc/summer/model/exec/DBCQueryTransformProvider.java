

package com.dc.summer.model.exec;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * Query transform provider.
 * This interface could be implemented by {@link DBPDataSource} implementor.
 */
public interface DBCQueryTransformProvider {

    /**
     * Creates new query transformer
     * @param type transformation type
     * @return new transformer or null if transformation of specified type is not supported
     */
    @Nullable
    DBCQueryTransformer createQueryTransformer(@NotNull DBCQueryTransformType type);

}
