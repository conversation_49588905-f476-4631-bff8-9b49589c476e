package com.dc.summer.model.impl;


import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Abstract DataSource.
 */
public abstract class AbstractDataSource implements DBPDataSource, DBSObject {

    protected static final boolean REFRESH_CREDENTIALS_ON_CONNECT = false;

    @NotNull
    protected final DBPDataSourceContainer container;
    private final Map<String, Object> contextAttributes = new LinkedHashMap<>();

    public AbstractDataSource(@NotNull DBPDataSourceContainer container) {
        this.container = container;
    }

    @NotNull
    @Override
    public DBPDataSourceContainer getContainer() {
        return container;
    }

    @NotNull
    @Override
    public DBPDataSource getDataSource() {
        return this;
    }

    @Override
    public DBSObject getParentObject() {
        return container;
    }

    @NotNull
    @Override
    public String getName() {
        return container.getName();
    }

    @Nullable
    @Override
    public String getDescription() {
        return container.getDescription();
    }

    @Override
    public boolean isPersisted() {
        return true;
    }

    @Override
    public Object getDataSourceFeature(String featureId) {
        return null;
    }

    @Override
    public Map<String, ?> getContextAttributes() {
        return new LinkedHashMap<>(contextAttributes);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getContextAttribute(String attributeName) {
        return (T) contextAttributes.get(attributeName);
    }

    @Override
    public <T> void setContextAttribute(String attributeName, T attributeValue) {
        contextAttributes.put(attributeName, attributeValue);
    }

    @Override
    public void removeContextAttribute(String attributeName) {
        contextAttributes.remove(attributeName);
    }

    public String getConnectionURL(DBPConnectionConfiguration connectionInfo) {
        String url = connectionInfo.getUrl();
        if (CommonUtils.isEmpty(url)) {
            url = getContainer().getDriver().getConnectionURL(connectionInfo);
        }
        return url;
    }
}

