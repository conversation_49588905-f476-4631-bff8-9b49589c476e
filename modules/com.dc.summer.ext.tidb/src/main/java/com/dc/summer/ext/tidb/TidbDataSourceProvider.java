package com.dc.summer.ext.tidb;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.ext.tidb.model.TidbDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class TidbDataSourceProvider extends MySQLDataSourceProvider {

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new TidbDataSource(monitor, container);
    }

}
