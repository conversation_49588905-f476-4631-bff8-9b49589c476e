<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.tencentdb.mysql.TencentDBMySQLDataSourceProvider"
                description="TD MySQL connector"
                icon="icons/mysql_icon.png"
                parent="mysql"
                id="td_mysql"
                label="TD MySQL"
                dialect="td_mysql">
            <drivers managable="true">
                <driver
                        id="mysql8"
                        label="MySQL"
                        icon="icons/mysql_icon.png"
                        iconBig="icons/mysql_icon_big.png"
                        class="com.mysql.cj.jdbc.Driver"
                        sampleURL="jdbc:mysql://{host}[:{port}]/[{database}]"
                        useURL="false"
                        defaultPort="3306"
                        defaultUser="root"
                        webURL="https://www.mysql.com/products/connector/"
                        propertiesURL="https://dev.mysql.com/doc/connector-j/8.0/en/connector-j-reference-configuration-properties.html"
                        description="Driver for MySQL 8 and later"
                        promoted="1"
                        categories="sql">
                    <file type="jar" path="maven:/mysql:mysql-connector-java:8.0.29" bundle="!drivers.mysql"/>
                    <file type="license" path="drivers/mysql/LICENSE.txt" bundle="drivers.mysql"/>
                    <file type="jar" path="drivers/mysql/mysql8" bundle="drivers.mysql"/>
                    <property name="connectTimeout" value="20000"/>
                    <property name="rewriteBatchedStatements" value="true"/>
                    <property name="useCursorFetch" value="true"/>
                    <property name="allowPublicKeyRetrieval" value="true"/>
                    <!-- https://dev.mysql.com/doc/connector-j/8.0/en/connector-j-reference-using-ssl.html -->
                    <property name="enabledTLSProtocols" value="TLSv1,TLSv1.1,TLSv1.2"/>
                    <property name="@summer-default-resultset.maxrows.sql" value="true"/>
                    <property name="@summer-default-resultset.format.datetime.native" value="true"/>
                    <property name="@summer-default-dataformat.type.timestamp.pattern" value="yyyy-MM-dd HH:mm:ss"/>
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="td_mysql" parent="mysql" class="com.dc.summer.ext.tencentdb.mysql.model.TencentDBMySQLDialect" label="TD MySQL" description="TD MySQL dialect." icon="icons/mysql_icon.png">
            <property name="insertMethods" value="mysqlInsertIgnore,mysqlReplaceIgnore,mysqlReplaceIgnoreUpdate"/>
            <keywords value=""/>
            <execKeywords value=""/>
            <ddlKeywords value=""/>
            <dmlKeywords value=""/>
            <functions value=""/>
            <types value=""/>

            <property name="" value=""/>
        </dialect>
    </extension>

</plugin>
