package com.dc.parser.ext.mongodb.visitor.statement.type;

import com.dc.parser.ext.mongodb.parser.autogen.MongoDBStatementParser.*;
import com.dc.parser.ext.mongodb.segment.*;
import com.dc.parser.ext.mongodb.segment.collection.*;
import com.dc.parser.ext.mongodb.statement.CollectionStatement;
import com.dc.parser.ext.mongodb.visitor.statement.MongoDBStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DMLStatementVisitor;

public class CollectionStatementVisitor extends MongoDBStatementVisitor implements DMLStatementVisitor {

    @Override
    public ASTNode visitCollection(CollectionContext ctx) {
        CollectionStatement result = new CollectionStatement();
        if (ctx.dbCollectionClause() != null) {
            result.setCollection(visitDbCollectionClause(ctx.dbCollectionClause()));
        }
        if (ctx.getChild(2) != null) {
            result.setMethod((MethodSegment) visit(ctx.getChild(2)));
            if (ctx.getChild(2).getChild(0) != null) {
                result.getMethod().setMethodName(ctx.getChild(2).getChild(0).getText());
            }
        }
        return result;
    }

    @Override
    public ASTNode visitFindClause(FindClauseContext ctx) {
        FindSegment result = new FindSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.projection() != null) {
            result.setProjection(visitProjection(ctx.projection()));
        }
        if (ctx.findOptions() != null) {
            result.setOptions(visitFindOptions(ctx.findOptions()));
        }
        if (ctx.findCursorClause() != null) {
            for (int i = 0; i < ctx.findCursorClause().size(); i++) {
                result.getCursors().add(visitFindCursorClause(ctx.findCursorClause(i)));
            }
        }
        return result;
    }

    @Override
    public CursorSegment visitFindCursorClause(FindCursorClauseContext ctx) {
        CursorSegment result = new CursorSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        result.setName(ctx.start.getText());
        return result;
    }

    @Override
    public BsonObjectSegment visitProjection(ProjectionContext ctx) {
        return visitBsonObject(ctx.bsonObject());
    }

    @Override
    public BsonObjectSegment visitFindOptions(FindOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitFindAndModifyClause(FindAndModifyClauseContext ctx) {
        FindAndModifySegment result = new FindAndModifySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.findAndModifyOptions() != null) {
            result.setOptions(visitFindAndModifyOptions(ctx.findAndModifyOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitFindAndModifyOptions(FindAndModifyOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitFindOneClause(FindOneClauseContext ctx) {
        FindOneSegment result = new FindOneSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.projection() != null) {
            result.setProjection(visitProjection(ctx.projection()));
        }
        if (ctx.findOptions() != null) {
            result.setOptions(visitFindOptions(ctx.findOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitFindOneAndDeleteClause(FindOneAndDeleteClauseContext ctx) {
        FindOneAndDeleteSegment result = new FindOneAndDeleteSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.findOneAndDeleteOptions() != null) {
            result.setOptions(visitFindOneAndDeleteOptions(ctx.findOneAndDeleteOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitFindOneAndDeleteOptions(FindOneAndDeleteOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitFindOneAndUpdateClause(FindOneAndUpdateClauseContext ctx) {
        FindOneAndUpdateSegment result = new FindOneAndUpdateSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.updateFilter() != null) {
            result.setUpdateFilter(visitUpdateFilter(ctx.updateFilter()));
        }
        if (ctx.findOneAndUpdateOptions() != null) {
            result.setOptions(visitFindOneAndUpdateOptions(ctx.findOneAndUpdateOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitUpdateFilter(UpdateFilterContext ctx) {
        return visitBsonObject(ctx.bsonObject());
    }

    @Override
    public BsonObjectSegment visitFindOneAndUpdateOptions(FindOneAndUpdateOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitFindOneAndReplaceClause(FindOneAndReplaceClauseContext ctx) {
        FindOneAndReplaceSegment result = new FindOneAndReplaceSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.replacement() != null) {
            result.setReplacement(visitReplacement(ctx.replacement()));
        }
        if (ctx.findOneAndReplaceOptions() != null) {
            result.setOptions(visitFindOneAndReplaceOptions(ctx.findOneAndReplaceOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitReplacement(ReplacementContext ctx) {
        return visitBsonObject(ctx.bsonObject());
    }

    @Override
    public BsonObjectSegment visitFindOneAndReplaceOptions(FindOneAndReplaceOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitGetIndexesClause(GetIndexesClauseContext ctx) {
        return new GetIndexesSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
    }

    @Override
    public ASTNode visitTotalIndexSizeClause(TotalIndexSizeClauseContext ctx) {
        return new TotalIndexSizeSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
    }

    @Override
    public ASTNode visitDistinctClause(DistinctClauseContext ctx) {
        DistinctSegment result = new DistinctSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.key != null) {
            result.setKey(visitString(ctx.key).getValue());
        }
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.commandOperationOptions() != null) {
            result.setOptions(visitCommandOperationOptions(ctx.commandOperationOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitDataSizeClause(DataSizeClauseContext ctx) {
        return new DataSizeSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
    }

    @Override
    public ASTNode visitInsertClause(com.dc.parser.ext.mongodb.parser.autogen.MongoDBStatementParser.InsertClauseContext ctx) {
        InsertSegment result = new InsertSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonObject() != null) {
            result.setDocument(visitBsonObject(ctx.bsonObject()));
        } else if (ctx.bsonArray() != null) {
            result.setDocuments(visitBsonArray(ctx.bsonArray()));
        }
        if (ctx.insertOneOptions() != null) {
            result.setOptions(visitInsertOneOptions(ctx.insertOneOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitInsertOneOptions(InsertOneOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitInsertOneClause(InsertOneClauseContext ctx) {
        InsertOneSegment result = new InsertOneSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonObject() != null) {
            result.setDocument(visitBsonObject(ctx.bsonObject()));
        }
        if (ctx.insertOneOptions() != null) {
            result.setOptions(visitInsertOneOptions(ctx.insertOneOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitInsertManyClause(InsertManyClauseContext ctx) {
        InsertManySegment result = new InsertManySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonArray() != null) {
            result.setDocuments(visitBsonArray(ctx.bsonArray()));
        }
        if (ctx.bulkWriteOptions() != null) {
            result.setOptions(visitBulkWriteOptions(ctx.bulkWriteOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitBulkWriteOptions(BulkWriteOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitUpdateClause(UpdateClauseContext ctx) {
        UpdateSegment result = new UpdateSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.updateFilter() != null) {
            result.setUpdate(visitUpdateFilter(ctx.updateFilter()));
        }
        if (ctx.updateOptions() != null) {
            result.setOptions(visitUpdateOptions(ctx.updateOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitUpdateOptions(UpdateOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitUpdateOneClause(UpdateOneClauseContext ctx) {
        UpdateOneSegment result = new UpdateOneSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.updateFilter() != null) {
            result.setUpdate(visitUpdateFilter(ctx.updateFilter()));
        }
        if (ctx.updateOptions() != null) {
            result.setOptions(visitUpdateOptions(ctx.updateOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitUpdateManyClause(UpdateManyClauseContext ctx) {
        UpdateManySegment result = new UpdateManySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.updateFilter() != null) {
            result.setUpdate(visitUpdateFilter(ctx.updateFilter()));
        }
        if (ctx.updateOptions() != null) {
            result.setOptions(visitUpdateOptions(ctx.updateOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitReplaceOneClause(ReplaceOneClauseContext ctx) {
        ReplaceOneSegment result = new ReplaceOneSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.replacement() != null) {
            result.setReplacement(visitReplacement(ctx.replacement()));
        }
        if (ctx.replaceOptions() != null) {
            result.setOptions(visitReplaceOptions(ctx.replaceOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitReplaceOptions(ReplaceOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitDeleteClause(DeleteClauseContext ctx) {
        DeleteSegment result = new DeleteSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.deleteOptions() != null) {
            result.setOptions(visitDeleteOptions(ctx.deleteOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitDeleteOptions(DeleteOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitDeleteOneClause(DeleteOneClauseContext ctx) {
        DeleteOneSegment result = new DeleteOneSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.deleteOptions() != null) {
            result.setOptions(visitDeleteOptions(ctx.deleteOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitDeleteManyClause(DeleteManyClauseContext ctx) {
        DeleteManySegment result = new DeleteManySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.deleteOptions() != null) {
            result.setOptions(visitDeleteOptions(ctx.deleteOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitExplainClause(ExplainClauseContext ctx) {
        ExplainSegment result = new ExplainSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.verbosity != null) {
            result.setVerbosity(visitString(ctx.verbosity).getValue());
        }
        if (ctx.explainableClause() != null) {
            result.setPlanMethod((MethodSegment) visitExplainableClause(ctx.explainableClause()));
        }
        return result;
    }

    @Override
    public ASTNode visitExplainableClause(ExplainableClauseContext ctx) {
        if (ctx.getChild(0) != null) {
            return visit(ctx.getChild(0));
        }
        return null;
    }

    @Override
    public ASTNode visitDropClause(DropClauseContext ctx) {
        DropSegment result = new DropSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.dropCollectionOptions() != null) {
            result.setOptions(visitDropCollectionOptions(ctx.dropCollectionOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitDropCollectionOptions(DropCollectionOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitDropIndexClause(DropIndexClauseContext ctx) {
        DropIndexSegment result = new DropIndexSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonObject() != null) {
            result.setIndex(visitBsonObject(ctx.bsonObject()));
        } else if (ctx.string() != null) {
            result.setIndexName(visitString(ctx.string()).getValue());
        }
        return result;
    }

    @Override
    public ASTNode visitDropIndexesClause(DropIndexesClauseContext ctx) {
        DropIndexesSegment result = new DropIndexesSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonObject() != null) {
            result.setIndex(visitBsonObject(ctx.bsonObject()));
        } else if (ctx.string() != null) {
            result.setIndexName(visitString(ctx.string()).getValue());
        } else if (ctx.stringArray() != null) {
            result.setIndexNames(visitStringArray(ctx.stringArray()).getValue());
        } else if (ctx.bsonArray() != null) {
            result.setIndexes(visitBsonArray(ctx.bsonArray()));
        }
        return result;
    }

    @Override
    public ASTNode visitCreateIndexClause(CreateIndexClauseContext ctx) {
        CreateIndexSegment result = new CreateIndexSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonObject() != null) {
            result.setKeys(visitBsonObject(ctx.bsonObject()));
        }
        if (ctx.createIndexesOptions() != null) {
            result.setOptions(visitCreateIndexesOptions(ctx.createIndexesOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitCreateIndexesOptions(CreateIndexesOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitCreateIndexesClause(CreateIndexesClauseContext ctx) {
        CreateIndexesSegment result = new CreateIndexesSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.bsonArray() != null) {
            result.setKeys(visitBsonArray(ctx.bsonArray()));
        }
        if (ctx.createIndexesOptions() != null) {
            result.setOptions(visitCreateIndexesOptions(ctx.createIndexesOptions()));
        }
        return result;
    }

    @Override
    public ASTNode visitRenameCollectionClause(RenameCollectionClauseContext ctx) {
        RenameCollectionSegment result = new RenameCollectionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.string() != null) {
            result.setNewName(visitString(ctx.string()).getValue());
        }
        if (ctx.boolean_() != null) {
            result.setDropTarget(visitBoolean(ctx.boolean_()).getValue());
        }
        return result;
    }

    @Override
    public ASTNode visitCountClause(CountClauseContext ctx) {
        CountSegment result = new CountSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.countOptions() != null) {
            result.setOptions(visitCountOptions(ctx.countOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitCountOptions(CountOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }

    @Override
    public ASTNode visitCountDocumentsClause(CountDocumentsClauseContext ctx) {
        CountDocumentsSegment result = new CountDocumentsSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.rootFilter() != null) {
            result.setFilter(visitRootFilter(ctx.rootFilter()));
        }
        if (ctx.countDocumentsOptions() != null) {
            result.setOptions(visitCountDocumentsOptions(ctx.countDocumentsOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitCountDocumentsOptions(CountDocumentsOptionsContext ctx) {
        return visitAggregateOptions(ctx.aggregateOptions());
    }

    @Override
    public ASTNode visitEstimatedDocumentCountClause(EstimatedDocumentCountClauseContext ctx) {
        EstimatedDocumentCountSegment result = new EstimatedDocumentCountSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (ctx.estimatedDocumentCountOptions() != null) {
            result.setOptions(visitEstimatedDocumentCountOptions(ctx.estimatedDocumentCountOptions()));
        }
        return result;
    }

    @Override
    public BsonObjectSegment visitEstimatedDocumentCountOptions(EstimatedDocumentCountOptionsContext ctx) {
        return visitCommandOperationOptions(ctx.commandOperationOptions());
    }
}
