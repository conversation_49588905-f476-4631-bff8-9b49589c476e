package com.dc.parser.ext.mongodb.segment.database;

import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateCollectionSegment extends MethodSegment {
    private String name;
    private BsonObjectSegment options;
    public CreateCollectionSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}
