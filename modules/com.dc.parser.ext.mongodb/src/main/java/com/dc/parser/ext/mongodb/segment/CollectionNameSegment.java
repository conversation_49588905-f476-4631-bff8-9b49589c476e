package com.dc.parser.ext.mongodb.segment;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class CollectionNameSegment implements SQLSegment {

    private final int startIndex;

    private final int stopIndex;

    private final IdentifierValue identifier;
}
