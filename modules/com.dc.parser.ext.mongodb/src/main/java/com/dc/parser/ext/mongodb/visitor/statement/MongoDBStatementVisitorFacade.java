
package com.dc.parser.ext.mongodb.visitor.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.ext.mongodb.visitor.statement.type.CollectionStatementVisitor;
import com.dc.parser.ext.mongodb.visitor.statement.type.DatabaseStatementVisitor;
import com.dc.parser.ext.mongodb.visitor.statement.type.OtherStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.*;
import com.dc.parser.model.spi.SQLStatementVisitorFacade;

/**
 * Statement visitor facade for MongoDB.
 */
public final class MongoDBStatementVisitorFacade implements SQLStatementVisitorFacade {
    
    @Override
    public Class<? extends DMLStatementVisitor> getDMLVisitorClass() {
        return CollectionStatementVisitor.class;
    }
    
    @Override
    public Class<? extends DDLStatementVisitor> getDDLVisitorClass() {
        return DatabaseStatementVisitor.class;
    }
    
    @Override
    public Class<? extends TCLStatementVisitor> getTCLVisitorClass() {
        return null;
    }
    
    @Override
    public Class<? extends DCLStatementVisitor> getDCLVisitorClass() {
        return null;
    }
    
    @Override
    public Class<? extends DALStatementVisitor> getDALVisitorClass() {
        return OtherStatementVisitor.class;
    }
    
    @Override
    public Class<? extends RLStatementVisitor> getRLVisitorClass() {
        return null;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.MONGO_DB;
    }
}
