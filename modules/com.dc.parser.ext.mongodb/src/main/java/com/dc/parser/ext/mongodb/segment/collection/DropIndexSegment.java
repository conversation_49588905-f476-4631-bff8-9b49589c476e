package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DropIndexSegment extends MethodSegment {
    private String indexName;
    private BsonObjectSegment index;
    public DropIndexSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}