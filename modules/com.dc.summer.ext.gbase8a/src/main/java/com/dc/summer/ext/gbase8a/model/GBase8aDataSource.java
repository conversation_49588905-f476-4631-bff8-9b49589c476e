package com.dc.summer.ext.gbase8a.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.LinkedHashMap;
import java.util.Map;

public class GBase8aDataSource extends MySQLDataSource {

    public GBase8aDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container);
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        return new LinkedHashMap<>(MySQLDataSourceProvider.getConnectionsProps());
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new GBase8aDataSourceInfo(metaData);
    }
}
