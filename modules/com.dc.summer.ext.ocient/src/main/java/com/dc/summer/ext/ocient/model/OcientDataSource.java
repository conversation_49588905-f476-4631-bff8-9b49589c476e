
package com.dc.summer.ext.ocient.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.ocient.model.plan.OcientQueryPlaner;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.model.exec.plan.DBCQueryPlanner;

public class OcientDataSource extends GenericDataSource {

    public OcientDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container, new GenericMetaModel(), new OcientSQLDialect());
    }

    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (adapter == DBCQueryPlanner.class) {
            return adapter.cast(new OcientQueryPlaner(this));
        }
        return super.getAdapter(adapter);
    }

    @Override
    protected boolean isPopulateClientAppName() {
        return false;
    }

}
