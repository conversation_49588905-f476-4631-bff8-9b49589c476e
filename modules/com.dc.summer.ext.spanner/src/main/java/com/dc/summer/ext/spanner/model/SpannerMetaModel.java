
package com.dc.summer.ext.spanner.model;

import com.dc.summer.ext.generic.model.meta.GenericMetaModel;

/**
 * Spanner meta model
 */
public class SpannerMetaModel extends GenericMetaModel {

    public SpannerMetaModel() {
    }
    
    // The default schema in Cloud Spanner is an empty string. This ensures that this
    // default schema will be shown as 'DEFAULT' in DBeaver, and enables auto complete
    // for tables and views in both the DEFAULT schema, as well as the INFORMATION_SCHEMA
    // and SPANNER_SYS schemas.
    public boolean supportsNullSchemas() {
        return true;
    }

}
