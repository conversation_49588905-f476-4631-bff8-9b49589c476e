package com.dc.summer.ext.analyticdb.mysql.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class AnalyticDBMySQLDataSource extends MySQLDataSource {
    public AnalyticDBMySQLDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container);
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new AnalyticDBMySQLDataSourceInfo(metaData);
    }
}
