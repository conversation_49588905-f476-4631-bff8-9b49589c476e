
package com.dc.summer.model.sql.parser.tokens.predicates;

import com.dc.code.NotNull;

/**
 * Represents node of token predicate describing optional token subsequence
 */
class OptionalTokenPredicateNode extends UnaryTokenPredicateNode {
    public OptionalTokenPredicateNode(@NotNull TokenPredicateNode child) {
        super(child);
    }

    @Override
    @NotNull
    protected <T, R> R applyImpl(@NotNull TokenPredicateNodeVisitor<T, R> visitor, @NotNull T arg) {
        return visitor.visitOptional(this, arg);
    }
}
