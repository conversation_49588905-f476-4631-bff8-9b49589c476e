
package com.dc.summer.model.sql.completion;

import com.dc.summer.model.sql.parser.SQLWordPartDetector;
import org.eclipse.jface.text.IDocument;
import com.dc.summer.model.sql.SQLScriptElement;

public class SQLCompletionRequest {

    public enum QueryType {
        TABLE,
        JOIN,
        COLUMN,
        EXEC
    }

    private final SQLCompletionContext context;
    private final IDocument document;
    private final int documentOffset;
    private final SQLScriptElement activeQuery;
    private final boolean simpleMode;

    private final SQLWordPartDetector wordDetector;

    private String wordPart;
    private QueryType queryType;
    private String contentType;

    public SQLCompletionRequest(SQLCompletionContext context, IDocument document, int documentOffset, SQLScriptElement activeQuery, boolean simpleMode) {
        this.context = context;
        this.document = document;
        this.documentOffset = documentOffset;
        this.activeQuery = activeQuery;
        this.simpleMode = simpleMode;

        this.wordDetector = new SQLWordPartDetector(document, context.getSyntaxManager(), documentOffset);
        this.wordPart = wordDetector.getWordPart();
    }

    public SQLCompletionContext getContext() {
        return context;
    }

    public IDocument getDocument() {
        return document;
    }

    public int getDocumentOffset() {
        return documentOffset;
    }

    public SQLScriptElement getActiveQuery() {
        return activeQuery;
    }

    public boolean isSimpleMode() {
        return simpleMode;
    }

    public SQLWordPartDetector getWordDetector() {
        return wordDetector;
    }

    public String getWordPart() {
        return wordPart;
    }

    public void setWordPart(String wordPart) {
        this.wordPart = wordPart;
    }

    public QueryType getQueryType() {
        return queryType;
    }

    public void setQueryType(QueryType queryType) {
        this.queryType = queryType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getContentType() {
        return contentType;
    }

}
