
package com.dc.summer.model.text.parser.rules;

import com.dc.summer.model.text.parser.TPCharacterScanner;
import com.dc.summer.model.text.parser.TPRule;
import com.dc.summer.model.text.parser.TPToken;
import com.dc.summer.model.text.parser.TPTokenAbstract;

/**
 * Whitespace rule
 */
public class WhitespaceRule implements TPRule {

    private final TPToken fWhitespaceToken;

    public WhitespaceRule(TPToken token) {
        fWhitespaceToken = token;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner) {
        int c = scanner.read();
        if (Character.isWhitespace((char) c)) {
            do {
                c = scanner.read();
            } while (Character.isWhitespace((char) c));
            scanner.unread();
            return fWhitespaceToken;
        }

        scanner.unread();
        return TPTokenAbstract.UNDEFINED;
    }
}
