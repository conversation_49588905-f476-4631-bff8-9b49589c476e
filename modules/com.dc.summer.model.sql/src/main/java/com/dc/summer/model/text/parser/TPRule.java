
package com.dc.summer.model.text.parser;


/**
 * Simple rule
 */
public interface TPRule {

	/**
	 * Evaluates the rule by examining the characters available from the provided character scanner.
	 * The token returned by this rule returns <code>true</code> when calling
	 * <code>isUndefined</code>, if the text that the rule investigated does not match the rule's
	 * requirements
	 *
	 * @param scanner the character scanner to be used by this rule
	 * @return the token computed by the rule
	 */
	TPToken evaluate(TPCharacterScanner scanner);
}
