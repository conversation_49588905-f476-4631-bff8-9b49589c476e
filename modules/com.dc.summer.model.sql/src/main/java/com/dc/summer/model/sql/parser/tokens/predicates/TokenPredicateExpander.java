
package com.dc.summer.model.sql.parser.tokens.predicates;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.utils.ListNode;
import com.dc.summer.model.sql.parser.TokenEntry;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Implements token predicate tree expansion into the list of all possible token entry sequences matching the predicate
 */
class TokenPredicateExpander implements TokenPredicateNodeVisitor<ListNode<SQLTokenEntry>, ListNode<ListNode<SQLTokenEntry>>> {
    private static final TokenPredicateExpander INSTANCE = new TokenPredicateExpander();

    @NotNull
    public static List<List<TokenEntry>> expand(@Nullable TokenPredicateNode node) {
        return node == null
                ? Collections.emptyList()
                : StreamSupport.stream(node.apply(INSTANCE, null).spliterator(), false)
                .filter(p -> p != null)
                .map(path -> {
                    // expanding traverse walks in the left-to-right order, so we've got the rightmost entry  as head of the list
                    List<TokenEntry> list = StreamSupport.stream(path.spliterator(), false).collect(Collectors.toList());
                    Collections.reverse(list); //can be removed if collect token entries in reverse order (right-to-left)
                    return list;
                })
                .collect(Collectors.toList());
    }

    private TokenPredicateExpander() {

    }

    @Override
    @NotNull
    public ListNode<ListNode<SQLTokenEntry>> visitSequence(@NotNull SequenceTokenPredicateNode sequence, @NotNull ListNode<SQLTokenEntry> head) {
        ListNode<ListNode<SQLTokenEntry>> results = ListNode.of(head);
        for (TokenPredicateNode child: sequence.childs) {
            ListNode<ListNode<SQLTokenEntry>> step = null;
            for (ListNode<SQLTokenEntry> prefix: results) {
                for (ListNode<SQLTokenEntry> childPath: child.apply(this, prefix)) {
                    step = ListNode.push(step, childPath);
                }
            }
            results = step;
        }
        return results;
    }

    @Override
    @NotNull
    public ListNode<ListNode<SQLTokenEntry>> visitAlternative(@NotNull AlternativeTokenPredicateNode alternative, @NotNull ListNode<SQLTokenEntry> head) {
        ListNode<ListNode<SQLTokenEntry>> results = null;
        for (TokenPredicateNode child: alternative.childs) {
            for (ListNode<SQLTokenEntry> childPath: child.apply(this, head)) {
                results = ListNode.push(results, childPath);
            }
        }
        return results;
    }

    @Override
    @NotNull
    public ListNode<ListNode<SQLTokenEntry>> visitOptional(@NotNull OptionalTokenPredicateNode optional, @NotNull ListNode<SQLTokenEntry> head) {
        return ListNode.push(optional.child.apply(this, head), head);
    }

    @Override
    @NotNull
    public ListNode<ListNode<SQLTokenEntry>> visitTokenEntry(@NotNull SQLTokenEntry token, @NotNull ListNode<SQLTokenEntry> head) {
        return ListNode.of(ListNode.push(head, token));
    }
}
