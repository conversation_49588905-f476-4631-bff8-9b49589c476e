
package com.dc.summer.model.sql.parser.rules;

import com.dc.summer.model.text.parser.TPCharacterScanner;
import com.dc.summer.model.text.parser.TPRule;
import com.dc.summer.model.text.parser.TPToken;
import com.dc.summer.model.text.parser.TPTokenAbstract;
import com.dc.utils.CommonUtils;

import java.util.Locale;

/**
* DelimiterRule
*/
public class SQLDelimiterRule implements TPRule {
    private final TPToken token;
    private char[][] delimiters, origDelimiters;
    private char[] buffer, origBuffer;

    public SQLDelimiterRule(String[] delimiters, TPToken token) {
        this.token = token;
        this.origDelimiters = this.delimiters = new char[delimiters.length][];
        int index = 0, maxLength = 0;
        for (String delim : delimiters) {
            this.delimiters[index] = delim.toCharArray();
            for (int i = 0; i < this.delimiters[index].length; i++) {
                this.delimiters[index][i] = Character.toUpperCase(this.delimiters[index][i]);
            }
            maxLength = Math.max(maxLength, this.delimiters[index].length);
            index++;
        }
        this.origBuffer = this.buffer = new char[maxLength];
    }

    public char[][] getDelimiters() {
        return delimiters;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner) {
        for (int i = 0; ; i++) {
            int c = scanner.read();
            boolean matches = false;
            if (c != TPCharacterScanner.EOF) {
                c = Character.toUpperCase(c);
                for (int k = 0; k < delimiters.length; k++) {
                    if (i < delimiters[k].length && delimiters[k][i] == c) {
                        buffer[i] = (char)c;
                        if (i == delimiters[k].length - 1 && equalsBegin(delimiters[k])) {
                            // Matched. Check next character
                            if (Character.isLetterOrDigit(c)) {
                                int cn = scanner.read();
                                scanner.unread();
                                if (Character.isUnicodeIdentifierPart(cn)) {
                                    matches = false;
                                    continue;
                                }
                            }
                            return token;
                        }
                        matches = true;
                        break;
                    }
                }
            }
            if (!matches) {
                for (int k = 0; k <= i; k++) {
                    scanner.unread();
                }
                return TPTokenAbstract.UNDEFINED;
            }
        }
    }

    private boolean equalsBegin(char[] delimiter) {
        for (int i = 0; i < delimiter.length; i++) {
            if (buffer[i] != delimiter[i]) {
                return false;
            }
        }
        return true;
    }

    public void changeDelimiter(String newDelimiter) {
        if (CommonUtils.isEmpty(newDelimiter)) {
            this.delimiters = this.origDelimiters;
            this.buffer = this.origBuffer;
        } else {
            for (char[] delim : delimiters) {
                String delimStr = String.valueOf(delim);
                if (newDelimiter.equals(delimStr)) {
                    return;
                }
                if (newDelimiter.endsWith(delimStr)) {
                    // New delimiter ends with old delimiter (as command terminator). Remove it.
                    newDelimiter = newDelimiter.substring(0, newDelimiter.length() - delimStr.length()).trim();
                }
            }
            this.delimiters = new char[1][];
            this.delimiters[0] = newDelimiter.toUpperCase(Locale.ENGLISH).toCharArray();
            this.buffer = new char[newDelimiter.length()];
        }
    }
}
