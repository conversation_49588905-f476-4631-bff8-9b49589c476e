
package com.dc.summer.model.sql.generator;

import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.DBException;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public abstract class SQLGeneratorBase<OBJECT> extends SQLGenerator<OBJECT> {

    protected abstract Collection<? extends DBSAttributeBase> getAllAttributes(DBRProgressMonitor monitor, OBJECT object) throws DBException;

    protected abstract Collection<? extends DBSAttributeBase> getKeyAttributes(DBRProgressMonitor monitor, OBJECT object) throws DBException;

    protected Collection<? extends DBSAttributeBase> getValueAttributes(DBRProgressMonitor monitor, OBJECT object, Collection<? extends DBSAttributeBase> keyAttributes) throws DBException {
        if (CommonUtils.isEmpty(keyAttributes)) {
            return getAllAttributes(monitor, object);
        }
        List<DBSAttributeBase> valueAttributes = new ArrayList<>(getAllAttributes(monitor, object));
        valueAttributes.removeIf(keyAttributes::contains);
        return valueAttributes;
    }

    protected void appendDefaultValue(StringBuilder sql, DBSAttributeBase attr) {
        String defValue = null;
        if (attr instanceof DBSEntityAttribute) {
            defValue = ((DBSEntityAttribute) attr).getDefaultValue();
        }
        if (!CommonUtils.isEmpty(defValue)) {
            sql.append(defValue);
        } else {
            switch (attr.getDataKind()) {
                case BOOLEAN:
                    sql.append("false");
                    break;
                case NUMERIC:
                    sql.append("0");
                    break;
                case STRING:
                case DATETIME:
                case CONTENT:
                    sql.append("''");
                    break;
                default:
                    sql.append("?");
                    break;
            }
        }
    }

}

