

package com.dc.summer.model.sql.translate;

import com.dc.summer.model.sql.parser.SQLScriptParser;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLScriptElement;

import java.util.ArrayList;
import java.util.List;

/**
 * SQL translator
 */
public final class SQLQueryTranslator {

    public static String translateScript(
        @NotNull SQLDialect sourceDialect,
        @NotNull SQLDialect targetDialect,
        @NotNull DBPPreferenceStore preferenceStore,
        @NotNull String script) throws DBException
    {
        SQLTranslateContext context = new SQLTranslateContext(sourceDialect, targetDialect, preferenceStore);

        List<SQLScriptElement> sqlScriptElements = SQLScriptParser.parseScript(sourceDialect, preferenceStore, script);
        List<SQLScriptElement> result = new ArrayList<>();
        for (SQLScriptElement element : sqlScriptElements) {
            result.addAll(
                context.translateCommand(element));
        }
        String scriptDelimiter = targetDialect.getScriptDelimiters()[0];

        StringBuilder sql = new StringBuilder();
        for (SQLScriptElement element : result) {
            sql.append(element.getText());
            sql.append(scriptDelimiter).append("\n");
        }
        return sql.toString();
    }


}
