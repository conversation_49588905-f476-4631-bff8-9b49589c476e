
package com.dc.summer.model.sql.registry;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SQLCommandsRegistry
{
    static final String TAG_COMMAND = "command"; //$NON-NLS-1$
    static final String TAG_PRAGMA = "pragma"; //$NON-NLS-1$

    private static SQLCommandsRegistry instance = null;

    public synchronized static SQLCommandsRegistry getInstance()
    {
        if (instance == null) {
            instance = new SQLCommandsRegistry();
            instance.loadExtensions(Global.getExtensionRegistry());
        }
        return instance;
    }

    private final Map<String, SQLCommandHandlerDescriptor> commandHandlers = new HashMap<>();
    private final Map<String, SQLPragmaHandlerDescriptor> pragmaHandlers = new HashMap<>();

    private SQLCommandsRegistry()
    {
    }

    private void loadExtensions(IExtensionRegistry registry)
    {
        IConfigurationElement[] extConfigs = registry.getConfigurationElementsFor(SQLCommandHandlerDescriptor.EXTENSION_ID);
        for (IConfigurationElement ext : extConfigs) {
            // Load functions
            if (TAG_COMMAND.equals(ext.getName())) {
                SQLCommandHandlerDescriptor commandDescriptor = new SQLCommandHandlerDescriptor(ext);
                this.commandHandlers.put(commandDescriptor.getId(), commandDescriptor);
            }
            if (TAG_PRAGMA.equals(ext.getName())) {
                final SQLPragmaHandlerDescriptor descriptor = new SQLPragmaHandlerDescriptor(ext);
                pragmaHandlers.put(descriptor.getId(), descriptor);
            }
        }
    }

    public void dispose()
    {
        commandHandlers.clear();
    }

    public List<SQLCommandHandlerDescriptor> getCommandHandlers() {
        return new ArrayList<>(commandHandlers.values());
    }

    public SQLCommandHandlerDescriptor getCommandHandler(String id) {
        return commandHandlers.get(id);
    }

    @Nullable
    public SQLPragmaHandlerDescriptor getPragmaHandler(@NotNull String id) {
        return pragmaHandlers.get(id);
    }
}
