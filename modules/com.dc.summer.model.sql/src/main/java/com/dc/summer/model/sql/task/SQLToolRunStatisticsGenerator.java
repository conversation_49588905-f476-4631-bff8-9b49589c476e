
package com.dc.summer.model.sql.task;

import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.struct.DBSObject;

import java.util.List;

/**
 * SQLToolRunStatisticsGenerator
 */
public interface SQLToolRunStatisticsGenerator<
    OBJECT_TYPE extends DBSObject,
    SETTINGS extends SQLToolExecuteSettings<OBJECT_TYPE>,
    PERSIST_ACTION extends DBEPersistAction> {

    List<? extends SQLToolStatistics<OBJECT_TYPE>> getExecuteStatistics(OBJECT_TYPE object, SETTINGS settings, PERSIST_ACTION action, DBCSession session, DBCStatement dbStat) throws DBCException;

}
