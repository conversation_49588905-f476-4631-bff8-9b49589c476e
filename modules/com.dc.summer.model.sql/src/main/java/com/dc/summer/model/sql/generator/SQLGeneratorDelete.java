
package com.dc.summer.model.sql.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.sql.ChangeTableDataStatement;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.utils.CommonUtils;

import java.util.Collection;

public class SQLGeneratorDelete extends SQLGeneratorTable {

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBSEntity object) throws DBException {
        String entityName = getEntityName(object);
        if (object instanceof ChangeTableDataStatement) {
            sql.append(((ChangeTableDataStatement) object).generateTableDeleteFrom(entityName));
        } else {
            sql.append("DELETE FROM ").append(entityName);
        }
        sql.append(getLineSeparator()).append("WHERE ");
        Collection<? extends DBSEntityAttribute> keyAttributes = getKeyAttributes(monitor, object);
        if (CommonUtils.isEmpty(keyAttributes)) {
            keyAttributes = getAllAttributes(monitor, object);
        }
        boolean hasAttr = false;
        for (DBSEntityAttribute attr : keyAttributes) {
            if (hasAttr) sql.append(" AND ");
            sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML)).append("=");
            appendDefaultValue(sql, attr);
            hasAttr = true;
        }
        sql.append(";\n");
    }
}
