
package com.dc.summer.model.text.parser.rules;


import com.dc.summer.model.text.parser.TPToken;

/**
 * A rule for detecting patterns which begin with a given
 * sequence and may end with a given sequence thereby spanning
 * multiple lines.
 */
public class MultiLineRule extends PatternRule {

	public MultiLineRule(String startSequence, String endSequence, TPToken token) {
		this(startSequence, endSequence, token, (char) 0);
	}

	public MultiLineRule(String startSequence, String endSequence, TPToken token, char escapeCharacter) {
		this(startSequence, endSequence, token, escapeCharacter, false);
	}

	public MultiLineRule(String startSequence, String endSequence, TPToken token, char escapeCharacter, boolean breaksOnEOF) {
		super(startSequence, endSequence, token, escapeCharacter, false, breaksOnEOF);
	}
}
