
package com.dc.summer.model.sql.task;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.meta.IPropertyValueValidator;
import com.dc.summer.model.struct.DBSObject;


public abstract class SQLToolTaskVersionValidator<SETTINGS extends SQLToolExecuteSettings<? extends DBSObject>, Object>
        implements IPropertyValueValidator<SETTINGS, Object> {

    @Override
    public boolean isValidValue(SETTINGS settings, Object value) throws IllegalArgumentException {
        if (!settings.getObjectList().isEmpty()) {
            DBPDataSource dataSource = settings.getObjectList().get(0).getDataSource();
            if (dataSource instanceof JDBCDataSource) {
                return ((JDBCDataSource) dataSource).isServerVersionAtLeast(getMajorVersion(), getMinorVersion());
            }
        }
        return false;
    }

    public abstract int getMajorVersion();

    public abstract int getMinorVersion();
}
