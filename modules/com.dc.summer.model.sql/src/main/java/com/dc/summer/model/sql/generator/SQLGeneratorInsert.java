
package com.dc.summer.model.sql.generator;

import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.DBException;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAttribute;

public class SQL<PERSON><PERSON>atorInsert extends SQLGeneratorTable {

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBSEntity object) throws DBException {

        sql.append("INSERT INTO ").append(getEntityName(object)).append(getLineSeparator()).append("(");
        boolean hasAttr = false;
        for (DBSEntityAttribute attr : getAllAttributes(monitor, object)) {
            if (DBUtils.isPseudoAttribute(attr) || DBUtils.isHiddenObject(attr) || attr.isAutoGenerated()) {
                continue;
            }
            if (hasAttr) sql.append(", ");
            sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML));
            hasAttr = true;
        }
        sql.append(")").append(getLineSeparator()).append("VALUES(");
        hasAttr = false;
        for (DBSEntityAttribute attr : getAllAttributes(monitor, object)) {
            if (DBUtils.isPseudoAttribute(attr) || DBUtils.isHiddenObject(attr) || attr.isAutoGenerated()) {
                continue;
            }
            if (hasAttr) sql.append(", ");
            appendDefaultValue(sql, attr);
            hasAttr = true;
        }
        sql.append(");\n");
    }

}
