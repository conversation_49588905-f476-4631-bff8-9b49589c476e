
package com.dc.summer.model.sql.registry;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class SQLDialectRegistry
{
    static final String TAG_DIALECT = "dialect"; //$NON-NLS-1$

    private static SQLDialectRegistry instance = null;

    public synchronized static SQLDialectRegistry getInstance()
    {
        if (instance == null) {
            instance = new SQLDialectRegistry();
            instance.loadExtensions(Global.getExtensionRegistry());
        }
        return instance;
    }

    private final Map<String, SQLDialectDescriptor> dialects = new LinkedHashMap<>();

    private SQLDialectRegistry()
    {
    }

    private void loadExtensions(IExtensionRegistry registry)
    {
        IConfigurationElement[] extConfigs = registry.getConfigurationElementsFor(SQLDialectDescriptor.EXTENSION_ID);
        for (IConfigurationElement ext : extConfigs) {
            if (TAG_DIALECT.equals(ext.getName())) {
                SQLDialectDescriptor dialectDescriptor = new SQLDialectDescriptor(ext);
                this.dialects.put(dialectDescriptor.getId(), dialectDescriptor);
            }
        }

        for (IConfigurationElement ext : extConfigs) {
            if (TAG_DIALECT.equals(ext.getName())) {
                String dialectId = ext.getAttribute("id");
                String parentDialectId = ext.getAttribute("parent");
                if (!CommonUtils.isEmpty(dialectId) && !CommonUtils.isEmpty(parentDialectId)) {
                    SQLDialectDescriptor dialect = dialects.get(dialectId);
                    SQLDialectDescriptor parentDialect = dialects.get(parentDialectId);
                    if (dialect != null && parentDialect != null) {
                        dialect.setParentDialect(parentDialect);
                    }
                }
            }
        }
    }

    public void dispose()
    {
        dialects.clear();
    }

    public List<SQLDialectDescriptor> getDialects() {
        return new ArrayList<>(dialects.values());
    }

    public SQLDialectDescriptor getDialect(String id) {
        return dialects.get(id);
    }

    public List<SQLDialectDescriptor> getRootDialects() {
        List<SQLDialectDescriptor> roots = new ArrayList<>();
        for (SQLDialectDescriptor dd : dialects.values()) {
            if (dd.getParentDialect() == null) {
                roots.add(dd);
            }
        }
        return roots;
    }
}
