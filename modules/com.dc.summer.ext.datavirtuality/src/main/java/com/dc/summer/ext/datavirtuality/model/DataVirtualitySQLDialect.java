
package com.dc.summer.ext.datavirtuality.model;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.text.parser.TPRule;
import com.dc.summer.model.text.parser.TPRuleProvider;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.sql.parser.rules.SQLDollarQuoteRule;

import java.util.Arrays;
import java.util.List;

public class DataVirtualitySQLDialect extends GenericSQLDialect implements TPRuleProvider {

    public DataVirtualitySQLDialect() {
        super("DataVirtuality", "datavirtuality");
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        addSQLKeywords(
                Arrays.asList(
                        "SIMILAR",
                        "VIRTUAL",
                        "OPTION",
                        "TEMPORARY",
                        "LOCAL",
                        "WITHOUT"
                ));
    }

    @Override
    public void extendRules(@Nullable DBPDataSourceContainer dataSource, @NotNull List<TPRule> rules, @NotNull RulePosition position) {
        if (position == RulePosition.INITIAL || position == RulePosition.PARTITION) {
            rules.add(new SQLDollarQuoteRule(position == RulePosition.PARTITION, false, false, true));
        }
    }

}
