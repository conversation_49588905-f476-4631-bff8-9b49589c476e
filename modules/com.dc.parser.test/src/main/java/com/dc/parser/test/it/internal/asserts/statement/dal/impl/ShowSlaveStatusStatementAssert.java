package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowSlaveStatusStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowSlaveStatusStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Show slave status statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowSlaveStatusStatementAssert {

    /**
     * Assert show slave status statement is correct with expected show slave status statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual show slave status statement
     * @param expected      expected show slave status statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowSlaveStatusStatement actual, final ShowSlaveStatusStatementTestCase expected) {
        if (null != expected.getChannel()) {
            assertThat(assertContext.getText("Actual show slave status channel name assertion error: "), actual.getChannel(), is(expected.getChannel()));
        }
    }
}
