package com.dc.parser.test.it.internal.asserts.statement.comment;

import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.comments.ExpectedComment;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Iterator;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Comment assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CommentAssert {

    /**
     * Assert comment is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual SQL statement
     * @param expected      expected statement test case
     */
    public static void assertComment(final SQLCaseAssertContext assertContext, final SQLStatement actual, final SQLParserTestCase expected) {
        if (expected.getComments().isEmpty()) {
            assertEmptyComment(assertContext, actual);
        } else {
            assertCorrectComment(assertContext, actual, expected);
        }
    }

    private static void assertEmptyComment(final SQLCaseAssertContext assertContext, final SQLStatement actual) {
        if (actual instanceof AbstractSQLStatement) {
            assertTrue(((AbstractSQLStatement) actual).getCommentSegments().isEmpty(), assertContext.getText("Comment should be empty."));
        }
    }

    private static void assertCorrectComment(final SQLCaseAssertContext assertContext, final SQLStatement actual, final SQLParserTestCase expected) {
        assertInstanceOf(AbstractSQLStatement.class, actual, assertContext.getText("Comment should exist."));
        assertThat(assertContext.getText("Comments size assertion error: "), ((AbstractSQLStatement) actual).getCommentSegments().size(), is(expected.getComments().size()));
        Iterator<CommentSegment> actualIterator = ((AbstractSQLStatement) actual).getCommentSegments().iterator();
        for (ExpectedComment each : expected.getComments()) {
            assertThat(assertContext.getText("Comments assertion error: "), actualIterator.next().getText(), is(each.getText()));
        }
    }
}
