package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.window;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.ExpectedOrderByClause;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public final class ExpectedWindowItem extends AbstractExpectedSQLSegment {

    @XmlElement(name = "order-by")
    private ExpectedOrderByClause orderByClause;
}
