package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.database.ExpectedDatabase;

import javax.xml.bind.annotation.XmlElement;

/**
 * Use statement test case.
 */
@Getter
@Setter
public final class UseStatementTestCase extends SQLParserTestCase {

    @XmlElement
    private ExpectedDatabase database;
}
