package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Kill statement test case.
 */
@Getter
@Setter
public final class KillStatementTestCase extends SQLParserTestCase {

    @XmlAttribute(name = "process-id")
    private String processId;
}
