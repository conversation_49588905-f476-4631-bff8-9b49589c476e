package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.query;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.DeleteStatementTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.InsertStatementTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.SelectStatementTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.UpdateStatementTestCase;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected prepare statement query.
 */
@Getter
@Setter
public final class ExpectedPrepareStatementQuery extends AbstractExpectedSQLSegment {

    @XmlElement(name = "select")
    private SelectStatementTestCase selectClause;

    @XmlElement(name = "insert")
    private InsertStatementTestCase insertClause;

    @XmlElement(name = "update")
    private UpdateStatementTestCase updateClause;

    @XmlElement(name = "delete")
    private DeleteStatementTestCase deleteClause;
}
