package com.dc.parser.test.it.internal.asserts.statement.tcl.impl;

import com.dc.parser.model.statement.tcl.PrepareTransactionStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.tcl.PrepareTransactionTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Prepare transaction statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PrepareTransactionStatementAssert {

    /**
     * Assert prepare transaction statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual prepare transaction statement
     * @param expected      expected prepare transaction statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final PrepareTransactionStatement actual, final PrepareTransactionTestCase expected) {
    }
}
