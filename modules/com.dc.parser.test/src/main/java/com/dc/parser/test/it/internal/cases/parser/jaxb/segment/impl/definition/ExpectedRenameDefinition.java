package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected rename table definition.
 */
@Getter
@Setter
public final class ExpectedRenameDefinition extends AbstractExpectedSQLSegment {

    @XmlElement(name = "table")
    private ExpectedSimpleTable table;

    @XmlElement(name = "rename-table")
    private ExpectedSimpleTable renameTable;
}
