package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.NotifyStmtStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.NotifyStmtStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NotifyStmtStatementAssert {

    /**
     * Assert notify statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual notify statement
     * @param expected      expected notify statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final NotifyStmtStatement actual, final NotifyStmtStatementTestCase expected) {
    }
}
