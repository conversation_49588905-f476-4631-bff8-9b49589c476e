package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.CheckTableStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.CheckTableStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Check table statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CheckTableStatementAssert {

    /**
     * Assert check table statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual check table statement
     * @param expected      expected check table statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CheckTableStatement actual, final CheckTableStatementTestCase expected) {
        TableAssert.assertIs(assertContext, actual.getTables(), expected.getTables());
    }
}
