package com.dc.parser.test.it.internal.asserts.segment.packages;

import com.dc.parser.model.segment.ddl.packages.PackageSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.asserts.segment.owner.OwnerAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.packages.ExpectedPackage;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Package assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PackageAssert {

    /**
     * Assert actual package segment is correct with expected package.
     *
     * @param assertContext assert context
     * @param actual        actual package segment
     * @param expected      expected package
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final PackageSegment actual, final ExpectedPackage expected) {
        assertNotNull(expected, assertContext.getText("Package should exist."));
        IdentifierValueAssert.assertIs(assertContext, actual.getIdentifier(), expected, "Package");
        if (null == expected.getOwner()) {
            assertFalse(actual.getOwner().isPresent(), assertContext.getText("Actual owner should not exist."));
        } else {
            assertTrue(actual.getOwner().isPresent(), assertContext.getText("Actual owner should exist."));
            OwnerAssert.assertIs(assertContext, actual.getOwner().get(), expected.getOwner());
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
