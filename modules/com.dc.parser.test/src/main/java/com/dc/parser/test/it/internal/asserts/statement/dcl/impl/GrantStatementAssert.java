package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.ext.mssql.statement.dcl.SQLServerGrantStatement;
import com.dc.parser.ext.mysql.statement.dcl.MySQLGrantStatement;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.statement.dcl.impl.mysql.MySQLGrantStatementAssert;
import com.dc.parser.test.it.internal.asserts.statement.dcl.impl.sqlserver.SQLServerGrantStatementAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.GrantStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Grant statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class GrantStatementAssert {

    /**
     * Assert grant statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual grant statement
     * @param expected      expected grant statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final GrantStatement actual, final GrantStatementTestCase expected) {
        if (actual instanceof MySQLGrantStatement) {
            MySQLGrantStatementAssert.assertIs(assertContext, (MySQLGrantStatement) actual, expected);
        } else if (actual instanceof SQLServerGrantStatement) {
            SQLServerGrantStatementAssert.assertIs(assertContext, (SQLServerGrantStatement) actual, expected);
        }
    }
}
