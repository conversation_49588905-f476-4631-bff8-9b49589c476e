package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ResetParameterStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ResetParameterStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Reset parameter statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ResetParameterStatementAssert {

    /**
     * Assert reset parameter statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual reset parameter statement
     * @param expected      expected reset parameter statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ResetParameterStatement actual, final ResetParameterStatementTestCase expected) {
        assertThat(assertContext.getText("Configuration parameter assertion error: "), actual.getConfigurationParameter(), is(expected.getConfigurationParameter()));
    }
}
