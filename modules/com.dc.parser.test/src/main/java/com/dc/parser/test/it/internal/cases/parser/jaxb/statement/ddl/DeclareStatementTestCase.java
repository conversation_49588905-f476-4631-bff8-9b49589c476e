package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.cursor.ExpectedCursorName;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.SelectStatementTestCase;

import javax.xml.bind.annotation.XmlElement;

/**
 * Declare statement test case.
 */
@Getter
@Setter
public final class DeclareStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "cursor-name")
    private ExpectedCursorName cursorName;

    @XmlElement(name = "select")
    private SelectStatementTestCase selectTestCase;
}
