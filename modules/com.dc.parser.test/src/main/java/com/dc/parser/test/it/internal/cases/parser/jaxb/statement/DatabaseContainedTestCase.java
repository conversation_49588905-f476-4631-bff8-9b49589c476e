package com.dc.parser.test.it.internal.cases.parser.jaxb.statement;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.database.ExpectedDatabase;

import javax.xml.bind.annotation.XmlElement;

/**
 * Database contained test case.
 */
@Getter
@Setter
public abstract class DatabaseContainedTestCase extends SQLParserTestCase {

    @XmlElement
    private ExpectedDatabase database;
}
