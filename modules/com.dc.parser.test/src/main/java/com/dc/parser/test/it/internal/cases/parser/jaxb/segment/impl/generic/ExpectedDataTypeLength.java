package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedBaseSimpleExpression;
import lombok.Getter;
import lombok.Setter;

/**
 * Expected data type length.
 */
@Getter
@Setter
public final class ExpectedDataTypeLength extends ExpectedBaseSimpleExpression {

    private Integer precision;

    private int scale;

    private String type;
}
