package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.subquery;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedDelimiterSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedSubquery;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.ExpectedProjection;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected subquery projection.
 */
@Getter
@Setter
public final class ExpectedSubqueryProjection extends AbstractExpectedDelimiterSQLSegment implements ExpectedProjection {

    @XmlAttribute
    private String alias;

    @XmlElement
    private ExpectedSubquery subquery;

    @XmlAttribute
    private String text;

    @XmlAttribute(name = "literal-text")
    private String literalText;
}
