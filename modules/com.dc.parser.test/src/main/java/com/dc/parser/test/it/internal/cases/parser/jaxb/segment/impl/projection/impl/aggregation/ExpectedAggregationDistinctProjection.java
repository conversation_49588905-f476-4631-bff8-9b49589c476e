package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.aggregation;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected aggregation distinct projection.
 */
@Getter
@Setter
public final class ExpectedAggregationDistinctProjection extends ExpectedAggregationProjection {

    @XmlAttribute(name = "distinct-inner-expression")
    private String distinctInnerExpression;
}
