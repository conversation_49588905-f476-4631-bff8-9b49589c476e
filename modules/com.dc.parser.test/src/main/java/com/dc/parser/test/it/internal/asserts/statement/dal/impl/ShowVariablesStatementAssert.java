package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowVariablesStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.show.ShowFilterAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowVariablesStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Show variables statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowVariablesStatementAssert {

    /**
     * Assert show variables statement is correct with expected show variables statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual show variables statement
     * @param expected      expected show variables statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowVariablesStatement actual, final ShowVariablesStatementTestCase expected) {
        if (actual.getFilter().isPresent()) {
            ShowFilterAssert.assertIs(assertContext, actual.getFilter().get(), expected.getFilter());
        }
    }
}
