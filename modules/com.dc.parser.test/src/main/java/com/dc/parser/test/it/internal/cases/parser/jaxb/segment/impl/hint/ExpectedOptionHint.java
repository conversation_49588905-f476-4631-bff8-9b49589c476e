package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.hint;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected option hint.
 **/
@RequiredArgsConstructor
@Getter
public class ExpectedOptionHint extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String text;
}
