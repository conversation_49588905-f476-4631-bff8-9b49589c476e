package com.dc.parser.test.it.internal.asserts.segment.insert;

import com.dc.parser.model.segment.dml.assignment.ColumnAssignmentSegment;
import com.dc.parser.model.segment.dml.column.OnDuplicateKeyColumnsSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.assignment.AssignmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.insert.ExpectedOnDuplicateKeyColumns;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * On duplicate key columns assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OnDuplicateKeyColumnsAssert {

    /**
     * Assert actual on duplicate key columns segment is correct with expected on duplicate key columns.
     *
     * @param assertContext assert context
     * @param actual        actual on duplicate key columns segment
     * @param expected      expected on duplicate key columns
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final OnDuplicateKeyColumnsSegment actual, final ExpectedOnDuplicateKeyColumns expected) {
        assertNotNull(expected, assertContext.getText("On duplicate key columns should exist."));
        assertThat(assertContext.getText("On duplicate key columns size assertion error: "), actual.getColumns().size(), is(expected.getAssignments().size()));
        int count = 0;
        for (ColumnAssignmentSegment each : actual.getColumns()) {
            AssignmentAssert.assertIs(assertContext, each, expected.getAssignments().get(count));
            count++;
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
