package com.dc.parser.test.it.internal.asserts.segment.prepare;

import com.dc.parser.model.segment.dml.prepare.PrepareStatementQuerySegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.DeleteStatementAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.InsertStatementAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.SelectStatementAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.UpdateStatementAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.query.ExpectedPrepareStatementQuery;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Prepare statement query assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PrepareStatementQueryAssert {

    /**
     * Assert actual prepare statement query segment is correct with expected prepare statement query.
     *
     * @param assertContext assert context
     * @param actual        actual prepare statement query segment
     * @param expected      expected prepare statement query
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final PrepareStatementQuerySegment actual, final ExpectedPrepareStatementQuery expected) {
        assertSelect(assertContext, actual, expected);
        assertInsert(assertContext, actual, expected);
        assertUpdate(assertContext, actual, expected);
        assertDelete(assertContext, actual, expected);
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }

    private static void assertSelect(final SQLCaseAssertContext assertContext, final PrepareStatementQuerySegment actual, final ExpectedPrepareStatementQuery expected) {
        if (null == expected.getSelectClause()) {
            assertFalse(actual.getSelect().isPresent(), assertContext.getText("Actual select statement should not exist."));
        } else {
            assertTrue(actual.getSelect().isPresent(), assertContext.getText("Actual select statement should exist."));
            SelectStatementAssert.assertIs(assertContext, actual.getSelect().get(), expected.getSelectClause());
        }
    }

    private static void assertInsert(final SQLCaseAssertContext assertContext, final PrepareStatementQuerySegment actual, final ExpectedPrepareStatementQuery expected) {
        if (null == expected.getInsertClause()) {
            assertFalse(actual.getInsert().isPresent(), assertContext.getText("Actual insert statement should not exist."));
        } else {
            assertTrue(actual.getInsert().isPresent(), assertContext.getText("Actual insert statement should exist."));
            InsertStatementAssert.assertIs(assertContext, actual.getInsert().get(), expected.getInsertClause());
        }
    }

    private static void assertUpdate(final SQLCaseAssertContext assertContext, final PrepareStatementQuerySegment actual, final ExpectedPrepareStatementQuery expected) {
        if (null == expected.getUpdateClause()) {
            assertFalse(actual.getUpdate().isPresent(), assertContext.getText("Actual update statement should not exist."));
        } else {
            assertTrue(actual.getUpdate().isPresent(), assertContext.getText("Actual update statement should exist."));
            UpdateStatementAssert.assertIs(assertContext, actual.getUpdate().get(), expected.getUpdateClause());
        }
    }

    private static void assertDelete(final SQLCaseAssertContext assertContext, final PrepareStatementQuerySegment actual, final ExpectedPrepareStatementQuery expected) {
        if (null == expected.getDeleteClause()) {
            assertFalse(actual.getDelete().isPresent(), assertContext.getText("Actual delete statement should not exist."));
        } else {
            assertTrue(actual.getDelete().isPresent(), assertContext.getText("Actual delete statement should exist."));
            DeleteStatementAssert.assertIs(assertContext, actual.getDelete().get(), expected.getDeleteClause());
        }
    }
}
