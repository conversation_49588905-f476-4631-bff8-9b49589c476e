package com.dc.parser.test.it.internal.asserts.segment.index;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.asserts.segment.owner.OwnerAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedIndex;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Index assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IndexAssert {

    /**
     * Assert actual index segment is correct with expected index.
     *
     * @param assertContext assert context
     * @param actual        actual index segment
     * @param expected      expected index
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final IndexSegment actual, final ExpectedIndex expected) {
        assertNotNull(expected, assertContext.getText("Index should exist."));
        IdentifierValueAssert.assertIs(assertContext, actual.getIndexName().getIdentifier(), expected, "Index");
        if (null == expected.getOwner()) {
            assertFalse(actual.getOwner().isPresent(), assertContext.getText("Actual owner should not exist."));
        } else {
            assertTrue(actual.getOwner().isPresent(), assertContext.getText("Actual owner should exist."));
            OwnerAssert.assertIs(assertContext, actual.getOwner().get(), expected.getOwner());
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
