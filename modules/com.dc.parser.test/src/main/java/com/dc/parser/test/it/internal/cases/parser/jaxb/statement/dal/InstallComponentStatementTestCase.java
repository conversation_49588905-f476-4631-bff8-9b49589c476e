package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.component.ExpectedComponent;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Install component statement test case.
 */
@Getter
public final class InstallComponentStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "component")
    private final List<ExpectedComponent> components = new LinkedList<>();
}
