package com.dc.parser.test.it.internal.asserts.segment.show;

import com.dc.parser.model.segment.dal.ShowFilterSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.where.WhereClauseAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.show.ExpectedShowFilter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Show filter assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowFilterAssert {

    /**
     * Assert actual show filter segment is correct with expected schema.
     *
     * @param assertContext assert context
     * @param actual        actual show filter segment
     * @param expected      expected show filter
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowFilterSegment actual, final ExpectedShowFilter expected) {
        if (actual.getLike().isPresent()) {
            assertThat(assertContext.getText("Show filter like segment pattern content assert error."),
                    actual.getLike().get().getPattern(), is(expected.getLike().getPattern()));
            SQLSegmentAssert.assertIs(assertContext, actual.getLike().get(), expected.getLike());
        }
        if (actual.getWhere().isPresent()) {
            WhereClauseAssert.assertIs(assertContext, actual.getWhere().get(), expected.getWhere());
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
