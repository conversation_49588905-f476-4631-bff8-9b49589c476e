package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.exec;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedOwner;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected execute clause.
 */
@Getter
@Setter
public final class ExpectedExecClause extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String name;

    @XmlElement
    private ExpectedOwner owner;

    @XmlElement(name = "parameter")
    private final List<ExpectedExpression> parameters = new LinkedList<>();
}
