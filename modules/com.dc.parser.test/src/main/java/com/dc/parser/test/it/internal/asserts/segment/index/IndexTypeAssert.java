package com.dc.parser.test.it.internal.asserts.segment.index;

import com.dc.parser.model.segment.ddl.index.IndexTypeSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.asserts.segment.owner.OwnerAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedIndexType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Index type assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IndexTypeAssert {

    /**
     * Assert actual index type segment is correct with expected index type.
     *
     * @param assertContext assert context
     * @param actual        actual index type segment
     * @param expected      expected index type
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final IndexTypeSegment actual, final ExpectedIndexType expected) {
        assertNotNull(expected, assertContext.getText("IndexType should exist."));
        IdentifierValueAssert.assertIs(assertContext, actual.getIdentifier(), expected, "IndexType");
        if (null == expected.getOwner()) {
            assertFalse(actual.getOwner().isPresent(), assertContext.getText("Actual owner should not exist."));
        } else {
            assertTrue(actual.getOwner().isPresent(), assertContext.getText("Actual owner should exist."));
            OwnerAssert.assertIs(assertContext, actual.getOwner().get(), expected.getOwner());
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
