package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.CursorStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.SelectStatementAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.CursorStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Cursor statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CursorStatementAssert {

    /**
     * Assert cursor statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual cursor statement
     * @param expected      expected cursor statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CursorStatement actual, final CursorStatementTestCase expected) {
        assertCursorName(assertContext, actual, expected);
        assertSelect(assertContext, actual, expected);
    }

    private static void assertCursorName(final SQLCaseAssertContext assertContext, final CursorStatement actual, final CursorStatementTestCase expected) {
        IdentifierValueAssert.assertIs(assertContext, actual.getCursorName().getIdentifier(), expected.getCursorName(), "Cursor");
        SQLSegmentAssert.assertIs(assertContext, actual.getCursorName(), expected.getCursorName());
    }

    private static void assertSelect(final SQLCaseAssertContext assertContext, final CursorStatement actual, final CursorStatementTestCase expected) {
        if (null == expected.getSelectTestCase()) {
            assertNull(actual.getSelect(), assertContext.getText("Actual select statement should not exist."));
        } else {
            assertNotNull(actual.getSelect(), assertContext.getText("Actual select statement should exist."));
            SelectStatementAssert.assertIs(assertContext, actual.getSelect(), expected.getSelectTestCase());
        }
    }
}
