package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.user.ExpectedUser;

/**
 * Set user statement test case.
 */
@Getter
@Setter
public final class SetUserStatementTestCase extends SQLParserTestCase {

    private ExpectedUser user;
}
