package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.dostatement;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.complex.ExpectedCommonExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedLiteralExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedParameterMarkerExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.function.ExpectedFunction;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected do parameter.
 */
@Getter
@Setter
public final class ExpectedDoParameter extends AbstractExpectedSQLSegment {

    @XmlElement(name = "parameter-marker-expression")
    private ExpectedParameterMarkerExpression parameterMarkerExpression;

    @XmlElement(name = "literal-expression")
    private ExpectedLiteralExpression literalExpression;

    @XmlElement(name = "common-expression")
    private ExpectedCommonExpression commonExpression;

    @XmlElement
    private ExpectedFunction function;
}
