package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected modify column definition.
 */
@Getter
@Setter
public final class ExpectedModifyColumnDefinition extends AbstractExpectedSQLSegment {

    @XmlElement(name = "column-definition")
    private ExpectedColumnDefinition columnDefinition;

    @XmlElement(name = "column-position")
    private ExpectedColumnPosition columnPosition;
}
