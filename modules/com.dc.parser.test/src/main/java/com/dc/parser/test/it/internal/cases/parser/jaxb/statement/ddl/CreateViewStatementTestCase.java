package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.SelectStatementTestCase;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Create view statement test case.
 */
@Getter
@Setter
public final class CreateViewStatementTestCase extends SQLParserTestCase {

    @XmlAttribute(name = "view-definition")
    private String viewDefinition;

    @XmlElement
    private ExpectedSimpleTable view;

    @XmlElement(name = "select")
    private SelectStatementTestCase selectStatement;
}
