package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected rename index definition.
 */
@Getter
@Setter
public final class ExpectedRenameIndexDefinition extends AbstractExpectedSQLSegment {

    @XmlElement(name = "index-definition")
    private ExpectedIndexDefinition indexDefinition;

    @XmlElement(name = "rename-index-definition")
    private ExpectedIndexDefinition renameIndexDefinition;
}
