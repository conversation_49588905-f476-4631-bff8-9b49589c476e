package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedIdentifierSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected load table index.
 */
@Getter
@Setter
public final class ExpectedLoadTableIndex extends AbstractExpectedIdentifierSQLSegment {

    @XmlElement
    private ExpectedSimpleTable table;

    @XmlElement(name = "index")
    private final List<ExpectedIndex> indexNames = new LinkedList<>();

    @XmlElement(name = "partition")
    private final List<ExpectedPartition> partitions = new LinkedList<>();
}
