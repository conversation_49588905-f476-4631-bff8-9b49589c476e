package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected match expression.
 */
@Getter
@Setter
public class ExpectedMatchExpression extends AbstractExpectedSQLSegment implements ExpectedExpressionSegment {

    @XmlElement
    private final List<ExpectedColumn> columns = new LinkedList<>();

    @XmlElement
    private ExpectedExpression expr;
}
