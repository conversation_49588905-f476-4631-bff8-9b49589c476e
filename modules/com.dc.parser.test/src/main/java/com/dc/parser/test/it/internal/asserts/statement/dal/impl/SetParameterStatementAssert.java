package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.segment.dal.VariableSegment;
import com.dc.parser.model.statement.dal.SetStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.assignment.ExpectedVariable;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.SetParameterStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Set parameter statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SetParameterStatementAssert {

    /**
     * Assert set parameter statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual set parameter statement
     * @param expected      expected parameter statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SetStatement actual, final SetParameterStatementTestCase expected) {
        assertThat(assertContext.getText("variableAssign size assertion error: "), actual.getVariableAssigns().size(), is(expected.getValueAssigns().size()));
        if (!expected.getValueAssigns().isEmpty()) {
            for (int i = 0; i < expected.getValueAssigns().size(); i++) {
                assertVariable(assertContext, actual.getVariableAssigns().get(i).getVariable(), expected.getValueAssigns().get(i).getParameter());
                assertThat(assertContext.getText("variableAssign assert error."), actual.getVariableAssigns().get(i).getAssignValue(), is(expected.getValueAssigns().get(i).getValue()));
            }
        }
    }

    private static void assertVariable(final SQLCaseAssertContext assertContext, final VariableSegment actual, final ExpectedVariable expected) {
        assertThat(assertContext.getText("variable assertion error: "), actual.getVariable(), is(expected.getName()));
        assertThat(assertContext.getText("scope assertion error: "), actual.getScope().orElse(null), is(expected.getScope()));
    }
}
