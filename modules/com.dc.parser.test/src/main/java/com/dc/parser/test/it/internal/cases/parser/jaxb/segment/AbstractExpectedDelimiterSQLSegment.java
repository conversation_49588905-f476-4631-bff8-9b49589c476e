package com.dc.parser.test.it.internal.cases.parser.jaxb.segment;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Abstract expected delimiter SQL segment.
 */
@Getter
@Setter
public abstract class AbstractExpectedDelimiterSQLSegment extends AbstractExpectedSQLSegment implements ExpectedDelimiterSQLSegment {

    @XmlAttribute(name = "start-delimiter")
    private String startDelimiter = "";

    @XmlAttribute(name = "end-delimiter")
    private String endDelimiter = "";
}
