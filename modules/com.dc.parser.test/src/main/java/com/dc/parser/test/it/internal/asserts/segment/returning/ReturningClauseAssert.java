package com.dc.parser.test.it.internal.asserts.segment.returning;

import com.dc.parser.model.segment.dml.ReturningSegment;
import com.dc.parser.model.segment.dml.item.ProjectionsSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.projection.ProjectionAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.insert.ExpectedReturningClause;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Returning clause assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ReturningClauseAssert {

    /**
     * Assert actual returning segment is correct with expected returning clause.
     *
     * @param assertContext assert context
     * @param actual        actual returning segment
     * @param expected      expected returning clause
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ReturningSegment actual, final ExpectedReturningClause expected) {
        ProjectionsSegment actualProjections = actual.getProjections();
        if (null != actualProjections.getProjections() || expected.getProjections().getSize() > 0) {
            ProjectionAssert.assertIs(assertContext, actualProjections, expected.getProjections());
        }
    }
}
