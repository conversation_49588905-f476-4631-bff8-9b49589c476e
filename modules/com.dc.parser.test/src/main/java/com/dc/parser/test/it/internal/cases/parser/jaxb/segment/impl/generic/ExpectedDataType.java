package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedBaseSimpleExpression;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected data type.
 */
@Getter
@Setter
public final class ExpectedDataType extends ExpectedBaseSimpleExpression {

    @XmlAttribute
    private String value;

    @XmlElement
    private ExpectedDataTypeLength dataLength;
}
