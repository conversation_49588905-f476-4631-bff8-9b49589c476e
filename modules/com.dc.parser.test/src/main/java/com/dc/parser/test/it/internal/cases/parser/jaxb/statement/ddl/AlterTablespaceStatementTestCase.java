package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.tablespace.ExpectedTablespace;

import javax.xml.bind.annotation.XmlElement;

/**
 * Alter tablespace statement test case.
 */
@Getter
@Setter
public final class AlterTablespaceStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "tablespace")
    private ExpectedTablespace tablespace;

    @XmlElement(name = "rename")
    private ExpectedTablespace renameTablespace;
}
