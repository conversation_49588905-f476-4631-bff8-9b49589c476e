package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.ExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.aggregation.ExpectedAggregationDistinctProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.aggregation.ExpectedAggregationProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.column.ExpectedColumnProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.expression.ExpectedExpressionProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.shorthand.ExpectedShorthandProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.subquery.ExpectedSubqueryProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.top.ExpectedTopProjection;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;

@Getter
@Setter
public final class ExpectedProjections extends AbstractExpectedSQLSegment {

    @XmlAttribute(name = "distinct-row")
    private boolean distinctRow;

    @XmlElement(name = "shorthand-projection")
    private final Collection<ExpectedShorthandProjection> shorthandProjections = new LinkedList<>();

    @XmlElement(name = "column-projection")
    private final Collection<ExpectedColumnProjection> columnProjections = new LinkedList<>();

    @XmlElement(name = "aggregation-projection")
    private final Collection<ExpectedAggregationProjection> aggregationProjections = new LinkedList<>();

    @XmlElement(name = "aggregation-distinct-projection")
    private final Collection<ExpectedAggregationDistinctProjection> aggregationDistinctProjections = new LinkedList<>();

    @XmlElement(name = "expression-projection")
    private final Collection<ExpectedExpressionProjection> expressionProjections = new LinkedList<>();

    @XmlElement(name = "top-projection")
    private final Collection<ExpectedTopProjection> topProjections = new LinkedList<>();

    @XmlElement(name = "subquery-projection")
    private final Collection<ExpectedSubqueryProjection> subqueryProjections = new LinkedList<>();

    /**
     * Get size.
     *
     * @return size
     */
    public int getSize() {
        return shorthandProjections.size() + columnProjections.size() + aggregationProjections.size() + aggregationDistinctProjections.size()
                + expressionProjections.size() + topProjections.size() + subqueryProjections.size();
    }

    /**
     * Get expected projections.
     *
     * @return expected projections
     */
    public List<ExpectedProjection> getExpectedProjections() {
        List<ExpectedProjection> result = new LinkedList<>();
        result.addAll(shorthandProjections);
        result.addAll(columnProjections);
        result.addAll(aggregationProjections);
        result.addAll(aggregationDistinctProjections);
        result.addAll(expressionProjections);
        result.addAll(topProjections);
        result.addAll(subqueryProjections);
        result.sort(Comparator.comparingInt(ExpectedSQLSegment::getStartIndex));
        return result;
    }
}
