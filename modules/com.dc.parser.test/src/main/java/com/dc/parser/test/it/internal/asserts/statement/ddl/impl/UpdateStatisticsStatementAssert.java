package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.UpdateStatisticsStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.index.IndexAssert;
import com.dc.parser.test.it.internal.asserts.segment.statistics.StatisticsStrategyAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedIndex;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.UpdateStatisticsStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Update statistics statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class UpdateStatisticsStatementAssert {

    /**
     * Assert update statistics statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual update statistics statement
     * @param expected      expected update statistics statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final UpdateStatisticsStatement actual, final UpdateStatisticsStatementTestCase expected) {
        assertTable(assertContext, actual, expected);
        assertIndex(assertContext, actual, expected);
        assertStrategy(assertContext, actual, expected);
    }

    private static void assertTable(final SQLCaseAssertContext assertContext, final UpdateStatisticsStatement actual, final UpdateStatisticsStatementTestCase expected) {
        if (null == expected.getTable()) {
            assertNull(actual.getTable(), assertContext.getText("Actual table segment should not exist."));
        } else {
            assertNotNull(actual.getTable(), assertContext.getText("Actual table segment should exist."));
            TableAssert.assertIs(assertContext, actual.getTable(), expected.getTable());
        }
    }

    private static void assertIndex(final SQLCaseAssertContext assertContext, final UpdateStatisticsStatement actual, final UpdateStatisticsStatementTestCase expected) {
        if (null == expected.getIndexes()) {
            assertNull(actual.getIndexes(), assertContext.getText("Actual index segment should not exist."));
        } else {
            int count = 0;
            assertNotNull(actual.getIndexes(), assertContext.getText("Actual index segment should exist."));
            for (ExpectedIndex index : expected.getIndexes()) {
                IndexAssert.assertIs(assertContext, actual.getIndexes().get(count), index);
                count++;
            }
        }
    }

    private static void assertStrategy(final SQLCaseAssertContext assertContext, final UpdateStatisticsStatement actual, final UpdateStatisticsStatementTestCase expected) {
        if (null == expected.getStrategy()) {
            assertNull(actual.getStrategy(), assertContext.getText("Actual strategy segment should not exist."));
        } else {
            assertNotNull(actual.getStrategy(), assertContext.getText("Actual strategy segment should exist."));
            StatisticsStrategyAssert.assertIs(assertContext, actual.getStrategy(), expected.getStrategy());
        }
    }

}
