package com.dc.parser.test.it.internal.cases.sql.registry;

import com.dc.parser.test.it.internal.cases.sql.SQLCases;
import com.dc.parser.test.it.internal.cases.sql.loader.SQLCaseLoaderCallback;
import com.dc.parser.test.it.internal.loader.CaseLoaderTemplate;
import lombok.Getter;

/**
 * Unsupported SQL cases registry.
 */
@Getter
public final class UnsupportedSQLCasesRegistry {

    private static final UnsupportedSQLCasesRegistry INSTANCE = new UnsupportedSQLCasesRegistry();

    private final SQLCases cases;

    private UnsupportedSQLCasesRegistry() {
        cases = new SQLCases(CaseLoaderTemplate.load("sql/unsupported/", new SQLCaseLoaderCallback()));
    }

    /**
     * Get singleton instance.
     *
     * @return singleton instance
     */
    public static UnsupportedSQLCasesRegistry getInstance() {
        return INSTANCE;
    }
}
