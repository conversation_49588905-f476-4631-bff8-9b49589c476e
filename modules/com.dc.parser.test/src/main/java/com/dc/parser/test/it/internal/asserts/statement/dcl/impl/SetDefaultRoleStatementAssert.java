package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.model.statement.dcl.SetDefaultRoleStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.SetDefaultRoleStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Set default role statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SetDefaultRoleStatementAssert {

    /**
     * Assert set role statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual set role statement
     * @param expected      expected set role statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SetDefaultRoleStatement actual, final SetDefaultRoleStatementTestCase expected) {
    }
}
