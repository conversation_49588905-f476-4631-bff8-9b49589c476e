package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Reset parameter statement test case for PostgreSQL/openGauss.
 */
@Getter
@Setter
public final class ResetParameterStatementTestCase extends SQLParserTestCase {

    @XmlAttribute(name = "configuration-parameter")
    private String configurationParameter;
}
