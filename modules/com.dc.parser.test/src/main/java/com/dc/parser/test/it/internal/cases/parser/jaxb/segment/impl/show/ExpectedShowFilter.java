package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.show;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.like.ExpectedLikeClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.where.ExpectedWhereClause;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected show filter segment.
 */
@Getter
@Setter
public final class ExpectedShowFilter extends AbstractExpectedSQLSegment {

    @XmlElement
    private ExpectedLikeClause like;

    @XmlElement
    private ExpectedWhereClause where;
}
