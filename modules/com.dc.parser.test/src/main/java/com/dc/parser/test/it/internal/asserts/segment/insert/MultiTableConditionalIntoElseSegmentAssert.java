package com.dc.parser.test.it.internal.asserts.segment.insert;

import com.dc.parser.model.segment.dml.table.MultiTableConditionalIntoElseSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.InsertStatementAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.insert.ExpectedMultiTableConditionalIntoElseClause;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Multi table conditional into else segment assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MultiTableConditionalIntoElseSegmentAssert {

    /**
     * Assert actual multi table conditional into else segment is correct with expected multi table conditional into else segment.
     *
     * @param assertContext assert context
     * @param actual        actual multi table conditional into else segment
     * @param expected      expected multi table conditional into else segment
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final MultiTableConditionalIntoElseSegment actual, final ExpectedMultiTableConditionalIntoElseClause expected) {
        assertThat(assertContext.getText("Multi table conditional into else segment' insert values size assertion error: "), actual.getInsertStatements().size(),
                is(expected.getInsertTestCases().size()));
        int count = 0;
        for (InsertStatement each : actual.getInsertStatements()) {
            InsertStatementAssert.assertIs(assertContext, each, expected.getInsertTestCases().get(count));
            SQLSegmentAssert.assertIs(assertContext, actual, expected);
            count++;
        }
    }
}
