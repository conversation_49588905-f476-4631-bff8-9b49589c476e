package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.segment.dal.ResetMasterOptionSegment;
import com.dc.parser.model.segment.dal.ResetOptionSegment;
import com.dc.parser.model.segment.dal.ResetSlaveOptionSegment;
import com.dc.parser.model.statement.dal.ResetStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.reset.ExpectedResetOptionSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ResetStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Reset statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ResetStatementAssert {

    /**
     * Assert reset statement is correct with expected reset statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual reset statement
     * @param expected      expected reset statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ResetStatement actual, final ResetStatementTestCase expected) {
        assertThat(assertContext.getText("Actual options size assertion error: "), actual.getOptions().size(), is(expected.getOptions().size()));
        assertOptions(assertContext, actual.getOptions(), expected.getOptions());
    }

    private static void assertOptions(final SQLCaseAssertContext assertContext, final List<ResetOptionSegment> actual, final List<ExpectedResetOptionSegment> expected) {
        int i = 0;
        for (ExpectedResetOptionSegment each : expected) {
            SQLSegmentAssert.assertIs(assertContext, actual.get(i), each);
            if (each.isMaster()) {
                assertMasterOption(assertContext, (ResetMasterOptionSegment) actual.get(i), each);
            } else {
                assertSlaveOption(assertContext, (ResetSlaveOptionSegment) actual.get(i), each);
            }
            i++;
        }
    }

    private static void assertMasterOption(final SQLCaseAssertContext assertContext, final ResetMasterOptionSegment actual, final ExpectedResetOptionSegment expected) {
        if (null != expected.getBinaryLogFileIndexNumber()) {
            assertThat(assertContext.getText("Actual reset master binlog index does not match: "), actual.getBinaryLogFileIndexNumber(), is(expected.getBinaryLogFileIndexNumber()));
        }
    }

    private static void assertSlaveOption(final SQLCaseAssertContext assertContext, final ResetSlaveOptionSegment actual, final ExpectedResetOptionSegment expected) {
        assertThat(assertContext.getText("Actual reset slave all does not match: "), actual.isAll(), is(expected.isAll()));
        if (null != expected.getChannel()) {
            assertThat(assertContext.getText("Actual reset slave channel does not match: "), actual.getChannelOption(), is(expected.getChannel()));
        }
    }
}
