package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.dostatement.ExpectedDoParameter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Do statement test case.
 */
@Getter
public final class DoStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "parameter")
    private final List<ExpectedDoParameter> doParameters = new LinkedList<>();
}
