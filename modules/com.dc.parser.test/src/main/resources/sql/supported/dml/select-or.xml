<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_or_with_same_sharding_columns"
              value="SELECT * FROM t_order WHERE order_id = ? OR order_id = ?"/>
    <sql-case id="select_or_with_different_sharding_columns"
              value="SELECT * FROM t_order WHERE order_id = ? OR user_id = ?"/>
    <sql-case id="select_or_with_none_sharding_columns" value="SELECT * FROM t_order WHERE order_id = ? OR status = ?"/>
    <sql-case id="select_or_mix_and_for_simple_pattern"
              value="SELECT * FROM t_order WHERE (order_id = ? OR status = ?) AND user_id = ?"/>
    <sql-case id="select_or_mix_and_for_complex_pattern"
              value="SELECT * FROM t_order WHERE ((status = ? AND (order_id = ? OR (order_id = ?)) AND (user_id = ? OR (user_id = ?))))"/>
    <sql-case id="select_or_mix_and_with_binding_tables"
              value="SELECT i.* FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE (o.order_id = ? OR o.order_id = ?) AND o.user_id = ?"/>
    <sql-case id="select_or_mix_and_with_binding_and_broadcast_tables"
              value="SELECT i.* FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id JOIN t_broadcast_table c ON o.status = c.status WHERE (o.order_id = ? OR o.order_id = ?) AND o.user_id = ? AND o.status = ?"/>
</sql-cases>
