<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_with_subquery_factoring"
              value="WITH dept_costs AS (SELECT department_name, SUM(salary) dept_total FROM departments d GROUP BY department_name) SELECT * FROM dept_costs WHERE dept_total > 304500 ORDER BY department_name"
              db-types="Oracle, SQLServer"/>
    <sql-case id="select_with_subquery_factoring_with_binding_tables_without_join"
              value="WITH dept_costs AS (SELECT department_name, SUM(salary) dept_total FROM employees e, departments d WHERE e.department_id = d.department_id GROUP BY department_name), avg_cost AS (SELECT SUM(dept_total)/COUNT(*) avg FROM dept_costs) SELECT * FROM dept_costs WHERE dept_total > (SELECT avg FROM avg_cost) ORDER BY department_name"
              db-types="Oracle, SQLServer"/>
    <sql-case id="select_with_subquery_factoring_with_search_depth_first"
              value="WITH org_chart (eid, emp_last, mgr_id, reportLevel, salary, job_id) AS (SELECT employee_id, last_name, manager_id, reportLevel, salary, job_id FROM employees WHERE manager_id IS NULL) SEARCH DEPTH FIRST BY emp_last SET order1 SELECT emp_name, eid, mgr_id, salary, job_id FROM org_chart ORDER BY order1"
              db-types="Oracle"/>
    <sql-case id="select_with_subquery_factoring_with_search_depth_first_with_cycle"
              value="WITH dup_hiredate (eid, emp_last, mgr_id, reportLevel, hire_date, job_id) AS (SELECT employee_id, last_name, manager_id, reportLevel, hire_date, job_id FROM employees WHERE manager_id IS NULL) SEARCH DEPTH FIRST BY hire_date SET order1 CYCLE hire_date SET is_cycle TO 'Y' DEFAULT 'N' SELECT lpad(' ',2*reportLevel)||emp_last emp_name, eid, mgr_id, hire_date, job_id, is_cycle FROM dup_hiredate ORDER BY order1"
              db-types="Oracle"/>
    <sql-case id="select_with_subquery_factoring_with_search_depth_first_with_having"
              value="WITH emp_count (eid, emp_last, mgr_id, mgrLevel, salary, cnt_employees) AS (SELECT employee_id, last_name, manager_id, mgrLevel, salary, cnt_employees FROM employees) SEARCH DEPTH FIRST BY emp_last SET order1 SELECT emp_last, eid, mgr_id, salary FROM emp_count GROUP BY emp_last, eid, mgr_id, salary HAVING salary > 24000 ORDER BY mgr_id NULLS FIRST, emp_last"
              db-types="Oracle"/>
    <sql-case id="select_with_multiple_cte_definitions"
              value="WITH cte1(status, user_id) AS (SELECT status, user_id FROM t_order), cte2(item_id) AS (SELECT item_id FROM t_order_item) SELECT status, user_id, item_id FROM cte1 INNER JOIN cte2 ON cte1.user_id = cte2.user_id"
              db-types="SQLServer"/>
    <sql-case id="select_with_single_subquery" value="WITH t AS (SELECT a+2 c,b FROM t1) SELECT c,b FROM t"
              db-types="MySQL"/>
    <sql-case id="select_with_oracle_single_subquery" value="WITH t AS (SELECT a+2 c,b FROM t1) SELECT c,b FROM t"
              db-types="Oracle"/>
    <sql-case id="select_with_multiple_subquery"
              value="WITH cte1(col1, col2, col3) as (SELECT emp_no,first_name,last_name FROM employees WHERE emp_no=10012), cte2(col1, col2, col3) as (SELECT emp_no,first_name,last_name from employees WHERE emp_no=10012) SELECT col1, col2, col3 FROM cte1"
              db-types="MySQL"/>
    <sql-case id="select_with_recursive_union_all1"
              value="WITH RECURSIVE DirectoryCTE as (SELECT * FROM table1 WHERE id = 1 AND project_id = 2 UNION ALL SELECT * FROM project_file_catalog t INNER JOIN DirectoryCTE cte ON t.project_id = cte.project_id AND t.parent_id = cte.id) SELECT * FROM DirectoryCTE ORDER BY level"
              db-types="MySQL"/>
    <sql-case id="select_with_oracle_recursive_union_all1"
              value="WITH DirectoryCTE as (SELECT table1.col1, table1.col2 FROM table1 WHERE id = 1 AND project_id = 2 UNION ALL SELECT t.col1, t.col2 FROM project_file_catalog t INNER JOIN DirectoryCTE cte ON t.project_id = cte.project_id AND t.parent_id = cte.id) SELECT DirectoryCTE.col1, DirectoryCTE.col2 FROM DirectoryCTE ORDER BY level"
              db-types="Oracle"/>
    <sql-case id="select_with_recursive_union_all2"
              value="WITH cte AS (SELECT 1 AS col1, 2 AS col2 UNION ALL SELECT 3, 4) SELECT col1, col2 FROM cte"
              db-types="MySQL"/>
</sql-cases>
