<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_diskgroup_with_external_redundancy" value="CREATE DISKGROUP dgroup_01
                                                                           EXTERNAL REDUNDANCY
                                                                           DISK '/devices/disks/c*';"
              db-types="Oracle"/>
    <sql-case id="create_diskgroup_with_single_attribute" value="CREATE DISKGROUP data1 DISK '/dev/sd*'
                                                                        ATTRIBUTE 'compatible.asm' = '11.2';"
              db-types="Oracle"/>
    <sql-case id="create_diskgroup_with_multi_attribute" value="CREATE DISKGROUP data2 DISK '/dev/sd*'
                                                                       ATTRIBUTE 'compatible.asm' = '11.2', 'compatible.rdbms' = '11.2',
                                                                                 'compatible.advm' = '11.2';"
              db-types="Oracle"/>
    <sql-case id="create_diskgroup_with_multi_failgroup" value="CREATE DISKGROUP data NORMAL REDUNDANCY
                                                                FAILGROUP controller1 DISK
                                                                '/devices/diska1',
                                                                '/devices/diska2',
                                                                '/devices/diska3',
                                                                '/devices/diska4'
                                                                FAILGROUP controller2 DISK
                                                                '/devices/diskb1',
                                                                '/devices/diskb2',
                                                                '/devices/diskb3',
                                                                '/devices/diskb4'
                                                                ATTRIBUTE 'compatible.asm' = '11.2', 'compatible.rdbms' = '11.2',
                                                                          'sector_size'='4096';" db-types="Oracle"/>
    <sql-case id="create_diskgroup_with_single_failgroup" value="CREATE DISKGROUP ocr_data NORMAL REDUNDANCY
                                                                 FAILGROUP fg1 DISK '/devices/diskg1'
                                                                 FAILGROUP fg2 DISK '/devices/diskg2'
                                                                 QUORUM FAILGROUP fg3 DISK '/devices/diskg3'
                                                                 ATTRIBUTE 'compatible.asm' = '11.2.0.0.0';"
              db-types="Oracle"/>
</sql-cases>
