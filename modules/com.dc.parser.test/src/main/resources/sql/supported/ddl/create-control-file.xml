<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_control_file" value="CREATE CONTROLFILE REUSE DATABASE demo NORESETLOGS NOARCHIVELOG
                                                  MAXLOGFILES 32
                                                  MAXLOGMEMBERS 2
                                                  MAXDATAFILES 32
                                                  MAXINSTANCES 1
                                                  MAXLOGHISTORY 449
                                              LOGFILE
                                                GROUP 1 '/path/oracle/dbs/t_log1.f'  SIZE 500K,
                                                GROUP 2 '/path/oracle/dbs/t_log2.f'  SIZE 500K
                                              DATAFILE
                                                '/path/oracle/dbs/t_db1.f',
                                                '/path/oracle/dbs/dbu19i.dbf',
                                                '/path/oracle/dbs/tbs_11.f',
                                                '/path/oracle/dbs/smundo.dbf',
                                                '/path/oracle/dbs/demo.dbf'
                                                CHARACTER SET WE8DEC;" db-types="Oracle"/>
</sql-cases>
