<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-table sql-case-id="create_table_with_column_dec">
        <table name="test3" start-index="13" stop-index="17"/>
        <column-definition type="int" auto-increment="true" start-index="19" stop-index="39">
            <column name="id"/>
        </column-definition>
        <column-definition type="dec" not-null="true" start-index="42" stop-index="68">
            <column name="col1"/>
        </column-definition>
        <column-definition type="DEC" not-null="true" start-index="71" stop-index="102">
            <column name="col2"/>
        </column-definition>
        <column-definition type="FLOAT" not-null="true" start-index="105" stop-index="133">
            <column name="col3"/>
        </column-definition>
        <column-definition type="DECIMAL" not-null="true" start-index="136" stop-index="171">
            <column name="col4"/>
        </column-definition>
        <constraint-definition constraint-name="test3_pk" start-index="174" stop-index="209">
            <primary-key-column name="id" start-index="207" stop-index="208"/>
        </constraint-definition>
    </create-table>
    <create-table sql-case-id="create_table_with_backtick_engine_with_string">
        <table name="t`1" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="18"/>
        <column-definition type="int" start-index="20" stop-index="24">
            <column name="a"/>
        </column-definition>
        <create-table-option start-index="27" stop-index="41">
            <engine name="Innodb" start-index="34" stop-index="41"/>
        </create-table-option>
    </create-table>
    <create-table sql-case-id="create_table_with_backtick_engine_innodb">
        <table name="t`1" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="18"/>
        <column-definition type="int" start-index="20" stop-index="24">
            <column name="a"/>
        </column-definition>
        <create-table-option start-index="27" stop-index="39">
            <engine name="INNODB" start-index="34" stop-index="39"/>
        </create-table-option>
    </create-table>
    <create-table sql-case-id="create_table_with_backtick_engine_myisam">
        <table name="t`1" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="18"/>
        <column-definition type="int" start-index="20" stop-index="24">
            <column name="a"/>
        </column-definition>
        <create-table-option start-index="27" stop-index="39">
            <engine name="myisam" start-index="34" stop-index="39"/>
        </create-table-option>
    </create-table>
    <create-table sql-case-id="create_table_with_backtick">
        <table name="`t_order" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="23"/>
        <column-definition type="int" start-index="26" stop-index="30">
            <column name="i"/>
        </column-definition>
    </create-table>
    <create-table sql-case-id="create_table_column_with_backtick">
        <table name="``t_o`r`d`e`r`" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="35"/>
        <column-definition type="int" start-delimiter="`" end-delimiter="`" start-index="38" stop-index="46">
            <column name="`i"/>
        </column-definition>
    </create-table>
    <create-table sql-case-id="create_table_with_like">
        <table name="t_log" start-index="13" stop-index="17"/>
        <like-table name="t_old_log" start-index="24" stop-index="32"/>
    </create-table>
    <create-table sql-case-id="create_table">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_engin_charset">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
        <create-table-option start-index="59" stop-index="95">
            <engine name="InnoDB" start-index="66" stop-index="71"/>
        </create-table-option>
    </create-table>

    <create-table sql-case-id="create_table_with_keyword">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="boolean" start-index="39" stop-index="52">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_if_not_exists">
        <table name="t_log" start-index="27" stop-index="31"/>
        <column-definition type="int" start-index="33" stop-index="38">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="41" stop-index="58">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_temporary_table_if_not_exists">
        <table name="t_temp_log" start-index="37" stop-index="46"/>
        <column-definition type="int" start-index="48" stop-index="53">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="56" stop-index="73">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_global_temporary_table">
        <table name="t_temp_log" start-index="30" stop-index="39"/>
        <column-definition type="int" start-index="41" stop-index="46">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_private_temporary_table">
        <table name="t_temp_log" start-index="31" stop-index="40"/>
        <column-definition type="int" start-index="42" stop-index="47">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="50" stop-index="67">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_shared_table">
        <table name="t_temp_log" start-index="21" stop-index="30"/>
        <column-definition type="int" start-index="32" stop-index="37">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="40" stop-index="57">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_duplicated_table">
        <table name="t_temp_log" start-index="24" stop-index="33"/>
        <column-definition type="int" start-index="35" stop-index="40">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="43" stop-index="60">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_sharing_metadata">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="39" stop-index="56">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="59" stop-index="76">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_sharing_data">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="35" stop-index="52">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="55" stop-index="72">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_sharing_extended_data">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="44" stop-index="61">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="64" stop-index="81">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_sharing_none">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="35" stop-index="52">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="55" stop-index="72">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_optimize_read">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_optimize_write">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_without_optimize_read">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_without_optimize_write">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_parent">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_object_table">
        <table name="t_log" start-index="13" stop-index="17"/>
    </create-table>

    <create-table sql-case-id="create_table_with_char_varing">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="char" start-index="19" stop-index="38">
            <column name="id"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_any_type">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="AnyData" start-index="19" stop-index="39">
            <column name="a_anydata"/>
        </column-definition>
        <column-definition type="AnyType" start-index="42" stop-index="62">
            <column name="a_anytype"/>
        </column-definition>
        <column-definition type="AnyDataSet" start-index="65" stop-index="91">
            <column name="a_anydataset"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_local_temp_table">
        <table name="t_temp_log" start-index="24" stop-index="33"/>
        <column-definition type="int" start-index="35" stop-index="40">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="43" stop-index="60">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_unlogged_table">
        <table name="t_log" start-index="22" stop-index="26"/>
        <column-definition type="int" start-index="28" stop-index="33">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="36" stop-index="53">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_space">
        <table name="t_order_item" start-index="17" stop-index="28"/>
        <column-definition type="INT" start-index="40" stop-index="50">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="INT" start-index="61" stop-index="72">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="83" stop-index="93">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="104" stop-index="121">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="132" stop-index="150">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="161" stop-index="179">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="190" stop-index="208">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_back_quota">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="21"/>
        <column-definition type="INT" start-index="24" stop-index="37">
            <column name="order_id" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="52">
            <column name="user_id" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="55" stop-index="74">
            <column name="status" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="77" stop-index="97">
            <column name="column1" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="100" stop-index="120">
            <column name="column2" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="123" stop-index="143">
            <column name="column3" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <create-table-option start-index="146" stop-index="158">
            <engine name="INNODB" start-index="153" stop-index="158"/>
        </create-table-option>
    </create-table>

    <create-table sql-case-id="create_temporary_table">
        <table name="t_order" start-index="23" stop-index="29"/>
        <column-definition type="INT" start-index="32" stop-index="43">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="46" stop-index="56">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="59" stop-index="76">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="79" stop-index="97">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="100" stop-index="118">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="121" stop-index="139">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_not_null">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="42">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="45" stop-index="55">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="58" stop-index="75">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="78" stop-index="96">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="99" stop-index="117">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="120" stop-index="138">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_default">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="43">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="46" stop-index="56">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="59" stop-index="76">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="79" stop-index="97">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="100" stop-index="118">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="121" stop-index="139">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_increment">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" auto-increment="true" start-index="22" stop-index="48">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="51" stop-index="61">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="64" stop-index="81">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="84" stop-index="102">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="105" stop-index="123">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="126" stop-index="144">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_generated">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="67">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="70" stop-index="80">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="83" stop-index="100">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="103" stop-index="121">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="124" stop-index="142">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="145" stop-index="163">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_comment">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="52">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="55" stop-index="65">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="68" stop-index="85">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="88" stop-index="106">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="109" stop-index="127">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="130" stop-index="148">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_primary_key">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="45">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="48" stop-index="58">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="61" stop-index="78">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="81" stop-index="99">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="102" stop-index="120">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="123" stop-index="141">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_unique_key">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="40">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="43" stop-index="53">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="56" stop-index="73">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="76" stop-index="94">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="97" stop-index="115">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="118" stop-index="136">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_foreign_key">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="INT" start-index="27" stop-index="37">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="117">
            <column name="order_id"/>
            <referenced-table name="t_order" start-index="64" stop-index="70"/>
        </column-definition>
        <column-definition type="INT" start-index="120" stop-index="130">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="133" stop-index="150">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="153" stop-index="171">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="174" stop-index="192">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="195" stop-index="213">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_constraints">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="52">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="55" stop-index="65">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="68" stop-index="85">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="88" stop-index="106">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="109" stop-index="127">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="130" stop-index="148">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_primary_key">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="pk_order_id" start-index="132" stop-index="176">
            <primary-key-column name="order_id" start-index="168" stop-index="175"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_composite_primary_key">
        <table name="t_order" start-index="13" stop-index="19"/>
        <!-- FIXME cannot parse pk -->
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="pk_order_id" start-index="132" stop-index="193">
            <primary-key-column name="order_id" start-index="168" stop-index="175"/>
            <primary-key-column name="user_id" start-index="178" stop-index="184"/>
            <primary-key-column name="status" start-index="187" stop-index="192"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_unique_key">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="uk_order_id" index-name="order_id_index" start-index="132"
                               stop-index="190">
            <index-column name="order_id" start-index="182" stop-index="189"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_composite_unique_key">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="uk_order_id" start-index="132" stop-index="188">
            <index-column name="order_id" start-index="163" stop-index="170"/>
            <index-column name="user_id" start-index="173" stop-index="179"/>
            <index-column name="status" start-index="182" stop-index="187"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_foreign_key">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="INT" start-index="27" stop-index="37">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="51">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="54" stop-index="64">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="67" stop-index="84">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="87" stop-index="105">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="108" stop-index="126">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="129" stop-index="147">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="fk_order_id" start-index="150" stop-index="260">
            <referenced-table name="t_order" start-index="207" stop-index="213"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_composite_foreign_key">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="INT" start-index="27" stop-index="37">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="51">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="54" stop-index="64">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="67" stop-index="84">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="87" stop-index="105">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="108" stop-index="126">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="129" stop-index="147">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="fk_order_id" start-index="150" stop-index="294">
            <referenced-table name="t_order" start-index="224" stop-index="230"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_check">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition start-index="132" stop-index="151"/>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_constraints">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="INT" start-index="27" stop-index="37">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="51">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="54" stop-index="64">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="67" stop-index="84">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="87" stop-index="105">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="108" stop-index="126">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="129" stop-index="147">
            <column name="column3"/>
        </column-definition>
        <constraint-definition start-index="150" stop-index="170">
            <primary-key-column name="item_id" start-index="163" stop-index="169"/>
        </constraint-definition>
        <constraint-definition start-index="173" stop-index="188">
            <index-column name="item_id" start-index="181" stop-index="187"/>
        </constraint-definition>
        <constraint-definition start-index="191" stop-index="278">
            <referenced-table name="t_order" start-index="225" stop-index="231"/>
        </constraint-definition>
        <constraint-definition start-index="281" stop-index="299"/>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_index">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition index-name="order_index" stop-index="159" start-index="132">
            <index-column name="order_id" start-index="151" stop-index="158"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_composite_index">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition index-name="order_index" start-index="132" stop-index="176">
            <index-column name="order_id" start-index="151" stop-index="158"/>
            <index-column name="user_id" start-index="161" stop-index="167"/>
            <index-column name="status" start-index="170" stop-index="175"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_btree_index">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <constraint-definition index-name="order_index" start-index="132" stop-index="171">
            <index-column name="order_id" start-index="151" stop-index="158"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_comment">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
        <create-table-option start-index="132" stop-index="148">
            <comment text="'t_order'" start-index="140" stop-index="148"/>
        </create-table-option>
    </create-table>

    <create-table sql-case-id="create_table_with_partition">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="90" stop-index="108">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="111" stop-index="129">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <!--<create-table sql-case-id="create_table_select">-->
    <!--<table name="t_order_bak" start-index="13" stop-index="22" />-->
    <!--<table name="t_order" start-index="42" stop-index="48" />-->
    <!--</create-table>-->

    <!--<create-table sql-case-id="create_table_like">-->
    <!--<table name="t_order_bak" start-index="13" stop-index="22" />-->
    <!--<table name="t_order" start-index="31" stop-index="37" />-->
    <!--</create-table>-->

    <create-table sql-case-id="create_table_with_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="13" stop-index="21"/>
        <column-definition type="NUMBER" start-index="24" stop-index="44">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="47" stop-index="66">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="69" stop-index="89">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="92" stop-index="113">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="116" stop-index="137">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="140" stop-index="161">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_on_null_default">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" start-index="22" stop-index="58">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="61" stop-index="78">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="81" stop-index="99">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="102" stop-index="121">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="124" stop-index="143">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="146" stop-index="165">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_identity">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" start-index="22" stop-index="99">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="102" stop-index="119">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="122" stop-index="140">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="143" stop-index="162">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="165" stop-index="184">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="187" stop-index="206">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_encrypt">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" start-index="22" stop-index="97">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="100" stop-index="117">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="120" stop-index="138">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="141" stop-index="160">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="163" stop-index="182">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="185" stop-index="204">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_foreign_key_reference">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="NUMBER" start-index="27" stop-index="44">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="47" stop-index="136">
            <column name="order_id"/>
            <referenced-table name="t_order" start-index="101" stop-index="107"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="139" stop-index="156">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="159" stop-index="177">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="180" stop-index="199">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="202" stop-index="221">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="224" stop-index="243">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_check">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" start-index="22" stop-index="85">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="88" stop-index="105">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="108" stop-index="126">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="129" stop-index="148">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="151" stop-index="170">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="173" stop-index="192">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_constraints_cascade">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="NUMBER" primary-key="true" start-index="27" stop-index="93">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="96" stop-index="185">
            <column name="order_id"/>
            <referenced-table name="t_order" start-index="150" stop-index="156"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="188" stop-index="205">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="208" stop-index="231">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="234" stop-index="253">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="256" stop-index="275">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="278" stop-index="297">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_foreign_key_oracle">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="NUMBER" start-index="27" stop-index="44">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="47" stop-index="65">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="68" stop-index="85">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="88" stop-index="106">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="109" stop-index="128">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="131" stop-index="150">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="153" stop-index="172">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="fk_order_id" start-index="175" stop-index="267">
            <referenced-table name="t_order" start-index="232" stop-index="238"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_composite_foreign_key_oracle">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="NUMBER" start-index="27" stop-index="44">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="47" stop-index="65">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="68" stop-index="85">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="88" stop-index="106">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="109" stop-index="128">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="131" stop-index="150">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="153" stop-index="172">
            <column name="column3"/>
        </column-definition>
        <constraint-definition constraint-name="fk_order_id" start-index="175" stop-index="301">
            <referenced-table name="t_order" start-index="249" stop-index="255"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_out_of_line_constraints_oracle">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="NUMBER" start-index="27" stop-index="44">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="47" stop-index="65">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="68" stop-index="85">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="88" stop-index="106">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="109" stop-index="128">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="131" stop-index="150">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="153" stop-index="172">
            <column name="column3"/>
        </column-definition>
        <constraint-definition start-index="175" stop-index="195">
            <primary-key-column name="item_id" start-index="188" stop-index="194"/>
        </constraint-definition>
        <constraint-definition start-index="198" stop-index="213">
            <index-column name="item_id" start-index="206" stop-index="212"/>
        </constraint-definition>
        <constraint-definition start-index="216" stop-index="285">
            <referenced-table name="t_order" start-index="250" stop-index="256"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_exist_index">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" primary-key="true" start-index="22" stop-index="76">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="79" stop-index="96">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="99" stop-index="117">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="120" stop-index="139">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="142" stop-index="161">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="164" stop-index="183">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_create_index">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" primary-key="true" start-index="22" stop-index="113">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="116" stop-index="133">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="136" stop-index="154">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="157" stop-index="176">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="179" stop-index="198">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="201" stop-index="220">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_partition_oracle">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="NUMBER" start-index="22" stop-index="40">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="43" stop-index="60">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="63" stop-index="81">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="84" stop-index="103">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="106" stop-index="125">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="128" stop-index="147">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_xmltype_table_oracle">
        <table name="xwarehouses" start-index="13" stop-index="23"/>
    </create-table>

    <create-table sql-case-id="create_table_with_xmltype_column_oracle">
        <table name="xwarehouses" start-index="13" stop-index="23"/>
        <column-definition type="NUMBER" start-index="26" stop-index="44">
            <column name="warehouse_id"/>
        </column-definition>
        <column-definition type="XMLTYPE" start-index="47" stop-index="68">
            <column name="warehouse_spec"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_xmltype_column_clob_oracle">
        <table name="xwarehouses" start-index="13" stop-index="23"/>
        <column-definition type="NUMBER" start-index="26" stop-index="44">
            <column name="warehouse_id"/>
        </column-definition>
        <column-definition type="XMLTYPE" start-index="47" stop-index="68">
            <column name="warehouse_spec"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_double_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="13" stop-index="21"/>
        <column-definition type="INTEGER" start-index="24" stop-index="41">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INTEGER" start-index="44" stop-index="60">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="63" stop-index="82">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="85" stop-index="105">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="108" stop-index="128">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="131" stop-index="151">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_local_temporary_table">
        <table name="t_order" start-index="29" stop-index="35"/>
        <column-definition type="INTEGER" start-index="38" stop-index="53">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INTEGER" start-index="56" stop-index="70">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="73" stop-index="90">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="93" stop-index="111">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="114" stop-index="132">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="135" stop-index="153">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_range_partition">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INTEGER" start-index="22" stop-index="37">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INTEGER" start-index="40" stop-index="54">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="57" stop-index="74">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="77" stop-index="95">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="98" stop-index="116">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="119" stop-index="137">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <!--<create-table sql-case-id="create_table_like">-->
    <!--<table name="t_order_bak" start-index="13" stop-index="22" />-->
    <!--<table name="t_order" start-index="31" stop-index="37" />-->
    <!--</create-table>-->

    <!--<create-table sql-case-id="create_table_inherits">-->
    <!--<table name="t_order_bak" start-index="13" stop-index="22" />-->
    <!--<table name="t_order" start-index="37" stop-index="43" />-->
    <!--</create-table>-->

    <create-table sql-case-id="create_table_with_bracket">
        <table name="t_order" start-delimiter="[" end-delimiter="]" start-index="13" stop-index="21"/>
        <column-definition type="INT" start-index="24" stop-index="37">
            <column name="order_id" start-delimiter="[" end-delimiter="]"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="52">
            <column name="user_id" start-delimiter="[" end-delimiter="]"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="55" stop-index="74">
            <column name="status" start-delimiter="[" end-delimiter="]"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="77" stop-index="97">
            <column name="column1" start-delimiter="[" end-delimiter="]"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="100" stop-index="120">
            <column name="column2" start-delimiter="[" end-delimiter="]"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="123" stop-index="143">
            <column name="column3" start-delimiter="[" end-delimiter="]"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_identity">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="42">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="45" stop-index="55">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="58" stop-index="75">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="78" stop-index="96">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="99" stop-index="117">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="120" stop-index="138">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_as">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="66">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="69" stop-index="87">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="116" stop-index="134">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="137" stop-index="155">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_column_encrypt_algorithm">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="158">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="161" stop-index="171">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="174" stop-index="191">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="194" stop-index="212">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="215" stop-index="233">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="236" stop-index="254">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_primary_key_sqlserver">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="68">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="71" stop-index="81">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="84" stop-index="101">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="104" stop-index="122">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="125" stop-index="143">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="146" stop-index="164">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_unique_key_sqlserver">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="63">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="66" stop-index="76">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="79" stop-index="96">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="99" stop-index="117">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="120" stop-index="138">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="141" stop-index="159">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_foreign_key_sqlserver">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <column-definition type="INT" start-index="27" stop-index="37">
            <column name="item_id"/>
        </column-definition>
        <column-definition type="INT" start-index="40" stop-index="140">
            <column name="order_id"/>
            <referenced-table name="t_order" start-index="87" stop-index="93"/>
        </column-definition>
        <column-definition type="INT" start-index="143" stop-index="153">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="156" stop-index="173">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="176" stop-index="194">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="197" stop-index="215">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="218" stop-index="236">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_index">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="51">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="54" stop-index="64">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="67" stop-index="84">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="87" stop-index="105">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="108" stop-index="126">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="129" stop-index="147">
            <column name="column3"/>
        </column-definition>
        <!-- FIXME cannot parse index -->
        <!--<index name="order_index" />-->
    </create-table>

    <create-table sql-case-id="create_table_with_inline_check_sqlserver">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="78">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="81" stop-index="91">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="94" stop-index="111">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="114" stop-index="132">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="135" stop-index="153">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="156" stop-index="174">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_inline_constraints_sqlserver">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="73">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="76" stop-index="86">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="89" stop-index="106">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="109" stop-index="127">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="130" stop-index="148">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="151" stop-index="169">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_range_partitioned_and_values">
        <table name="t_order" start-index="13" stop-index="19"/>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_hash">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="bigint" start-index="22" stop-index="45">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="bigint" start-index="47" stop-index="69">
            <column name="cust_id"/>
        </column-definition>
        <column-definition type="text" start-index="71" stop-index="81">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_list">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="bigserial" start-index="22" stop-index="47">
            <column name="city_id"/>
        </column-definition>
        <column-definition type="text" start-index="49" stop-index="66">
            <column name="name"/>
        </column-definition>
        <column-definition type="bigint" start-index="68" stop-index="84">
            <column name="population"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_of_type">
        <table name="t_order" start-index="13" stop-index="19"/>
    </create-table>

    <create-table sql-case-id="create_table_in_tablespace">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="serial" start-index="22" stop-index="30">
            <column name="id"/>
        </column-definition>
        <column-definition type="text" start-index="32" stop-index="40">
            <column name="name"/>
        </column-definition>
        <column-definition type="text" start-index="42" stop-index="54">
            <column name="location"/>
        </column-definition>
    </create-table>
    <create-table sql-case-id="create_table_with_sign_column">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="21" stop-index="38">
            <column name="id"/>
        </column-definition>
        <column-definition type="BIGINT" start-index="41" stop-index="66">
            <column name="order_id"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_unsigned_column">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="21" stop-index="38">
            <column name="id"/>
        </column-definition>
        <column-definition type="BIGINT" start-index="41" stop-index="68">
            <column name="order_id"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_on_update_current_timestamp">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="45">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="DATETIME" start-index="48" stop-index="93">
            <column name="create_time"/>
        </column-definition>
        <column-definition type="DATETIME" start-index="96" stop-index="178">
            <column name="modify_time"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_on_update_current_timestamp_and_fsp">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="45">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="DATETIME" start-index="48" stop-index="93">
            <column name="create_time"/>
        </column-definition>
        <column-definition type="DATETIME" start-index="96" stop-index="187">
            <column name="modify_time"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_on_other_vendor_data_type">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="45">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="MIDDLEINT" start-index="48" stop-index="64">
            <column name="num"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_enum_and_character_set">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" primary-key="true" start-index="22" stop-index="45">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="ENUM" start-index="48" stop-index="87">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_storage_parameter">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="43">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="46" stop-index="56">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="59" stop-index="76">
            <column name="status"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="79" stop-index="97">
            <column name="column1"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="100" stop-index="118">
            <column name="column2"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="121" stop-index="139">
            <column name="column3"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_as_select">
        <table name="t_order_new" start-index="13" stop-index="23"/>
        <select>
            <from>
                <simple-table name="t_order" start-index="178" stop-index="184"/>
            </from>
            <projections start-index="171" stop-index="171">
                <shorthand-projection start-index="171" stop-index="171"/>
            </projections>
        </select>
    </create-table>

    <create-table sql-case-id="create_table_as_select_with_explicit_column_names">
        <table name="t_order_new" start-index="13" stop-index="23"/>
        <column name="order_id_new" start-index="26" stop-index="37"/>
        <column name="user_id_new" start-index="40" stop-index="50"/>
        <select>
            <from>
                <simple-table name="t_order" start-index="222" stop-index="228"/>
            </from>
            <projections start-index="199" stop-index="215">
                <column-projection name="order_id" start-index="199" stop-index="206"/>
                <column-projection name="user_id" start-index="209" stop-index="215"/>
            </projections>
        </select>
    </create-table>

    <create-table sql-case-id="create_table_as_select_with_query_hint">
        <table name="t_order_new" start-index="13" stop-index="27">
            <owner name="dbo" start-index="13" stop-index="15"/>
        </table>
        <select>
            <from>
                <join-table join-type="INNER">
                    <left>
                        <simple-table name="t_order" alias="o" start-index="111" stop-index="119"/>
                    </left>
                    <right>
                        <simple-table name="t_order_item" alias="i" start-index="126" stop-index="139"/>
                    </right>
                    <on-condition>
                        <binary-operation-expression start-index="144" stop-index="166">
                            <left>
                                <column name="order_id" start-index="144" stop-index="153">
                                    <owner name="o" start-index="144" stop-index="144"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="157" stop-index="166">
                                    <owner name="i" start-index="157" stop-index="157"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </on-condition>
                </join-table>
            </from>
            <projections start-index="102" stop-index="104">
                <shorthand-projection start-index="102" stop-index="104">
                    <owner name="i" start-index="102" stop-index="102"/>
                </shorthand-projection>
            </projections>
        </select>
    </create-table>

    <create-table sql-case-id="create_remote_table_as_select">
        <table name="t_order_new" start-index="20" stop-index="30"/>
        <select>
            <from>
                <join-table join-type="INNER">
                    <left>
                        <simple-table name="t_order_item" alias="i" start-index="119" stop-index="132"/>
                    </left>
                    <right>
                        <simple-table name="t_order" alias="o" start-index="139" stop-index="147"/>
                    </right>
                    <on-condition>
                        <binary-operation-expression start-index="152" stop-index="174">
                            <left>
                                <column name="order_id" start-index="152" stop-index="161">
                                    <owner name="i" start-index="152" stop-index="152"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="165" stop-index="174">
                                    <owner name="o" start-index="165" stop-index="165"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </on-condition>
                </join-table>
            </from>
            <projections start-index="110" stop-index="112">
                <shorthand-projection start-index="110" stop-index="112">
                    <owner name="i" start-index="110" stop-index="110"/>
                </shorthand-projection>
            </projections>
        </select>
    </create-table>

    <create-table sql-case-id="create_table_with_chinese_word">
        <table name="测试表" start-index="13" stop-index="15"/>
        <column-definition type="int" primary-key="true" start-index="17" stop-index="34">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="37" stop-index="54">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_chinese_word_with_quote_mysql">
        <table name="测试表" start-index="13" stop-index="17" start-delimiter="`" end-delimiter="`"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_chinese_word_with_quote">
        <table name="测试表" start-index="13" stop-index="17" start-delimiter="&quot;" end-delimiter="&quot;"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_bit_xor_table">
        <table name="BIT_XOR" start-index="13" stop-index="19"/>
        <column-definition type="int" start-index="21" stop-index="25">
            <column name="a"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_bit_xor_table_with_space">
        <table name="BIT_XOR" start-index="13" stop-index="19"/>
        <column-definition type="int" start-index="22" stop-index="26">
            <column name="a"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_path">
        <table name="files" start-index="13" stop-index="17"/>
        <column-definition type="PATH" start-index="20" stop-index="28">
            <column name="path"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_national">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="int" start-index="22" stop-index="33">
            <column name="national"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_visible">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="74">
            <column name="status"/>
        </column-definition>
        <create-table-option start-index="77" stop-index="89">
            <engine name="INNODB" start-index="84" stop-index="89"/>
        </create-table-option>
    </create-table>

    <create-table sql-case-id="create_table_with_invisible">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="VARCHAR" start-index="49" stop-index="76">
            <column name="status"/>
        </column-definition>
        <create-table-option start-index="79" stop-index="91">
            <engine name="INNODB" start-index="86" stop-index="91"/>
        </create-table-option>
    </create-table>

    <create-table sql-case-id="create_table_with_varchar2_char_and_byte_type">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="VARCHAR2" start-index="22" stop-index="62">
            <column name="SYS_ID"/>
            <dataType value="VARCHAR2" start-index="29" stop-index="45">
                <dataLength precision="32" scale="0" type="CHAR" start-index="37" stop-index="45"/>
            </dataType>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="65" stop-index="118">
            <column name="ATTACHMENT_NAME"/>
            <dataType value="VARCHAR2" start-index="81" stop-index="99">
                <dataLength precision="1024" scale="0" type="BYTE" start-index="89" stop-index="99"/>
            </dataType>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_character_varying">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="CHARACTER VARYING" start-index="49" stop-index="76">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_geomcollection">
        <table name="t1" start-index="13" stop-index="14"/>
        <column-definition type="GEOMCOLLECTION" start-index="17" stop-index="32">
            <column name="g"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_start_transaction">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_cast_function">
        <table name="t1" start-index="13" stop-index="14"/>
        <column-definition type="json" start-index="16" stop-index="21">
            <column name="j"/>
        </column-definition>
        <constraint-definition index-name="mv_idx" start-index="24" stop-index="64"/>
    </create-table>

    <create-table sql-case-id="create_table_with_long_char_varying">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="LONG CHAR VARYING" start-index="49" stop-index="72">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_signed_int_cast_function">
        <table name="t1" start-index="13" stop-index="14"/>
        <column-definition type="json" start-index="16" stop-index="21">
            <column name="j"/>
        </column-definition>
        <constraint-definition index-name="mv_idx" start-index="24" stop-index="66"/>
    </create-table>

    <create-table sql-case-id="create_table_with_signed_int">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="SIGNED INT" start-index="49" stop-index="65">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_unsigned_int_cast_function">
        <table name="t1" start-index="13" stop-index="14"/>
        <column-definition type="json" start-index="16" stop-index="21">
            <column name="j"/>
        </column-definition>
        <constraint-definition index-name="mv_idx" start-index="24" stop-index="68"/>
    </create-table>

    <create-table sql-case-id="create_table_with_unsigned_int">
        <table name="t_order" start-index="13" stop-index="19"/>
        <column-definition type="INT" start-index="22" stop-index="33">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="INT" start-index="36" stop-index="46">
            <column name="user_id"/>
        </column-definition>
        <column-definition type="UNSIGNED INT" start-index="49" stop-index="67">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_partition_less_than">
        <table name="t_sales" start-index="13" stop-index="19"/>
        <column-definition type="INTEGER" start-index="22" stop-index="46">
            <column name="order_id"/>
        </column-definition>
        <column-definition type="CHAR" start-index="49" stop-index="76">
            <column name="goods_name"/>
        </column-definition>
        <column-definition type="DATE" start-index="79" stop-index="102">
            <column name="sales_date"/>
        </column-definition>
        <column-definition type="INTEGER" start-index="105" stop-index="124">
            <column name="sales_volume"/>
        </column-definition>
        <column-definition type="CHAR" start-index="127" stop-index="146">
            <column name="sales_store"/>
        </column-definition>
        <constraint-definition start-index="149" stop-index="172">
            <primary-key-column name="order_id" start-index="163" stop-index="170"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_negative_data_type">
        <table name="T" start-index="13" stop-index="13" literal-start-index="13" literal-stop-index="13"/>
        <column-definition type="NUMBER" start-index="15" stop-index="25">
            <column name="COL1"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="28" stop-index="41">
            <column name="COL2"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="44" stop-index="59">
            <column name="COL3"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="62" stop-index="78">
            <column name="COL4"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_ref_data_type">
        <table name="location_table" start-index="13" stop-index="26"/>
        <column-definition type="NUMBER" start-index="29" stop-index="50">
            <column name="location_number"/>
        </column-definition>
        <column-definition type="warehouse_typ" start-index="53" stop-index="103">
            <column name="building"/>
            <referenced-table name="warehouse_table" start-index="89" stop-index="103"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_select">
        <table name="t_order_new" start-index="13" stop-index="23"/>
    </create-table>

    <create-table sql-case-id="create_table_organization_index_parallel_with_select">
        <table name="admin_iot3" start-index="13" stop-index="22"/>
        <column-definition start-index="24" stop-index="36" primary-key="true">
            <column name="i" start-index="24" stop-index="24"/>
        </column-definition>
        <column-definition start-index="39" stop-index="39">
            <column name="j" start-index="39" stop-index="39"/>
        </column-definition>
        <column-definition start-index="42" stop-index="42">
            <column name="k" start-index="42" stop-index="42"/>
        </column-definition>
        <column-definition start-index="45" stop-index="45">
            <column name="l" start-index="45" stop-index="45"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_range">
        <table name="costs_demo" start-index="13" stop-index="22"/>
        <column-definition type="NUMBER" start-index="25" stop-index="41">
            <column name="prod_id" start-index="25" stop-index="31"/>
        </column-definition>
        <column-definition type="DATE" start-index="44" stop-index="55">
            <column name="time_id" start-index="44" stop-index="50"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="58" stop-index="79">
            <column name="unit_cost" start-index="58" stop-index="66"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="82" stop-index="104">
            <column name="unit_price" start-index="82" stop-index="91"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_default_user">
        <table name="audit_trail" start-index="13" stop-index="23"/>
        <column-definition type="NUMBER" start-index="26" stop-index="38">
            <column name="value1" start-index="26" stop-index="31"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="41" stop-index="59">
            <column name="value2" start-index="41" stop-index="46"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="62" stop-index="95">
            <column name="inserter" start-index="62" stop-index="69"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_list_subpartition_by_hash1">
        <table name="car_rentals" start-index="13" stop-index="23"/>
        <column-definition type="NUMBER" start-index="30" stop-index="47">
            <column name="id" start-index="30" stop-index="31"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="55" stop-index="82">
            <column name="customer_id" start-index="55" stop-index="65"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="90" stop-index="131">
            <column name="confirmation_number" start-index="90" stop-index="108"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="139" stop-index="153">
            <column name="car_id" start-index="139" stop-index="144"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="161" stop-index="181">
            <column name="car_type" start-index="161" stop-index="168"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="189" stop-index="230">
            <column name="requested_car_type" start-index="189" stop-index="206"/>
        </column-definition>
        <column-definition type="DATE" start-index="238" stop-index="267">
            <column name="reservation_date" start-index="238" stop-index="253"/>
        </column-definition>
        <column-definition type="DATE" start-index="275" stop-index="300">
            <column name="start_date" start-index="275" stop-index="284"/>
        </column-definition>
        <column-definition type="DATE" start-index="308" stop-index="320">
            <column name="end_date" start-index="308" stop-index="315"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_list_subpartition_by_hash2">
        <table name="credit_card_accounts" start-index="13" stop-index="32"/>
        <column-definition type="NUMBER" start-index="39" stop-index="72">
            <column name="account_number" start-index="39" stop-index="52"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="79" stop-index="105">
            <column name="customer_id" start-index="79" stop-index="89"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="112" stop-index="147">
            <column name="customer_region" start-index="112" stop-index="126"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="154" stop-index="183">
            <column name="is_active" start-index="154" stop-index="162"/>
        </column-definition>
        <column-definition type="DATE" start-index="190" stop-index="214">
            <column name="date_opened" start-index="190" stop-index="200"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_subpartition_by_range_subpartition_template">
        <table name="account_balance_history" start-index="13" stop-index="35"/>
        <column-definition type="NUMBER" start-index="42" stop-index="59">
            <column name="id" start-index="42" stop-index="43"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="67" stop-index="96">
            <column name="account_number" start-index="67" stop-index="80"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="104" stop-index="133">
            <column name="customer_id" start-index="104" stop-index="114"/>
        </column-definition>
        <column-definition type="DATE" start-index="141" stop-index="172">
            <column name="transaction_date" start-index="141" stop-index="156"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="180" stop-index="204">
            <column name="amount_credited" start-index="180" stop-index="194"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="212" stop-index="232">
            <column name="amount_debited" start-index="212" stop-index="225"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="240" stop-index="273">
            <column name="end_of_day_balance" start-index="240" stop-index="257"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_range_subpartition_by_list">
        <table name="call_detail_records" start-index="13" stop-index="31"/>
        <column-definition type="NUMBER" start-index="38" stop-index="46">
            <column name="id" start-index="38" stop-index="39"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="55" stop-index="78">
            <column name="from_number" start-index="55" stop-index="65"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="87" stop-index="108">
            <column name="to_number" start-index="87" stop-index="95"/>
        </column-definition>
        <column-definition type="DATE" start-index="117" stop-index="133">
            <column name="date_of_call" start-index="117" stop-index="128"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="142" stop-index="161">
            <column name="distance" start-index="142" stop-index="149"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="170" stop-index="197">
            <column name="call_duration_in_s" start-index="170" stop-index="187"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_range_subpartition_by_hash">
        <table name="composite_sales" start-index="13" stop-index="27"/>
        <column-definition type="NUMBER" start-index="35" stop-index="51">
            <column name="prod_id" start-index="35" stop-index="41"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="60" stop-index="73">
            <column name="cust_id" start-index="60" stop-index="66"/>
        </column-definition>
        <column-definition type="DATE" start-index="82" stop-index="93">
            <column name="time_id" start-index="82" stop-index="88"/>
        </column-definition>
        <column-definition type="CHAR" start-index="102" stop-index="119">
            <column name="channel_id" start-index="102" stop-index="111"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="128" stop-index="145">
            <column name="promo_id" start-index="128" stop-index="135"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="154" stop-index="176">
            <column name="quantity_sold" start-index="154" stop-index="166"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="185" stop-index="208">
            <column name="amount_sold" start-index="185" stop-index="195"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_list_subpartition_by_list">
        <table name="current_inventory" start-index="13" stop-index="29"/>
        <column-definition type="NUMBER" start-index="36" stop-index="54">
            <column name="warehouse_id" start-index="36" stop-index="47"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="61" stop-index="89">
            <column name="warehouse_region" start-index="61" stop-index="76"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="96" stop-index="112">
            <column name="product_id" start-index="96" stop-index="105"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="119" stop-index="147">
            <column name="product_category" start-index="119" stop-index="134"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="154" stop-index="175">
            <column name="amount_in_stock" start-index="154" stop-index="168"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="182" stop-index="211">
            <column name="unit_of_shipping" start-index="182" stop-index="197"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="218" stop-index="241">
            <column name="products_per_unit" start-index="218" stop-index="234"/>
        </column-definition>
        <column-definition type="DATE" start-index="248" stop-index="264">
            <column name="last_updated" start-index="248" stop-index="259"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_organization_index">
        <table name="admin_docindex" start-index="13" stop-index="26"/>
        <column-definition type="char" start-index="33" stop-index="46">
            <column name="token" start-index="33" stop-index="37"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="53" stop-index="65">
            <column name="doc_id" start-index="53" stop-index="58"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="72" stop-index="93">
            <column name="token_frequency" start-index="72" stop-index="86"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="100" stop-index="127">
            <column name="token_offsets" start-index="100" stop-index="112"/>
        </column-definition>
        <constraint-definition constraint-name="pk_admin_docindex" start-index="134" stop-index="189">
            <primary-key-column name="token" start-index="176" stop-index="180"/>
            <primary-key-column name="doc_id" start-index="183" stop-index="188"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_ref_type1">
        <table name="departments_t" start-index="13" stop-index="25"/>
        <column-definition type="NUMBER" start-index="28" stop-index="38">
            <column name="d_no" start-index="28" stop-index="31"/>
        </column-definition>
        <column-definition type="employees_typ" start-index="41" stop-index="90">
            <column name="mgr_ref" start-index="41" stop-index="47"/>
            <referenced-table name="employees_obj_t" start-index="76" stop-index="90"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_ref_type2">
        <table name="departments_t" start-index="13" stop-index="25"/>
        <column-definition type="NUMBER" start-index="28" stop-index="38">
            <column name="d_no" start-index="28" stop-index="31"/>
        </column-definition>
        <column-definition type="employees_typ" start-index="41" stop-index="114">
            <column name="mgr_ref" start-index="41" stop-index="47"/>
            <referenced-table name="employees_obj_t" start-index="100" stop-index="114"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_partition_by_range_subpartitions">
        <table name="customers_part" start-index="13" stop-index="26"/>
        <column-definition type="NUMBER" start-index="33" stop-index="59">
            <column name="customer_id" start-index="33" stop-index="43"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="66" stop-index="96">
            <column name="cust_first_name" start-index="66" stop-index="80"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="103" stop-index="133">
            <column name="cust_last_name" start-index="103" stop-index="116"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="140" stop-index="170">
            <column name="nls_territory" start-index="140" stop-index="152"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="177" stop-index="206">
            <column name="credit_limit" start-index="177" stop-index="188"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_queue">
        <table name="t_log" start-index="13" stop-index="17"/>
        <column-definition type="int" primary-key="true" start-index="19" stop-index="36">
            <column name="id"/>
        </column-definition>
        <column-definition type="varchar" start-index="39" stop-index="56">
            <column name="status"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_relational_and_physical_properties">
        <table name="TEST_BASIC2" start-delimiter="&quot;" end-delimiter="&quot;" start-index="13" stop-index="36">
            <owner name="C##TPCC2" start-delimiter="&quot;" end-delimiter="&quot;" start-index="13" stop-index="22"/>
        </table>
        <column-definition type="NUMBER" start-index="43" stop-index="59">
            <column name="BS_COL01"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="66" stop-index="82">
            <column name="BS_COL02"/>
        </column-definition>
        <constraint-definition start-index="89" stop-index="406">
            <primary-key-column name="BS_COL01" start-delimiter="&quot;" end-delimiter="&quot;" start-index="102"
                                stop-index="111"/>
        </constraint-definition>
    </create-table>

    <create-table sql-case-id="create_table_cluster_with_select1">
        <table name="dept_10" start-index="13" stop-index="19"/>
    </create-table>

    <create-table sql-case-id="create_table_cluster_with_select2">
        <table name="dept_20" start-index="13" stop-index="19"/>
    </create-table>

    <create-table sql-case-id="create_table_cluster_with_select3">
        <table name="customers_demo" start-index="13" stop-index="26"/>
    </create-table>

    <create-table sql-case-id="create_table_with_ref_type_column_department_typ">
        <table name="employees_obj" start-index="13" stop-index="25"/>
        <column-definition type="VARCHAR2" start-index="28" stop-index="49">
            <column name="e_name" start-index="28" stop-index="33"/>
        </column-definition>
        <column-definition type="NUMBER" start-index="52" stop-index="66">
            <column name="e_number" start-index="52" stop-index="59"/>
        </column-definition>
        <column-definition type="department_typ" start-index="69" stop-index="122">
            <column name="e_dept" start-index="69" stop-index="74"/>
            <referenced-table name="departments_obj_t" start-index="106" stop-index="122"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_result_cache_annotations">
        <table name="foo" start-index="13" stop-index="15"/>
        <column-definition type="NUMBER" start-index="18" stop-index="25">
            <column name="a" start-index="18" stop-index="18"/>
        </column-definition>
        <column-definition type="VARCHAR2" start-index="28" stop-index="41">
            <column name="b" start-index="28" stop-index="28"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_as_sub_query">
        <table name="employees_temp" start-index="13" stop-index="26"/>
    </create-table>

    <create-table sql-case-id="create_table_parallel_with_as_sub_query">
        <table name="admin_emp_dept" start-index="13" stop-index="29">
            <owner name="hr" start-index="13" stop-index="14"/>
        </table>
    </create-table>

    <create-table sql-case-id="create_table_with_edge_constraint">
        <table name="bought" start-index="13" stop-index="18"/>
        <column-definition type="INT" start-index="22" stop-index="38">
            <column name="PurchaseCount"/>
        </column-definition>
        <constraint-definition constraint-name="EC_BOUGHT" start-index="41" stop-index="113"/>
    </create-table>

    <create-table sql-case-id="create_table_with_enclosed">
        <table name="emp_load" start-index="13" stop-index="20"/>
        <column-definition type="CHAR" start-index="22" stop-index="40">
            <column name="first_name" start-index="22" stop-index="31"/>
        </column-definition>
        <column-definition type="CHAR" start-index="43" stop-index="60">
            <column name="last_name" start-index="43" stop-index="51"/>
        </column-definition>
        <column-definition type="CHAR" start-index="63" stop-index="83">
            <column name="year_of_birth" start-index="63" stop-index="75"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_builtin_function">
        <table name="tab" start-index="13" stop-index="15"/>
    </create-table>

    <create-table sql-case-id="create_table_with_autoextend_size">
        <table name="t1" start-index="13" stop-index="14"/>
        <column-definition type="INT" start-index="17" stop-index="22">
            <column name="c1" start-index="17" stop-index="18"/>
        </column-definition>
    </create-table>

    <create-table sql-case-id="create_table_with_cast_function1">
        <table name="tb_ods_bs_class_all" start-index="13" stop-index="43">
            <owner name="test_schema" start-index="13" stop-index="23"/>
        </table>
        <select>
            <projections start-index="63" stop-index="137">
                <column-projection name="id" start-index="63" stop-index="67">
                    <owner name="jw" start-index="63" stop-index="64"/>
                </column-projection>
                <column-projection name="product_level_code" start-index="85" stop-index="105">
                    <owner name="jw" start-index="85" stop-index="86"/>
                </column-projection>
                <column-projection name="product_type" start-index="123" stop-index="137">
                    <owner name="jw" start-index="123" stop-index="124"/>
                </column-projection>
            </projections>
            <from>
                <simple-table name="tb_bs_class" alias="jw" start-index="152" stop-index="177">
                    <owner name="test_schema" start-index="152" stop-index="162"/>
                </simple-table>
            </from>
            <combine combine-type="UNION_ALL" start-index="240" stop-index="775">
                <left start-index="56" stop-index="230">
                    <projections start-index="63" stop-index="137">
                        <column-projection name="id" start-index="63" stop-index="67">
                            <owner name="jw" start-index="63" stop-index="64"/>
                        </column-projection>
                        <column-projection name="product_level_code" start-index="85" stop-index="105">
                            <owner name="jw" start-index="85" stop-index="86"/>
                        </column-projection>
                        <column-projection name="product_type" start-index="123" stop-index="137">
                            <owner name="jw" start-index="123" stop-index="124"/>
                        </column-projection>
                    </projections>
                    <from>
                        <simple-table name="tb_bs_class" alias="jw" start-index="152" stop-index="177">
                            <owner name="test_schema" start-index="152" stop-index="162"/>
                        </simple-table>
                    </from>
                    <where start-index="187" stop-index="230">
                        <expr>
                            <binary-operation-expression start-index="193" stop-index="230">
                                <left start-index="193" stop-index="223">
                                    <function function-name="if" start-index="193" stop-index="223"
                                              text="if(op_type is null,'0',op_type)">
                                        <parameter>
                                            <binary-operation-expression start-index="196" stop-index="210">
                                                <left>
                                                    <column name="op_type" start-index="196" stop-index="202"/>
                                                </left>
                                                <right>
                                                    <literal-expression value="null" start-index="207"
                                                                        stop-index="210"/>
                                                </right>
                                                <operator>IS</operator>
                                            </binary-operation-expression>
                                        </parameter>
                                        <parameter>
                                            <literal-expression value="0" start-index="212" stop-index="214"/>
                                        </parameter>
                                        <parameter>
                                            <column name="op_type" start-index="216" stop-index="222"/>
                                        </parameter>
                                    </function>
                                </left>
                                <right>
                                    <literal-expression value="1" start-index="228" stop-index="230"/>
                                </right>
                                <operator>!=</operator>
                            </binary-operation-expression>
                        </expr>
                    </where>
                </left>
                <right start-index="258" right-index="775">
                    <projections start-index="265" stop-index="409">
                        <column-projection name="id" start-index="265" stop-index="270">
                            <owner name="arc" start-index="265" stop-index="267"/>
                        </column-projection>
                        <expression-projection start-index="288" stop-index="321"
                                               text="cast(arc.ClassScaleType as bigint)">
                            <expr>
                                <function function-name="cast" start-index="288" stop-index="321"
                                          text="cast(arc.ClassScaleType as bigint)">
                                    <parameter>
                                        <column name="ClassScaleType" start-index="293" stop-index="310">
                                            <owner name="arc" start-index="293" stop-index="295"/>
                                        </column>
                                    </parameter>
                                    <parameter>
                                        <data-type value="bigint" start-index="315" stop-index="320"/>
                                    </parameter>
                                </function>
                            </expr>
                        </expression-projection>
                        <column-projection name="mother_class_id" start-index="339" stop-index="356">
                            <owner name="jw" start-index="339" stop-index="340"/>
                        </column-projection>
                        <column-projection name="product_type" start-index="394" stop-index="409">
                            <owner name="arc" start-index="394" stop-index="396"/>
                        </column-projection>
                    </projections>
                    <from>
                        <join-table join-type="LEFT">
                            <left>
                                <simple-table name="tb_archive_bs_class" start-index="424" stop-index="458" alias="arc">
                                    <owner name="test_schema" start-index="424" stop-index="434"/>
                                </simple-table>
                            </left>
                            <right>
                                <subquery-table alias="jw" start-index="487" stop-index="604">
                                    <subquery>
                                        <select>
                                            <projections start-index="495" stop-index="509">
                                                <column-projection name="mother_class_id" start-index="495"
                                                                   stop-index="509"/>
                                            </projections>
                                            <from>
                                                <simple-table name="tb_bs_class" start-index="516" stop-index="538">
                                                    <owner name="test_schema" start-index="516" stop-index="526"/>
                                                </simple-table>
                                            </from>
                                            <where start-index="557" stop-index="600">
                                                <expr>
                                                    <binary-operation-expression start-index="563" stop-index="600"
                                                                                 text="if(op_type is null,'0',op_type)">
                                                        <left>
                                                            <function function-name="if" start-index="563"
                                                                      stop-index="593"
                                                                      text="if(op_type is null,'0',op_type)">
                                                                <parameter>
                                                                    <binary-operation-expression start-index="566"
                                                                                                 stop-index="580">
                                                                        <left>
                                                                            <column name="op_type" start-index="566"
                                                                                    stop-index="572"/>
                                                                        </left>
                                                                        <right>
                                                                            <literal-expression value="null"
                                                                                                start-index="577"
                                                                                                stop-index="580"/>
                                                                        </right>
                                                                        <operator>IS</operator>
                                                                    </binary-operation-expression>
                                                                </parameter>
                                                                <parameter>
                                                                    <literal-expression value="0" start-index="582"
                                                                                        stop-index="584"/>
                                                                </parameter>
                                                                <parameter>
                                                                    <column name="op_type" start-index="586"
                                                                            stop-index="592"/>
                                                                </parameter>
                                                            </function>
                                                        </left>
                                                        <right>
                                                            <literal-expression value="1" start-index="598"
                                                                                stop-index="600"/>
                                                        </right>
                                                        <operator>!=</operator>
                                                    </binary-operation-expression>
                                                </expr>
                                            </where>
                                        </select>
                                    </subquery>
                                </subquery-table>
                            </right>
                            <on-condition>
                                <binary-operation-expression start-index="636" stop-index="688">
                                    <left>
                                        <binary-operation-expression start-index="636" stop-index="663">
                                            <left>
                                                <column name="nSchoolId" start-index="636" stop-index="648">
                                                    <owner name="arc" start-index="636" stop-index="638"/>
                                                </column>
                                            </left>
                                            <right>
                                                <column name="NSCHOOLID" start-index="652" stop-index="663">
                                                    <owner name="jw" start-index="652" stop-index="653"/>
                                                </column>
                                            </right>
                                            <operator>=</operator>
                                        </binary-operation-expression>
                                    </left>
                                    <right>
                                        <binary-operation-expression start-index="669" stop-index="688">
                                            <left>
                                                <column name="sCode" start-index="669" stop-index="677">
                                                    <owner name="arc" start-index="669" stop-index="671"/>
                                                </column>
                                            </left>
                                            <right>
                                                <column name="SCODE" start-index="681" stop-index="688">
                                                    <owner name="jw" start-index="681" stop-index="682"/>
                                                </column>
                                            </right>
                                            <operator>=</operator>
                                        </binary-operation-expression>
                                    </right>
                                    <operator>and</operator>
                                </binary-operation-expression>
                            </on-condition>
                        </join-table>
                    </from>
                    <where start-index="698" stop-index="775">
                        <expr>
                            <binary-operation-expression start-index="705" stop-index="775">
                                <left>
                                    <binary-operation-expression start-index="705" stop-index="750"
                                                                 text="if(arc.op_type is null,'0',arc.op_type)">
                                        <left>
                                            <function function-name="if" start-index="705" stop-index="743"
                                                      text="if(arc.op_type is null,'0',arc.op_type)">
                                                <parameter>
                                                    <binary-operation-expression start-index="708" stop-index="726">
                                                        <left>
                                                            <column name="op_type" start-index="708" stop-index="718">
                                                                <owner name="arc" start-index="708" stop-index="710"/>
                                                            </column>
                                                        </left>
                                                        <right>
                                                            <literal-expression value="null" start-index="723"
                                                                                stop-index="726"/>
                                                        </right>
                                                        <operator>IS</operator>
                                                    </binary-operation-expression>
                                                </parameter>
                                                <parameter>
                                                    <literal-expression value="0" start-index="728" stop-index="730"/>
                                                </parameter>
                                                <parameter>
                                                    <column name="op_type" start-index="732" stop-index="742">
                                                        <owner name="arc" start-index="732" stop-index="734"/>
                                                    </column>
                                                </parameter>
                                            </function>
                                        </left>
                                        <right>
                                            <literal-expression value="1" start-index="748" stop-index="750"/>
                                        </right>
                                        <operator>!=</operator>
                                    </binary-operation-expression>
                                </left>
                                <right>
                                    <binary-operation-expression start-index="756" stop-index="775">
                                        <left>
                                            <column name="NSCHOOLID" start-index="756" stop-index="767">
                                                <owner name="jw" start-index="756" stop-index="757"/>
                                            </column>
                                        </left>
                                        <right>
                                            <literal-expression value="null" start-index="772" stop-index="775"/>
                                        </right>
                                        <operator>IS</operator>
                                    </binary-operation-expression>
                                </right>
                                <operator>and</operator>
                            </binary-operation-expression>
                        </expr>
                    </where>
                </right>
            </combine>
        </select>
        <comment text="-- mother_class_id&#x000A;" start-index="359" stop-index="378"/>
    </create-table>

    <create-table sql-case-id="create_table_with_split">
        <table name="tmp_classcode_property_new" start-index="13" stop-index="50">
            <owner name="test_schema" start-index="13" stop-index="23"/>
        </table>
        <select>
            <projections start-index="70" stop-index="413">
                <column-projection name="scode" start-index="70" stop-index="89" alias="classcode">
                    <owner name="a" start-index="70" stop-index="70"/>
                </column-projection>
                <column-projection name="all_layer_value_name" start-index="107" stop-index="128">
                    <owner name="c" start-index="107" stop-index="107"/>
                </column-projection>
                <expression-projection start-index="146" stop-index="203"
                                       text="split(c.all_layer_value_name,',')[1] as f_dept_name_update">
                    <expr>
                        <function function-name="split" start-index="146" stop-index="203"
                                  text="split(c.all_layer_value_name,',')[1] as f_dept_name_update">
                            <parameter>
                                <column name="all_layer_value_name" start-index="152" stop-index="173">
                                    <owner name="c" start-index="152" stop-index="152"/>
                                </column>
                            </parameter>
                            <parameter>
                                <literal-expression value="," start-index="175" stop-index="177"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
                <expression-projection start-index="221" stop-index="283"
                                       text="split(c.all_layer_value_name,',')[4] as productlevelname_update">
                    <expr>
                        <function function-name="split" start-index="221" stop-index="283"
                                  text="split(c.all_layer_value_name,',')[4] as productlevelname_update">
                            <parameter>
                                <column name="all_layer_value_name" start-index="227" stop-index="248">
                                    <owner name="c" start-index="227" stop-index="227"/>
                                </column>
                            </parameter>
                            <parameter>
                                <literal-expression value="," start-index="250" stop-index="252"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
                <column-projection name="memo" start-index="301" stop-index="306">
                    <owner name="c" start-index="301" stop-index="301"/>
                </column-projection>
                <expression-projection start-index="308" stop-index="349"
                                       text="split(c.memo,',')[1] as f_dept_code_update">
                    <expr>
                        <function function-name="split" start-index="308" stop-index="349"
                                  text="split(c.memo,',')[1] as f_dept_code_update">
                            <parameter>
                                <column name="memo" start-index="314" stop-index="319">
                                    <owner name="c" start-index="314" stop-index="314"/>
                                </column>
                            </parameter>
                            <parameter>
                                <literal-expression value="," start-index="321" stop-index="323"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
                <expression-projection start-index="367" stop-index="413"
                                       text="split(c.memo,',')[4] as productlevelcode_update">
                    <expr>
                        <function function-name="split" start-index="367" stop-index="413"
                                  text="split(c.memo,',')[4] as productlevelcode_update">
                            <parameter>
                                <column name="memo" start-index="373" stop-index="378">
                                    <owner name="c" start-index="373" stop-index="373"/>
                                </column>
                            </parameter>
                            <parameter>
                                <literal-expression value="," start-index="380" stop-index="382"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <join-table join-type="INNER">
                    <left>
                        <simple-table name="tb_ods_bs_class_all" alias="a" start-index="428" stop-index="460">
                            <owner name="test_schema" start-index="428" stop-index="438"/>
                        </simple-table>
                    </left>
                    <right>
                        <subquery-table alias="b" start-index="490" stop-index="589">
                            <subquery>
                                <select>
                                    <projections start-index="498" stop-index="498">
                                        <shorthand-projection start-index="498" stop-index="498"/>
                                    </projections>
                                    <from>
                                        <simple-table name="tb_product_course_product" start-index="505"
                                                      stop-index="541">
                                            <owner name="test_schema" start-index="505" stop-index="515"/>
                                        </simple-table>
                                    </from>
                                    <where start-index="543" stop-index="586">
                                        <expr>
                                            <binary-operation-expression start-index="549" stop-index="586">
                                                <left>
                                                    <function function-name="if" text="if(op_type is null,'0',op_type)"
                                                              start-index="549" stop-index="579">
                                                        <parameter>
                                                            <binary-operation-expression start-index="552"
                                                                                         stop-index="566">
                                                                <left>
                                                                    <column name="op_type" start-index="552"
                                                                            stop-index="558"/>
                                                                </left>
                                                                <right>
                                                                    <literal-expression value="null" start-index="563"
                                                                                        stop-index="566"/>
                                                                </right>
                                                                <operator>IS</operator>
                                                            </binary-operation-expression>
                                                        </parameter>
                                                        <parameter>
                                                            <literal-expression value="0" start-index="568"
                                                                                stop-index="570"/>
                                                        </parameter>
                                                        <parameter>
                                                            <column name="op_type" start-index="572" stop-index="578"/>
                                                        </parameter>
                                                    </function>
                                                </left>
                                                <right>
                                                    <literal-expression value="1" start-index="584" stop-index="586"/>
                                                </right>
                                                <operator>!=</operator>
                                            </binary-operation-expression>
                                        </expr>
                                    </where>
                                </select>
                            </subquery>
                        </subquery-table>
                    </right>
                    <on-condition>
                        <binary-operation-expression start-index="594" stop-index="623"
                                                     text="aa.course_code_target = b.code">
                            <left>
                                <column name="course_code_target" start-index="594" stop-index="614">
                                    <owner name="aa" start-index="594" stop-index="595"/>
                                </column>
                            </left>
                            <right>
                                <column name="code" start-index="618" stop-index="623">
                                    <owner name="b" start-index="618" stop-index="618"/>
                                </column>
                            </right>
                            <operator>=</operator>
                        </binary-operation-expression>
                    </on-condition>
                </join-table>
            </from>
            <where start-index="633" stop-index="740">
                <expr>
                    <binary-operation-expression start-index="639" stop-index="740"
                                                 text="coalesce(a.f_dept_code,'') in ('05','16','31') and date(a.dtbegindate) >= date('2018-06-01')">
                        <left>
                            <in-expression start-index="639" stop-index="684">
                                <left>
                                    <function function-name="coalesce" text="coalesce(a.f_dept_code,'')"
                                              start-index="639" stop-index="664">
                                        <parameter>
                                            <column name="f_dept_code" start-index="648" stop-index="660">
                                                <owner name="a" start-index="648" stop-index="648"/>
                                            </column>
                                        </parameter>
                                        <parameter>
                                            <literal-expression value="" start-index="662" stop-index="663"/>
                                        </parameter>
                                    </function>
                                </left>
                                <right>
                                    <list-expression start-index="669" stop-index="684">
                                        <items>
                                            <literal-expression value="05" start-index="670" stop-index="673"/>
                                        </items>
                                        <items>
                                            <literal-expression value="16" start-index="675" stop-index="678"/>
                                        </items>
                                        <items>
                                            <literal-expression value="31" start-index="680" stop-index="683"/>
                                        </items>
                                    </list-expression>
                                </right>
                            </in-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="700" stop-index="740"
                                                         text="date(a.dtbegindate) >= date('2018-06-01')">
                                <left>
                                    <function function-name="date" start-index="700" stop-index="718"
                                              text="date(a.dtbegindate)">
                                        <parameter>
                                            <column name="dtbegindate" start-index="705" stop-index="717">
                                                <owner name="a" start-index="705" stop-index="705"/>
                                            </column>
                                        </parameter>
                                    </function>
                                </left>
                                <right>
                                    <function function-name="date" text="date('2018-06-01')" start-index="723"
                                              stop-index="740">
                                        <parameter>
                                            <literal-expression value="2018-06-01" start-index="728" stop-index="739"/>
                                        </parameter>
                                    </function>
                                </right>
                                <operator>>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>and</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-table>

    <create-table sql-case-id="create_table_with_properties">
        <table name="dim_ehr_all_dimension_dict1" start-index="13" stop-index="41" start-delimiter="`"
               end-delimiter="`"/>
        <column-definition type="varchar" start-index="51" stop-index="85">
            <column name="id" start-index="51" stop-index="54" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="varchar" start-index="94" stop-index="130">
            <column name="code" start-index="94" stop-index="99" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <column-definition type="int" start-index="139" stop-index="168">
            <column name="px" start-index="139" stop-index="142" start-delimiter="`" end-delimiter="`"/>
        </column-definition>
        <create-table-option start-index="180" stop-index="190">
            <engine name="OLAP" start-index="187" stop-index="190"/>
        </create-table-option>
    </create-table>

    <create-table sql-case-id="create_table_with_column_default_system_user_function">
        <table name="t" start-index="13" stop-index="13"/>
        <column-definition type="VARCHAR" start-index="16" stop-index="53">
            <column name="c"/>
        </column-definition>
    </create-table>
</sql-parser-test-cases>
