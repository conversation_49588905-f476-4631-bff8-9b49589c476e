<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <deny-user sql-case-id="deny_user">
        <table name="t_order" start-index="15" stop-index="21"/>
    </deny-user>

    <deny-user sql-case-id="deny_select">
        <table name="t_order" start-index="15" stop-index="21"/>
    </deny-user>

    <deny-user sql-case-id="deny_select_column">
        <table name="t_order" start-index="26" stop-index="32"/>
        <column name="order_id" start-index="13" stop-index="20"/>
    </deny-user>

    <deny-user sql-case-id="deny_select_to_users">
        <table name="t_order" start-index="26" stop-index="32"/>
        <column name="order_id" start-index="13" stop-index="20"/>
    </deny-user>

    <deny-user sql-case-id="deny_crud">
        <table name="t_order" start-index="39" stop-index="45"/>
    </deny-user>

    <deny-user sql-case-id="deny_all">
        <table name="t_order" start-index="23" stop-index="29"/>
    </deny-user>

    <deny-user sql-case-id="deny_view_definition_on_availability_group_to_login"/>
    <deny-user sql-case-id="deny_take_ownership_on_availability_group_to_user"/>
    <deny-user sql-case-id="deny_create_certificate_to_user"/>
    <deny-user sql-case-id="deny_references_to_role"/>
    <deny-user sql-case-id="deny_view_definition_to_user"/>
    <deny-user sql-case-id="deny_control_on_user"/>
    <deny-user sql-case-id="deny_view_definition_on_role_to_user"/>
    <deny-user sql-case-id="deny_impersonate_on_user_to_role"/>
    <deny-user sql-case-id="deny_view_definition_on_endpoint_to_login"/>
    <deny-user sql-case-id="deny_take_ownership_on_endpoint_to_user"/>

    <deny-user sql-case-id="deny_select_on_object_to_user">
        <table name="t_order" start-index="23" stop-index="33">
            <owner name="db1" start-index="23" stop-index="25"/>
        </table>
    </deny-user>

    <deny-user sql-case-id="deny_execute_on_object_to_role">
        <table name="t_order" start-index="24" stop-index="34">
            <owner name="db1" start-index="24" stop-index="26"/>
        </table>
    </deny-user>

    <deny-user sql-case-id="deny_references_on_object_to_user">
        <table name="t_order" start-index="38" stop-index="48">
            <owner name="db1" start-index="38" stop-index="40"/>
        </table>
        <column name="order_id" start-index="17" stop-index="24"/>
    </deny-user>

    <deny-user sql-case-id="deny_connect_sql_to_login"/>
    <deny-user sql-case-id="deny_create_endpoint_to_user_as_principal"/>
    <deny-user sql-case-id="deny_impersonate_on_login_to_windows_user"/>
    <deny-user sql-case-id="deny_view_definition_on_login"/>
    <deny-user sql-case-id="deny_view_definition_on_server_role"/>
    <deny-user sql-case-id="deny_alter_on_symmetric_key_to_user"/>

    <deny-user sql-case-id="deny_execute_on_system_object">
        <table name="xp_cmdshell" start-index="16" stop-index="30">
            <owner name="sys" start-index="16" stop-index="18"/>
        </table>
    </deny-user>

    <deny-user sql-case-id="deny_view_definition_on_type_to_user"/>

    <deny-user sql-case-id="deny_execute_on_xml_schema_collection_to_user">
        <table name="xmlschemacollection1" start-index="39" stop-index="66">
            <owner name="schema1" start-index="39" stop-index="45"/>
        </table>
    </deny-user>
</sql-parser-test-cases>
