<?xml version="1.0"?>
<configuration>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.dc.parser" level="warn" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <root>
        <level value="error"/>
        <appender-ref ref="console"/>
    </root>
</configuration> 
