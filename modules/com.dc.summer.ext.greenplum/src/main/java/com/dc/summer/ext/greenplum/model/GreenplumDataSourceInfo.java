package com.dc.summer.ext.greenplum.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataSourceInfo;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class GreenplumDataSourceInfo extends PostgreDataSourceInfo {


    public GreenplumDataSourceInfo(GreenplumDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(dataSource, metaData);
    }

    @Override
    public boolean supportDDLCallable() {
        return true;
    }

}
