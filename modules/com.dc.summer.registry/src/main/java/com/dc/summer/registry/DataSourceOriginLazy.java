
package com.dc.summer.registry;

import com.dc.summer.DBException;
import com.dc.summer.model.*;
import com.dc.summer.model.auth.SMSessionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.Map;

/**
 * DataSourceOriginLazy
 */
class DataSourceOriginLazy implements DBPDataSourceOriginExternal
{
    private final String originId;
    private final Map<String, Object> originProperties;
    private final DBPExternalConfiguration externalConfiguration;

    public DataSourceOriginLazy(
        String originId,
        Map<String, Object> originProperties,
        DBPExternalConfiguration externalConfiguration)
    {
        this.originId = originId;
        this.originProperties = originProperties;
        this.externalConfiguration = externalConfiguration;
    }

    @NotNull
    @Override
    public String getType() {
        return originId;
    }

    @Nullable
    @Override
    public String getSubType() {
        return null;
    }

    @NotNull
    @Override
    public String getDisplayName() {
        return originId;
    }

    @Override
    public boolean isDynamic() {
        return false;
    }

    @NotNull
    @Override
    public Map<String, Object> getDataSourceConfiguration() {
        return originProperties;
    }

    @Nullable
    @Override
    public DBPObject getObjectDetails(@NotNull DBRProgressMonitor monitor, @NotNull SMSessionContext sessionContext, @NotNull DBPDataSourceContainer dataSource) throws DBException {
        DBPDataSourceOrigin realOrigin = resolveRealOrigin();
        return realOrigin == null ? null : realOrigin.getObjectDetails(monitor, sessionContext, dataSource);
    }

    @Override
    public String toString() {
        return getType();
    }

    @Nullable
    DBPDataSourceOrigin resolveRealOrigin() throws DBException {
        // Loaded from configuration
        // Instantiate in lazy mode
        DBPDataSourceOriginProvider originProvider = DataSourceProviderRegistry.getInstance().getDataSourceOriginProvider(originId);
        if (originProvider != null) {
            return originProvider.getOrigin(originProperties, externalConfiguration);
        }
        return null;
    }

    @Nullable
    @Override
    public DBPExternalConfiguration getExternalConfiguration() {
        return externalConfiguration;
    }
}
