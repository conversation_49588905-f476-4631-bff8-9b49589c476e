
package com.dc.summer.registry.task;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.model.task.DBTTaskFolder;

import java.util.ArrayList;
import java.util.List;

public class TaskFolderImpl implements DBTTaskFolder {

    private String folderName;
    private DBPProject folderProject;
    private List<DBTTask> folderTasks;
    private DBTTaskFolder parentFolder;
    private List<DBTTaskFolder> nestedFolders = new ArrayList<>();

    TaskFolderImpl(@NotNull String folderName,
                   @Nullable DBTTaskFolder parentFolder,
                   @NotNull DBPProject folderProject,
                   @Nullable List<DBTTask> folderTasks) {
        this.folderName = folderName;
        this.parentFolder = parentFolder;
        this.folderProject = folderProject;
        this.folderTasks = folderTasks;
    }

    @NotNull
    @Override
    public DBPProject getProject() {
        return folderProject;
    }

    @Nullable
    public DBTTaskFolder getParentFolder() {
        return parentFolder;
    }

    @Nullable
    @Override
    public List<DBTTask> getTasks() {
        return folderTasks;
    }

    @Override
    public void addTaskToFolder(@NotNull DBTTask task) {
        folderTasks.add(task);
    }

    @Override
    public void removeTaskFromFolder(DBTTask task) {
        folderTasks.remove(task);
    }

    @Nullable
    @Override
    public List<DBTTaskFolder> getNestedTaskFolders() {
        return nestedFolders;
    }

    @Override
    public void addFolderToFoldersList(@NotNull DBTTaskFolder taskFolder) {
        nestedFolders.add(taskFolder);
    }

    @Override
    public boolean removeFolderFromFoldersList(@NotNull DBTTaskFolder taskFolder) {
        return nestedFolders.remove(taskFolder);
    }

    @Override
    public void setName(String newName) {
        this.folderName = newName;
    }

    @NotNull
    @Override
    public String getName() {
        return folderName;
    }
}
