

package com.dc.summer.registry;

import com.dc.summer.model.impl.AbstractDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;

/**
 * ProductBundleDescriptor
 */
public class ProductBundleDescriptor extends AbstractDescriptor {

    private final String id;
    private final String label;
    private final String description;

    public ProductBundleDescriptor(IConfigurationElement config)
    {
        super(config);
        this.id = config.getAttribute(RegistryConstants.ATTR_ID);
        this.label = config.getAttribute(RegistryConstants.ATTR_LABEL);
        this.description = config.getAttribute(RegistryConstants.ATTR_DESCRIPTION);
    }

    public String getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public String getDescription() {
        return description;
    }

}
