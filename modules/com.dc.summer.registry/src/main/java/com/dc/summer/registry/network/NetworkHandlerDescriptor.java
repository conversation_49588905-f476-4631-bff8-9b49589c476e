
package com.dc.summer.registry.network;

import com.dc.summer.Log;
import com.dc.summer.model.impl.AbstractContextDescriptor;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.PropertyDescriptor;
import com.dc.summer.model.net.DBWHandlerDescriptor;
import com.dc.summer.model.net.DBWHandlerType;
import com.dc.summer.model.net.DBWNetworkHandler;
import com.dc.summer.registry.RegistryConstants;
import com.dc.utils.CommonUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * NetworkHandlerDescriptor
 */
public class NetworkHandlerDescriptor extends AbstractContextDescriptor implements DBWHandlerDescriptor {
    private static final Log log = Log.getLog(NetworkHandlerDescriptor.class);

    public static final String EXTENSION_ID = "com.dc.summer.networkHandler"; //$NON-NLS-1$

    private final String id;
    private final String label;
    private final String codeName;
    private final String description;
    private final DBWHandlerType type;
    private final boolean secured;
    private final ObjectType handlerType;
    private final int order;
    private final List<String> replacesIDs;
    private NetworkHandlerDescriptor replacedBy;
    private final DBPPropertyDescriptor[] properties;

    NetworkHandlerDescriptor(
        IConfigurationElement config) {
        super(config);

        this.id = config.getAttribute(RegistryConstants.ATTR_ID);
        this.codeName = config.getAttribute("codeName") == null ? this.id : config.getAttribute("codeName");
        this.label = config.getAttribute(RegistryConstants.ATTR_LABEL);
        this.description = config.getAttribute(RegistryConstants.ATTR_DESCRIPTION);
        this.type = DBWHandlerType.valueOf(config.getAttribute(RegistryConstants.ATTR_TYPE).toUpperCase(Locale.ENGLISH));
        this.secured = CommonUtils.getBoolean(config.getAttribute(RegistryConstants.ATTR_SECURED), false);
        this.handlerType = new ObjectType(config.getAttribute(RegistryConstants.ATTR_HANDLER_CLASS));
        this.order = CommonUtils.toInt(config.getAttribute(RegistryConstants.ATTR_ORDER), 1);

        this.replacesIDs = Arrays.stream(config.getChildren("replace"))
            .map(re -> re.getAttribute("id"))
            .collect(Collectors.toList());

        this.properties = Arrays.stream(config.getChildren(PropertyDescriptor.TAG_PROPERTY_GROUP))
            .map(PropertyDescriptor::extractProperties)
            .flatMap(List<DBPPropertyDescriptor>::stream)
            .toArray(DBPPropertyDescriptor[]::new);
    }

    @NotNull
    public String getId() {
        return id;
    }

    @NotNull
    public String getCodeName() {
        return codeName;
    }

    public String getLabel() {
        return label;
    }

    public String getDescription() {
        return description;
    }

    public DBWHandlerType getType() {
        return type;
    }

    public boolean isSecured() {
        return secured;
    }

    @Override
    public DBPPropertyDescriptor[] getHandlerProperties() {
        return properties;
    }

    public int getOrder() {
        return order;
    }

    public boolean matches(DBPDriver driver) {
        try {
            return appliesTo(driver.getDataSourceProvider(), driver);
        } catch (Exception e) {
            log.debug(e);
            return false;
        }
    }

    public ObjectType getHandlerType() {
        return handlerType;
    }

    public <T extends DBWNetworkHandler> T createHandler(Class<T> impl)
        throws DBException {
        return handlerType.createInstance(impl);
    }

    public boolean replaces(NetworkHandlerDescriptor otherDesc) {
        return replacesIDs.contains(otherDesc.id);
    }

    @Override
    public String toString() {
        return id;
    }

    NetworkHandlerDescriptor getReplacedBy() {
        return replacedBy;
    }

    void setReplacedBy(NetworkHandlerDescriptor replacedBy) {
        this.replacedBy = replacedBy;
    }

}
