

package com.dc.summer.registry;

import com.dc.summer.Log;
import com.dc.summer.model.impl.AbstractContextDescriptor;
import org.apache.commons.jexl3.JexlExpression;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.summer.DBException;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Datasource binding descriptor
 */
public class DataSourceBindingDescriptor extends AbstractContextDescriptor {
    private static final Log log = Log.getLog(DataSourceBindingDescriptor.class);

    public static class DataSourceInfo {
        private String id;
        private String driver;
        private JexlExpression expression;

        DataSourceInfo(IConfigurationElement cfg) {
            String condition = cfg.getAttribute("if");
            if (!CommonUtils.isEmpty(condition)) {
                try {
                    this.expression = parseExpression(condition);
                } catch (DBException ex) {
                    log.warn("Can't parse auth model datasource expression: " + condition, ex); //$NON-NLS-1$
                }
            }
            this.id = cfg.getAttribute("id");
            this.driver = cfg.getAttribute("driver");
        }

        public boolean appliesTo(DBPDriver driver, Object context) {
            if (!CommonUtils.isEmpty(id) && !driver.getProviderDescriptor().matchesId(id)) {
                return false;
            }
            if (!CommonUtils.isEmpty(this.driver) && !this.driver.equals(driver.getId())) {
                return false;
            }
            if (expression != null) {
                try {
                    return CommonUtils.toBoolean(
                        expression.evaluate(makeContext(driver, context)));
                } catch (Exception e) {
                    log.debug("Error evaluating expression '" + expression + "'", e);
                    return false;
                }
            }
            return true;
        }
    }

    private List<DataSourceInfo> dataSources = new ArrayList<>();

    public DataSourceBindingDescriptor(IConfigurationElement config) {
        super(config);

        for (IConfigurationElement dsConfig : config.getChildren("datasource")) {
            this.dataSources.add(new DataSourceInfo(dsConfig));
        }
    }

    public boolean isDriverApplicable(DBPDriver driver) {
        if (dataSources.isEmpty()) {
            return true;
        }
        for (DataSourceInfo dsi : dataSources) {
            if (dsi.appliesTo(driver, null)) {
                return true;
            }
        }
        return false;
    }

}
