
package com.dc.summer.registry;

import com.dc.summer.Log;
import com.dc.summer.model.app.DBPApplication;
import com.dc.summer.model.impl.app.DefaultSecureStorage;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import org.eclipse.core.runtime.Platform;
import org.eclipse.equinox.app.IApplication;
import org.eclipse.equinox.app.IApplicationContext;
import com.dc.code.NotNull;
import com.dc.summer.model.app.DBASecureStorage;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.impl.app.ApplicationDescriptor;
import com.dc.summer.model.impl.app.ApplicationRegistry;
import com.dc.utils.CommonUtils;

/**
 * Base application implementation
 */
public abstract class BaseApplicationImpl implements IApplication, DBPApplication {

    private static final Log log = Log.getLog(BaseApplicationImpl.class);

    private static DBPApplication INSTANCE;

    protected BaseApplicationImpl() {
        if (INSTANCE != null && !(INSTANCE instanceof EclipsePluginApplicationImpl)) {
            log.error("Multiple application instances created: " + INSTANCE.getClass().getName() + ", " + this.getClass().getName());
        }
        INSTANCE = this;
    }

    public static DBPApplication getInstance() {
        if (INSTANCE == null) {
            DBPApplication instance = null;
            ApplicationDescriptor application = ApplicationRegistry.getInstance().getApplication();
            if (application != null && application.getImplClass() != null) {
                try {
                    instance = application.getImplClass().getConstructor().newInstance();
                } catch (Throwable e) {
                    log.error(e);
                }
            }
            if (instance == null) {
                instance = new EclipsePluginApplicationImpl();
            }
            INSTANCE = instance;
        }
        return INSTANCE;
    }

    public boolean isStandalone() {
        return true;
    }

    @Override
    public boolean isPrimaryInstance() {
        return true;
    }

    @Override
    public boolean isHeadlessMode() {
        return false;
    }

    @Override
    public boolean isExclusiveMode() {
        return false;
    }

    @Override
    public boolean isMultiuser() {
        return false;
    }

    @Override
    public boolean isDistributed() {
        return false;
    }

    @NotNull
    @Override
    public DBASecureStorage getSecureStorage() {
        return DefaultSecureStorage.INSTANCE;
    }

    @NotNull
    @Override
    public DBASecureStorage getProjectSecureStorage(DBPProject project) {
        return new ProjectSecureStorage(project);
    }

    @Override
    public String getInfoDetails(DBRProgressMonitor monitor) {
        return "N/A";
    }

    /**
     * Returns last user activity time
     * @return -1 by default
     */
    @Override
    public long getLastUserActivityTime() {
        return -1;
    }

    @Override
    public String getProductProperty(String propName) {
        return Platform.getProduct().getProperty(propName);
    }

    @Override
    public boolean hasProductFeature(String featureName) {
        return CommonUtils.toBoolean(
            Platform.getProduct().getProperty("feature." + featureName));
    }

    /////////////////////////////////////////
    // IApplication

    @Override
    public Object start(IApplicationContext context) throws Exception {
        return EXIT_OK;
    }

    @Override
    public void stop() {

    }

}
