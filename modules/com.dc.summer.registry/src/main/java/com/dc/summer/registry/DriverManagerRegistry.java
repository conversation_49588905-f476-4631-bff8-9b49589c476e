
package com.dc.summer.registry;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.ArrayList;
import java.util.List;

public class DriverManagerRegistry {

    public static final String EXTENSION_ID = "com.dc.summer.driverManager"; //$NON-NLS-1$

    private static DriverManagerRegistry instance = null;

    private final List<DriverCategoryDescriptor> categories = new ArrayList<>();

    public synchronized static DriverManagerRegistry getInstance() {
        if (instance == null) {
            instance = new DriverManagerRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private DriverManagerRegistry(IExtensionRegistry registry) {
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement viewElement : extElements) {
            if (viewElement.getName().equals("category")) {
                this.categories.add(
                    new DriverCategoryDescriptor(viewElement));
            }
        }
    }

    public List<DriverCategoryDescriptor> getCategories() {
        return new ArrayList<>(categories);
    }

    public DriverCategoryDescriptor getCategory(String id) {
        for (DriverCategoryDescriptor categoryDescriptor : categories) {
            if (id.equalsIgnoreCase(categoryDescriptor.getId())) {
                return categoryDescriptor;
            }
        }
        return null;
    }

}
