
package com.dc.summer.registry;

import com.dc.code.Nullable;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectFilter;
import com.dc.utils.CommonUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Filter mapping
 */
public class FilterMapping {
    public final String typeName;
    DBSObjectFilter defaultFilter;
    Map<String, DBSObjectFilter> customFilters = new HashMap<>();

    FilterMapping(String typeName) {
        this.typeName = typeName;
    }

    // Copy constructor
    FilterMapping(FilterMapping mapping) {
        this.typeName = mapping.typeName;
        this.defaultFilter = mapping.defaultFilter == null ? null : new DBSObjectFilter(mapping.defaultFilter);
        for (Map.Entry<String, DBSObjectFilter> entry : mapping.customFilters.entrySet()) {
            this.customFilters.put(entry.getKey(), new DBSObjectFilter(entry.getValue()));
        }
    }

    @Nullable
    DBSObjectFilter getFilter(@Nullable DBSObject parentObject, boolean firstMatch) {
        if (parentObject == null) {
            return defaultFilter;
        }
        if (!customFilters.isEmpty()) {
            String objectID = getFilterContainerUniqueID(parentObject);
            DBSObjectFilter filter = customFilters.get(objectID);
            if ((filter != null && !filter.isNotApplicable()) || firstMatch) {
                return filter;
            }
        }

        return firstMatch ? null : defaultFilter;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof FilterMapping)) {
            return false;
        }
        FilterMapping source = (FilterMapping) obj;
        return
                CommonUtils.equalObjects(typeName, source.typeName) &&
                        CommonUtils.equalObjects(defaultFilter, source.defaultFilter) &&
                        CommonUtils.equalObjects(customFilters, source.customFilters);
    }

    @Override
    public int hashCode() {
        return
            CommonUtils.hashCode(typeName) +
            CommonUtils.hashCode(defaultFilter) +
            CommonUtils.hashCode(customFilters);
    }

    public static String getFilterContainerUniqueID(@Nullable DBSObject parentObject) {
        String objectFullName = DBUtils.getObjectFullName(parentObject, DBPEvaluationContext.UI);
        DBSInstance ownerInstance = DBUtils.getObjectOwnerInstance(parentObject);
        if (!CommonUtils.equalObjects(ownerInstance.getName(), parentObject.getDataSource().getName())) {
            return ownerInstance.getName() + ":" + objectFullName;
        } else {
            return objectFullName;
        }
    }

}
