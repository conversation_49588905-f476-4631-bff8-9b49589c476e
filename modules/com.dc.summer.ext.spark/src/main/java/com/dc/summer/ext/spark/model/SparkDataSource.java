package com.dc.summer.ext.spark.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.hive.model.HiveDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCFeatureNotSupportedException;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Locale;

@Slf4j
public class SparkDataSource extends HiveDataSource {

    private String databaseProductName = null;

    public SparkDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel) throws DBException {
        super(monitor, container, metaModel);
    }

    @Override
    protected Connection openConnection(DBRProgressMonitor monitor, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration configuration) throws DBCException {
        Connection connection = super.openConnection(monitor, context, purpose, configuration);
        if (configuration.isCheckDatabaseProduct()) {
            if (databaseProductName == null) {
                try {
                    String name = connection.getMetaData().getDatabaseProductName();
                    databaseProductName = name != null ? name : "";
                } catch (SQLException e) {
                    log.error("load databaseProductName error.", e);
                    databaseProductName = "";
                }
            }
            if (!databaseProductName.toLowerCase(Locale.ROOT).contains("spark")) {
                throw new DBCFeatureNotSupportedException();
            }
        }
        return connection;
    }

}
