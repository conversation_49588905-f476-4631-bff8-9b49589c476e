package com.dc.summer.ext.mongodb.exec;

import com.dc.summer.ext.mongodb.model.MGCollection;
import com.dc.summer.model.document.exec.DocumentReadStatement;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.client.result.UpdateResult;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntity;
import org.mozilla.javascript.Undefined;

@Slf4j
public abstract class MGBaseStatement extends DocumentReadStatement<MGSession> {
    private String query;
    @Getter
    private final String originalQuery;
    protected MGCollection collection;
    protected MGCursor result;
    protected MongoIterable iterableResult;
    protected List<Document> resultList;
    protected long updateRowCount = -1L;
    private Throwable executeError;
    protected long offset;
    protected long limit;
    private int fetchSize;

    private int maxBindSize = 0;

    private final Map<Integer, Object> bindStatementMap = new LinkedHashMap<>(maxBindSize);

    @SuppressWarnings("StatementWithEmptyBody")
    protected MGBaseStatement(MGSession session, String query) {
        super(session);
        this.originalQuery = query;
        this.query = query;
        if (session.isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementOpen(this);
        }
        Matcher matcher = Pattern.compile("\\?").matcher(query);
        for (;matcher.find(); maxBindSize++) {
        }
    }

    public String getQueryString() {
        return this.query;
    }

    public String bindingQueryString() {
        if (!bindStatementMap.isEmpty()) {
            StringBuilder sql = new StringBuilder();
            String[] parts = this.query.split("\\?");
            for (int index = 0; index < parts.length; index++) {
                sql.append(parts[index]);
                if (index < parts.length - 1) {
                    if (!bindStatementMap.containsKey(index)) {
                        throw new NullPointerException(String.format("没有找到指定的下标[%s]", index));
                    }
                    sql.append(bindStatementMap.get(index));
                }
            }
            this.query = sql.toString();
            bindStatementMap.clear();
        }
        return this.query;
    }

    public void setObject(Integer index, Object value) throws DBCException {
        if (index >= maxBindSize) {
            throw new DBCException(String.format("参数绑定失败，绑定参数[%s]，超过最大下标[%s]。", index, maxBindSize));
        }
        bindStatementMap.put(index, value);
    }

    public void addToBatch() throws DBCException {
        throw new DBCException("Not implemented");
    }

    public int[] executeStatementBatch() throws DBCException {
        throw new DBCException("Not implemented");
    }

    public MGResultSet openResultSet() throws DBCException {
        if (this.resultList != null) {
            return new MGResultSet(this, this.resultList);
        } else if (this.result != null) {
            return new MGResultSet(this, this.result);
        } else {
            return this.iterableResult != null ? new MGResultSet(this, this.iterableResult.cursor(), this.iterableResult.iterator()) : null;
        }
    }

    public boolean hasResultSet() {
        return this.resultList != null || this.result != null || this.iterableResult != null;
    }

    public DBCResultSet openGeneratedKeysResultSet() throws DBCException {
        throw new DBCException("Not implemented");
    }

    public long getUpdateRowCount() {
        return this.updateRowCount;
    }

    public boolean nextResults() throws DBCException {
        this.updateRowCount = -1L;
        this.result = null;
        this.iterableResult = null;
        this.resultList = null;
        return false;
    }

    public void close() {
        this.result = null;
        this.iterableResult = null;
        this.resultList = null;
        if ((this.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementClose(this, this.getUpdateRowCount());
        }

    }

    @Override
    public void setLimit(long offset, long limit) throws DBCException {
        this.offset = offset;
        this.limit = limit;
    }

    @Override
    public List<String> getStatementWarningList() throws DBCException {
        throw new DBCException("Not implemented");
    }

    public @Nullable Throwable[] getStatementWarnings() throws DBCException {
        throw new DBCException("Not implemented");
    }

    public void setStatementTimeout(int timeout) throws DBCException {
    }

    public int getResultsFetchSize() {
        return this.fetchSize;
    }

    public void setResultsFetchSize(int fetchSize) throws DBCException {
        this.fetchSize = fetchSize;
    }

    public void cancelBlock(@NotNull DBRProgressMonitor monitor, @Nullable Thread blockThread) throws DBException {
        if (blockThread != null) {
            blockThread.interrupt();
        } else {
            throw new DBException("Mongo query cancel not implemented");
        }
    }

    public long getOffset() {
        return this.offset;
    }

    public long getLimit() {
        return limit;
    }

    protected DBCException handleExecuteError(Throwable ex) {
        this.executeError = ex;
        return ex instanceof DBCException ? (DBCException) ex : new DBCException(ex, (this.getSession()).getExecutionContext());
    }

    protected void beforeExecute() {
        this.executeError = null;
        if ((this.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementExecuteBegin(this);
        }

        this.startBlock();
    }

    protected void afterExecute() {
        this.endBlock();
        if ((this.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementExecuteEnd(this, this.getUpdateRowCount(), this.executeError);
        }

    }

    protected void startBlock() {
        (this.getSession()).getProgressMonitor().startBlock(this, (String) null);
    }

    protected void endBlock() {
        (this.getSession()).getProgressMonitor().endBlock();
    }

    protected void setExecutionResult(Document commandResult) {
        String key = "retval";
        Object retval = commandResult.get(key);
        setExecutionResult(retval, key);
    }

    protected void setExecutionResult(Object retval) {
        setExecutionResult(retval, "result");
    }

    private void setExecutionResult(Object retval, String key) {
        if (retval instanceof FindIterable) {
            this.result = new MGCursor((FindIterable) retval);
        } else if (retval instanceof MGCursor) {
            this.result = (MGCursor) retval;
        } else if (retval instanceof MongoIterable) {
            this.iterableResult = (MongoIterable<?>) retval;
        } else if (retval instanceof Collection && ((Collection<?>) retval).stream().allMatch(o -> o instanceof Map)) {
            Collection collection = (Collection) retval;
            this.resultList = new ArrayList<>(collection.size());
            for (Object o : collection) {
                if (o instanceof Document) {
                    this.resultList.add((Document) o);
                } else {
                    this.resultList.add(new Document((Map<String, Object>) o));
                }
            }
        } else if (retval instanceof Document) {
            addResultList((Document) retval);
        } else if (retval instanceof Map) {
            addResultList(new Document((Map<String, Object>) retval));
        } else if (retval instanceof Undefined) {
            log.debug(String.format("[%s] retval is undefined.", key));
        } else if (retval instanceof  MGUpdateRowCount) {
            this.updateRowCount = ((MGUpdateRowCount) retval).longValue();
        } else {
            addResultList(new Document(key, retval));
        }
    }

    private void addResultList(Document document) {
        this.resultList = new ArrayList<>(1) {{
            this.add(document);
        }};
    }

    protected void setExecutionResult(UpdateResult updateResult) {
        this.updateRowCount = updateResult.getModifiedCount();
    }

    protected void setExecutionResult(DeleteResult deleteResult) {
        this.updateRowCount = deleteResult.getDeletedCount();
    }

    protected void setExecutionResult(InsertOneResult insertOneResult) {
        this.updateRowCount = 1L;
    }

    public DBSEntity getSourceEntity() {
        return this.collection;
    }
}
