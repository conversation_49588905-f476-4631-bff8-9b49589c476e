package com.dc.summer.ext.mongodb.data.format;

import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.dc.utils.StringUtils;
import org.bson.BsonTimestamp;

public class Timestamp implements DBFunctionObject {

    private final BsonTimestamp bsonTimestamp;

    public Timestamp(BsonTimestamp bsonTimestamp) {
        this.bsonTimestamp = bsonTimestamp;
    }

    public Timestamp(String str) {
        String time = StringUtils.substring(str, "Timestamp(", ", ");
        String inc = StringUtils.substring(str, ", ", -1);
        this.bsonTimestamp = new BsonTimestamp(Integer.parseInt(time), Integer.parseInt(inc));
    }

    @Override
    public String toString() {
        return String.format("Timestamp(%s, %s)", bsonTimestamp.getTime(), bsonTimestamp.getInc());
    }

}
