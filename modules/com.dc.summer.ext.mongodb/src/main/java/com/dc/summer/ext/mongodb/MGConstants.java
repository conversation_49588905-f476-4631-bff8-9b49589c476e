package com.dc.summer.ext.mongodb;

import com.dc.summer.ext.mongodb.exec.MGTransactionIsolation;
import com.dc.summer.model.DBPTransactionIsolation;

public class MGConstants {
   public static final String DRIVER_BUNDLE_NAME = "org.mongodb.mongo-java-driver";
   public static final int DEFAULT_PORT = 27017;
   public static final String CAT_OPTIONS = "Options";
   public static final String NONE_MECHANISM = "NONE";
   public static final String MONGODB_CR_MECHANISM = "MONGODB-CR";
   public static final int DEF_SERVER_SELECT_TIMEOUT = 5000;
   public static final int ERROR_CMD_NOT_ALLOWED = 8000;
   public static final String AUTH_TOKEN_DIV = "|";
   public static final String ATTR_ID = "_id";
   public static final String FUNC_OBJECT_ID = "ObjectId";
   public static final String FUNC_ISO_DATE = "ISODate";
   public static final String DEFAULT_DATABASE_NAME = "local";
   public static final String ADMIN_DATABASE_NAME = "admin";
   public static final String SYSTEM_DB_ADMIN = "admin";
   public static final String SYSTEM_DB_LOCAL = "local";
   public static final String COL_NAME_INPROG = "$cmd.sys.inprog";
   /** @deprecated */
   @Deprecated(
      since = "22.2"
   )
   public static final String PROP_AUTH_MECHANISM_LEGACY = "@summer-mongo-cred-mechanism@";
   /** @deprecated */
   @Deprecated(
      since = "22.2"
   )
   public static final String PROP_AUTH_SOURCE_LEGACY = "@dbeaver-auth-source@";
   /** @deprecated */
   @Deprecated(
      since = "22.2"
   )
   public static final String PROP_AUTH_LEGACY = "@dbeaver-auth@";
   /** @deprecated */
   @Deprecated(
      since = "22.2"
   )
   public static final String PROP_SEED_LEGACY = "seed";
   public static final String PROP_AUTH_MECHANISM = "authMechanism";
   public static final String PROP_AUTH_SOURCE = "authSource";
   public static final String PROP_REPLICA_SET = "replicaSet";
   public static final String PROP_SHOW_ALL_DATABASES = "showAllDatabases";
   public static final String PROP_CATEGORY_CONNECTION = "Connection";
   public static final String PROP_CATEGORY_HEARTBEAT = "Heartbeat";
   public static final String PROP_CATEGORY_SSL = "SSL";
   public static final String PROP_CATEGORY_MISC = "Miscellaneous";
   public static final String PROP_SERVER_SELECT_TIMEOUT = "serverSelectTimeout";
   public static final String PROP_CONNECT_TIMEOUT = "connectTimeout";
   public static final String PROP_SOCKET_TIMEOUT = "socketTimeout";
   public static final String PROP_MAX_CONNECTION_IDLE_TIME = "maxConnectionIdleTime";
   public static final String PROP_MAX_CONNECTION_LIFE_TIME = "maxConnectionLifeTime";
   public static final String PROP_MAX_WAIT_TIME = "maxWaitTime";
   public static final String PROP_SINGLE_NODE_MODE = "singleNodeMode";
   public static final String PROP_RETRY_READS = "retryReads";
   public static final String PROP_RETRY_WRITES = "retryWrites";
   public static final String PROP_HEARTBEAT_FREQUENCY = "heartbeatFrequency";
   public static final String PROP_MIN_HEARTBEAT_FREQUENCY = "minHeartbeatFrequency";
   public static final String PROP_FORCE_CLIENT_SIDE_JS = "forceClientSideJS";
   public static final String PROP_SSL_ENABLED = "sslEnabled";
   public static final String PROP_SSL_INVALID_HOST_NAME_ALLOWED = "sslInvalidHostNameAllowed";
   public static final String SYSTEM_COL_JS = "system.js";
   public static final String SYSTEM_COL_USERS = "system.users";
   public static final String SYSTEM_COL_ROLES = "system.roles";
   public static final String DRIVER_MONGODB = "mongodb";
   public static final String DRIVER_AWS_DOCUMENTDB = "documentdb";
   public static final String[] JS_KEYWORDS = new String[]{"ABSTRACT", "ARGUMENTS", "BREAK", "CASE", "CATCH", "CLASS", "CONST", "CONTINUE", "DEFAULT", "DB", "DO", "ELSE", "EVAL", "FALSE", "FINALLY", "FOR", "FUNCTION", "GOTO", "IF", "IN", "INSTANCEOF", "INTERFACE", "LET", "NATIVE", "NEW", "NULL", "PACKAGE", "PRIVATE", "PROTECTED", "PUBLIC", "RETURN", "STATIC", "SUPER", "SWITCH", "THIS", "THROW", "THROWS", "TRANSIENT", "TRUE", "TRY", "TYPEOF", "VAR", "VOLATILE", "WHILE"};
   public static final String[] JS_TYPES = new String[]{"BOOLEAN", "BYTE", "CHAR", "DOUBLE", "FLOAT", "INT", "LONG", "SHORT", "VOID"};
   public static final String[] SQL_FUNCTIONS = new String[]{"MIN", "MAX", "SUM", "AVG", "ISODate", "ObjectId"};
   public static final String DOCUMENT_ATTR_NAME = "document";
   public static final String DEFAULT_QUOTE_STR = "\"";
   public static final String HANDLER_SSL = "mongo_ssl";
   public static final String LEGACY_URL_PREFIX = "mongo://";
   public static final DBPTransactionIsolation DEFAULT_TXN_ISOLATION = new MGTransactionIsolation();
}
