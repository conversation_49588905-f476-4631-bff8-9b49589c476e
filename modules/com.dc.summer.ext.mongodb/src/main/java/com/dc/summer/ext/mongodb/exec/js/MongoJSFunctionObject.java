package com.dc.summer.ext.mongodb.exec.js;

import com.dc.summer.DBException;
import com.dc.summer.ext.mongodb.data.format.ISODate;
import com.dc.utils.DateUtil;
import org.bson.BsonTimestamp;
import org.bson.types.*;
import org.mozilla.javascript.Undefined;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Base64;
import java.util.Date;

public class MongoJSFunctionObject {

    public static ObjectId ObjectId(Object o) throws DBException {
        final int len = 24;
        StringBuilder builder = new StringBuilder();
        byte[] strBytes = String.valueOf(o).getBytes();
        if (strBytes.length > len) {
            throw new DBException("Object ID exceeds the maximum length limit.");
        } else {
            for (byte strByte : strBytes) {
                if (strByte < (byte) '0' || strByte > (byte) 'f' || (strByte > (byte) '9' && strByte < (byte) 'a')) {
                    builder.append("0");
                } else {
                    builder.append(new String(new byte[]{strByte}));
                }
            }
            if (builder.length() < len) {
                builder.append("0".repeat(Math.max(0, len - builder.length())));
            }
        }
        return new ObjectId(builder.toString());
    }

    public static Object ObjectID(Object o) throws DBException {
        return ObjectId(o);
    }

    public static Binary Binary(Integer integer, Object o) {
        byte[] bytes;
        if (o instanceof byte[]) {
            bytes = (byte[]) o;
        } else {
            bytes = String.valueOf(o).getBytes();
        }
        bytes = Base64.getDecoder().decode(bytes);
        return new Binary(integer.byteValue(), bytes);
    }

    public static Binary BinData(Integer integer, Object o) {
        return Binary(integer, o);
    }

    public static Binary UUID(Object o) {
        return BinData(4, o);
    }

    public static Double Double(Object o) {
        if (o instanceof Double) {
            return (Double) o;
        } else {
            return Double.valueOf(String.valueOf(o));
        }
    }

    public static Date ISODate(Object o) throws ParseException {
        return DateUtil.str2DateUtc(String.valueOf(o));
    }

    public static Integer NumberInt(Object o) {
        if (o instanceof Integer) {
            return (Integer) o;
        } else if (o instanceof Double) {
            return ((Double) o).intValue();
        } else {
            return Integer.valueOf(String.valueOf(o));
        }
    }

    public static Integer Int32(Object o) {
        return NumberInt(o);
    }

    public static Long NumberLong(Object o) {
        if (o instanceof Integer) {
            return ((Integer) o).longValue();
        } else if (o instanceof Long) {
            return (Long) o;
        } else if (o instanceof Double) {
            return ((Double) o).longValue();
        } else {
            return Long.valueOf(String.valueOf(o));
        }
    }

    public static Long Long(Object o) {
        return NumberLong(o);
    }

    public static Code Code(String str) {
        return new Code(str);
    }

    public static Decimal128 NumberDecimal(Object o) {
        if (o instanceof Long) {
            return new Decimal128((Long) o);
        } else if (o instanceof BigDecimal) {
            return new Decimal128((BigDecimal) o);
        } else {
            return Decimal128.parse(String.valueOf(o));
        }
    }
    public static Decimal128 Decimal128(Object o) {
        return NumberDecimal(o);
    }

    public static MaxKey MaxKey() {
        return new MaxKey();
    }

    public static MinKey MinKey() {
        return new MinKey();
    }

    public static BsonTimestamp Timestamp(Object first, Object second) {
        if (first == null || first.getClass() == Undefined.class) {
            return new BsonTimestamp();
        } else if (second == null || second.getClass() == Undefined.class) {
            return new BsonTimestamp(Long.parseLong(String.valueOf(first)));
        } else {
            int increment;
            if (second instanceof Double) {
                increment = ((Double) second).intValue();
            } else {
                increment = Integer.parseInt(String.valueOf(second));
            }
            return new BsonTimestamp(Integer.parseInt(String.valueOf(first)), increment);
        }
    }

}
