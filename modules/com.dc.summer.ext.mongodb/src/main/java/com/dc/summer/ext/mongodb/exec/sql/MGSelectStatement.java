package com.dc.summer.ext.mongodb.exec.sql;

import com.dc.summer.ext.mongodb.exec.MGBaseStatement;
import com.dc.summer.ext.mongodb.exec.MGCursor;
import com.dc.summer.ext.mongodb.exec.MGSession;
import com.dc.summer.ext.mongodb.model.MGCollection;
import java.util.Iterator;
import org.bson.Document;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDAttributeConstraint;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.exec.DBCException;

public class MGSelectStatement extends MGBaseStatement {
   @Nullable
   private final DBDDataFilter dataFilter;

   public MGSelectStatement(@NotNull MGSession mongoSession, @NotNull MGCollection collection, @Nullable DBDDataFilter dataFilter) {
      super(mongoSession, MongoSQLUtils.makeSQLQuery(collection, dataFilter));
      this.collection = collection;
      this.dataFilter = dataFilter;
   }

   public boolean executeStatement() throws DBCException {
      this.beforeExecute();

      try {
         Document ref = MongoSQLUtils.makeQueryFromFilter(((MGSession)this.getSession()).getDataSource(), this.dataFilter);
         this.result = new MGCursor(ref == null ? this.collection.getMongoCollection((MGSession)this.getSession()).find() : this.collection.getMongoCollection((MGSession)this.getSession()).find(ref));
         if (this.dataFilter != null && this.dataFilter.hasOrdering()) {
            Document order = new Document();

            for (DBDAttributeConstraint constr : this.dataFilter.getOrderConstraints()) {
               String name = MongoSQLUtils.unquote(constr.getFullAttributeName());
               order.put(name, constr.isOrderDescending() ? -1 : 1);
            }

            this.result.sort(order);
         }
      } catch (Throwable var9) {
         throw this.handleExecuteError(var9);
      } finally {
         this.afterExecute();
      }

      return true;
   }
}
