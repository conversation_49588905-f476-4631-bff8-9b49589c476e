package com.dc.summer.ext.mongodb.model;

import com.dc.summer.ext.mongodb.exec.MGSession;
import com.dc.summer.model.impl.jdbc.cache.IgnoreCache;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import org.bson.BsonDocument;
import org.bson.BsonInt64;
import org.bson.Document;
import org.bson.conversions.Bson;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.DBPObjectStatistics;
import com.dc.summer.model.DBPRefreshableObject;
import com.dc.summer.model.DBPSaveableObject;
import com.dc.summer.model.DBPSystemObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.BasicObjectCache;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.utils.CommonUtils;

public class MGDatabase implements DBSCatalog, DBPSaveableObject, DBPNamedObject2, DBPSystemObject, DBPRefreshableObject, DBPObjectStatistics {
   private final MGDataSource dataSource;
   private String dbName;
   private final CollectionCache collectionCache = new CollectionCache();
   private final FunctionCache functionCache = IgnoreCache.get(FunctionCache.class, this);
   private final UsersCache userCache = IgnoreCache.get(UsersCache.class, this);
   private boolean persisted;
   private Document dbStats;

   public MGDatabase(MGDataSource dataSource, String dbName) {
      this.dataSource = dataSource;
      this.dbName = dbName;
      this.persisted = true;
   }

   public CollectionCache getCollectionCache() {
      return this.collectionCache;
   }

   public @NotNull MGDataSource getDataSource() {
      return this.dataSource;
   }

   @Property(
      viewable = true,
      editable = true,
      valueTransformer = DBObjectNameCaseTransformer.class,
      order = 1
   )
   public @NotNull String getName() {
      return this.dbName;
   }

   public void setName(String dbName) {
      this.dbName = dbName;
   }

   public String getDescription() {
      return null;
   }

   public DBSObject getParentObject() {
      return this.dataSource.getContainer();
   }

   public boolean isPersisted() {
      return this.persisted;
   }

   public void setPersisted(boolean persisted) {
      this.persisted = persisted;
   }

   public MGCollection getCollection(DBRProgressMonitor monitor, String name) throws DBException {
      return this.collectionCache.getObject(monitor, this, name);
   }

   public @Association Collection<MGCollection> getCollections(DBRProgressMonitor monitor) throws DBException {
      return this.collectionCache.getAllObjects(monitor, this);
   }

   public Collection<MGCollection> getChildren(@NotNull DBRProgressMonitor monitor) throws DBException {
      return this.collectionCache.getAllObjects(monitor, this);
   }

   public MGCollection getChild(@NotNull DBRProgressMonitor monitor, @NotNull String childName) throws DBException {
      MGCollection collection = this.collectionCache.getObject(monitor, this, childName);
      return collection == null ? new MGCollection(getDataSource().getDatabase(dbName), childName, false) : collection;
   }

   public @NotNull Class<MGCollection> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) throws DBException {
      return MGCollection.class;
   }

   public @Association Collection<MGFunction> getFunctions(DBRProgressMonitor monitor) throws DBException {
      return this.functionCache.getAllObjects(monitor, this);
   }

   public @Association Collection<MGUser> getUsers(DBRProgressMonitor monitor) throws DBException {
      return this.userCache.getAllObjects(monitor, this);
   }

   public void cacheStructure(@NotNull DBRProgressMonitor monitor, int scope) throws DBException {
      this.collectionCache.getAllObjects(monitor, this);
   }

   public synchronized @NotNull MongoDatabase getDatabase(MGSession session) throws DBCException {
      MongoClient client = session.getExecutionContext().getClient();
      if (client == null) {
         throw new DBCException("Not connected to Mongo");
      } else {
         return client.getDatabase(this.dbName);
      }
   }

   public String toString() {
      return this.getName();
   }

   public boolean isSystem() {
      return this.dbName.equals("admin") || this.dbName.equals("local");
   }

   public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
      this.collectionCache.clearCache();
      this.functionCache.clearCache();
      this.userCache.clearCache();
      this.dbStats = null;
      return this;
   }

   public boolean hasStatistics() {
      return this.dbStats != null;
   }

   public long getStatObjectSize() {
      if (this.dbStats != null) {
         Object dataSize = this.dbStats.get("dataSize");
         if (dataSize == null) {
            dataSize = this.dbStats.get("storageSize");
         }

         return dataSize == null ? -1L : CommonUtils.toLong(dataSize);
      } else {
         return -1L;
      }
   }

   public @Nullable DBPPropertySource getStatProperties() {
      return null;
   }

   public void readStatistics(DBRProgressMonitor monitor) throws DBException {
      if (this.dbStats == null) {
         try {
            try (MGSession session = DBUtils.openMetaSession(monitor, this, "Read collections")) {
               this.dbStats = this.getDatabase(session).runCommand(new BsonDocument("dbStats", new BsonInt64(1L)));
            }
         } catch (Exception var13) {
            throw new DBException("Error reading database statistics", var13);
         }

         if (this.dbStats.get("error") != null) {
            throw new DBException("Error reading database statistics: " + this.dbStats.get("error"));
         }
      }
   }

   public class CollectionCache extends BasicObjectCache<MGDatabase, MGCollection> {
      public @NotNull List<MGCollection> getAllObjects(@NotNull DBRProgressMonitor monitor, @Nullable MGDatabase mongoDatabase) throws DBException {
         if (!this.isFullyCached()) {
            List<MGCollection> tables = new ArrayList<>();

            try (MGSession session = DBUtils.openMetaSession(monitor, MGDatabase.this, "Read collections")) {
               if (MGDatabase.this.dataSource.isServerVersionAtLeast(4, 0)) {
                  Document listCollectionsCommand = new Document("listCollections", "1");
                  listCollectionsCommand.put("nameOnly", true);
                  listCollectionsCommand.put("authorizedCollections", true);
                  Document result = MGDatabase.this.getDatabase(session).runCommand(listCollectionsCommand);
                  if (result.get("error") != null) {
                     throw new DBException("Error reading collection names: " + result.get("error"));
                  }

                  Document colNamesCursor = (Document) result.get("cursor");
                  if (colNamesCursor != null) {
                     List<Document> firstBatch = colNamesCursor.getList("firstBatch", Document.class);
                     if (firstBatch != null) {
                        for (Document colDocument : firstBatch) {
                           String colName = (String) colDocument.get("name");
                           if (colName != null) {
                              tables.add(new MGCollection(MGDatabase.this, colName, true));
                           }
                        }
                     }
                  }
               } else {

                  for (String colNamex : MGDatabase.this.getDatabase(session).listCollectionNames()) {
                     tables.add(new MGCollection(MGDatabase.this, colNamex, true));
                  }
               }

               tables.sort(DBUtils.nameComparator());
               this.setCache(tables);
            }
         }

         return this.getCachedObjects();
      }
   }

   class FunctionCache extends BasicObjectCache<MGDatabase, MGFunction> {
      public @NotNull List<MGFunction> getAllObjects(@NotNull DBRProgressMonitor monitor, @Nullable MGDatabase mongoDatabase) throws DBException {
         if (!this.isFullyCached()) {

            try (MGSession session = DBUtils.openMetaSession(monitor, MGDatabase.this, "Read functions")) {
               List<MGFunction> functions = new ArrayList<>();
               MongoCollection<Document> jsCol = MGDatabase.this.getDatabase(session).getCollection("system.js", Document.class);

               for (Document jsObject : jsCol.find()) {
                  String name = (String) jsObject.get("_id");
                  Object value = jsObject.get("value");
                  functions.add(new MGFunction(mongoDatabase, true, name, CommonUtils.toString(value)));
                  if (monitor.isCanceled()) {
                     break;
                  }
               }

               this.setCache(functions);
            }
         }

         return this.getCachedObjects();
      }
   }

   class UsersCache extends BasicObjectCache<MGDatabase, MGUser> {
      public @NotNull List<MGUser> getAllObjects(@NotNull DBRProgressMonitor monitor, @Nullable MGDatabase mongoDatabase) throws DBException {
         if (!this.isFullyCached()) {

            try (MGSession session = DBUtils.openMetaSession(monitor, MGDatabase.this, "Read users")) {
               List<MGUser> functions = new ArrayList<>();
               MongoDatabase adminDB = MGDatabase.this.getDataSource().getAdminDatabase(session);
               MongoCollection<Document> usersCol = adminDB.getCollection("system.users", Document.class);
               Bson dbCriteria = new BasicDBObject("db", MGDatabase.this.getName());

               for (Document jsObject : usersCol.find(dbCriteria)) {
                  String name = (String) jsObject.get("user");
                  functions.add(new MGUser(mongoDatabase, name));
                  if (monitor.isCanceled()) {
                     break;
                  }
               }

               this.setCache(functions);
            }

         }

         return this.getCachedObjects();
      }
   }
}
