package com.dc.summer.ext.mongodb.model;

import com.dc.summer.model.security.StaticTLSUtils;
import com.mongodb.MongoCredential;
import java.nio.file.Path;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.registry.network.NetworkHandlerDescriptor;
import com.dc.summer.registry.network.NetworkHandlerRegistry;

public class MGDataSourceDocumentDB extends MGDataSource {
   private static final Log log = Log.getLog(MGDataSourceDocumentDB.class);
   private static final String AWS_RDS_CA_BUNDLE_URL = "https://s3.amazonaws.com/rds-downloads/rds-combined-ca-bundle.pem";
   private static final String AWS_LOCAL_CONFIG_FOLDER = ".aws";

   public MGDataSourceDocumentDB(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
      super(monitor, container);
   }

   public boolean supportsDecimal128() {
      return false;
   }

   public DBWHandlerConfiguration getCustomSSLConfiguration(DBRProgressMonitor monitor, DBPConnectionConfiguration connectionInfo) {
      NetworkHandlerDescriptor sslHD = NetworkHandlerRegistry.getInstance().getDescriptor("mongo_ssl");
      if (sslHD == null) {
         log.warn("No Mongo SSL handler descriptor found");
         return null;
      } else {
         Path caCertFile = StaticTLSUtils.getStaticCertFile(monitor, MGDataSourceDocumentDB.class, ".aws", "https://s3.amazonaws.com/rds-downloads/rds-combined-ca-bundle.pem");
         if (caCertFile != null) {
            DBWHandlerConfiguration sslConfig = new DBWHandlerConfiguration(sslHD, this.getContainer());
            sslConfig.setEnabled(true);
            sslConfig.setProperty("ssl.ca.cert", caCertFile.toAbsolutePath().toString());
            sslConfig.setProperty("sslInvalidHostNameAllowed", true);
            sslConfig.setProperty("ssl.self-signed-cert", true);
            return sslConfig;
         } else {
            return null;
         }
      }
   }

   public String getDefaultAuthMechanism() {
      return MongoCredential.SCRAM_SHA_1_MECHANISM;
   }

   public boolean isEvalSupported() {
      return false;
   }

   public String getDatabaseType() {
      return "DocumentDB";
   }
}
