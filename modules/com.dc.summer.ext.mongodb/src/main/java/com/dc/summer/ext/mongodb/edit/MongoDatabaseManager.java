package com.dc.summer.ext.mongodb.edit;

import com.dc.summer.ext.mongodb.exec.MGExecutionContext;
import com.dc.summer.ext.mongodb.exec.MGSession;
import com.dc.summer.ext.mongodb.model.MGDataSource;
import com.dc.summer.ext.mongodb.model.MGDatabase;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import java.util.List;
import java.util.Map;
import org.bson.Document;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.edit.DirectDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;

public class MongoDatabaseManager extends SQLObjectEditor<MGDatabase, MGDataSource> {
   public long getMakerOptions(DBPDataSource dataSource) {
      return 1L;
   }

   public @Nullable DBSObjectCache<? extends DBSObject, MGDatabase> getObjectsCache(MGDatabase object) {
      return object.getDataSource().getDatabaseCache();
   }

   protected MGDatabase createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container, Object copyFrom, Map<String, Object> options) {
      return new MGDatabase((MGDataSource)container, "NewDatabase");
   }

   protected void addObjectCreateActions(DBRProgressMonitor monitor, final DBCExecutionContext executionContext, List<DBEPersistAction> actions, final SQLObjectEditor<MGDatabase, MGDataSource>.ObjectCreateCommand command, Map<String, Object> options) {
      actions.add(new DirectDatabasePersistAction("Create database") {
         public void afterExecute(DBCSession session, Throwable error) throws DBCException {
            MGDatabase database = (MGDatabase)command.getObject();
            if (database.getDataSource().getDatabase(database.getName()) != null) {
               throw new DBCException("Database '" + database.getName() + "' already exists");
            } else {
               MongoDatabase db = ((MGExecutionContext)executionContext).getClient().getDatabase(database.getName());
               Document testObject = new Document("test", "test");
               MongoCollection testCollection = db.getCollection("test");
               testCollection.insertOne(testObject);
               testCollection.drop();
            }
         }
      });
   }

   protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, final SQLObjectEditor<MGDatabase, MGDataSource>.ObjectDeleteCommand command, Map<String, Object> options) {
      actions.add(new DirectDatabasePersistAction("Drop database") {
         public void afterExecute(DBCSession session, Throwable error) throws DBCException {
            MGDatabase database = (MGDatabase)command.getObject();
            database.getDatabase((MGSession)session).drop();
         }
      });
   }
}
