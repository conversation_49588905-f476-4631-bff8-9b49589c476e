package com.dc.summer.ext.mongodb.model;

import java.util.Collection;
import java.util.Map;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPScriptObject;
import com.dc.summer.model.impl.struct.AbstractProcedure;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSProcedureParameter;
import com.dc.summer.model.struct.rdb.DBSProcedureType;

public class MGFunction extends AbstractProcedure<MGDataSource, MGDatabase> implements DBPScriptObject {
   private static final Log log = Log.getLog(MGFunction.class);
   private String value;

   public MGFunction(MGDatabase database, boolean persisted, String name, String value) {
      super(database, persisted, name, (String)null);
      this.value = value;
   }

   public DBSProcedureType getProcedureType() {
      return DBSProcedureType.FUNCTION;
   }

   public Collection<? extends DBSProcedureParameter> getParameters(DBRProgressMonitor monitor) throws DBException {
      return null;
   }

   public @NotNull String getFullyQualifiedName(DBPEvaluationContext context) {
      return ((MGDatabase)this.getContainer()).getName() + "." + this.getName();
   }

   public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException {
      return this.value;
   }
}
