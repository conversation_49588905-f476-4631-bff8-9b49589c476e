package com.dc.summer.ext.mongodb.model;

import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.rdb.DBSTableColumn;

public abstract class MGCollectionAttribute implements DBSTableColumn {
   private final MGCollection table;
   private String name;
   private int position;

   public MGCollectionAttribute(MGCollection table, String name, int position) {
      this.table = table;
      this.name = name;
      this.position = position;
   }

   public @NotNull DBPDataSource getDataSource() {
      return this.table.getDataSource();
   }

   @Property(
      viewable = true,
      editable = true,
      valueTransformer = DBObjectNameCaseTransformer.class,
      order = 1
   )
   public @NotNull String getName() {
      return this.name;
   }

   public boolean isPersisted() {
      return true;
   }

   public int getOrdinalPosition() {
      return this.position;
   }

   public @NotNull MGCollection getParentObject() {
      return this.table;
   }

   public String getDefaultValue() {
      return null;
   }

   public String getDescription() {
      return null;
   }

   public String getFullTypeName() {
      return DBUtils.getFullTypeName(this);
   }

   public Integer getScale() {
      return 0;
   }

   public Integer getPrecision() {
      return 0;
   }

   public long getMaxLength() {
      return 0L;
   }

   public long getTypeModifiers() {
      return 0L;
   }

   public String toString() {
      return DBUtils.getFullQualifiedName(this.getDataSource(), new DBPNamedObject[]{this.table, this});
   }
}
