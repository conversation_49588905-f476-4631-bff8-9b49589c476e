package com.dc.summer.ext.oscar.cluster.data;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class OscarClusterStringValueHandler extends JDBCObjectValueHandler {

    public static final OscarClusterStringValueHandler INSTANCE = new OscarClusterStringValueHandler();

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        if ("CLOB".equalsIgnoreCase(type.getTypeName()) ||
                "BYTEA".equalsIgnoreCase(type.getTypeName()) ||
                "FLOAT4".equalsIgnoreCase(type.getTypeName())) {
            return resultSet.getString(index);
        }
        return super.fetchColumnValue(session, resultSet, type, index);
    }
}
