package com.dc.summer.ext.oscar.cluster.model;

import com.dc.summer.ext.oscar.model.OscarDialect;
import com.dc.summer.model.sql.SQLConstants;

public class OscarClusterDialect extends OscarDialect {

    private static final String[][] OSCAR_CLUSTER_BEGIN_END_BLOCK = new String[][]{
            {SQLConstants.BLOCK_BEGIN, SQLConstants.BLOCK_END},
            {"IF", SQLConstants.BLOCK_END + " IF"},
            {"LOOP", SQLConstants.BLOCK_END + " LOOP"},
            {SQLConstants.KEYWORD_CASE, SQLConstants.BLOCK_END + " " + SQLConstants.KEYWORD_CASE},
            {SQLConstants.BLOCK_BEGIN, "TRAN"},
            {SQLConstants.BLOCK_BEGIN, "TRANSACTION"},
    };

    public OscarClusterDialect() {
        super("神通xCluster", "oscar_cluster");
    }

    @Override
    public String[][] getBlockBoundStrings() {
        return OSCAR_CLUSTER_BEGIN_END_BLOCK;
    }
}
