package com.dc.summer.ext.oscar.cluster.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.oscar.model.OscarDataSource;
import com.dc.summer.ext.oscar.model.OscarDataTypeCache;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.impl.jdbc.cache.JDBCBasicDataTypeCache;
import com.dc.summer.model.impl.jdbc.struct.JDBCDataType;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class Oscar<PERSON>lusterMetaModel extends GenericMetaModel {

    @Override
    public GenericDataSource createDataSourceImpl(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new OscarClusterDataSource(monitor, container, this);
    }

    @Override
    public JDBCBasicDataTypeCache<GenericStructContainer, ? extends JDBCDataType> createDataTypeCache(GenericStructContainer container) {
        return new OscarDataTypeCache(container);
    }

}
