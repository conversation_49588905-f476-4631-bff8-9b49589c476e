package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreatePackageStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create package statement context.
 */
@Getter
public class CreatePackageStatementContext extends CommonSQLStatementContext {

    public CreatePackageStatementContext(final CreatePackageStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CreatePackageStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(SqlConstant.PACKAGE);
        sqlAuthModel.setName(sqlStatement.getPackageSegment().getIdentifier().getValue());
        sqlAuthModel.setSchemaName(sqlStatement.getPackageSegment().getOwner().map(owner -> owner.getIdentifier().getValue()).orElse(currentDatabaseName));
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreatePackageStatement getSqlStatement() {
        return (CreatePackageStatement) super.getSqlStatement();
    }
}
