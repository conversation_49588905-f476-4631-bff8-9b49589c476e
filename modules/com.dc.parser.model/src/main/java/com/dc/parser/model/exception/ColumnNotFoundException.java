package com.dc.parser.model.exception;

import com.dc.infra.exception.sqlstate.XOpenSQLState;
import com.dc.infra.exception.type.kernel.category.MetaDataSQLException;

/**
 * Column not found exception.
 */
public final class ColumnNotFoundException extends MetaDataSQLException {

    private static final long serialVersionUID = -1305402273592303335L;

    public ColumnNotFoundException(final String columnExpression, final String segmentTypeMessage) {
        super(XOpenSQLState.NOT_FOUND, 3, "Unknown column '%s' in '%s'.", columnExpression, segmentTypeMessage);
    }
}
