package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterPackageStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter package statement context.
 */
@Getter
public class AlterPackageStatementContext extends CommonSQLStatementContext {

    public AlterPackageStatementContext(final AlterPackageStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterPackageStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType(SqlConstant.PACKAGE);
        sqlAuthModel.setName(sqlStatement.getPackageSegment().getIdentifier().getValue());
        sqlAuthModel.setSchemaName(sqlStatement.getPackageSegment().getOwner().map(owner -> owner.getIdentifier().getValue()).orElse(currentDatabaseName));
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterPackageStatement getSqlStatement() {
        return (AlterPackageStatement) super.getSqlStatement();
    }
}
