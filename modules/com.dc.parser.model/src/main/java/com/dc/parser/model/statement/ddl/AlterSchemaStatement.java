package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Alter schema statement.
 */
@Getter
@Setter
public abstract class AlterSchemaStatement extends AbstractSQLStatement implements DDLStatement {
    
    private IdentifierValue schemaName;

    /**
     * Get rename schema.
     *
     * @return rename schema
     */
    public Optional<IdentifierValue> getRenameSchema() {
        return Optional.empty();
    }
}
