package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class IndexCountRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateIndexStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        if ((parameter.getIndexCount() + 1) <= Integer.parseInt(parameter.getCheckRuleContent().getValue())) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public void prepare(SQLStatement sqlStatement, CheckRuleParameter parameter, Function<String, List<Map<String, Object>>> function) {

        if (!(sqlStatement instanceof CreateIndexStatement)) {
            return;
        }

        CreateIndexStatement checkStatement = (CreateIndexStatement) sqlStatement;

        if (checkStatement.getTable() != null && checkStatement.getTable().getTableName() != null) {
            TableNameSegment tableNameSegment = checkStatement.getTable().getTableName();

            if (tableNameSegment.getIdentifier() != null ) {
                String tableName = tableNameSegment.getIdentifier().getValue();

                String schemaName = parameter.getDefaultSchemaName();
                if (checkStatement.getTable().getOwner().isPresent() && checkStatement.getTable().getOwner().get().getIdentifier() != null) {
                    schemaName = checkStatement.getTable().getOwner().get().getIdentifier().getValue();
                }

                String queryIndexCountSql = getQueryIndexCountSql(schemaName, tableName);

                List<Map<String, Object>> apply = function.apply(queryIndexCountSql);
                if (!apply.isEmpty() && apply.get(0).get("COUNT") != null) {
                    int count = Integer.parseInt(apply.get(0).get("COUNT").toString()) ;
                    parameter.setIndexCount(count);
                }
            }
        }
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_INDEX_COUNT.getValue();
    }

    public String getQueryIndexCountSql(String schemaName, String tableName) {
        return null;
    }
}
