package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.util.RowFilterRewriteUtil;

public class SubQueryDepthRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof SelectStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        SelectStatement checkStatement = (SelectStatement) sqlStatement;
        int quantity = Integer.parseInt(parameter.getCheckRuleContent().getValue());
        int nestLoopCount = RowFilterRewriteUtil.getSelectNestLoop(checkStatement, 1);
        return nestLoopCount > quantity ? CheckResult.buildFailResult(parameter.getCheckRuleContent()) : CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_SUB_QUERY_DEPTH.getValue();
    }

}
