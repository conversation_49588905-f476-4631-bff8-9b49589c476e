
package com.dc.parser.model.segment.dml.expr.simple;

import com.dc.parser.model.enums.ParameterMarkerType;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.generic.AliasAvailable;
import com.dc.parser.model.segment.generic.AliasSegment;
import com.dc.parser.model.segment.generic.ParameterMarkerSegment;
import com.dc.parser.model.segment.generic.bounded.ColumnSegmentBoundInfo;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Optional;

/**
 * Parameter marker expression segment.
 */
@RequiredArgsConstructor
@Getter
@EqualsAndHashCode(exclude = "boundedInfo")
public class ParameterMarkerExpressionSegment implements SimpleExpressionSegment, ProjectionSegment, AliasAvailable, ParameterMarkerSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final int parameterMarkerIndex;
    
    private final ParameterMarkerType parameterMarkerType;
    
    @Setter
    private AliasSegment alias;
    
    @Setter
    private ColumnSegmentBoundInfo boundedInfo;
    
    public ParameterMarkerExpressionSegment(final int startIndex, final int stopIndex, final int parameterMarkerIndex) {
        this.startIndex = startIndex;
        this.stopIndex = stopIndex;
        this.parameterMarkerIndex = parameterMarkerIndex;
        this.parameterMarkerType = ParameterMarkerType.QUESTION;
    }
    
    @Override
    public String getColumnLabel() {
        return getAliasName().orElse(String.valueOf(parameterMarkerIndex));
    }
    
    @Override
    public Optional<String> getAliasName() {
        return null == alias ? Optional.empty() : Optional.ofNullable(alias.getIdentifier().getValue());
    }
    
    @Override
    public Optional<IdentifierValue> getAlias() {
        return Optional.ofNullable(alias).map(AliasSegment::getIdentifier);
    }
    
    @Override
    public int getParameterIndex() {
        return parameterMarkerIndex;
    }
    
    @Override
    public ColumnSegmentBoundInfo getBoundedInfo() {
        return Optional.ofNullable(boundedInfo).orElseGet(() -> new ColumnSegmentBoundInfo(new IdentifierValue("")));
    }
    
    @Override
    public int getStopIndex() {
        return null == alias ? stopIndex : alias.getStopIndex();
    }
    
    @Override
    public String getText() {
        return parameterMarkerType.getMarker();
    }
    
    /**
     * Get alias segment.
     * 
     * @return alias segment
     */
    public Optional<AliasSegment> getAliasSegment() {
        return Optional.ofNullable(alias);
    }
}
