package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropDatabaseLinkStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.List;

/**
 * Drop database link statement context.
 */
@Getter
public class DropDatabaseLinkStatementContext extends CommonSQLStatementContext {

    public DropDatabaseLinkStatementContext(final DropDatabaseLinkStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropDatabaseLinkStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.KEY_DATABASE_LINK);
        List<IdentifierValue> dbLinkNames = sqlStatement.getDbLinkSegment().getDbLinkNames();
        sqlAuthModel.setName(dbLinkNames.get(dbLinkNames.size() - 1).getValue());
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropDatabaseLinkStatement getSqlStatement() {
        return (DropDatabaseLinkStatement) super.getSqlStatement();
    }
}
