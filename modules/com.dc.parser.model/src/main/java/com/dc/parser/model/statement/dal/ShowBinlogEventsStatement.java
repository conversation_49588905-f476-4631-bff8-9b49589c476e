package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Show binlog events statement.
 */
@Getter
@Setter
public abstract class ShowBinlogEventsStatement extends AbstractSQLStatement implements DALStatement {

    private String logName;

    private LimitSegment limit;
}
