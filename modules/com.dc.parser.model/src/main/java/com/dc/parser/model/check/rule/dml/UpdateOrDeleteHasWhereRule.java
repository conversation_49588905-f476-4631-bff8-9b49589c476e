package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;

public class UpdateOrDeleteHasWhereRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof UpdateStatement) {
            UpdateStatement checkStatement = (UpdateStatement) sqlStatement;
            return checkStatement.getWhere().isEmpty() ? CheckResult.buildFailResult(parameter.getCheckRuleContent()) : CheckResult.DEFAULT_SUCCESS_RESULT;
        } else if (sqlStatement instanceof DeleteStatement) {
            DeleteStatement checkStatement = (DeleteStatement) sqlStatement;
            return checkStatement.getWhere().isEmpty() ? CheckResult.buildFailResult(parameter.getCheckRuleContent()) : CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_UPDATE_OR_DELETE_HAS_WHERE.getValue();
    }

}
