
package com.dc.parser.model.segment.dml.predicate;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

/**
 * Having segment.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class HavingSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ExpressionSegment expr;
}
