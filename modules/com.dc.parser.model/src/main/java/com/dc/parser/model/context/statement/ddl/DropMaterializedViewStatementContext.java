package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.DropMaterializedViewStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

@Getter
public class DropMaterializedViewStatementContext extends CommonSQLStatementContext {

    public DropMaterializedViewStatementContext(final DropMaterializedViewStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropMaterializedViewStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.KEY_VIEW);
        SimpleTableSegment view = sqlStatement.getView();
        sqlAuthModel.setName(view.getTableName().getIdentifier().getValue());
        sqlAuthModel.setSchemaName(view.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(currentDatabaseName));
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropMaterializedViewStatement getSqlStatement() {
        return (DropMaterializedViewStatement) super.getSqlStatement();
    }
}
