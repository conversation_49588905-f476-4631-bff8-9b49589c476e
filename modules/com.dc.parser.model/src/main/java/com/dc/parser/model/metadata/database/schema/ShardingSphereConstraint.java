package com.dc.parser.model.metadata.database.schema;

import com.dc.parser.model.metadata.model.ConstraintMetaData;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * ShardingSphere constraint.
 */
@RequiredArgsConstructor
@Getter
@ToString
public final class ShardingSphereConstraint {

    private final String name;

    private final String referencedTableName;

    public ShardingSphereConstraint(final ConstraintMetaData constraintMetaData) {
        name = constraintMetaData.getName();
        referencedTableName = constraintMetaData.getReferencedTableName();
    }
}
