package com.dc.parser.model.segment.ddl.dblink;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * Database link segment.
 */
@Getter
@RequiredArgsConstructor
public class DBLinkSegment implements SQLSegment {

    private final int startIndex;

    private final int stopIndex;

    private final List<IdentifierValue> dbLinkNames;
}
