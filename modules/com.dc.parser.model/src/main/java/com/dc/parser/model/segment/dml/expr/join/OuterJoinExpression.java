
package com.dc.parser.model.segment.dml.expr.join;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

/**
 * Outer join expression.
 */
@RequiredArgsConstructor
@Getter
public class OuterJoinExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ColumnSegment columnName;
    
    private final String joinOperator;
    
    @Override
    public String getText() {
        return getColumnName().toString();
    }
}
