
package com.dc.parser.model.statement.dcl;

import com.dc.parser.model.segment.dcl.QuotasSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Data;

import java.util.List;

/**
 * Alter user statement.
 */
@Data
public abstract class AlterUserStatement extends AbstractSQLStatement implements DCLStatement {

    public List<IdentifierValue> getDefaultRoles() {
        return List.of();
    }

    public List<IdentifierValue> getUsers() {
        return List.of();
    }

    public List<IdentifierValue> getExpectRoles() {
        return List.of();
    }

    public QuotasSegment getQuotasSegment() {
        return null;
    }

    public boolean isALL() {
        return false;
    }

    public boolean isNone() {
        return false;
    }
}
