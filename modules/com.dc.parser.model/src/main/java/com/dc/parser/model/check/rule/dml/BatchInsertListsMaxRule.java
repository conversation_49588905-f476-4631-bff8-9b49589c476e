package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;


public class BatchInsertListsMaxRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof InsertStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        InsertStatement checkStatement = (InsertStatement) sqlStatement;
        if (checkStatement.getValues().size() >= Integer.parseInt(parameter.getCheckRuleContent().getValue())) {
            parameter.getCheckRuleContent().appendAffectedRows(checkStatement.getValues().size());
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        } else if (checkStatement.getInsertSelect().isPresent()
                && StringUtils.isNotBlank(checkStatement.getInsertSelect().get().getText())
                && parameter.getAffectedRows() >= Integer.parseInt(parameter.getCheckRuleContent().getValue())) {
            parameter.getCheckRuleContent().appendAffectedRows(parameter.getAffectedRows());
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public void prepare(SQLStatement sqlStatement, CheckRuleParameter parameter, Function<String, List<Map<String, Object>>> function) {
        if (parameter.getAffectedRows() > 0) {
            return;
        }

        if (parameter.isEditing()) {
            parameter.setAffectedRows(1);
            return;
        }

        if (sqlStatement instanceof InsertStatement) {
            InsertStatement checkStatement = (InsertStatement) sqlStatement;
            if (checkStatement.getInsertSelect().isPresent() && StringUtils.isNotBlank(checkStatement.getInsertSelect().get().getText())) {
                String text = checkStatement.getInsertSelect().get().getText();
                String countSql = String.format("SELECT COUNT(*) as \"COUNT\" FROM ( %s )" , text);

                List<Map<String, Object>> apply = function.apply(countSql);
                if (!apply.isEmpty() && apply.get(0).get("COUNT") != null) {
                    int count = Integer.parseInt(apply.get(0).get("COUNT").toString()) ;
                    parameter.setAffectedRows(count);
                }
            }
        }
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_BATCH_INSERT_LISTS_MAX.getValue();
    }

}
