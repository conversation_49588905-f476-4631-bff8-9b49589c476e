
package com.dc.parser.model.segment.dml.item;

import com.dc.parser.model.segment.SQLSegment;

/**
 * Projection segment.
 */
public interface ProjectionSegment extends SQLSegment {
    
    /**
     * Get column label.
     *
     * @return column label
     */
    String getColumnLabel();
    
    /**
     * Judge whether column is visible or not.
     * 
     * @return whether column is visible or not
     */
    default boolean isVisible() {
        return true;
    }
}
