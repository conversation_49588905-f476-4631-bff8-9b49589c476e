
package com.dc.parser.model.segment.dml.expr;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

/**
 * List expression.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class ListExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final List<ExpressionSegment> items = new LinkedList<>();
    
    @Override
    public String getText() {
        StringBuilder result = new StringBuilder();
        for (ExpressionSegment each : items) {
            result.append(each.getText());
        }
        return result.toString();
    }
}
