package com.dc.parser.model.context.segment.insert.values;

import com.dc.parser.model.extractor.ExpressionExtractor;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.ToString;

import java.util.*;

/**
 * Insert value context.
 */
@Getter
@ToString
public final class InsertValueContext {

    private final int parameterCount;

    private final List<ExpressionSegment> valueExpressions;

    private final List<ParameterMarkerExpressionSegment> parameterMarkerExpressions;

    private final List<Object> parameters;

    public InsertValueContext(final Collection<ExpressionSegment> assignments, final List<Object> params, final int parametersOffset) {
        valueExpressions = getValueExpressions(assignments);
        parameterMarkerExpressions = ExpressionExtractor.getParameterMarkerExpressions(assignments);
        parameterCount = parameterMarkerExpressions.size();
        parameters = getParameters(params, parametersOffset);
    }

    private List<ExpressionSegment> getValueExpressions(final Collection<ExpressionSegment> assignments) {
        List<ExpressionSegment> result = new ArrayList<>(assignments.size());
        result.addAll(assignments);
        return result;
    }

    private List<Object> getParameters(final List<Object> params, final int paramsOffset) {
        if (params.isEmpty() || 0 == parameterCount) {
            return Collections.emptyList();
        }
        List<Object> result = new ArrayList<>(parameterCount);
        result.addAll(params.subList(paramsOffset, paramsOffset + parameterCount));
        return result;
    }

    /**
     * Get literal value.
     *
     * @param index index
     * @return literal value
     */
    public Optional<Object> getLiteralValue(final int index) {
        ExpressionSegment valueExpression = valueExpressions.get(index);
        if (valueExpression instanceof ParameterMarkerExpressionSegment) {
            return Optional.ofNullable(parameters.get(getParameterIndex((ParameterMarkerExpressionSegment) valueExpression)));
        }
        if (valueExpression instanceof LiteralExpressionSegment) {
            return Optional.ofNullable(((LiteralExpressionSegment) valueExpression).getLiterals());
        }
        return Optional.empty();
    }

    private int getParameterIndex(final ParameterMarkerExpressionSegment paramMarkerExpression) {
        int result = parameterMarkerExpressions.indexOf(paramMarkerExpression);
        Preconditions.checkArgument(result >= 0, "Can not get parameter index.");
        return result;
    }

    /**
     * Get parameter index via column index.
     *
     * @param index column index
     * @return parameter index
     */
    public int getParameterIndex(final int index) {
        ExpressionSegment valueExpression = valueExpressions.get(index);
        return valueExpression instanceof ParameterMarkerExpressionSegment ? getParameterIndex((ParameterMarkerExpressionSegment) valueExpression) : -1;
    }
}
