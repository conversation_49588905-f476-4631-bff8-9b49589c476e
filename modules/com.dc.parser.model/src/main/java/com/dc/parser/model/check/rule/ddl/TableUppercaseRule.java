package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.function.IntPredicate;

public class TableUppercaseRule implements SQLRule {

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_TABLE_UPPERCASE.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        // 1 大写/2 小写
        String caseType = parameter.getCheckRuleContent().getValue();

        IntPredicate characterPredicate = "2".equals(caseType) ? Character::isLowerCase : Character::isUpperCase;

        boolean allTableUppercase = true;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            allTableUppercase = allTableIsValid(createTableStatement.getTable(), characterPredicate);

        } else if (sqlStatement instanceof AlterTableStatement) {
            AlterTableStatement alterTableStatement = (AlterTableStatement) sqlStatement;

            SimpleTableSegment tableSegment = alterTableStatement.getRenameTable().orElse(null);
            if (tableSegment != null) {
                allTableUppercase = allTableIsValid(tableSegment, characterPredicate);
            }
        }

        if (!allTableUppercase) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public boolean allTableIsValid(SimpleTableSegment tableSegment, IntPredicate characterPredicate) {
        return tableSegment.getTableName()
                .getIdentifier()
                .getValue()
                .chars()
                .filter(Character::isLetter)
                .allMatch(characterPredicate);
    }
}
