
package com.dc.parser.model.segment.dml.pagination.rownum;

import com.dc.parser.model.segment.dml.expr.BinaryOperationExpression;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;

import java.util.List;

/**
 * Row number value segment for expression.
 */
public final class ExpressionRowNumberValueSegment extends RowNumberValueSegment {
    
    private final ExpressionSegment expressionSegment;
    
    public ExpressionRowNumberValueSegment(final int startIndex, final int stopIndex, final ExpressionSegment expressionSegment, final boolean boundOpened) {
        super(startIndex, stopIndex, boundOpened);
        this.expressionSegment = expressionSegment;
    }
    
    /**
     * Get value.
     *
     * @param params parameters
     * @return value
     */
    public Long getValue(final List<Object> params) {
        return getValueFromExpression(expressionSegment, params);
    }
    
    private Long getValueFromExpression(final ExpressionSegment expressionSegment, final List<Object> params) {
        if (expressionSegment instanceof ParameterMarkerExpressionSegment) {
            return null == params || params.isEmpty() ? 0L : Long.parseLong(params.get(((ParameterMarkerExpressionSegment) expressionSegment).getParameterMarkerIndex()).toString());
        }
        if (expressionSegment instanceof BinaryOperationExpression) {
            return getValueFromBinaryOperationExpression((BinaryOperationExpression) expressionSegment, params);
        }
        if (expressionSegment instanceof LiteralExpressionSegment) {
            return Long.parseLong(expressionSegment.getText());
        }
        throw new UnsupportedOperationException(String.format("Unsupported expression: %s in page expression", expressionSegment.getClass().getName()));
    }
    
    private Long getValueFromBinaryOperationExpression(final BinaryOperationExpression binaryOperationExpression, final List<Object> params) {
        String operator = binaryOperationExpression.getOperator();
        Long leftValue = getValueFromExpression(binaryOperationExpression.getLeft(), params);
        Long rightValue = getValueFromExpression(binaryOperationExpression.getRight(), params);
        switch (operator) {
            case "+":
                return leftValue + rightValue;
            case "-":
                return leftValue - rightValue;
            case "*":
                return leftValue * rightValue;
            case "/":
                return leftValue / rightValue;
            default:
                throw new UnsupportedOperationException(String.format("Unsupported operator: %s in page expression", operator));
        }
    }
}
