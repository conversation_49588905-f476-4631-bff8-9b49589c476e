package com.dc.parser.model.context.statement.dml;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dml.MergeStatement;
import com.dc.summer.parser.sql.model.SqlActionModel;
import lombok.Getter;

/**
 * Merge statement context.
 */
@Getter
public class MergeStatementContext extends CommonSQLStatementContext {

    public MergeStatementContext(MergeStatement sqlStatement) {
        super(sqlStatement);

        // 构造SQL动作模型
        extractSqlActionModel();
    }

    private void extractSqlActionModel() {
        SqlActionModel sqlActionModel = getSqlActionModel();
        sqlActionModel.setMerge(true);
    }

    @Override
    public MergeStatement getSqlStatement() {
        return (MergeStatement) super.getSqlStatement();
    }
}
