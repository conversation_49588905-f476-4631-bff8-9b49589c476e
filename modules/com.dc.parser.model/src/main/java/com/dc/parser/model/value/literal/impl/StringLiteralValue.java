
package com.dc.parser.model.value.literal.impl;

import lombok.Getter;
import com.dc.parser.model.value.literal.LiteralValue;

import java.util.regex.Pattern;

/**
 * String literal value.
 */
@Getter
public final class StringLiteralValue implements LiteralValue<String> {


    private final String value;
    
    public StringLiteralValue(final String value) {
        this.value = value.substring(1, value.length() - 1);
    }


}
