
package com.dc.parser.model.value.literal.impl;

import lombok.RequiredArgsConstructor;
import com.dc.parser.model.value.literal.LiteralValue;

/**
 * Boolean literal value.
 */
@RequiredArgsConstructor
public final class BooleanLiteralValue implements LiteralValue<Boolean> {
    
    private final boolean value;
    
    public BooleanLiteralValue(final String value) {
        this.value = Boolean.parseBoolean(value);
    }
    
    @Override
    public Boolean getValue() {
        return value;
    }
}
