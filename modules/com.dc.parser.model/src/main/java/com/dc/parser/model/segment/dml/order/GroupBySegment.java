
package com.dc.parser.model.segment.dml.order;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.order.item.OrderByItemSegment;

import java.util.Collection;

/**
 * Group by segment.
 */
@RequiredArgsConstructor
@Getter
public final class GroupBySegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final Collection<OrderByItemSegment> groupByItems;
}
