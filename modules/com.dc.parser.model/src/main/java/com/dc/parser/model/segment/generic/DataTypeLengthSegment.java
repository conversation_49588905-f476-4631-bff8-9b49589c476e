package com.dc.parser.model.segment.generic;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;

import java.util.Optional;

@Getter
@Setter
public final class DataTypeLengthSegment implements SQLSegment {
    
    private int startIndex;
    
    private int stopIndex;
    
    private int precision;
    
    private int scale;

    private String type;
    
    /**
     * Get scale.
     * 
     * @return scale
     */
    public Optional<Integer> getScale() {
        return Optional.of(scale);
    }

    /**
     * Get type.
     *
     * @return type
     */
    public Optional<String> getType() {
        return Optional.ofNullable(type);
    }
}
