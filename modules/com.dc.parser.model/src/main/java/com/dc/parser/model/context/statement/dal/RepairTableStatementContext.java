package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.RepairTableStatement;
import lombok.Getter;

/**
 * Repair table statement context.
 */
@Getter
public class RepairTableStatementContext extends CommonSQLStatementContext {

    public RepairTableStatementContext(RepairTableStatement sqlStatement) {
        super(sqlStatement);
    }

    @Override
    public RepairTableStatement getSqlStatement() {
        return (RepairTableStatement) super.getSqlStatement();
    }
}
