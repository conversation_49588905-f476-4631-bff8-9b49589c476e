package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.cursor.CursorNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Cursor statement.
 */
@Getter
@Setter
public abstract class CursorStatement extends AbstractSQLStatement implements DDLStatement {

    private CursorNameSegment cursorName;

    private SelectStatement select;
}
