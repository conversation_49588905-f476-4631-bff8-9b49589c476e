package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropProcedureStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop procedure statement context.
 */
@Getter
public class DropProcedureStatementContext extends CommonSQLStatementContext {

    public DropProcedureStatementContext(final DropProcedureStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropProcedureStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.PROCEDURE);
        sqlAuthModel.setName(sqlStatement.getProcedureNameSegment().getName().getValue());
        sqlAuthModel.setSchemaName(sqlStatement.getProcedureNameSegment().getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(currentDatabaseName));
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropProcedureStatement getSqlStatement() {
        return (DropProcedureStatement) super.getSqlStatement();
    }
}
