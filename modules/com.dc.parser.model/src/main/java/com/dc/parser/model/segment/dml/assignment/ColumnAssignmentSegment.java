
package com.dc.parser.model.segment.dml.assignment;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

import java.util.List;

/**
 * Column Assignment segment.
 */
@RequiredArgsConstructor
@Getter
public final class ColumnAssignmentSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final List<ColumnSegment> columns;
    
    private final ExpressionSegment value;
}
