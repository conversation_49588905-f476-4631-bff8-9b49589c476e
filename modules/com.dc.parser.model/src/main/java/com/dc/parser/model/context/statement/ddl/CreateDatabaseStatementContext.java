package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreateDatabaseStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create database statement context.
 */
@Getter
public class CreateDatabaseStatementContext extends CommonSQLStatementContext {

    public CreateDatabaseStatementContext(final CreateDatabaseStatement sqlStatement) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement);
    }

    public void extractSqlAuthModel(final CreateDatabaseStatement sqlStatement) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(SqlConstant.KEY_DATABASE);
        String schemaName = sqlStatement.getDatabaseName().getIdentifier().getValue();
        sqlAuthModel.setName(schemaName);
        sqlAuthModel.setSchemaName(schemaName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateDatabaseStatement getSqlStatement() {
        return (CreateDatabaseStatement) super.getSqlStatement();
    }
}
