package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropFunctionStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop function statement context.
 */
@Getter
public class DropFunctionStatementContext extends CommonSQLStatementContext {

    public DropFunctionStatementContext(final DropFunctionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropFunctionStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlStatement.getFunctionName().ifPresent(functionNameSegment -> {
            sqlAuthModel.setName(functionNameSegment.getIdentifier().getValue());
            sqlAuthModel.setSchemaName(functionNameSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(currentDatabaseName));
        });
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropFunctionStatement getSqlStatement() {
        return (DropFunctionStatement) super.getSqlStatement();
    }
}
