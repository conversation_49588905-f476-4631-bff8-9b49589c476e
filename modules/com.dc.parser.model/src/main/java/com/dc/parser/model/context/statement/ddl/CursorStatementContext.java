package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CursorStatement;
import lombok.Getter;

/**
 * Cursor statement context.
 */
@Getter
public class CursorStatementContext extends CommonSQLStatementContext {

    public CursorStatementContext(CursorStatement sqlStatement) {
        super(sqlStatement);
    }

    @Override
    public CursorStatement getSqlStatement() {
        return (CursorStatement) super.getSqlStatement();
    }
}
