
package com.dc.parser.model.segment.dml.expr;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;

/**
 * Exists subquery expression.
 */
@RequiredArgsConstructor
@Getter
public class ExistsSubqueryExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final SubquerySegment subquery;
    
    @Setter
    private boolean not;
    
    @Override
    public String getText() {
        return subquery.getText();
    }
}
