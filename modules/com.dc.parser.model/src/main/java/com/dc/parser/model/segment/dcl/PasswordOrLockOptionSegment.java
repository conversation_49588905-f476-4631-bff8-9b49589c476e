package com.dc.parser.model.segment.dcl;

import com.dc.parser.model.enums.ACLAttributeType;
import com.dc.parser.model.segment.SQLSegment;
import lombok.Getter;
import lombok.Setter;

/**
 * Password or lock option segment.
 */
@Getter
@Setter
public final class PasswordOrLockOptionSegment implements SQLSegment {
    
    private int startIndex;
    
    private int stopIndex;
    
    private boolean updatePasswordExpiredFields;
    
    private boolean updatePasswordExpiredColumn;
    
    private boolean useDefaultPasswordLifeTime;
    
    private int expireAfterDays;
    
    private boolean updateAccountLockedColumn;
    
    private boolean accountLocked;
    
    private int passwordHistoryLength;
    
    private boolean useDefaultPasswordHistory;
    
    private boolean updatePasswordHistory;
    
    private int passwordReuseInterval;
    
    private boolean useDefaultPasswordReuseInterval;
    
    private boolean updatePasswordReuseInterval;
    
    private int failedLoginAttempts;
    
    private boolean updateFailedLoginAttempts;
    
    private int passwordLockTime;
    
    private boolean updatePasswordLockTime;

    private ACLAttributeType updatePasswordRequireCurrent;
}
