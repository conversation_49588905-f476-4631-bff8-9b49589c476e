package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.EventNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Drop event statement.
 */
@Getter
@Setter
public abstract class DropEventStatement extends AbstractSQLStatement implements DDLStatement {

    private EventNameSegment eventNameSegment;
}
