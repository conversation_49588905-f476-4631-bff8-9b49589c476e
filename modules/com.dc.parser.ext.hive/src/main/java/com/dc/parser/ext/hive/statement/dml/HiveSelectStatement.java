
package com.dc.parser.ext.hive.statement.dml;

import lombok.Setter;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.dml.predicate.LockSegment;
import com.dc.parser.model.segment.generic.WindowSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.ext.hive.statement.HiveStatement;

import java.util.Optional;

/**
 * Hive select statement.
 */
@Setter
public final class HiveSelectStatement extends SelectStatement implements HiveStatement {
    
    private SimpleTableSegment table;
    
    private LimitSegment limit;
    
    private LockSegment lock;
    
    private WindowSegment window;
    
    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }

    public Optional<LockSegment> getLock() {
        return Optional.ofNullable(lock);
    }
    
    public Optional<WindowSegment> getWindow() {
        return Optional.ofNullable(window);
    }
    
    /**
     * Get simple table segment.
     *
     * @return simple table segment
     */
    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }
}
