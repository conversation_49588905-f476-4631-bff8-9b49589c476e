
grammar GaussDBStatement;

import Comments, TCLStatement, DCLStatement, DALStatement, StoreProcedure;

execute
    : (select
    | insert
    | update
    | delete
    | createIndex
    | alterIndex
    | dropIndex
    | createTable
    | alterTable
    | dropTable
    | truncateTable
    | setTransaction
    | beginTransaction
    | startTransaction
    | end
    | commit
    | commitPrepared
    | rollback
    | rollbackPrepared
    | abort
    | savepoint
    | releaseSavepoint
    | rollbackToSavepoint
    | grant
    | revoke
    | createUser
    | dropUser
    | alterUser
    | createRole
    | dropRole
    | alterRole
    | show
    | set
    | resetParameter
    | call
    | alterAggregate
    | alterFunction
    | alterDatabase
    | alterDomain
    | alterDefaultPrivileges
    | alterForeignTable
    | alterGroup
    | alterMaterializedView
    | alterProcedure
    | alterServer
    | alterSequence
    | alterView
    | comment
    | createDatabase
    | createFunction
    | createProcedure
    | createServer
    | createTrigger
    | createView
    | createSequence
    | createDomain
    | createRule
    | createSchema
    | alterSchema
    | dropSchema
    | createType
    | dropType
    | createTextSearch
    | dropDatabase
    | dropFunction
    | dropProcedure
    | dropRule
    | dropServer
    | dropTrigger
    | dropView
    | dropSequence
    | dropDomain
    | vacuum
    | prepare
    | executeStmt
    | deallocate
    | explain
    | analyzeTable
    | load
    | createTablespace
    | alterTablespace
    | dropTablespace
    | setConstraints
    | copy
    | createLanguage
    | alterLanguage
    | dropLanguage
    | createConversion
    | alterConversion
    | dropConversion
    | alterTextSearchDictionary
    | alterTextSearchTemplate
    | alterTextSearchParser
    | createExtension
    | alterExtension
    | dropExtension
    | dropTextSearch
    | createSynonym
    | alterSynonym
    | dropSynonym
    | declare
    | cursor
    | close
    | move
    | fetch
    | createDirectory
    | alterDirectory
    | dropDirectory
    | createCast
    | dropCast
    | alterRule
    | checkpoint
    | alterType
    | createPublication
    | dropPublication
    | createAggregate
    | alterPackage
    | emptyStatement
    ) SEMI_? EOF
    ;
