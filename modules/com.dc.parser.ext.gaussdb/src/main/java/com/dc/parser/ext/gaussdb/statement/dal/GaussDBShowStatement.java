package com.dc.parser.ext.gaussdb.statement.dal;

import com.dc.parser.ext.gaussdb.statement.GaussDBStatement;
import com.dc.parser.model.statement.dal.ShowStatement;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

/**
 * GaussDB show statement.
 */
@RequiredArgsConstructor
public final class GaussDBShowStatement extends ShowStatement implements GaussDBStatement {

    private final String name;

    @Override
    public Optional<String> getName() {
        return Optional.ofNullable(name);
    }
}
