
package com.dc.parser.ext.gaussdb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.AlterViewStatement;
import com.dc.parser.ext.gaussdb.statement.GaussDBStatement;

import java.util.Optional;

/**
 * GaussDB alter view statement.
 */
@Getter
@Setter
public final class GaussDBAlterViewStatement extends AlterViewStatement implements GaussDBStatement {
    
    private SimpleTableSegment renameView;
    
    /**
     * Get rename view.
     *
     * @return rename view
     */
    public Optional<SimpleTableSegment> getRenameView() {
        return Optional.ofNullable(renameView);
    }
}
