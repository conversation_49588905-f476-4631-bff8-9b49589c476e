package com.dc.parser.ext.gaussdb.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.statement.SQLStatement;

/**
 * GaussDB statement.
 */
public interface GaussDBStatement extends SQLStatement {
    
    @Override
    default DatabaseType getDatabaseType() {
        return TypedSPILoader.getService(DatabaseType.class, DatabaseType.Constant.GAUSSDB);
    }
}
