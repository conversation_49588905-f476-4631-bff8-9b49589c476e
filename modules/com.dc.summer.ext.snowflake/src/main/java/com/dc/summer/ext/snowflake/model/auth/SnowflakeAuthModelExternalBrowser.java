

package com.dc.summer.ext.snowflake.model.auth;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Properties;

/**
 * Oracle database native auth model.
 */
public class SnowflakeAuthModelExternalBrowser extends SnowflakeAuthModelSnowflake {

    @Override
    public Object initAuthentication(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSource dataSource, AuthModelDatabaseNativeCredentials credentials, DBPConnectionConfiguration configuration, @NotNull Properties connProperties) throws DBException {
        return super.initAuthentication(monitor, dataSource, credentials, configuration, connProperties);
    }

    @Override
    public void endAuthentication(@NotNull DBPDataSourceContainer dataSource, @NotNull DBPConnectionConfiguration configuration, @NotNull Properties connProperties) {
        super.endAuthentication(dataSource, configuration, connProperties);
    }

    @Override
    protected String getAuthenticator(DBPDataSource dataSource, AuthModelDatabaseNativeCredentials credentials, DBPConnectionConfiguration configuration) {
        return "externalbrowser";
    }

}
