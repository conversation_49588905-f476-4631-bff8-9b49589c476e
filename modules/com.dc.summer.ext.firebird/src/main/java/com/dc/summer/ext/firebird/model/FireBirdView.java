
package com.dc.summer.ext.firebird.model;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectWithScript;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.firebird.FireBirdUtils;
import com.dc.summer.ext.generic.model.GenericTableColumn;
import com.dc.summer.ext.generic.model.GenericView;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.rdb.DBSView;

import java.util.*;

public class FireBirdView extends GenericView implements FireBirdTableBase, DBSObjectWithScript, DBSView {

    private String ownerName;
    private Map<String, String> columnDomainTypes;

    public FireBirdView(GenericStructContainer container, @Nullable String tableName, @Nullable String tableType, @Nullable JDBCResultSet dbResult) {
        super(container, tableName, tableType, dbResult);

        if (dbResult != null) {
            ownerName = JDBCUtils.safeGetStringTrimmed(dbResult, "RDB$OWNER_NAME");
        }
    }

    @Override
    public boolean isView() {
        return true;
    }

    @Property(viewable = true, order = 20)
    public String getOwnerName() {
        return ownerName;
    }

    @Override
    public synchronized List<FireBirdTableColumn> getAttributes(@NotNull DBRProgressMonitor monitor) throws DBException {
        Collection<? extends GenericTableColumn> childColumns = super.getAttributes(monitor);
        if (childColumns == null) {
            return Collections.emptyList();
        }
        List<FireBirdTableColumn> columns = new ArrayList<>();
        for (GenericTableColumn gtc : childColumns) {
            columns.add((FireBirdTableColumn) gtc);
        }
        columns.sort(DBUtils.orderComparator());
        return columns;
    }

    public String getColumnDomainType(DBRProgressMonitor monitor, FireBirdTableColumn column) throws DBException {
        if (columnDomainTypes == null) {
            columnDomainTypes = FireBirdUtils.readColumnDomainTypes(monitor, this);
        }
        return columnDomainTypes.get(column.getName());
    }

}
