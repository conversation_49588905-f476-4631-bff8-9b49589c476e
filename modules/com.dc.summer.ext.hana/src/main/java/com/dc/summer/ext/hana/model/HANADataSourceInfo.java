
package com.dc.summer.ext.hana.model;

import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.utils.NewJdbcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
class HANADataSourceInfo extends JDBCDataSourceInfo {

    public HANADataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean supportsMultipleResults() {
        return true;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "select column_name as \"column_name\"\n" +
                "from public.constraints \n" +
                "where schema_name='" + schemaName + "' and table_name='" + tableName + "' and is_primary_key = 'TRUE'";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("column_name") != null) {
                columns.add(map.get("column_name").toString());
            } else if (map.get("COLUMN_NAME") != null) {
                columns.add(map.get("COLUMN_NAME").toString());
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        return "select TABLE_NAME as \"table_name\" from PUBLIC.TABLES \n" +
                "where IS_USER_DEFINED_TYPE='FALSE' and upper(schema_name)=upper('" + schemaName + "') and upper(table_name)=upper('" + tableName + "') ";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("table_name") != null) {
                realName = map.get("table_name").toString();
                break;
            } else if (map.get("TABLE_NAME") != null) {
                realName = map.get("TABLE_NAME").toString();
                break;
            }
        }

        return realName;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {

            String regExp = ".*\\(\\d+\\).*";
            String regReplace = "\\(\\d+\\)";
            String replace = "";
            String columnType = null;
            if (Pattern.matches(regExp, item.getFieldType())) {
                columnType = item.getFieldType().replaceAll(regReplace, replace);
            } else {
                columnType = item.getFieldType();
            }
            if ("DATE".equals(columnType) && null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                try {
                    if (item.getFieldValue() != null) {
                        String str = item.getFieldValue().toString();
                        if (str.contains(".")) {
                            str = str.substring(0, str.lastIndexOf("."));
                        }
                        data.add(NewJdbcUtil.valueToDateFormat(str, "SYYYY-MM-DD HH24:MI:SS"));
                    } else {
                        data.add("NULL");
                    }
                } catch (Exception e) {
                    data.add("NULL");
                    log.error("generateInsertSql error : ", e);
                }
                continue;
            } else if (Arrays.asList("TIMESTAMP", "TIMESTAMP WITH LOCAL TIME ZONE").contains(columnType) && null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                data.add(NewJdbcUtil.valueToTimeStampFormat(item.getFieldValue().toString(), "yyyy-mm-dd hh24:mi:ss:ff"));
                continue;
            } else if ("TIMESTAMP WITH TIME ZONE".equals(columnType) && null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                data.add(NewJdbcUtil.valueToTimeStampTZFormat(item.getFieldValue().toString(), "SYYYY-MM-DD HH24:MI:SS:FF6 TZR"));
                continue;
            } else if ("INTERVAL DAY TO SECOND".equals(columnType) && null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                data.add(NewJdbcUtil.valueToDSINTERVAL(item.getFieldValue().toString()));
                continue;
            } else if ("INTERVAL YEAR TO MONTH".equals(columnType) && null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                data.add(NewJdbcUtil.valueToYMINTERVAL(item.getFieldValue().toString()));
                continue;
            }
            if (item.getFieldValue() instanceof BigDecimal) {
                BigDecimal bigDecimal = new BigDecimal(item.getFieldValue().toString());
                data.add(String.format("'%s'", bigDecimal.toPlainString()));
                continue;
            }
            if (null != item.getFieldValue()) {
                if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(columnType.toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = org.apache.commons.lang3.StringUtils.join(fields, "\",\"");
        String values = org.apache.commons.lang3.StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO %s.\"%s\" (%s) VALUES (%s)", schemaName, tableName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES (%s)", tableName, "\"" + columns + "\"", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE %s.\"%s\"", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE \"%s\"", tableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select \n" +
                        "\ta.schema_name as \"schema_name\",\n" +
                        "\tcount(1) as \"count\" \n" +
                        "from \"PUBLIC\".\"SCHEMAS\" a\n" +
                        "left join SYS.TABLES b on a.schema_name=b.schema_name and b.IS_USER_DEFINED_TYPE='FALSE'\n" +
                        "where HAS_PRIVILEGES = 'TRUE' \n" +
                        "group by a.schema_name\n" +
                        "order by a.schema_name");

        List<Map<String, Object>> returnList = new ArrayList<>();

        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();

            returnMap.put("addLabel", "dc_hana_db_schema");
            returnMap.put("username", map.get("schema_name"));
            returnMap.put("charset", "UTF-8");

            if (Arrays.asList("HANA_XS_BASE","SAP_REST_API","SAP_XS_LM","SAP_XS_LM_PE","SAP_XS_USAGE","SYS","SYSTEM",
                            "UIS","_SYS_AFL","_SYS_BI","_SYS_BIC","_SYS_DATA_ANONYMIZATION","_SYS_EPM","_SYS_LDB",
                            "_SYS_PLAN_STABILITY","_SYS_REPO","_SYS_RT","_SYS_SECURITY","_SYS_SQL_ANALYZER","_SYS_STATISTICS",
                            "_SYS_TABLE_REPLICAS","_SYS_TASK","_SYS_TELEMETRY","_SYS_XS")
                    .contains(map.get("schema_name").toString().toUpperCase(Locale.ROOT))) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", Long.parseLong(map.get("count").toString()));

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return "SELECT\n" +
                "                schema_name \"schema_name\", \n" +
                "                schema_name \"username\", \n" +
                "                table_name \"table_name\",\n" +
                "                column_name \"column_name\",\n" +
                "                DATA_TYPE_NAME \"real_type\",\n" +
                "                length \"data_length\",\n" +
                "                scale \"numeric_scale\",\n" +
                "                is_nullable \"nullable\",\n" +
                "                default_value \"column_default\",\n" +
                "                default_value \"data_default\",\n" +
                "                comments \"comments\",\n" +
                "                0 as \"is_increment\",\n" +
                "                cs_data_type_name as \"data_type\",\n" +
                "                case when column_name in (\n" +
                "                    select \n" +
                "                           column_name \n" +
                "                    from public.CONSTRAINTS \n" +
                "                    where \n" +
                "                          schema_name = SYS.TABLE_COLUMNS.schema_name and \n" +
                "                          table_name =  SYS.TABLE_COLUMNS.table_name and \n" +
                "                          IS_PRIMARY_KEY='TRUE'\n" +
                "                ) then 1 else 0 end as \"is_primary_key\"\n" +
                "            FROM SYS.TABLE_COLUMNS WHERE  schema_name = '" + schemaName + "' and table_name = '" + tableName + "' \n" +
                "            order by POSITION";
    }
}
