
package com.dc.summer.ext.hana.model;

import java.util.List;

import com.dc.summer.model.impl.DBPositiveNumberTransformer;
import com.dc.summer.model.meta.Association;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericProcedure;
import com.dc.summer.ext.generic.model.GenericProcedureParameter;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.rdb.DBSProcedureParameterKind;

public class HANAProcedureParameter extends GenericProcedureParameter {

    private DBSObject tableType;
    private List<HANAInplaceTableTypeColumn> inplaceTableType;
    
    public HANAProcedureParameter(GenericProcedure procedure, String columnName, String typeName, int typeId,
            int ordinalPosition, int columnSize, Integer scale, DBSProcedureParameterKind parameterKind,
            DBSObject tableType, List<HANAInplaceTableTypeColumn> inplaceTableType, boolean hasDefaultValue) {
        super(procedure, columnName, typeName, typeId, ordinalPosition, columnSize, scale,
        		0 /*precision*/, false /*notNull*/, null /*remarks*/, parameterKind);
        this.tableType = tableType;
        this.inplaceTableType = inplaceTableType;
        this.autoGenerated = hasDefaultValue;
    }

    @Property(viewable = true, order = 25, optional = true)
    public DBSObject getTableType() {
        return tableType;
    }

    // optional column in summary view and optional table in detail view with plugin.xml/visibleIf=...
    @Property(viewable = true, order = 26, optional = true)
    public Boolean hasInplaceTableType() {
        if(inplaceTableType != null)
            return true;
        return null;
    }

    @Override
    @Property(viewable = true, valueRenderer = DBPositiveNumberTransformer.class, order = 40)
    public long getMaxLength() {
        return super.getMaxLength();
    }
    
    @Override
    @Property(viewable = true, valueRenderer = DBPositiveNumberTransformer.class, order = 41)
    public Integer getScale() {
        return super.getScale();
    }
    
    @Override
    @Property(hidden = true)
    public boolean isRequired() {
        return super.isRequired();
    }

    @Association
    public List<HANAInplaceTableTypeColumn> getInplaceTableType(DBRProgressMonitor monitor) throws DBException {
        return inplaceTableType;
    }
    
}
