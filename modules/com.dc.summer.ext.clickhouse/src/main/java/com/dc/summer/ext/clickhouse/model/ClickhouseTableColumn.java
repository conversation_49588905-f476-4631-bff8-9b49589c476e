
package com.dc.summer.ext.clickhouse.model;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.clickhouse.ClickhouseTypeParser;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericTableColumn;

import java.util.Map;

public class ClickhouseTableColumn extends GenericTableColumn {
    private final Map<String, Integer> enumEntries;
    private String fullTypeName;

    public ClickhouseTableColumn(GenericTableBase table, String columnName, String typeName, int valueType, int sourceType, int ordinalPosition, long columnSize, long charLength, Integer scale, Integer precision, int radix, boolean notNull, String remarks, String defaultValue, boolean autoIncrement, boolean autoGenerated) {
        super(table, columnName, typeName, valueType, sourceType, ordinalPosition, columnSize, charLength, scale, precision, radix, notNull, remarks, defaultValue, autoIncrement, autoGenerated);
        this.typeName = ClickhouseTypeParser.getTypeNameWithoutModifiers(typeName);
        this.fullTypeName = typeName;
        this.enumEntries = ClickhouseTypeParser.tryParseEnumEntries(typeName);
    }

    @Override
    public String getFullTypeName() {
        return fullTypeName;
    }

    @Override
    public void setFullTypeName(String fullTypeName) throws DBException {
        this.fullTypeName = fullTypeName;
        super.setFullTypeName(fullTypeName);
    }

    @NotNull
    public Map<String, Integer> getEnumEntries() {
        return enumEntries;
    }
}
