
package com.dc.summer.ext.clickhouse.model;

import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.utils.NewJdbcUtil;
import com.dc.utils.BitTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ClickhouseDataSourceInfo
 */
@Slf4j
class ClickhouseDataSourceInfo extends JDBCDataSourceInfo {

    public ClickhouseDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "SELECT\n" +
                "  a.name as \"column_name\" \n" +
                "FROM\n" +
                "  system.columns a\n" +
                "  left join system.tables b on a.database = b.database\n" +
                "  and a.table = b.name\n" +
                "where\n" +
                "  a.database = '"+schemaName+"'\n" +
                "  and a.table = '"+tableName+"'\n" +
                "  and b.engine not like '%View%'\n" +
                "  and a.is_in_primary_key = 1";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map: list) {
            if (map.get("COLUMN_NAME") != null) {
                columns.add(map.get("COLUMN_NAME").toString());
            } else if (map.get("column_name") != null) {
                columns.add(map.get("column_name").toString());
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        return "select\n" +
                "  name as \"table_name\"\n" +
                "from\n" +
                "  system.tables\n" +
                "where\n" +
                "  engine not like '%View%'\n" +
                "  and upper(database) = upper('"+schemaName+"')\n" +
                "  and upper(name) = upper('"+tableName+"')";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map: list) {
            if (map.get("table_name") != null) {
                realName = map.get("table_name").toString();
                break;
            } else if (map.get("TABLE_NAME") != null) {
                realName = map.get("TABLE_NAME").toString();
                break;
            }
        }

        return realName;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {

        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && org.apache.commons.lang3.StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("VARCHAR", "NVARCHAR", "CHAR").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("\\") ? item.getFieldValue().toString().replace("\\\\", "\\\\\\\\") : item.getFieldValue().toString();
                    varcharData = varcharData.contains("\"") ? varcharData.replace("\"", "\\\"") : varcharData;
                    varcharData = varcharData.contains("\'") ? varcharData.replace("\'", "''") : varcharData;
                    data.add(String.format("'%s'", varcharData));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && "BIT".equals(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String value;
                    String fieldValue = item.getFieldValue().toString();

                    if (BitTypeUtils.isValidPattern(fieldValue)) {
                        value = fieldValue;
                    } else {
                        value = String.format("b'%s'", fieldValue);
                    }
                    data.add(value);
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("BINARY", "VARBINARY").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String hex = NewJdbcUtil.bytes2HexString(NewJdbcUtil.string2Bytes(item.getFieldValue().toString()));
                    data.add(String.format("0x%s", hex));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("GEOMETRY", "GEOMETRYCOLLECTION", "MULTIPOINT", "MULTIPOLYGON", "POLYGON", "LINESTRING", "MULTILINESTRING", "POINT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    data.add(String.format("ST_GeomFromText('%s')", item.getFieldValue()));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("ARRAY", "TUPLE", "MAP", "JSON").contains(item.getFieldType().replaceAll("\\(.*?\\)", "").toUpperCase(Locale.ROOT))) {
                    //ClickHouse-数据类型
                    data.add(item.getFieldValue().toString().replaceAll("\\\\'", "\'"));
                } else {
                    String varcharData = item.getFieldValue().toString().contains("\"") ? item.getFieldValue().toString().replaceAll("\"", "\\\"") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                }
            } else {
                data.add(String.format("%s", "''"));
            }
        }
        String columns = StringUtils.join(fields, "`,`");
        String values = StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO `%s`.`%s` (%s) VALUES (%s)", schemaName, tableName, "`" + columns + "`", values);
        }
        return String.format("INSERT INTO `%s` (%s) VALUES (%s)", tableName, "`" + columns + "`", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("`,`"));

        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                if (null != item.getFieldValue() && org.apache.commons.lang3.StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("VARCHAR", "NVARCHAR", "CHAR").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("\\") ? item.getFieldValue().toString().replace("\\\\", "\\\\\\\\") : item.getFieldValue().toString();
                        varcharData = varcharData.contains("\"") ? varcharData.replace("\"", "\\\"") : varcharData;
                        varcharData = varcharData.contains("\'") ? varcharData.replace("\'", "''") : varcharData;
                        data.add(String.format("'%s'", varcharData));
                    } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && "BIT".equals(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String value;
                        String fieldValue = item.getFieldValue().toString();

                        if (BitTypeUtils.isValidPattern(fieldValue)) {
                            value = fieldValue;
                        } else {
                            value = String.format("b'%s'", fieldValue);
                        }
                        data.add(value);
                    } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("BINARY", "VARBINARY").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String hex = NewJdbcUtil.bytes2HexString(NewJdbcUtil.string2Bytes(item.getFieldValue().toString()));
                        data.add(String.format("0x%s", hex));
                    } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("GEOMETRY", "GEOMETRYCOLLECTION", "MULTIPOINT", "MULTIPOLYGON", "POLYGON", "LINESTRING", "MULTILINESTRING", "POINT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        data.add(String.format("ST_GeomFromText('%s')", item.getFieldValue()));
                    } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("ARRAY", "TUPLE", "MAP", "JSON").contains(item.getFieldType().replaceAll("\\(.*?\\)", "").toUpperCase(Locale.ROOT))) {
                        //ClickHouse-数据类型
                        data.add(item.getFieldValue().toString().replaceAll("\\\\'", "\'"));
                    } else {
                        String varcharData = item.getFieldValue().toString().contains("\"") ? item.getFieldValue().toString().replaceAll("\"", "\\\"") : item.getFieldValue().toString();
                        data.add(String.format("'%s'", varcharData));
                    }
                } else {
                    data.add(String.format("%s", "''"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        if (null != schemaName) {
            return String.format("INSERT INTO `%s`.`%s` (%s) VALUES %s", schemaName, tableName, "`" + fields + "`", values);
        }
        return String.format("INSERT INTO `%s` (%s) VALUES %s", tableName, "`" + fields + "`", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE `%s`.`%s`", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE `%s`", tableName);
        }

    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "SELECT\n" +
                        "  a.name as \"username\",\n" +
                        "  a.engine as \"engine\",\n" +
                        "  'UTF-8' as \"charset\",\n" +
                        "  b.tab_count as \"count\"\n" +
                        "from\n" +
                        "  system.databases a\n" +
                        "  left join (\n" +
                        "    select\n" +
                        "      database,\n" +
                        "      count(1) as tab_count\n" +
                        "    from\n" +
                        "      system.tables\n" +
                        "    where\n" +
                        "      engine not like '%View%'\n" +
                        "    group by\n" +
                        "      database\n" +
                        "  ) b on a.name = b.database\n" +
                        "order by\n" +
                        "  a.name");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for(Map<String, Object> map: list){
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_click_house_db_schema");
            returnMap.put("username", map.get("username"));
            returnMap.put("charset", map.get("charset"));

            if(Arrays.asList("system")
                    .contains(((String)map.get("username")).toLowerCase())){
                returnMap.put("is_sys", 1);
            }else{
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", 0L);
            if(map.get("count")!=null){
                String count = map.get("count").toString();
                if(!count.isEmpty()){
                    returnMap.put("count", Long.parseLong(count));
                }
            }
            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return "SELECT a.table as \"table_name\",\n" +
                "        a.name as \"column_name\",\n" +
                "        null as \"data_length\",\n" +
                "        null as \"precision\",\n" +
                "        null as \"numeric_scale\",\n" +
                "        a.database as \"username\",\n" +
                "        a.database as \"schema_name\",\n" +
                "        a.type as \"data_type\",\n" +
                "        a.type as \"column_type\",\n" +
                "        a.comment AS \"comments\",\n" +
                "        a.default_expression AS \"column_default\",\n" +
                "        a.default_expression AS \"data_default\",\n" +
                "        0 as \"is_increment\",\n" +
                "        a.is_in_primary_key as \"is_primary_key\",\n" +
                "        a.is_in_partition_key as \"is_partition_key\",\n" +
                "        a.is_in_sorting_key as \"is_sorting_key\",\n" +
                "        a.is_in_sampling_key as \"is_sampling_key\"\n" +
                "        FROM system.columns a\n" +
                "        left join system.tables b on a.database = b.database and a.table = b.name\n" +
                "        where b.engine not like '%View%'\n" +
                "        and a.database = '" + schemaName + "'\n" +
                "        and a.table = '" + tableName + "'\n" +
                "        order by a.position asc";
    }


    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (List.of("NULLABLE(FLOAT32)", "NULLABLE(FLOAT64)").contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            return false;
        }
        return super.showInteger(column);
    }


    @Override
    public String generateTableUpdateBegin(String tableName) {
        return "ALTER TABLE " + tableName + " UPDATE ";
    }

    @Override
    public String generateTableUpdateSet() {
        return "";
    }

    @Override
    public String generateTableDeleteFrom(String tableName) {
        return "ALTER TABLE " + tableName + " DELETE ";
    }
}
