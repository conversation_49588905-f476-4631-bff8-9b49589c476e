
package com.dc.summer.ext.clickhouse.model.data;

import com.dc.summer.ext.clickhouse.model.ClickhouseTableColumn;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStringValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;

import java.sql.SQLException;
import java.util.Map;

public class ClickhouseEnumValueHandler extends JDBCStringValueHandler {
    public static final ClickhouseEnumValueHandler INSTANCE = new ClickhouseEnumValueHandler();

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws SQLException {
        return resultSet.getString(index);
    }

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws SQLException {
        DBSTypedObject type = paramType;
        if (type instanceof DBDAttributeBinding) {
            type = ((DBDAttributeBinding) type).getAttribute();
        }
        if (type instanceof ClickhouseTableColumn) {
            final Map<String, Integer> entries = ((ClickhouseTableColumn) type).getEnumEntries();
            final Integer ordinal = entries.get((String) value);
            if (ordinal != null) {
                statement.setInt(paramIndex, ordinal);
                return;
            }
        }
        super.bindParameter(session, statement, paramType, paramIndex, value);
    }
}
