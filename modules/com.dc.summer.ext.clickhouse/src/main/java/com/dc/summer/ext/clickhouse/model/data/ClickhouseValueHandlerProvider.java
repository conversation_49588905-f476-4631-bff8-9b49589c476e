
package com.dc.summer.ext.clickhouse.model.data;

import com.dc.summer.ext.clickhouse.ClickhouseConstants;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCUUIDValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.util.Locale;

public class ClickhouseValueHandlerProvider implements DBDValueHandlerProvider {
    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject type) {
        String lowerTypeName = type.getTypeName().toLowerCase(Locale.ENGLISH);
        if ("enum8".equals(lowerTypeName) || "enum16".equals(lowerTypeName)) {
            return ClickhouseEnumValueHandler.INSTANCE;
        } else if (type.getDataKind() == DBPDataKind.ARRAY) {
            return ClickhouseArrayValueHandler.INSTANCE;
        } else if (type.getDataKind() == DBPDataKind.STRUCT || type.getDataKind() == DBPDataKind.OBJECT) {
            return ClickhouseStructValueHandler.INSTANCE;
        } else if ("int128".equals(lowerTypeName) || "int256".equals(lowerTypeName)
            || "uint64".equals(lowerTypeName) || "uint128".equals(lowerTypeName) || "uint256".equals(lowerTypeName)) {
            return new ClickhouseBigNumberValueHandler(type, preferences);
        } else if ("bool".equals(lowerTypeName)) {
            return ClickhouseBoolValueHandler.INSTANCE;
        } else if ("uuid".equals(lowerTypeName)) {
            return JDBCUUIDValueHandler.INSTANCE;
        } else if (ClickhouseConstants.DATA_TYPE_IPV4.equals(lowerTypeName) || ClickhouseConstants.DATA_TYPE_IPV6.equals(lowerTypeName)) {
            return ClikhouseInetTypeValueHandler.INSTANCE;
        } else {
            return null;
        }
    }
}
