
package com.dc.summer.ext.athena;

import com.dc.summer.ext.athena.model.AthenaConstants;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.athena.model.AthenaDataSource;
import com.dc.summer.ext.athena.model.AthenaMetaModel;
import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPInformationProvider;
import com.dc.summer.model.DBPObject;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

public class AthenaDataSourceProvider extends GenericDataSourceProvider implements DBPInformationProvider {

    private static final Log log = Log.getLog(AthenaDataSourceProvider.class);

    public AthenaDataSourceProvider()
    {
    }

    @Override
    public void init(@NotNull DBPPlatform platform) {

    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBPDataSourceContainer container)
        throws DBException
    {
        return new AthenaDataSource(monitor, container, new AthenaMetaModel());
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        //************************************;
        String urlTemplate = driver.getSampleURL();
        String regionName = connectionInfo.getServerName();
        if (regionName == null) {
            regionName = connectionInfo.getProviderProperty(AthenaConstants.DRIVER_PROP_REGION);
        }
        if (CommonUtils.isEmpty(urlTemplate) || !urlTemplate.startsWith(AthenaConstants.JDBC_URL_PREFIX)) {
            return AthenaConstants.JDBC_URL_PREFIX + AthenaConstants.DRIVER_PROP_REGION + "=" + regionName + ";";
        }
        urlTemplate = urlTemplate
            .replace("{region}", regionName)
            .replace("{server}", regionName)
            .replace("=region;", "=" + regionName + ";"); // Left for backward compatibility
        return urlTemplate;
    }

    @Nullable
    @Override
    public String getObjectInformation(@NotNull DBPObject object, @NotNull String infoType) {
        if (object instanceof DBPDataSourceContainer && infoType.equals(INFO_TARGET_ADDRESS)) {
            return ((DBPDataSourceContainer) object).getConnectionConfiguration().getServerName();
        }
        return null;
    }

}
