
package com.dc.summer.ext.athena.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.utils.CommonUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Athena datasource
 */
public class AthenaDataSource extends GenericDataSource {

    public AthenaDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, AthenaMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new AthenaSQLDialect());
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> props = new HashMap<>();
        if (CommonUtils.isEmpty(connectionInfo.getDatabaseName())) {
            connectionInfo.setDatabaseName(connectionInfo.getProviderProperty(AthenaConstants.DRIVER_PROP_S3_OUTPUT_LOCATION));
        }
        props.put(AthenaConstants.DRIVER_PROP_S3_OUTPUT_LOCATION, connectionInfo.getDatabaseName());
        props.put(AthenaConstants.DRIVER_PROP_AWS_CREDENTIALS_PROVIDER_CLASS, "com.amazonaws.auth.DefaultAWSCredentialsProviderChain");

        return props;
    }

    @Override
    public boolean isOmitCatalog() {
        return true;
    }

    @Override
    protected boolean isPopulateClientAppName() {
        return false;
    }
}
