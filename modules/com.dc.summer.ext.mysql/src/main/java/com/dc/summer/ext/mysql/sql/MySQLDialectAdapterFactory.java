

package com.dc.summer.ext.mysql.sql;

import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.text.parser.TPRuleProvider;
import org.eclipse.core.runtime.IAdapterFactory;

public class MySQLDialectAdapterFactory implements IAdapterFactory {

    private static final Class<?>[] CLASSES = new Class[] { TPRuleProvider.class };
    
    @Override
    public <T> T getAdapter(Object adaptableObject, Class<T> adapterType) {
        if (adaptableObject instanceof SQLDialect) {
            if (adapterType == TPRuleProvider.class) {
                return adapterType.cast(new MySQLDialectRules());
            }
        }
        return null;
    }

    @Override
    public Class<?>[] getAdapterList() {
        return CLASSES;
    }

}
