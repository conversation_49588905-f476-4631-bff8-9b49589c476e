
package com.dc.summer.ext.mysql.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAssociation;
import com.dc.summer.model.struct.DBSEntityReferrer;
import com.dc.summer.model.struct.rdb.DBSForeignKeyModifyRule;
import com.dc.summer.model.struct.rdb.DBSTableForeignKeyColumn;
import com.dc.code.NotNull;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableForeignKey;
import com.dc.summer.model.struct.DBSEntityAttributeRef;

import java.util.ArrayList;
import java.util.List;

/**
 * GenericForeignKey
 */
public class MySQLTableForeign<PERSON><PERSON> extends JDBCTableForeignKey<MySQLTable, MySQLTableConstraint>
{
    private List<MySQLTableForeignKeyColumn> columns;

    public MySQLTableForeignKey(
        MySQLTable table,
        String name,
        String remarks,
        MySQLTableConstraint referencedKey,
        DBSForeignKeyModifyRule deleteRule,
        DBSForeignKeyModifyRule updateRule,
        boolean persisted)
    {
        super(table, name, remarks, referencedKey, deleteRule, updateRule, persisted);
    }

    // Copy constructor
    public MySQLTableForeignKey(DBRProgressMonitor monitor, MySQLTable table, DBSEntityAssociation source) throws DBException {
        super(
            monitor,
            table,
            source,
            false);
        if (source instanceof DBSEntityReferrer) {
            List<? extends DBSEntityAttributeRef> columns = ((DBSEntityReferrer) source).getAttributeReferences(monitor);
            if (columns != null) {
                this.columns = new ArrayList<>(columns.size());
                for (DBSEntityAttributeRef srcCol : columns) {
                    if (srcCol instanceof DBSTableForeignKeyColumn) {
                        DBSTableForeignKeyColumn fkCol = (DBSTableForeignKeyColumn) srcCol;
                        this.columns.add(new MySQLTableForeignKeyColumn(
                            this,
                            table.getAttribute(monitor, fkCol.getName()),
                            this.columns.size(),
                            table.getAttribute(monitor, fkCol.getReferencedColumn().getName())));
                    }
                }
            }
        }
    }

    @Override
    public List<MySQLTableForeignKeyColumn> getAttributeReferences(DBRProgressMonitor monitor)
    {
        return columns;
    }

    @NotNull
    @Override
    @Property(viewable = true, editable = true, updatable = true, listProvider = ConstraintModifyRuleListProvider.class, order = 5)
    public DBSForeignKeyModifyRule getDeleteRule() {
        return super.getDeleteRule();
    }

    @NotNull
    @Override
    @Property(viewable = true, editable = true, updatable = true, listProvider = ConstraintModifyRuleListProvider.class, order = 6)
    public DBSForeignKeyModifyRule getUpdateRule() {
        return super.getUpdateRule();
    }

    public void addColumn(MySQLTableForeignKeyColumn column)
    {
        if (columns == null) {
            columns = new ArrayList<>();
        }
        columns.add(column);
    }

    public boolean hasColumn(MySQLTableForeignKeyColumn column) {
        if (columns != null) {
            String columnName = column.getName();
            String refName = column.getReferencedColumn().getName();
            for (MySQLTableForeignKeyColumn col : columns) {
                if (columnName.equals(col.getName()) &&
                    refName.equals(col.getReferencedColumn().getName())) {
                    return true;
                }
            }
        }
        return false;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context)
    {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getTable().getContainer(),
            getTable(),
            this);
    }

    @NotNull
    @Override
    public MySQLDataSource getDataSource()
    {
        return getTable().getDataSource();
    }
}
