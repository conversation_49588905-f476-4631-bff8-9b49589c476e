/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2023 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.mysql.data;

import com.dc.code.NotNull;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCUUIDValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class MySQLUUIDValueHandlerProvider extends JDBC<PERSON><PERSON>DValueHandler {

    static final MySQLUUIDValueHandlerProvider INSTANCE = new MySQLUUIDValueHandlerProvider();

    @NotNull
    @Override
    public String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
        return DBValueFormatting.getDefaultValueDisplayString(value, format);
    }
}
