
package com.dc.summer.ext.mysql.model;

import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDInsertReplaceMethod;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.code.NotNull;

import java.util.StringJoiner;

/**
 *   If you specify an ON DUPLICATE KEY UPDATE clause and a row to be inserted would cause a duplicate value in a UNIQUE index or PRIMARY KEY, an UPDATE of the old row occurs.
 *   It can be better then MySQLInsertReplaceMethod class with REPLACE INTO, because REPLACE INTO first deletes a row, and then insert a new one.
 * Example:
 *   INSERT INTO insert_duplicate_values(id, name, another_name) VALUES (5,'Ben','Solo')
 *     ON DUPLICATE KEY UPDATE id=VALUES(id), name=VALUES(name), another_name=VALUES(another_name);
 */

public class MySQLInsertReplaceMethodUpdate implements DBDInsertReplaceMethod {
    @NotNull
    @Override
    public String getOpeningClause(@NotNull DBSTable table, @NotNull DBRProgressMonitor monitor) {
        return "INSERT INTO";
    }

    @Override
    public String getTrailingClause(@NotNull DBSTable table, @NotNull DBRProgressMonitor monitor, DBSAttributeBase[] attributes) {
        StringBuilder query = new StringBuilder();
        query.append(" ON DUPLICATE KEY UPDATE ");
        appendUpdateCase(query, table, attributes);
        return query.toString();
    }

    private void appendUpdateCase(@NotNull StringBuilder query, @NotNull DBSTable table, DBSAttributeBase[] attributes) {
        final StringJoiner names = new StringJoiner(",");
        for (DBSAttributeBase attribute : attributes) {
            if (DBUtils.isPseudoAttribute(attribute)) {
                continue;
            }
            String attrName = getAttributeName(table, attribute);
            names.add(attrName + "=VALUES(" + attrName + ")");
        }
        query.append(names);
    }

    private String getAttributeName(@NotNull DBSTable table, @NotNull DBSAttributeBase attribute) {
        // Do not quote pseudo attribute name
        return DBUtils.isPseudoAttribute(attribute) ? attribute.getName() : DBUtils.getObjectFullName(table.getDataSource(), attribute, DBPEvaluationContext.DML);
    }
}
