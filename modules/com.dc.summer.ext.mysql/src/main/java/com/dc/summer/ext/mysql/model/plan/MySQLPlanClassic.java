
package com.dc.summer.ext.mysql.model.plan;

import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.plan.DBCPlanCostNode;
import com.dc.summer.model.exec.plan.DBCPlanNode;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MySQL execution plan analyser
 */
public class MySQLPlanClassic extends MySQLPlanAbstract {

    private List<MySQLPlanNodePlain> rootNodes;

    public MySQLPlanClassic(JDBCSession session, String query) throws DBCException {
        super((MySQLDataSource) session.getDataSource(), query);
        try (JDBCPreparedStatement dbStat = session.prepareStatement(getPlanQueryString())) {
            try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                List<MySQLPlanNodePlain> nodes = new ArrayList<>();
                while (dbResult.next()) {
                    MySQLPlanNodePlain node = new MySQLPlanNodePlain(null, dbResult);
                    nodes.add(node);
                }

                rootNodes = nodes;
            }
        } catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
    }

    public MySQLPlanClassic(MySQLDataSource dataSource, String query, List<MySQLPlanNodePlain> rootNodes) {
        super(dataSource, query);
        this.rootNodes = rootNodes;
    }

    @Override
    public Object getPlanFeature(String feature) {
        if (DBCPlanCostNode.FEATURE_PLAN_ROWS.equals(feature)) {
            return true;
        }
        return super.getPlanFeature(feature);
    }

    @Override
    public String getQueryString() {
        return query;
    }

    @Override
    public String getPlanQueryString() {
        return "EXPLAIN EXTENDED " + query;
    }

    @Override
    public List<? extends DBCPlanNode> getPlanNodes(Map<String, Object> options) {
        return rootNodes;
    }

    private List<MySQLPlanNodePlain> convertToPlanTree(List<MySQLPlanNodePlain> srcNodes) {
        List<MySQLPlanNodePlain> roots = new ArrayList<>();

        if (srcNodes.size() == 1) {
            // Just one node
            roots.add(srcNodes.get(0));
        } else {
            List<MySQLPlanNodePlain> parsed = new ArrayList<>();
            MySQLPlanNodePlain lastCompositeNode = null;
            for (int id = 1; ; id++) {
                List<MySQLPlanNodePlain> nodes = getQueriesById(srcNodes, id);
                if (nodes.isEmpty()) {
                    break;
                }
                if (nodes.size() == 1) {
                    MySQLPlanNodePlain firstNode = nodes.get(0);
                    if (lastCompositeNode != null) {
                        firstNode.setParent(lastCompositeNode);
                    } else {
                        roots.add(firstNode);
                    }
                    if (firstNode.isCompositeNode()) {
                        lastCompositeNode = firstNode;
                    }
                } else {
                    MySQLPlanNodePlain leftNode = lastCompositeNode;
                    if (leftNode == null) {
                        leftNode = nodes.get(0).getParent();
                    }
                    MySQLPlanNodePlain joinNode = joinNodes(srcNodes, leftNode, nodes);
                    if (leftNode == null) {
                        roots.add(joinNode);
                    }
                }
                parsed.addAll(nodes);
            }
            // Add the rest
            for (MySQLPlanNodePlain node : srcNodes) {
                if (node.getId() != null && !parsed.contains(node)) {
                    roots.add(node);
                }
            }
        }

        for (MySQLPlanNodePlain node : roots) {
            node.computeStats();
        }

        return roots;
    }

    private List<MySQLPlanNodePlain> getQueriesById(List<MySQLPlanNodePlain> srcNodes, int id) {
        List<MySQLPlanNodePlain> subList = new ArrayList<>();
        for (MySQLPlanNodePlain node : srcNodes) {
            if (node.getId() != null && node.getId() == id) {
                subList.add(node);
            }
        }
        return subList;
    }

    private MySQLPlanNodePlain joinNodes(List<MySQLPlanNodePlain> srcNodes, MySQLPlanNodePlain parent, List<MySQLPlanNodePlain> nodes) {
        MySQLPlanNodePlain leftNode = nodes.get(0);
        for (int i = 1; i < nodes.size(); i++) {
            MySQLPlanNodePlain rightNode = nodes.get(i);
            MySQLPlanNodeJoin nodeJoin = new MySQLPlanNodeJoin(parent, leftNode, rightNode);
            leftNode.setParent(nodeJoin);
            rightNode.setParent(nodeJoin);
            if (parent != null) {
                nodeJoin.setParent(parent);
            }
            leftNode = nodeJoin;
        }
        return leftNode;
    }

}
