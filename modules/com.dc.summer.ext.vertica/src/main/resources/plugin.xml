<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="vertica" class="com.dc.summer.ext.vertica.model.VerticaMetaModel" driverClass="com.vertica.jdbc.Driver"/>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
            class="com.dc.summer.ext.vertica.VerticaDataSourceProvider"
            description="Vertica JDBC connector"
            id="vertica"
            parent="generic"
            label="Vertica"
            icon="icons/vertica_icon.png"
            dialect="vertica">

            <tree path="vertica" label="Vertica data source">
                <folder type="com.dc.summer.ext.vertica.model.VerticaSchema" label="%tree.schemas.node.name" icon="#folder_schema" description="Schemas">
                    <items label="#schema" path="schema" property="schemaList" icon="#schema" optional="true">
                        <icon if="object.system" icon="#schema_system"/>
                        <folder type="com.dc.summer.ext.vertica.model.VerticaTable" label="%tree.tables.node.name" icon="#folder_table" description="Tables">
                            <items label="%tree.table.node.name" path="table" property="physicalTables" icon="#table">
                                <folder type="com.dc.summer.ext.vertica.model.VerticaTableColumn" label="%tree.columns.node.name" icon="#columns" description="Table columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                                <folder type="com.dc.summer.ext.vertica.model.VerticaConstraint" label="%tree.uni_keys.node.name" icon="#constraints" description="Table unique keys" visibleIf="!object.view">
                                    <items label="%tree.uni_key.node.name" path="uniqueKey" property="constraints" icon="#unique-key">
                                        <items label="%tree.uni_key.columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder type="com.dc.summer.ext.generic.model.GenericTableForeignKey" label="%tree.foreign_keys.node.name" icon="#foreign-keys" description="Table foreign keys" visibleIf="!object.view &amp;&amp; object.dataSource.info.supportsReferentialIntegrity()">
                                    <items label="%tree.foreign_key.node.name" path="association" property="associations" icon="#foreign-key">
                                        <items label="%tree.foreign_key_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>

                                <folder label="%tree.references.node.name" icon="#references" description="Table references" visibleIf="!object.view &amp;&amp; object.dataSource.info.supportsReferentialIntegrity()" virtual="true">
                                    <items label="%tree.reference.node.name" path="reference" property="references" icon="#reference" virtual="true">
                                        <items label="%tree.reference_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true" virtual="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder type="com.dc.summer.ext.generic.model.GenericTrigger" label="%tree.triggers.node.name" icon="#triggers" description="Table triggers" visibleIf="object.dataSource.metaModel.supportsTriggers(object.dataSource)">
                                    <items label="%tree.trigger.node.name" path="trigger" property="triggers" icon="#trigger"/>
                                </folder>
                            </items>
                        </folder>
                        <folder type="com.dc.summer.ext.vertica.model.VerticaFlexTable" label="%tree.flexTables.node.name" icon="#folder_table" description="Flex tables">
                            <items label="%tree.flexTable.node.name" path="flexTable" property="flexTables" icon="#table_link">
                                <folder type="com.dc.summer.ext.generic.model.GenericTableColumn" label="%tree.columns.node.name" icon="#columns" description="Flex table columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="com.dc.summer.ext.vertica.model.VerticaView" label="%tree.tviews.node.name" icon="#folder_view" description="Views">
                            <items label="%tree.tview.node.name" path="view" property="views" icon="#view">
                                <folder type="com.dc.summer.ext.generic.model.GenericTableColumn" label="%tree.columns.node.name" icon="#columns" description="View columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="com.dc.summer.ext.vertica.model.VerticaProjection" label="%tree.projections.node.name" icon="#folder_projection" description="Projections">
                            <items label="%tree.projection.node.name" path="projection" property="projections" icon="#table_index">
                                <folder type="com.dc.summer.ext.vertica.model.VerticaProjectionColumn" label="%tree.columns.node.name" icon="#columns" description="Projection columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>

                        <folder type="com.dc.summer.ext.generic.model.GenericScriptObject" label="%tree.functions.node.name" icon="#procedures" description="Functions/UDFs">
                            <items label="%tree.functions.node.name" itemLabel="%tree.function.node.name" path="procedure" property="procedures" icon="#function">
                                <items label="%tree.function_columns.node.name" itemLabel="%tree.function_column.node.name" path="column" property="parameters" navigable="false"/>
                            </items>
                        </folder>
                        <folder type="com.dc.summer.ext.vertica.model.VerticaSequence" label="%tree.sequences.node.name" icon="#sequences" description="Sequences" >
                            <items label="%tree.sequence.node.name" path="sequence" property="sequences" icon="#sequence"/>
                        </folder>
                    </items>
                </folder>
                <folder label="%tree.admin.node.name" icon="#folder_admin" description="%tree.admin.node.description">
                    <folder type="com.dc.summer.model.struct.DBSDataType" label="%tree.dataTypes.node.name" icon="#data_types" description="Global data types">
                        <items label="%tree.dataType.node.name" path="dataType" property="dataTypes" icon="#data_type"/>
                    </folder>
                    <folder type="com.dc.summer.ext.vertica.model.VerticaNode" label="%tree.nodes.node.name" icon="#folder_server" description="Nodes">
                        <items label="%tree.node.node.name" path="clusterNodes" property="clusterNodes" icon="#server">
                        </items>
                    </folder>
                </folder>
            </tree>

            <drivers manageable="true">
                <driver
                    id="vertica-jdbc"
                    label="Vertica"
                    icon="icons/vertica_icon.png"
                    iconBig="icons/vertica_icon_big.png"
                    class="com.vertica.jdbc.Driver"
                    sampleURL="jdbc:vertica://{host}:{port}/[{database}]"
                    defaultPort="5433"
                    webURL="https://my.vertica.com/download/vertica/client-drivers/"
                    description="Driver for Vertica Database"
                    categories="sql,analytic,columns">

                    <file type="jar" path="maven:/com.vertica.jdbc:vertica:12.0.2-0" bundle="!drivers.vertica"/>

                    <parameter name="query-get-active-db" value="select current_schema()"/>
                    <parameter name="query-set-active-db" value="SET search_path = ?,&quot;$user&quot;,public,v_catalog,v_monitor,v_internal"/>
                    <parameter name="active-entity-type" value="schema"/>

                    <parameter name="ddl-drop-column-short" value="true"/>
                    <parameter name="supports-scroll" value="false"/>
                </driver>

                <!--<driver
                        id="vertica-jdbc-6"
                        label="Vertica"
                        icon="icons/vertica_icon.png"
                        iconBig="icons/vertica_icon_big.png"
                        class="com.vertica.jdbc.Driver"
                        sampleURL="jdbc:vertica://{host}:{port}/[{database}]"
                        defaultPort="5433"
                        webURL="https://www.vertica.com/"
                        description="Driver for Vertica Database"
                        categories="sql,analytic,columns">
                    <replace provider="generic" driver="vertica"/>
                    <replace provider="vertica" driver="vertica-jdbc"/>

                    <file type="jar" path="maven:/com.vertica:vertica-jdbc-driver:06.00.0000" bundle="!drivers.vertica"/>

                    <parameter name="query-get-active-db" value="select current_schema()"/>
                    <parameter name="query-set-active-db" value="SET search_path = ?,&quot;$user&quot;,public,v_catalog,v_monitor,v_internal"/>
                    <parameter name="active-entity-type" value="schema"/>

                    <parameter name="ddl-drop-column-short" value="true"/>
                    <parameter name="supports-scroll" value="false"/>
                </driver>-->

                <driver
                    id="vertica-jdbc-8"
                    label="Vertica"
                    icon="icons/vertica_icon.png"
                    iconBig="icons/vertica_icon_big.png"
                    class="com.vertica.jdbc.Driver"
                    sampleURL="jdbc:vertica://{host}:{port}/[{database}]"
                    defaultPort="5433"
                    webURL="https://my.vertica.com/download/vertica/client-drivers/"
                    description="JDBC driver for Vertica 8+ Database"
                    categories="sql,analytic,columns">
                    <replace provider="generic" driver="vertica"/>
                    <replace provider="vertica" driver="vertica-jdbc-6"/>

                    <file type="jar" path="maven:/com.vertica:vertica-jdbc:8.1.1-0" bundle="!drivers.vertica"/>

                    <parameter name="query-get-active-db" value="select current_schema()"/>
                    <parameter name="query-set-active-db" value="SET search_path = ?,&quot;$user&quot;,public,v_catalog,v_monitor,v_internal"/>
                    <parameter name="active-entity-type" value="schema"/>

                    <parameter name="ddl-drop-column-short" value="true"/>
                    <parameter name="supports-scroll" value="false"/>
                    <!-- Limits affect INSERT INTO and other DML queries-->
                    <parameter name="supports-limits" value="false"/>

                    <property name="@summer-default-resultset.maxrows.sql" value="true"/>
                </driver>
            </drivers>

        </datasource>
    </extension>


<!--
    FIXME: it is not official and it doesn't support https
    <extension point="com.dc.summer.mavenRepository">
        <repository id="vertica-maven-repo" name="Vertica Repository" url="https://maven.icm.edu.pl/artifactory/repo/" order="10">
            <scope group="com.vertica"/>
        </repository>
    </extension>
-->

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.vertica.edit.VerticaFlexTableManager" objectType="com.dc.summer.ext.vertica.model.VerticaFlexTable"/>
        <manager class="com.dc.summer.ext.vertica.edit.VerticaTableColumnManager" objectType="com.dc.summer.ext.vertica.model.VerticaTableColumn"/>
        <manager class="com.dc.summer.ext.vertica.edit.VerticaProjectionColumnManager" objectType="com.dc.summer.ext.vertica.model.VerticaProjectionColumn"/>
        <manager class="com.dc.summer.ext.vertica.edit.VerticaConstraintManager" objectType="com.dc.summer.ext.vertica.model.VerticaConstraint"/>
        <manager class="com.dc.summer.ext.vertica.edit.VerticaSequenceManager" objectType="com.dc.summer.ext.vertica.model.VerticaSequence"/>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="vertica" parent="generic" class="com.dc.summer.ext.vertica.model.VerticaSQLDialect" label="Vertica" description="Vertica SQL dialect." icon="icons/vertica_icon.png">
        </dialect>
    </extension>

</plugin>
