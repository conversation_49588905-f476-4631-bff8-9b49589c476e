
package com.dc.summer.ext.vertica.model;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericScriptObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.jdbc.cache.JDBCStructCache;
import com.dc.summer.model.struct.rdb.DBSTableConstraint;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.vertica.VerticaUtils;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.struct.JDBCTable;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAssociation;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.rdb.DBSTableIndex;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * VerticaMetaModel
 */
public class VerticaProjection extends JDBCTable<VerticaDataSource, VerticaSchema> implements GenericScriptObject
{
    private static final Log log = Log.getLog(VerticaProjection.class);

    private final long objectId;
    private final String baseName;
    private final String ownerName;
    private final String anchorTableName;
    private final String nodeName;
    private final boolean isPreJoin;
    private final String createType;
    private final String segmentExpression;
    private final String segmentRange;
    private final boolean isSuperProjection;
    private final boolean isKeyConstraintProjection;
    private final boolean hasExpressions;
    private final boolean isAggregateProjection;
    private final String aggregateType;
    private final String description;

    public VerticaProjection(VerticaSchema schema, JDBCResultSet dbResult) {
        super(schema, JDBCUtils.safeGetString(dbResult, "projection_name"), true);

        this.objectId = JDBCUtils.safeGetLong(dbResult, "projection_id");
        this.baseName = JDBCUtils.safeGetString(dbResult, "projection_basename");
        this.ownerName = JDBCUtils.safeGetString(dbResult, "owner_name");
        this.anchorTableName = JDBCUtils.safeGetString(dbResult, "anchor_table_name");
        this.nodeName = JDBCUtils.safeGetString(dbResult, "node_name");
        this.isPreJoin = JDBCUtils.safeGetBoolean(dbResult, "is_prejoin");
        this.createType = JDBCUtils.safeGetString(dbResult, "create_type");
        this.segmentExpression = JDBCUtils.safeGetString(dbResult, "segment_expression");
        this.segmentRange = JDBCUtils.safeGetString(dbResult, "segment_range");
        this.isSuperProjection = JDBCUtils.safeGetBoolean(dbResult, "is_super_projection");
        this.isKeyConstraintProjection = JDBCUtils.safeGetBoolean(dbResult, "is_key_constraint_projection");
        this.hasExpressions = JDBCUtils.safeGetBoolean(dbResult, "has_expressions");
        this.isAggregateProjection = JDBCUtils.safeGetBoolean(dbResult, "is_aggregate_projection");
        this.aggregateType = JDBCUtils.safeGetString(dbResult, "aggregate_type");
        this.description = JDBCUtils.safeGetString(dbResult, "comment");

    }

    public long getObjectId() {
        return objectId;
    }

    @Property(viewable = true, order = 10)
    public String getBaseName() {
        return baseName;
    }

    @Property(viewable = true, order = 11)
    public String getOwnerName() {
        return ownerName;
    }

    @Property(viewable = true, order = 12)
    public String getAnchorTableName() {
        return anchorTableName;
    }

    @Property(viewable = true, order = 13)
    public String getNodeName() {
        return nodeName;
    }

    @Property(order = 50)
    public boolean isPreJoin() {
        return isPreJoin;
    }

    @Property(order = 51)
    public String getCreateType() {
        return createType;
    }

    @Property(order = 52)
    public String getSegmentExpression() {
        return segmentExpression;
    }

    @Property(order = 53)
    public String getSegmentRange() {
        return segmentRange;
    }

    @Property(order = 54)
    public boolean isSuperProjection() {
        return isSuperProjection;
    }

    @Property(order = 55)
    public boolean isKeyConstraintProjection() {
        return isKeyConstraintProjection;
    }

    @Property(order = 56)
    public boolean isHasExpressions() {
        return hasExpressions;
    }

    @Property(order = 57)
    public boolean isIsAggregateProjection() {
        return isAggregateProjection;
    }

    @Property(order = 58)
    public String getAggregateType() {
        return aggregateType;
    }

    @Override
    public JDBCStructCache<VerticaSchema, ? extends DBSEntity, ? extends DBSEntityAttribute> getCache() {
        return getContainer().projectionCache;
    }

    @Override
    public boolean isView() {
        return false;
    }

    @Override
    public Collection<? extends DBSTableIndex> getIndexes(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public List<? extends DBSEntityAttribute> getAttributes(@NotNull DBRProgressMonitor monitor) throws DBException {
        return getContainer().projectionCache.getChildren(monitor, getContainer(), this);
    }

    @Override
    public DBSEntityAttribute getAttribute(@NotNull DBRProgressMonitor monitor, @NotNull String attributeName) throws DBException {
        return getContainer().projectionCache.getChild(monitor, getContainer(), this, attributeName);
    }

    @Override
    public Collection<? extends DBSTableConstraint> getConstraints(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getReferences(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(
            getDataSource(),
            getContainer(),
            this);
    }

    @Property(viewable = true, order = 100)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException {
        if (isPersisted()) {
            return VerticaUtils.getObjectDDL(monitor, getDataSource(), this);
        } else {
            return null;
        }
    }

}
