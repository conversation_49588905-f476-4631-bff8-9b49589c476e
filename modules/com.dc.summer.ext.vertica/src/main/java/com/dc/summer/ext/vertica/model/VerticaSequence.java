
package com.dc.summer.ext.vertica.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericScriptObject;
import com.dc.summer.ext.generic.model.GenericSequence;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPRefreshableObject;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.util.Map;

public class VerticaSequence extends GenericSequence implements GenericScriptObject, DBPRefreshableObject {

    private static final Log log = Log.getLog(VerticaSequence.class);

    private String name;
    private String identityTableName;
    private long cacheCount;
    private boolean isCycle;
    private VerticaSchema schema;
    private String description;
    private String source;
    private boolean isPersisted;

    public VerticaSequence(GenericStructContainer container, String name, String description, Number lastValue, Number minValue, Number maxValue, Number incrementBy, String identityTableName, long cacheCount, boolean isCycle) {
        super(container, name, description, lastValue, minValue, maxValue, incrementBy);
        this.name = name;
        this.identityTableName = identityTableName;
        this.cacheCount = cacheCount;
        this.isCycle = isCycle;
        this.schema = (VerticaSchema) container.getSchema();
        this.description = description;
        this.isPersisted = true;
    }

    public VerticaSequence(GenericStructContainer container, String name) {
        super(container, name, null, 0, 1, 9223372036854775807L, 1);
        this.schema = (VerticaSchema) container.getSchema();
        this.cacheCount = 25000;
        this.isPersisted = false;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Property(viewable = true, order = 11)
    public GenericTableBase getIdentityTableName(DBRProgressMonitor monitor) {
        GenericTableBase table = null;
        if (CommonUtils.isEmpty(identityTableName)) {
            return null;
        }
        try {
            table = schema.getTable(monitor, identityTableName);
        } catch (DBException e) {
            log.debug("Can't find identity table", e);
        }
        return table;
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, order = 2)
    public Long getLastValue() {
        return super.getLastValue().longValue();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, order = 3)
    public Long getMinValue() {
        return super.getMinValue().longValue();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, order = 4)
    public Long getMaxValue() {
        return super.getMaxValue().longValue();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, order = 5)
    public Long getIncrementBy() {
        return super.getIncrementBy().longValue();
    }

    @Property(viewable = true, editable = true, updatable = true, order = 7)
    public long getCacheCount() {
        return cacheCount;
    }

    public void setCacheCount(long cacheCount) {
        this.cacheCount = cacheCount;
    }

    @Property(viewable = true, editable = true, updatable = true, order = 8)
    public boolean isCycle() {
        return isCycle;
    }

    public void setCycle(boolean cycle) {
        isCycle = cycle;
    }

    @Nullable
    @Override
    @Property(viewable = true, length = PropertyLength.MULTILINE, editable = true, updatable = true, order = 10)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public boolean isPersisted() {
        return isPersisted;
    }

    @Override
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException {
        if (source == null) {
            if (!isPersisted) {
                source = "CREATE SEQUENCE " + getFullyQualifiedName(DBPEvaluationContext.DML);
            } else {
                StringBuilder ddl = new StringBuilder();
                ddl.append("CREATE SEQUENCE ")
                    .append(getFullyQualifiedName(DBPEvaluationContext.DML))
                    .append("\n\tINCREMENT BY ").append(getIncrementBy())
                    .append("\n\tMINVALUE ").append(getMinValue())
                    .append("\n\tMAXVALUE ").append(getMaxValue())
                    .append("\n\tSTART WITH ").append(getLastValue());

                if (cacheCount <= 1) {
                    ddl.append("\n\tNO CACHE");
                } else {
                    ddl.append("\n\tCACHE ").append(cacheCount);
                }
                ddl.append("\n\t").append(isCycle ? "" : "NO ").append("CYCLE;");

                if (!CommonUtils.isEmpty(description)) {
                    ddl.append("\n\nCOMMENT ON SEQUENCE ").append(getFullyQualifiedName(DBPEvaluationContext.DML)).append(" IS ")
                        .append(SQLUtils.quoteString(this, description)).append(";");
                }
                source = ddl.toString();
            }
        }
        return source;
    }

    @Nullable
    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) {
        source = null;
        return this;
    }

}
