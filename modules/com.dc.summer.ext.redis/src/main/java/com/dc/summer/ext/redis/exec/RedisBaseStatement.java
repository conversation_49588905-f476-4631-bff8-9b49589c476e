package com.dc.summer.ext.redis.exec;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionSource;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import redis.clients.jedis.JedisPooled;
import redis.clients.jedis.UnifiedJedis;

import java.util.List;

public abstract class RedisBaseStatement implements DBCStatement {
   protected final RedisSession session;
   protected String query;
   protected DBCExecutionSource source;
   protected Throwable executeError;
   protected long offset;
   protected long limit;
   protected long updateCount;

   protected RedisBaseStatement(RedisSession session, String query) {
      this(session, query, 0L, 0L);
   }

   protected RedisBaseStatement(RedisSession session, String query, long firstRow, long maxRows) {
      this.updateCount = -1L;
      this.session = session;
      this.query = query;
      this.offset = firstRow;
      this.limit = maxRows;
      if (session.isLoggingEnabled()) {
         QMUtils.getDefaultHandler().handleStatementOpen(this);
      }

   }

   public <T> @NotNull T getCommands(Class<T> cmdClass) {
      return this.getSession().getCommands(cmdClass);
   }

   public @NotNull RedisSession getSession() {
      return this.session;
   }

   public String getQueryString() {
      return this.query;
   }

   public void setQuery(String query) {
      this.query = query;
   }

   public DBCExecutionSource getStatementSource() {
      return this.source;
   }

   public void setStatementSource(@Nullable DBCExecutionSource source) {
      this.source = source;
   }

   public void addToBatch() throws DBCException {
      throw new DBCException("Not implemented");
   }

   public int[] executeStatementBatch() throws DBCException {
      throw new DBCException("Not implemented");
   }

   public DBCResultSet openGeneratedKeysResultSet() throws DBCException {
      throw new DBCException("Not implemented");
   }

   public long getUpdateRowCount() {
      return this.updateCount;
   }

   public boolean nextResults() {
      this.updateCount = -1L;
      return false;
   }

   public void close() {
      if (this.session.isLoggingEnabled()) {
         QMUtils.getDefaultHandler().handleStatementClose(this, this.getUpdateRowCount());
      }
   }

   public void setLimit(long offset, long limit) throws DBCException {
      this.offset = offset;
      this.limit = limit;
   }

   public @Nullable Throwable[] getStatementWarnings() throws DBCException {
      throw new DBCException("Not implemented");
   }

   public void cancelBlock(@NotNull DBRProgressMonitor monitor, @Nullable Thread blockThread) throws DBException {
      if (blockThread != null) {
         blockThread.interrupt();
      } else {
         throw new DBException("Redis query cancel not implemented");
      }
   }

   public void setStatementTimeout(int timeout) throws DBCException {
   }

   public void setResultsFetchSize(int fetchSize) throws DBCException {
   }

   public long getOffset() {
      return this.offset;
   }

   public long getLimit() {
      return this.limit;
   }

   protected DBCException handleExecuteError(Throwable ex) {
      this.executeError = ex;
      return ex instanceof DBCException ? (DBCException)ex : new DBCException(ex, this.session.getExecutionContext());
   }

   protected String getClusterSampleKey() {
      return "";
   }


   @Override
   public List<String> getStatementWarningList() throws DBCException {
      throw new DBCException("Not implemented");
   }
}
