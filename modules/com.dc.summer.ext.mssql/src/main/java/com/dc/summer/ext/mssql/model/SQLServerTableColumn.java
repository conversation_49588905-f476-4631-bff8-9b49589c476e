
package com.dc.summer.ext.mssql.model;

import com.dc.summer.Log;
import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.summer.model.*;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.DBPositiveNumberTransformer;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.jdbc.struct.JDBCColumnKeyType;
import com.dc.summer.model.meta.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableColumn;
import com.dc.summer.model.meta.*;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.*;
import com.dc.summer.model.struct.rdb.DBSTableColumn;
import com.dc.utils.CommonUtils;
import com.dc.utils.Pair;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SQLServerTableColumn
 */
public class SQLServerTableColumn extends JDBCTableColumn<SQLServerTableBase> implements
    DBSTableColumn, DBSTypedObjectEx, DBPNamedObject2, DBPOrderedObject, DBPHiddenObject,
    SQLServerObject, JDBCColumnKeyType, DBSTypedObjectExt4<SQLServerDataType>, DBPRefreshableObject,
    SQLServerExtendedPropertyOwner {
    private static final Log log = Log.getLog(SQLServerTableColumn.class);

    private long objectId;
    private int userTypeId;
    private SQLServerDataType dataType;
    private String collationName;
    private String description;
    private boolean hidden;
    private boolean computedPersisted;
    private String computedDefinition;
    private String defaultConstraintName;
    private IdentityInfo identityInfo = new IdentityInfo();

    private final SQLServerExtendedPropertyCache extendedPropertyCache = new SQLServerExtendedPropertyCache();

    public static class IdentityInfo {
        private long seedValue;
        private long incrementValue;
        private long lastValue;
        private boolean loaded;

        public long getSeedValue() {
            return seedValue;
        }

        public long getIncrementValue() {
            return incrementValue;
        }

        @Property(viewable = false, order = 60)
        public long getLastValue() {
            return lastValue;
        }
    }

    public static class IdentityInfoValidator implements IPropertyCacheValidator<SQLServerTableColumn> {
        @Override
        public boolean isPropertyCached(SQLServerTableColumn object, Object propertyId) {
            return object.identityInfo.loaded;
        }
    }

    public SQLServerTableColumn(SQLServerTableBase table) {
        super(table, false);
    }

    public SQLServerTableColumn(
        DBRProgressMonitor monitor,
        SQLServerTableBase table,
        ResultSet dbResult)
        throws DBException {
        super(table, true);
        loadInfo(monitor, dbResult);
    }

    // Copy constructor
    public SQLServerTableColumn(
        DBRProgressMonitor monitor,
        SQLServerTableBase table,
        DBSEntityAttribute source)
        throws DBException {
        super(table, source, false);
        this.description = source.getDescription();
        if (source instanceof SQLServerTableColumn) {
            SQLServerTableColumn mySource = (SQLServerTableColumn) source;
            // Copy
        }
    }

    private void loadInfo(DBRProgressMonitor monitor, ResultSet dbResult)
        throws DBException {
        this.objectId = JDBCUtils.safeGetLong(dbResult, "column_id");

        setName(JDBCUtils.safeGetString(dbResult, "name"));
        setOrdinalPosition(JDBCUtils.safeGetInt(dbResult, "column_id"));

        this.userTypeId = JDBCUtils.safeGetInt(dbResult, "user_type_id");
        this.dataType = getTable().getDatabase().getDataTypeByUserTypeId(monitor, userTypeId);
        if (this.dataType == null) {
            throw new DBCException("Data type '" + userTypeId + "' not found for column '" + getName() + "'");
        }
        if (getDataSource().supportsColumnProperty()) {
            this.maxLength = JDBCUtils.safeGetLong(dbResult, "char_max_length");
        }
        if (this.maxLength == 0) {
            this.maxLength = JDBCUtils.safeGetInt(dbResult, "max_length");

            if (maxLength > 0 && SQLServerUtils.isUnicodeCharStoredAsBytePairs(getDataSource())) {
                final String typeName = getTypeName();
                if (typeName.equals(SQLServerConstants.TYPE_NVARCHAR) || typeName.equals(SQLServerConstants.TYPE_NCHAR)) {
                    // https://docs.microsoft.com/en-us/sql/t-sql/data-types/nchar-and-nvarchar-transact-sql#arguments
                    this.maxLength /= 2;
                }
            }
        }
        if (this.maxLength > 0) {
            final String typeName = getTypeName();
            if (typeName.equals(SQLServerConstants.TYPE_NVARCHAR) || typeName.equals(SQLServerConstants.TYPE_NCHAR)) {
                // https://docs.microsoft.com/en-us/sql/t-sql/data-types/nchar-and-nvarchar-transact-sql#arguments
                this.maxLength /= 2;
            }
        }
        setRequired(JDBCUtils.safeGetInt(dbResult, "is_nullable") == 0);
        setScale(JDBCUtils.safeGetInteger(dbResult, "scale"));
        setPrecision(JDBCUtils.safeGetInteger(dbResult, "precision"));
        setAutoGenerated(JDBCUtils.safeGetInt(dbResult, "is_identity") != 0);

        if (this.getName().equals(SQLServerConstants.TYPE_TIMESTAMP)) {
            setAutoGenerated(true);
        }
        setValueType(dataType.getTypeID());

        this.hidden = JDBCUtils.safeGetInt(dbResult, "is_hidden") != 0;
        this.collationName = JDBCUtils.safeGetString(dbResult, "collation_name");
        String dv = JDBCUtils.safeGetString(dbResult, "default_definition");
        if (!CommonUtils.isEmpty(dv)) {
            // Remove redundant brackets
            while (dv.startsWith("(") && dv.endsWith(")")) {
                dv = dv.substring(1, dv.length() - 1);
            }
            this.setDefaultValue(dv);
            this.defaultConstraintName = JDBCUtils.safeGetString(dbResult, "default_constraint_name");
        }
        this.description = JDBCUtils.safeGetString(dbResult, "description");
        this.computedPersisted = JDBCUtils.safeGetInt(dbResult, "is_persisted") != 0;
        this.computedDefinition = JDBCUtils.safeGetString(dbResult, "computed_definition");
    }

    @NotNull
    @Override
    public SQLServerDataSource getDataSource() {
        return getTable().getDataSource();
    }

    //@Property(viewable = true, editable = true, updatable = true, order = 20, listProvider = ColumnTypeNameListProvider.class)
    public String getFullTypeName() {
        if (dataType == null) {
            return String.valueOf(userTypeId);
        }

        String typeName = dataType.getName();
        String typeModifiers = SQLUtils.getColumnTypeModifiers(getDataSource(), this, typeName, dataType.getDataKind());
        return typeModifiers == null ? typeName : (typeName + CommonUtils.notEmpty(typeModifiers));
    }

    @Override
    public String getTypeName() {
        return dataType == null ? String.valueOf(userTypeId) : dataType.getTypeName();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, order = 40, listProvider = DataTypeListProvider.class)
    public DBSDataType getDataType() {
        return dataType;
    }

    @Override
    public void setDataType(SQLServerDataType dataType) {
        this.dataType = dataType;
        this.typeName = dataType.getTypeName();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, order = 41)
    public long getMaxLength() {
        return super.getMaxLength();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, valueRenderer = DBPositiveNumberTransformer.class, order = 43)
    public Integer getScale() {
        return super.getScale();
    }

    @Override
    @Property(viewable = true, editable = true, updatable = true, valueRenderer = DBPositiveNumberTransformer.class, order = 44)
    public Integer getPrecision() {
        return super.getPrecision();
    }

    @Property(viewable = true, editable = true, updatable = true, order = 50)
    @Override
    public boolean isRequired() {
        return super.isRequired();
    }

    @Override
    public boolean isAutoGenerated() {
        // Computed columns are "auto-generated" (from supplied definition)
        return super.isAutoGenerated() || CommonUtils.isNotEmpty(getComputedDefinition());
    }

    @Property(viewable = true, editable = true, order = 55)
    public boolean isIdentity() {
        // We have to do that to avoid treating computed column as an identity column (see #isAutoGenerated)
        return super.isAutoGenerated();
    }

    public void setIdentity(boolean identity) {
        super.setAutoGenerated(identity);
    }

    @Property(viewable = true, editable = true, updatable = true, order = 70)
    @Override
    public String getDefaultValue() {
        return super.getDefaultValue();
    }

    @Nullable
    public String getDefaultConstraintName() {
        return defaultConstraintName;
    }

    @Override
    public DBPDataKind getDataKind() {
        return dataType == null ? DBPDataKind.UNKNOWN : dataType.getDataKind();
    }

    @Property(viewable = true, editable = true, updatable = true, order = 75)
    public String getCollationName() {
        return collationName;
    }

    public void setCollationName(String collationName) {
        this.collationName = collationName;
    }

    @Property(editable = true, order = 76)
    public String getComputedDefinition() {
        return computedDefinition;
    }

    public void setComputedDefinition(String computedDefinition) {
        this.computedDefinition = computedDefinition;
    }

    @Property(editable = true, order = 77)
    public boolean isComputedPersisted() {
        return computedPersisted;
    }

    public void setComputedPersisted(boolean computedPersisted) {
        this.computedPersisted = computedPersisted;
    }

    @Property(viewable = false, order = 80)
    @Override
    public boolean isHidden() {
        return hidden;
    }

    @Nullable
    @Override
    @Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 100)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    @Property(viewable = false, order = 80)
    public long getObjectId() {
        return objectId;
    }

    @Override
    protected JDBCColumnKeyType getKeyType() {
        return this;
    }

    @Override
    public boolean isInUniqueKey() {
        // Mark all identity columns as unique keys
        return isIdentity();
    }

    @Override
    public boolean isInReferenceKey() {
        return false;
    }

    @Nullable
    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        extendedPropertyCache.clearCache();
        return this;
    }

    //////////////////////////////////////////////////
    // Extended Properties

    @Association
    @NotNull
    public Collection<SQLServerExtendedProperty> getExtendedProperties(@NotNull DBRProgressMonitor monitor) throws DBException {
        return extendedPropertyCache.getAllObjects(monitor, this);
    }

    @Override
    public long getMajorObjectId() {
        return getTable().getObjectId();
    }

    @Override
    public long getMinorObjectId() {
        return getObjectId();
    }

    @Override
    public Pair<String, SQLServerObject> getExtendedPropertyObject(@NotNull DBRProgressMonitor monitor, int level) {
        switch (level) {
            case 0:
                return new Pair<>("Schema", getTable().getSchema());
            case 1:
                return new Pair<>("Table", getTable());
            case 2:
                return new Pair<>("Column", this);
            default:
                return null;
        }
    }

    @NotNull
    @Override
    public SQLServerObjectClass getExtendedPropertyObjectClass() {
        return SQLServerObjectClass.OBJECT_OR_COLUMN;
    }

    @NotNull
    @Override
    public SQLServerExtendedPropertyCache getExtendedPropertyCache() {
        return extendedPropertyCache;
    }

    @NotNull
    @Override
    public SQLServerDatabase getDatabase() {
        return getTable().getDatabase();
    }

    @PropertyGroup()
    @LazyProperty(cacheValidator = IdentityInfoValidator.class)
    public IdentityInfo getIdentityInfo(DBRProgressMonitor monitor) throws DBCException {
        if (!identityInfo.loaded) {
            if (!isIdentity()) {
                identityInfo.loaded = true;
            } else {
                loadIdentityInfo(monitor);
            }
        }
        return identityInfo;
    }

    private void loadIdentityInfo(DBRProgressMonitor monitor) throws DBCException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load column identity info")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                "SELECT seed_value,increment_value,last_value FROM " +
                    SQLServerUtils.getSystemTableName(getTable().getDatabase(), "identity_columns") + " WHERE object_id=?")) {
                dbStat.setLong(1, getTable().getObjectId());
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    if (dbResult.next()) {
                        identityInfo.seedValue = CommonUtils.toLong(JDBCUtils.safeGetObject(dbResult, "seed_value"));
                        identityInfo.incrementValue = CommonUtils.toLong(JDBCUtils.safeGetObject(dbResult, "increment_value"));
                        identityInfo.lastValue = CommonUtils.toLong(JDBCUtils.safeGetObject(dbResult, "last_value"));
                    }
                    identityInfo.loaded = true;
                }
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }
    }

    public static class DataTypeListProvider implements IPropertyValueListProvider<SQLServerTableColumn> {
        @Override
        public boolean allowCustomValue() {
            return false;
        }

        @Override
        public Object[] getPossibleValues(SQLServerTableColumn object) {
            List<SQLServerDataType> allTypes = new ArrayList<>(object.getDataSource().getLocalDataTypes());
            try {
                List<SQLServerDataType> schemaTypes = object.getTable().getSchema().getDataTypes(new VoidProgressMonitor())
                        .stream().filter(type -> !type.isTableType()).collect(Collectors.toList()); //do not show table types in types list
                allTypes.addAll(schemaTypes);
            } catch (DBException e) {
                log.debug("Error getting schema data types", e);
            }
            return allTypes.toArray();
        }
    }
}
