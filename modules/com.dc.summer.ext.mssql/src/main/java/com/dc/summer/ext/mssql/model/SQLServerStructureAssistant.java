
package com.dc.summer.ext.mssql.model;

import com.dc.summer.Log;
import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSStructureAssistant;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.struct.AbstractObjectReference;
import com.dc.summer.model.impl.struct.RelationalObjectType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectReference;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * SQLServerStructureAssistant
 */
public class SQLServerStructureAssistant implements DBSStructureAssistant<SQLServerExecutionContext> {
    private static final Log log = Log.getLog(SQLServerStructureAssistant.class);

    private final SQLServerDataSource dataSource;

    public SQLServerStructureAssistant(SQLServerDataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes()
    {
        return new DBSObjectType[] {
            SQLServerObjectType.S,
            SQLServerObjectType.U,
            SQLServerObjectType.IT,
            SQLServerObjectType.V,
            SQLServerObjectType.SN,
            SQLServerObjectType.P,
            SQLServerObjectType.FN,
            SQLServerObjectType.FT,
            SQLServerObjectType.FS,
            SQLServerObjectType.X,
            };
    }

    @Override
    public DBSObjectType[] getSearchObjectTypes() {
        return new DBSObjectType[] {
            RelationalObjectType.TYPE_TABLE,
            RelationalObjectType.TYPE_VIEW,
            SQLServerObjectType.SN,
            RelationalObjectType.TYPE_PROCEDURE,
        };
    }

    @Override
    public DBSObjectType[] getHyperlinkObjectTypes()
    {
        return new DBSObjectType[] {
            SQLServerObjectType.S,
            SQLServerObjectType.U,
            SQLServerObjectType.IT,
            SQLServerObjectType.V,
            RelationalObjectType.TYPE_PROCEDURE,
        };
    }

    @Override
    public DBSObjectType[] getAutoCompleteObjectTypes()
    {
        return new DBSObjectType[] {
            SQLServerObjectType.U,
            SQLServerObjectType.V,
            SQLServerObjectType.P,
            SQLServerObjectType.FN,
            SQLServerObjectType.IF,
            SQLServerObjectType.TF,
            SQLServerObjectType.X,
            SQLServerObjectType.SN
        };
    }

    @NotNull
    @Override
    public List<DBSObjectReference> findObjectsByMask(@NotNull DBRProgressMonitor monitor, @NotNull SQLServerExecutionContext executionContext,
                                                      @NotNull ObjectsSearchParams params) throws DBException {
        if (params.getMask().startsWith("%#") || params.getMask().startsWith("#")) {
            try (JDBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.META, "Find temp tables by name")) {
                List<DBSObjectReference> objects = new ArrayList<>();
                searchTempTables(session, params, objects);
                return objects;
            }
        }

        return findAllObjects(monitor, executionContext, params);
    }

    private List<DBSObjectReference> findAllObjects(@NotNull DBRProgressMonitor monitor, @NotNull SQLServerExecutionContext executionContext,
                                                    @NotNull ObjectsSearchParams params) throws DBException {

        DBSObject parentObject = params.getParentObject();
        boolean globalSearch = params.isGlobalSearch();
        Collection<SQLServerDatabase> databases;
        SQLServerSchema schema = null;

        if (parentObject == null || parentObject instanceof SQLServerDataSource) {
            if (globalSearch) {
                databases = executionContext.getDataSource().getDatabases(monitor);
            } else {
                SQLServerDatabase database = executionContext.getContextDefaults().getDefaultCatalog();
                if (database == null) {
                    database = executionContext.getDataSource().getDefaultDatabase(monitor);
                }
                schema = executionContext.getContextDefaults().getDefaultSchema();
                if (database == null || schema == null) {
                    return Collections.emptyList();
                }
                databases = Collections.singletonList(database);
            }
        } else if (parentObject instanceof SQLServerDatabase) {
            SQLServerDatabase database = (SQLServerDatabase) parentObject;
            databases = Collections.singletonList(database);
            if (!globalSearch) {
                schema = executionContext.getContextDefaults().getDefaultSchema();
                if (schema == null) {
                    return Collections.emptyList();
                }
            }
        } else if (parentObject instanceof SQLServerSchema) {
            schema = (SQLServerSchema) parentObject;
            SQLServerDatabase database = schema.getDatabase();
            databases = Collections.singletonList(database);
        } else {
            if (parentObject instanceof SQLServerObject) {
                databases = Collections.singletonList(((SQLServerObject) parentObject).getDatabase());
            } else if (parentObject instanceof DBPDataSourceContainer) {
                SQLServerDatabase database = executionContext.getContextDefaults().getDefaultCatalog();
                if (database == null) {
                    database = executionContext.getDataSource().getDefaultDatabase(monitor);
                }
                databases = Collections.singletonList(database);
            } else {
                return Collections.emptyList();
            }
        }

        if (CommonUtils.isEmpty(databases)) {
            return Collections.emptyList();
        }

        Collection<SQLServerObjectType> supObjectTypes = new ArrayList<>(params.getObjectTypes().length + 2);
        for (DBSObjectType objectType : params.getObjectTypes()) {
            if (objectType instanceof SQLServerObjectType) {
                supObjectTypes.add((SQLServerObjectType) objectType);
            } else if (objectType == RelationalObjectType.TYPE_PROCEDURE) {
                supObjectTypes.addAll(SQLServerObjectType.getTypesForClass(SQLServerProcedure.class));
            } else if (objectType == RelationalObjectType.TYPE_TABLE) {
                supObjectTypes.addAll(SQLServerObjectType.getTypesForClass(SQLServerTable.class));
            } else if (objectType == RelationalObjectType.TYPE_CONSTRAINT) {
                supObjectTypes.addAll(SQLServerObjectType.getTypesForClass(SQLServerTableCheckConstraint.class));
                supObjectTypes.addAll(SQLServerObjectType.getTypesForClass(SQLServerTableForeignKey.class));
            } else if (objectType == RelationalObjectType.TYPE_VIEW) {
                supObjectTypes.addAll(SQLServerObjectType.getTypesForClass(SQLServerView.class));
            }
        }
        if (supObjectTypes.isEmpty()) {
            return Collections.emptyList();
        }
        StringBuilder objectTypeClause = new StringBuilder(100);
        for (SQLServerObjectType objectType : supObjectTypes) {
            if (objectTypeClause.length() > 0) objectTypeClause.append(",");
            objectTypeClause.append("'").append(objectType.getTypeID()).append("'");
        }
        if (objectTypeClause.length() == 0) {
            return Collections.emptyList();
        }
        boolean hasMask = !CommonUtils.isEmpty(params.getMask()) && !params.getMask().equals("%");

        StringBuilder sqlBuilder = new StringBuilder("SELECT TOP %d * FROM %s o");
        if (params.isSearchInComments()) {
            sqlBuilder.append(" LEFT JOIN sys.extended_properties ep ON ((o.parent_object_id = 0 AND ep.minor_id = 0 AND o.object_id = ep.major_id) OR (o.parent_object_id <> 0 AND ep.minor_id = o.parent_object_id AND ep.major_id = o.object_id)) ");
        }
        sqlBuilder.append(" WHERE o.type IN (").append(objectTypeClause).append(") ");
        if (hasMask) {
            sqlBuilder.append("AND ");
            boolean addParentheses = params.isSearchInComments() || params.isSearchInDefinitions();
            if (addParentheses) {
                sqlBuilder.append("(");
            }
            sqlBuilder.append("o.name LIKE ? ");
            if (params.isSearchInComments()) {
                sqlBuilder.append("OR (ep.name = 'MS_Description' AND CAST(ep.value AS nvarchar) LIKE ?)");
            }
            if (params.isSearchInDefinitions()) {
                if (params.isSearchInComments()) {
                    sqlBuilder.append(" ");
                }
                sqlBuilder.append("OR OBJECT_DEFINITION(o.object_id) LIKE ?");
            }
            if (addParentheses) {
                sqlBuilder.append(") ");
            }
        }
        if (schema != null) {
            sqlBuilder.append("AND o.schema_id = ? ");
        }
        sqlBuilder.append("ORDER BY o.name");
        String template = sqlBuilder.toString();

        List<DBSObjectReference> objects = new ArrayList<>();
        try (JDBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.META, "Find objects by name")) {
            for (SQLServerDatabase database: databases) {
                int rowsToFetch = params.getMaxResults() - objects.size();
                if (rowsToFetch < 1) {
                    break;
                }
                String sql = String.format(template, rowsToFetch, SQLServerUtils.getSystemTableName(database, "all_objects"));
                try (JDBCPreparedStatement dbStat = session.prepareStatement(sql)) {
                    int idx = 1;
                    if (hasMask) {
                        dbStat.setString(1, params.getMask());
                        idx++;
                        if (params.isSearchInComments()) {
                            dbStat.setString(idx, params.getMask());
                            idx++;
                        }
                        if (params.isSearchInDefinitions()) {
                            dbStat.setString(idx, params.getMask());
                            idx++;
                        }
                    }
                    if (schema != null) {
                        dbStat.setLong(idx, schema.getObjectId());
                    }
                    dbStat.setFetchSize(DBConstants.METADATA_FETCH_SIZE);

                    try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                        while (dbResult.next() && !session.getProgressMonitor().isCanceled() && objects.size() < params.getMaxResults()) {
                            final long schemaId = JDBCUtils.safeGetLong(dbResult, "schema_id");
                            final String objectName = JDBCUtils.safeGetString(dbResult, "name");
                            final String objectTypeName = JDBCUtils.safeGetStringTrimmed(dbResult, "type");
                            final SQLServerObjectType objectType = SQLServerObjectType.valueOf(objectTypeName);
                            SQLServerSchema objectSchema = schemaId == 0 ? null : database.getSchema(session.getProgressMonitor(), schemaId);
                            objects.add(new AbstractObjectReference<DBSObject>(
                                objectName,
                                objectSchema != null ? objectSchema : database,
                                null,
                                objectType.getTypeClass(),
                                objectType)
                            {
                                @Override
                                public DBSObject resolveObject(DBRProgressMonitor monitor) throws DBException {
                                    DBSObject object = objectType.findObject(session.getProgressMonitor(), database, objectSchema, objectName);
                                    if (object == null) {
                                        throw new DBException(objectTypeName + " '" + objectName + "' not found");
                                    }
                                    return object;
                                }
                            });
                        }
                    }
                } catch (SQLException e) {
                    // Among other reasons, this can happen if access is denied for the current database
                    log.debug(
                    "Unable to perform metadata search in mssql instance. databaseName="
                            + database.getName()
                            + ", schema="
                            + (schema != null ? schema.getName() : "null"),
                        e
                    );
                }
            }
        }

        return objects;
    }

    @Override
    public boolean supportsSearchInCommentsFor(@NotNull DBSObjectType objectType) {
        return true;
    }

    @Override
    public boolean supportsSearchInDefinitionsFor(@NotNull DBSObjectType objectType) {
        return true;
    }
  
    private void searchTempTables(@NotNull JDBCSession session, @NotNull ObjectsSearchParams params, @NotNull List<DBSObjectReference> objects) throws DBException {
        final SQLServerDatabase database = dataSource.getDatabase(session.getProgressMonitor(), SQLServerConstants.TEMPDB_DATABASE);
        final SQLServerSchema schema = database.getSchema(session.getProgressMonitor(), SQLServerConstants.DEFAULT_SCHEMA_NAME);

        final StringBuilder sql = new StringBuilder()
            .append("SELECT TOP ").append(params.getMaxResults() - objects.size()).append(" *")
            .append("\nFROM ").append(SQLServerUtils.getSystemTableName(database, "all_objects"))
            .append("\nWHERE type = '").append(SQLServerObjectType.U.name())
            .append("' AND name LIKE '#%' AND name LIKE ? AND OBJECT_ID(CONCAT('").append(SQLServerConstants.TEMPDB_DATABASE).append("..', QUOTENAME(name))) <> 0");

        try (JDBCPreparedStatement dbStat = session.prepareStatement(sql.toString())) {
            dbStat.setString(1, "%" + params.getMask() + "%");
            dbStat.setFetchSize(DBConstants.METADATA_FETCH_SIZE);

            try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                while (dbResult.next() && !session.getProgressMonitor().isCanceled()) {
                    final String objectName = JDBCUtils.safeGetString(dbResult, "name");
                    final String objectNameTrimmed = extractTempTableName(objectName);
                    final SQLServerObjectType objectType = SQLServerObjectType.valueOf(JDBCUtils.safeGetStringTrimmed(dbResult, "type"));

                    objects.add(new AbstractObjectReference<>(objectName, database, null, objectType.getTypeClass(), objectType) {
                        @Override
                        public DBSObject resolveObject(DBRProgressMonitor monitor) throws DBException {
                            DBSObject object = schema.getChild(session.getProgressMonitor(), objectName);
                            if (object == null) {
                                // Likely not cached, invalidate and try again
                                schema.getTableCache().setFullCache(false);
                                object = schema.getChild(session.getProgressMonitor(), objectName);
                            }
                            if (object == null) {
                                throw new DBException(objectType.name() + " '" + objectName + "' not found");
                            }
                            return object;
                        }

                        @NotNull
                        @Override
                        public String getFullyQualifiedName(DBPEvaluationContext context) {
                            return objectNameTrimmed;
                        }
                    });
                }
            }
        } catch (Throwable e) {
            throw new DBException("Error while searching in system catalog", e, dataSource);
        }
    }

    @NotNull
    private static String extractTempTableName(@NotNull String originalName) {
        if (originalName.startsWith("##")) {
            // Global temporary tables does not contain padding in their names. Use as-is
            return originalName;
        }
        final String name = originalName.substring(0, 116);
        for (int i = name.length() - 1; i >= 0; i--) {
            if (name.charAt(i) != '_') {
                return name.substring(0, i + 1);
            }
        }
        return name;
    }
}
