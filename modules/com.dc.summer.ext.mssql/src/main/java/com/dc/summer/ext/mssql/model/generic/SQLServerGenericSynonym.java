
package com.dc.summer.ext.mssql.model.generic;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericCatalog;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.GenericSynonym;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericSchema;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;

/**
* SQL server synonym
*/
public class SQLServerGenericSynonym extends GenericSynonym implements DBPQualifiedObject {

    private static final Log log = Log.getLog(SQLServerGenericSynonym.class);

    private String targetObjectName;

    public SQLServerGenericSynonym(GenericStructContainer container, String name, String description, String targetObjectName) {
        super(container, name, description);
        this.targetObjectName = targetObjectName;
    }

    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(
            getDataSource(),
            getParentObject(),
            this);
    }

    @Property(viewable = true, order = 20)
    @Override
    public DBSObject getTargetObject(DBRProgressMonitor monitor) throws DBException {
        int divPos = targetObjectName.indexOf("].[");
        if (divPos == -1) {
            log.debug("Bad target object name '" + targetObjectName + "' for synonym '" + getName() + "'");
            return null;
        }
        String schemaName = DBUtils.getUnQuotedIdentifier(getDataSource(), targetObjectName.substring(0, divPos + 1));
        String objectName = DBUtils.getUnQuotedIdentifier(getDataSource(), targetObjectName.substring(divPos + 2));
        GenericCatalog database = getParentObject().getCatalog();
        GenericSchema schema = database.getSchema(monitor, schemaName);
        if (schema == null) {
            log.debug("Schema '" + schemaName + "' not found for synonym '" + getName() + "'");
            return null;
        }
        return schema.getChild(monitor, objectName);
    }
}
