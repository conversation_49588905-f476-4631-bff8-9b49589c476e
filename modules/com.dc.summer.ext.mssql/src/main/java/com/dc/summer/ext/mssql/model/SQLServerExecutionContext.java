
package com.dc.summer.ext.mssql.model;

import com.dc.annotation.SQL;
import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionBootstrap;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.utils.CommonUtils;
import com.dc.utils.StackTraceUtils;

import java.sql.SQLException;
import java.util.*;

/**
 * SQLServerExecutionContext
 */
public class SQLServerExecutionContext extends JDBCExecutionContext implements DBCExecutionContextDefaults<SQLServerDatabase, SQLServerSchema> {
    private static final Log log = Log.getLog(SQLServerExecutionContext.class);

    //private SQLServerDatabase activeDatabase;
    private String activeDatabaseName;
    private String activeSchemaName;
    private String currentUser;

    private volatile String prefs;

    SQLServerExecutionContext(@NotNull JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @NotNull
    @Override
    public SQLServerDataSource getDataSource() {
        return (SQLServerDataSource) super.getDataSource();
    }

    @NotNull
    @Override
    public SQLServerExecutionContext getContextDefaults() {
        return this;
    }

    public String getActiveDatabaseName() {
        return activeDatabaseName;
    }

    @Override
    public SQLServerDatabase getDefaultCatalog() {
        return getDataSource().getDatabase(activeDatabaseName);
    }

    @Nullable
    @Override
    public SQLServerSchema getDefaultSchema() {
        if (CommonUtils.isEmpty(activeSchemaName)) {
            return null;
        }
        try {
            SQLServerDatabase defaultCatalog = getDefaultCatalog();
            if (defaultCatalog == null) {
                return null;
            }
            SQLServerSchema schema = defaultCatalog.getSchema(new VoidProgressMonitor(), activeSchemaName);
            if (schema == null) {
                // If DBO is not the default schema and default schema doesn't exist (or was filtered out) let's try DBO though
                schema = defaultCatalog.getSchema(new VoidProgressMonitor(), SQLServerConstants.DEFAULT_SCHEMA_NAME);
            }
            return schema;
        } catch (DBException e) {
            log.error(e);
            return null;
        }
    }

    @Override
    public boolean supportsCatalogChange() {
        return true;
    }

    @Override
    public boolean supportsSchemaChange() {
        return false;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, SQLServerDatabase catalog, @Nullable SQLServerSchema schema, boolean force) throws DBCException {
        if (activeDatabaseName != null && activeDatabaseName.equals(catalog.getName()) && !force) {
            return;
        }
        final SQLServerDatabase oldActiveDatabase = getDefaultCatalog();

        if (!setCurrentDatabase(monitor, catalog)) {
            return;
        }
        try {
            catalog.getSchemas(monitor);
        } catch (DBException e) {
            log.debug("Error caching database schemas", e);
        }
        activeDatabaseName = catalog.getName();

        // Send notifications
        DBUtils.fireObjectSelectionChange(oldActiveDatabase, catalog);

        if (schema != null) {
            setDefaultSchema(monitor, schema, false);
        }
    }

    @Override
    public void setDefaultSchema(DBRProgressMonitor monitor, SQLServerSchema schema, boolean force) throws DBCException {
        if (activeSchemaName != null && activeSchemaName.equals(schema.getName()) && !force) {
            return;
        }
        final SQLServerSchema oldActiveSchema = getDefaultSchema();

//        if (!setCurrentSchema(monitor, schema.getName())) {
//            return;
//        }
        activeSchemaName = schema.getName();

        // Send notifications
        DBUtils.fireObjectSelectionChange(oldActiveSchema, schema);
    }

    @Override
    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
        boolean refreshed = false;
        // Check default active schema
        try (JDBCSession session = openSession(monitor, DBCExecutionPurpose.META, "Query active schema and database")) {
            String currentDatabase = null;
            String currentSchema = null;
            try {
                try (JDBCStatement dbStat = session.createStatement()) {
                    String query = "SELECT db_name(), schema_name(), original_login()";
                    if (SQLServerUtils.isDriverBabelfish(session.getDataSource().getContainer().getDriver())) {
                        query = "SELECT db_name(), s.name AS schema_name, session_user AS original_login FROM sys.schemas s";
                    }
                    try (JDBCResultSet dbResult = dbStat.executeQuery(query)) {
                        dbResult.next();
                        currentDatabase = dbResult.getString(1);
                        currentSchema = dbResult.getString(2);
                        currentUser = dbResult.getString(3);
                    }
                }
            } catch (Throwable e) {
                log.debug("Error getting current user: " + e.getMessage());
            }
            if (!CommonUtils.isEmpty(currentDatabase) && (activeDatabaseName == null || !CommonUtils.equalObjects(currentDatabase, activeDatabaseName))) {
                activeDatabaseName = currentDatabase;
                refreshed = true;
            }
            if (CommonUtils.isEmpty(currentSchema)) {
                currentSchema = SQLServerConstants.DEFAULT_SCHEMA_NAME;
            }
            if (activeSchemaName == null || !CommonUtils.equalObjects(currentSchema, activeSchemaName)) {
                activeSchemaName = currentSchema;
                refreshed = true;
            }
            if (useBootstrapSettings) {
                DBPConnectionBootstrap bootstrap = getBootstrapSettings();
                if (!CommonUtils.isEmpty(bootstrap.getDefaultCatalogName()) && supportsCatalogChange() && !CommonUtils.equalObjects(bootstrap.getDefaultCatalogName(), activeDatabaseName)) {
                    setCurrentDatabase(monitor, bootstrap.getDefaultCatalogName());
                    refreshed = true;
                }
/*
                if (!CommonUtils.isEmpty(bootstrap.getDefaultSchemaName()) && supportsSchemaChange()) {
                    setCurrentSchema(monitor, bootstrap.getDefaultSchemaName());
                    refreshed = true;
                }
*/
            }
        }

        return refreshed;
    }

    boolean setCurrentDatabase(DBRProgressMonitor monitor, SQLServerDatabase object) throws DBCException {
        if (object == null) {
            log.debug("Null current schema");
            return false;
        }
        String databaseName = object.getName();
        return setCurrentDatabase(monitor, databaseName);
    }

    private boolean setCurrentDatabase(DBRProgressMonitor monitor, String databaseName) {
        try (JDBCSession session = openSession(monitor, DBCExecutionPurpose.UTIL, "Set active database")) {
            SQLServerUtils.setCurrentDatabase(session, databaseName);
            activeDatabaseName = databaseName;
            return true;
        } catch (SQLException e) {
            log.error(e);
            return false;
        }
    }

/*
    private boolean setCurrentSchema(DBRProgressMonitor monitor, String schemaName) {
        try (JDBCSession session = openSession(monitor, DBCExecutionPurpose.UTIL, "Set active schema")) {
            SQLServerUtils.setCurrentSchema(session, currentUser, schemaName);
            activeSchemaName = schemaName;
            return true;
        } catch (SQLException e) {
            log.error(e);
            return false;
        }
    }
*/

    @Override
    protected List<Map<String, Object>> initContextBootstrap(DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        String sql = "SELECT @@SPID AS 'sid'";
        getBootstrapSettings().setInitQueries(Collections.singleton(sql));
        List<Map<String, Object>> list = execContextBootstrap(monitor, autoCommit);
        if (list.size() > 0) {
            super.setProcessId(String.valueOf(list.get(0).get("sid")));
        }
        return list;
    }

    @Override
    public void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException {
        if (prefs == null || prefs.isBlank()) {
            prefs = SetOptionsType.DEFAULT_OPTIONS;
        }
        if (prefs.equals(this.prefs)) {
            return;
        }
        this.prefs = prefs;
        final String methodName = StackTraceUtils.getCurrentMethodName();
        List<String> list = List.of(this.prefs.split(","));

        try (DBCSession session = this.openSession(monitor, DBCExecutionPurpose.UTIL, methodName)) {
            String onOff;
            @SQL String sql;
            for (SetOptionsType setOptionsType : SetOptionsType.values()) {
                onOff = list.contains(setOptionsType.getCode()) ? "ON" : "OFF";
                sql = String.format("SET %s %s;", setOptionsType.name(), onOff);
                try (DBCStatement dbcStatement = DBUtils.createStatement(session, sql, true)) {
                    dbcStatement.executeStatement();
                }
            }

            onOff = isAutoCommit() ? "OFF" : "ON";
            sql = String.format("SET IMPLICIT_TRANSACTIONS %s;", onOff);
            try (DBCStatement dbcStatement = DBUtils.createStatement(session, sql, true)) {
                dbcStatement.executeStatement();
            }
        }


    }
}
