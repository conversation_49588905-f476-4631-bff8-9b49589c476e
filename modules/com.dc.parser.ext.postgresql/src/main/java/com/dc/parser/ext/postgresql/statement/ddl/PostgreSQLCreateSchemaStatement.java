
package com.dc.parser.ext.postgresql.statement.ddl;

import com.dc.parser.ext.postgresql.statement.PostgreSQLStatement;
import com.dc.parser.model.statement.ddl.CreateSchemaStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * PostgreSQL create schema statement.
 */
@Getter
@Setter
public final class PostgreSQLCreateSchemaStatement extends CreateSchemaStatement implements PostgreSQLStatement {
    
    private IdentifierValue username;
    
    /**
     * Get username.
     *
     * @return username
     */
    @Override
    public Optional<IdentifierValue> getUsername() {
        return Optional.ofNullable(username);
    }
}
