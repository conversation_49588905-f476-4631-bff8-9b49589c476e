
package com.dc.parser.ext.postgresql.statement.ddl;

import com.dc.parser.ext.postgresql.statement.PostgreSQLStatement;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.ClusterStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * PostgreSQL cluster statement.
 */
@Getter
@Setter
public final class PostgreSQLClusterStatement extends ClusterStatement implements PostgreSQLStatement {

    private SimpleTableSegment simpleTable;
    
    private IndexSegment index;
    
    /**
     * Get simple table segment.
     *
     * @return simple table segment
     */
    @Override
    public Optional<SimpleTableSegment> getSimpleTable() {
        return Optional.ofNullable(simpleTable);
    }
    
    /**
     * Get index segment.
     *
     * @return index segment
     */
    @Override
    public Optional<IndexSegment> getIndex() {
        return Optional.ofNullable(index);
    }
}
