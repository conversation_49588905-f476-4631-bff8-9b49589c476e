
package com.dc.parser.ext.postgresql.visitor.statement.type;

import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.TCLStatementVisitor;
import com.dc.parser.ext.postgresql.parser.autogen.PostgreSQLStatementParser.*;
import com.dc.parser.ext.postgresql.visitor.statement.PostgreSQLStatementVisitor;
import com.dc.parser.model.enums.TransactionAccessType;
import com.dc.parser.model.enums.TransactionIsolationLevel;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.ext.postgresql.statement.tcl.*;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * TCL statement visitor for PostgreSQL.
 */
public final class PostgreSQLTCLStatementVisitor extends PostgreSQLStatementVisitor implements TCLStatementVisitor {
    
    @Override
    public ASTNode visitSetTransaction(final SetTransactionContext ctx) {
        PostgreSQLSetTransactionStatement result = new PostgreSQLSetTransactionStatement();
        if (null != ctx.transactionModeList()) {
            ctx.transactionModeList().transactionModeItem().forEach(each -> {
                result.setAccessMode(getTransactionAccessType(each));
                result.setIsolationLevel(getTransactionIsolationLevel(each));
            });
        }
        return result;
    }
    
    private TransactionAccessType getTransactionAccessType(final TransactionModeItemContext modeItemContext) {
        if (null != modeItemContext.ONLY()) {
            return TransactionAccessType.READ_ONLY;
        }
        if (null != modeItemContext.WRITE()) {
            return TransactionAccessType.READ_WRITE;
        }
        return null;
    }
    
    private TransactionIsolationLevel getTransactionIsolationLevel(final TransactionModeItemContext modeItemContext) {
        if (null == modeItemContext.isoLevel()) {
            return null;
        }
        if (null != modeItemContext.isoLevel().UNCOMMITTED()) {
            return TransactionIsolationLevel.READ_UNCOMMITTED;
        }
        if (null != modeItemContext.isoLevel().COMMITTED()) {
            return TransactionIsolationLevel.READ_COMMITTED;
        }
        if (null != modeItemContext.isoLevel().REPEATABLE()) {
            return TransactionIsolationLevel.REPEATABLE_READ;
        }
        if (null != modeItemContext.isoLevel().SERIALIZABLE()) {
            return TransactionIsolationLevel.SERIALIZABLE;
        }
        return null;
    }
    
    @Override
    public ASTNode visitBeginTransaction(final BeginTransactionContext ctx) {
        return new PostgreSQLBeginTransactionStatement();
    }
    
    @Override
    public ASTNode visitCommit(final CommitContext ctx) {
        return new PostgreSQLCommitStatement();
    }
    
    @Override
    public ASTNode visitRollback(final RollbackContext ctx) {
        return new PostgreSQLRollbackStatement();
    }
    
    @Override
    public ASTNode visitAbort(final AbortContext ctx) {
        return new PostgreSQLRollbackStatement();
    }
    
    @Override
    public ASTNode visitSavepoint(final SavepointContext ctx) {
        String savepointName = ctx.colId().getText();
        PostgreSQLSavepointStatement result = new PostgreSQLSavepointStatement();
        result.setSavepointName(savepointName);
        return result;
    }
    
    @Override
    public ASTNode visitRollbackToSavepoint(final RollbackToSavepointContext ctx) {
        PostgreSQLRollbackStatement result = new PostgreSQLRollbackStatement();
        result.setSavepointName(ctx.colId().getText());
        return result;
    }
    
    @Override
    public ASTNode visitReleaseSavepoint(final ReleaseSavepointContext ctx) {
        String savepointName = ctx.colId().getText();
        PostgreSQLReleaseSavepointStatement result = new PostgreSQLReleaseSavepointStatement();
        result.setSavepointName(savepointName);
        return result;
    }
    
    @Override
    public ASTNode visitStartTransaction(final StartTransactionContext ctx) {
        return new PostgreSQLStartTransactionStatement();
    }
    
    @Override
    public ASTNode visitEnd(final EndContext ctx) {
        return new PostgreSQLCommitStatement();
    }
    
    @Override
    public ASTNode visitSetConstraints(final SetConstraintsContext ctx) {
        return new PostgreSQLSetConstraintsStatement();
    }
    
    @Override
    public ASTNode visitCommitPrepared(final CommitPreparedContext ctx) {
        return new PostgreSQLCommitPreparedStatement();
    }
    
    @Override
    public ASTNode visitRollbackPrepared(final RollbackPreparedContext ctx) {
        return new PostgreSQLRollbackPreparedStatement();
    }
    
    @Override
    public ASTNode visitLock(final LockContext ctx) {
        PostgreSQLLockStatement result = new PostgreSQLLockStatement();
        if (null != ctx.relationExprList()) {
            result.getTables().addAll(getLockTables(ctx.relationExprList().relationExpr()));
        }
        return result;
    }
    
    private Collection<SimpleTableSegment> getLockTables(final List<RelationExprContext> relationExprContexts) {
        Collection<SimpleTableSegment> result = new LinkedList<>();
        for (RelationExprContext each : relationExprContexts) {
            SimpleTableSegment tableSegment = (SimpleTableSegment) visit(each.qualifiedName());
            result.add(tableSegment);
        }
        return result;
    }
    
    @Override
    public ASTNode visitPrepareTransaction(final PrepareTransactionContext ctx) {
        return new PostgreSQLPrepareTransactionStatement();
    }
}
