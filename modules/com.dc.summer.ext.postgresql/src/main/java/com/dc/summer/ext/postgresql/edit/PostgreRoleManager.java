
package com.dc.summer.ext.postgresql.edit;

import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.ext.postgresql.model.PostgreRole;
import com.dc.summer.ext.postgresql.model.PostgreServerExtension;
import com.dc.summer.ext.postgresql.model.impls.cockroach.PostgreServerCockroachDB;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectMaker;
import com.dc.summer.model.edit.DBEObjectRenamer;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * PostgreRoleManager
 */
public class PostgreRoleManager extends SQLObjectEditor<PostgreRole, PostgreDataSource> implements DBEObjectRenamer<PostgreRole> {

    @Override
    public long getMakerOptions(DBPDataSource dataSource)
    {
        return DBEObjectMaker.FEATURE_EDITOR_ON_CREATE;
    }

    @Nullable
    @Override
    public DBSObjectCache<? extends DBSObject, PostgreRole> getObjectsCache(PostgreRole object)
    {
        return null;
    }

    @Override
    protected PostgreRole createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container, Object copyFrom, Map<String, Object> options) throws DBException {
        return new PostgreRole(((PostgreDataSource) container).getDefaultInstance(), "NewRole", "", true);
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options) {
        final PostgreRole role = command.getObject();
        final StringBuilder script = new StringBuilder("CREATE ROLE " + DBUtils.getQuotedIdentifier(role));
        addRoleOptions(script, role, command, true);

        actions.add(
            new SQLDatabasePersistAction("Create role", script.toString()) //$NON-NLS-2$
        );
    }

    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actionList, ObjectChangeCommand command, Map<String, Object> options) {
        if (!command.hasProperty(DBConstants.PROP_ID_DESCRIPTION) || command.getProperties().size() > 1) {
            final PostgreRole role = command.getObject();
            final StringBuilder script = new StringBuilder("ALTER ROLE " + DBUtils.getQuotedIdentifier(role));
            addRoleOptions(script, role, command, false);

            actionList.add(
                    new SQLDatabasePersistAction("Alter role", script.toString()) //$NON-NLS-2$
            );
        }
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options) {
        actions.add(
            new SQLDatabasePersistAction("Drop role", "DROP ROLE " + DBUtils.getQuotedIdentifier(command.getObject())) //$NON-NLS-2$
        );
    }

    @Override
    protected void addObjectRenameActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectRenameCommand command, Map<String, Object> options) {
        final PostgreRole role = command.getObject();
        final DBPDataSource dataSource = role.getDataSource();

        actions.add(new SQLDatabasePersistAction(
            "Rename role",
            "ALTER ROLE " + DBUtils.getQuotedIdentifier(dataSource, command.getOldName()) + " RENAME TO " + DBUtils.getQuotedIdentifier(dataSource, command.getNewName())
        ));
    }

    @Override
    public void renameObject(@NotNull DBECommandContext commandContext, @NotNull PostgreRole object, @NotNull Map<String, Object> options, @NotNull String newName) throws DBException {
        processObjectRename(commandContext, object, options, newName);
    }

    private void addRoleOptions(StringBuilder script, PostgreRole role, NestedObjectCommand command, boolean create) {
        final PostgreServerExtension extension = role.getDataSource().getServerType();
        final StringBuilder options = new StringBuilder();
        if (extension.supportsSuperusers()) {
            if (role.isSuperUser()) options.append(" SUPERUSER"); else options.append(" NOSUPERUSER");
        }
        if (extension.supportsRolesWithCreateDBAbility()) {
            if (role.isCreateDatabase()) options.append(" CREATEDB"); else options.append(" NOCREATEDB");
        }
        if (role.isCreateRole()) options.append(" CREATEROLE"); else options.append(" NOCREATEROLE");
        if (extension.supportsInheritance()) {
            if (role.isInherit()) options.append(" INHERIT"); else options.append(" NOINHERIT");
        }
        if (role.isCanLogin()) options.append(" LOGIN"); else options.append(" NOLOGIN");

        if (extension.supportsRoleReplication()) {
            if (role.isReplication()) {
                options.append(" REPLICATION");
            } else {
                options.append(" NOREPLICATION");
            }
        }
        if (extension.supportsRoleBypassRLS()) {
            if (role.isBypassRls()) {
                options.append(" BYPASSRLS");
            } else {
                options.append(" NOBYPASSRLS");
            }
        }

        if (create && !CommonUtils.isEmpty(role.getPassword())) {
            // A password is only of use for roles having the LOGIN attribute, but you can nonetheless define one for roles without it
            options.append(" PASSWORD ").append("'").append(role.getDataSource().getSQLDialect().escapeString(role.getPassword())).append("'");
            command.setDisableSessionLogging(true); // Hide password from Query Manager
        }
        if (options.length() != 0 && extension instanceof PostgreServerCockroachDB) {
            // FIXME: use some generic approach
            script.append(" WITH");
        }
        script.append(options);
    }


    @Override
    protected void addObjectExtraActions(
            DBRProgressMonitor monitor,
            DBCExecutionContext executionContext,
            List<DBEPersistAction> actions,
            NestedObjectCommand<PostgreRole, PropertyHandler> command,
            Map<String, Object> options)
    {
        if (command.hasProperty(DBConstants.PROP_ID_DESCRIPTION)) {
            PostgreRole role = command.getObject();
            actions.add(new SQLDatabasePersistAction(
                    "Comment role",
                    "COMMENT ON ROLE " + DBUtils.getQuotedIdentifier(role.getDataSource(), role.getName()) +
                            " IS " + SQLUtils.quoteString(role, CommonUtils.notEmpty(role.getDescription()))));
        }
    }
}
