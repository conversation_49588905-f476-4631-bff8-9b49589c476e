
package com.dc.summer.ext.postgresql.model.data;

import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCTransactionManager;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDCursor;
import com.dc.summer.model.exec.DBCSession;

import java.sql.SQLException;

/**
 * Cursor references (named)
 */
public class PostgreRefCursor implements DBDCursor {

    private static final Log log = Log.getLog(PostgreRefCursor.class);

    private final JDBCSession session;
    private String cursorName;
    private boolean isOpen;
    private JDBCStatement cursorStatement;

    public PostgreRefCursor(JDBCSession session, @NotNull String cursorName) throws SQLException {
        this.session = session;
        this.cursorName = cursorName;
        this.isOpen = true;
    }

    @Override
    public Object getRawValue() {
        return cursorName;
    }

    @Override
    public boolean isNull() {
        return false;
    }

    @Override
    public boolean isModified() {
        return false;
    }

    @Override
    public void release() {
        if (this.isOpen) {
            try {
                JDBCUtils.executeStatement(session, "CLOSE \"" + cursorName + "\"");
            } catch (Exception e) {
                log.error(e);
            }
        }
        if (cursorStatement != null) {
            cursorStatement.close();
            cursorStatement = null;
        }
    }

    @Override
    public DBCResultSet openResultSet(DBCSession session) throws DBCException {
        try {
            DBCTransactionManager txnManager = DBUtils.getTransactionManager(session.getExecutionContext());
            if (txnManager != null && txnManager.isAutoCommit()) {
                throw new DBCException("Ref cursors are not available in auto-commit mode");
            }
            if (cursorStatement != null) {
                cursorStatement.close();
            }
            JDBCUtils.executeStatement(this.session, "MOVE ABSOLUTE 0 IN \"" + cursorName + "\"");
            cursorStatement = this.session.createStatement();
            return cursorStatement.executeQuery("FETCH ALL IN \"" + cursorName + "\"");
        } catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
    }

    @Nullable
    @Override
    public String getCursorName() {
        return cursorName;
    }

    @Override
    public String toString() {
        return cursorName;
    }

}
