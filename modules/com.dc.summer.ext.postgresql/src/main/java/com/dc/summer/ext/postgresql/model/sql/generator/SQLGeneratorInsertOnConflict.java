/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2023 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.postgresql.model.sql.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.generator.SQLGeneratorTable;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.utils.CommonUtils;

import java.util.Collection;

public class SQLGeneratorInsertOnConflict extends SQLGeneratorTable {

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBSEntity object) throws DBException {
        sql.append("INSERT INTO ").append(getEntityName(object)).append(getLineSeparator()).append("(");
        boolean hasAttr = false;
        for (DBSEntityAttribute attr : getAllAttributes(monitor, object)) {
            if (DBUtils.isPseudoAttribute(attr) || DBUtils.isHiddenObject(attr) || attr.isAutoGenerated()) {
                continue;
            }
            if (hasAttr) {
                sql.append(", ");
            }
            sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML));
            hasAttr = true;
        }
        sql.append(")").append(getLineSeparator());
        
        sql.append("SELECT ");
        hasAttr = false;
        for (DBSEntityAttribute attr : getAllAttributes(monitor, object)) {
            if (DBUtils.isPseudoAttribute(attr) || DBUtils.isHiddenObject(attr) || attr.isAutoGenerated()) {
                continue;
            }
            if (hasAttr) {
                sql.append(", ");
            }
            sql.append("src." + DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML));
            hasAttr = true;
        }
        sql.append(getLineSeparator()).append("FROM SOURCE_TABLE AS src").append(getLineSeparator());
        sql.append("ON CONFLICT ");
        // key fields
        hasAttr = false;
        Collection<? extends DBSEntityAttribute> keyAttributes = getKeyAttributes(monitor, object);
        if (!CommonUtils.isEmpty(keyAttributes)) {
            sql.append("(");
            for (DBSEntityAttribute attr : keyAttributes) {
                if (hasAttr) {
                    sql.append(", ");
                }
                sql.append(DBUtils.getQuotedIdentifier(attr));
                hasAttr = true;
            }
            sql.append(")").append(getLineSeparator());
        } else {
            sql.append("('/* insert on conflict attributes here, e.g. ID, ... */')").append(getLineSeparator());
        }
        sql.append("/* or you may use [DO NOTHING;] */").append(getLineSeparator());
        sql.append("DO UPDATE ").append(getLineSeparator());
        sql.append("SET ");
        hasAttr = false;
        for (DBSAttributeBase attr : getValueAttributes(monitor, object, keyAttributes)) {
            if (DBUtils.isPseudoAttribute(attr) || DBUtils.isHiddenObject(attr)) {
                continue;
            }
            if (hasAttr) {
                sql.append(", ");
            }
            sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML)).append("=");
            sql.append("EXCLUDED." + DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML));
            hasAttr = true;
        }        
        sql.append(";").append(getLineSeparator());
    }
}
