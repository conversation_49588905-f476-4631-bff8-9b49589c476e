

package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.DBPNamedObject;

/**
 * PostgreTriggerType
 */
public class PostgreTriggerType implements DBPNamedObject
{
    public static final PostgreTriggerType ROW = new PostgreTriggerType("ROW");
    public static final PostgreTriggerType STATEMENT = new PostgreTriggerType("STATEMENT");

    private final String name;

    protected PostgreTriggerType(String name)
    {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String toString()
    {
        return getName();
    }

}