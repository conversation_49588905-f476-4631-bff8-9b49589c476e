
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.ext.postgresql.model.PostgreMaterializedView;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

public class PostgreToolMViewRefresh extends PostgreToolWithStatus<PostgreMaterializedView, PostgreToolMViewRefreshSettings>  {
    @NotNull
    @Override
    public PostgreToolMViewRefreshSettings createToolSettings() {
        return new PostgreToolMViewRefreshSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, PostgreToolMViewRefreshSettings settings, List<DBEPersistAction> queries, PostgreMaterializedView object) throws DBCException {
        String sql = "REFRESH MATERIALIZED VIEW " + object.getFullyQualifiedName(DBPEvaluationContext.DDL) + " ";
        boolean isWithData = settings.isWithData();
        if (isWithData) {
            sql += "WITH DATA";
        }
        else {
            sql += "WITH NO DATA";
        }
        queries.add(new SQLDatabasePersistAction(sql));
    }
}
