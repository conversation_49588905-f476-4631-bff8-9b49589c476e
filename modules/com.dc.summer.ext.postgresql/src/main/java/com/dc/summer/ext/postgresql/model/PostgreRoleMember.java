
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSObject;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostgreRoleMember
 */
public class PostgreRoleMember implements DBSObject {

    private final PostgreRole owner;
    private long role;
    private long member;
    private long grantor;
    private boolean adminOption;

    public PostgreRoleMember(PostgreRole owner, ResultSet dbResult)
        throws SQLException
    {
        this.owner = owner;
        this.loadInfo(dbResult);
    }

    private void loadInfo(ResultSet dbResult)
        throws SQLException
    {
        this.role = JDBCUtils.safeGetLong(dbResult, "roleid");
        this.member = JDBCUtils.safeGetLong(dbResult, "member");
        this.grantor = JDBCUtils.safeGetLong(dbResult, "grantor");
        this.adminOption = JDBCUtils.safeGetBoolean(dbResult, "admin_option");
    }

    @Nullable
    @Override
    public String getDescription() {
        return null;
    }

    @Nullable
    @Override
    public DBSObject getParentObject() {
        return owner;
    }

    @NotNull
    @Override
    public PostgreDataSource getDataSource() {
        return owner.getDataSource();
    }

    @Override
    public boolean isPersisted() {
        return true;
    }

    @NotNull
    @Override
    public String getName()
    {
        return member + "." + role;
    }

    @Property(viewable = true, order = 1)
    public PostgreRole getOwner(DBRProgressMonitor monitor) throws DBException {
        return owner.getDatabase().getRoleById(monitor, role);
    }

    @Property(viewable = true, order = 2)
    public PostgreRole getMember(DBRProgressMonitor monitor) throws DBException {
        return owner.getDatabase().getRoleById(monitor, member);
    }

    @Property(viewable = true, order = 3)
    public PostgreRole getGrantor(DBRProgressMonitor monitor) throws DBException {
        return owner.getDatabase().getRoleById(monitor, grantor);
    }

    @Property(viewable = true, order = 4)
    public boolean isAdminOption() {
        return adminOption;
    }

    @Override
    public String toString() {
        return getName();
    }

}

