
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.ext.postgresql.model.PostgreMaterializedView;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.sql.task.SQLToolExecuteSettings;

import java.util.Map;

public class PostgreToolMViewRefreshSettings extends SQLToolExecuteSettings<PostgreMaterializedView> {
    private boolean isWithData;

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isWithData() {
        return isWithData;
    }

    public void setWithData(boolean withData) {
        isWithData = withData;
    }

    @Override
    public void loadConfiguration(DBRRunnableContext runnableContext, Map<String, Object> config) {
        super.loadConfiguration(runnableContext, config);
        isWithData = JSONUtils.getBoolean(config, "with_data");
    }

    @Override
    public void saveConfiguration(Map<String, Object> config) {
        super.saveConfiguration(config);
        config.put("with_data", isWithData);
    }
}
