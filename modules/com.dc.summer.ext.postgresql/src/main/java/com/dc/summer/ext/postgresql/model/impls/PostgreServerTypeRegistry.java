
package com.dc.summer.ext.postgresql.model.impls;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PostgreServerTypeRegistry {

    public static final String EXTENSION_ID = "com.dc.summer.postgresql.serverType"; //$NON-NLS-1$

    public synchronized static PostgreServerTypeRegistry getInstance() {
        if (instance == null) {
            instance = new PostgreServerTypeRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private static PostgreServerTypeRegistry instance = null;

    private final Map<String, PostgreServerType> serverTypes = new HashMap<>();

    private PostgreServerTypeRegistry(IExtensionRegistry registry) {
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement ext : extElements) {
            PostgreServerType type = new PostgreServerType(ext);
            serverTypes.put(type.getId(), type);
        }
    }

    public List<PostgreServerType> getServerTypes() {
        return new ArrayList<>(serverTypes.values());
    }

    public PostgreServerType getServerType(String id) {
        return serverTypes.get(id);
    }


}
