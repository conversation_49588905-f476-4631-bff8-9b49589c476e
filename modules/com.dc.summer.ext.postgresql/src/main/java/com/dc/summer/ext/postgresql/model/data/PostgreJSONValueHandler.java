
package com.dc.summer.ext.postgresql.model.data;

import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCContentValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.NotNull;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.exec.DBCSession;

import java.sql.SQLException;

/**
 * PostgreJSONValueHandler
 */
public class PostgreJSONValueHandler extends JDBCContentValueHandler {

    public static final PostgreJSONValueHandler INSTANCE = new PostgreJSONValueHandler();

    @Override
    protected DBDContent fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws SQLException {
        String json = resultSet.getString(index);
        return new PostgreContentJSON(session.getExecutionContext(), json);
    }

    @Override
    public DBDContent getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException
    {
        if (PostgreUtils.isPGObject(object)) {
            object = PostgreUtils.extractPGObjectValue(object);
        }
        if (object == null) {
            return new PostgreContentJSON(session.getExecutionContext(), null);
        } else if (object instanceof PostgreContentJSON) {
            return copy ? ((PostgreContentJSON) object).cloneValue(session.getProgressMonitor()) : (PostgreContentJSON) object;
        } else if (object instanceof String) {
            return new PostgreContentJSON(session.getExecutionContext(), (String) object);
        }
        return super.getValueFromObject(session, type, object, copy, validateValue);
    }
}
