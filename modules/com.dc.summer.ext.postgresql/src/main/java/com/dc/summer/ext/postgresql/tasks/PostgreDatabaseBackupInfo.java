
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.ext.postgresql.model.PostgreDatabase;
import com.dc.summer.ext.postgresql.model.PostgreSchema;
import com.dc.summer.ext.postgresql.model.PostgreTableBase;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.List;

/**
* PostgreDatabaseBackupInfo
*/
public class PostgreDatabaseBackupInfo extends PostgreDatabaseBackupRestoreInfo {
    @Nullable
    private List<PostgreSchema> schemas;
    @Nullable
    private List<PostgreTableBase> tables;

    public PostgreDatabaseBackupInfo(@NotNull PostgreDatabase database, @Nullable List<PostgreSchema> schemas, @Nullable List<PostgreTableBase> tables) {
        super(database);
        this.schemas = schemas;
        this.tables = tables;
    }

    @Nullable
    public List<PostgreSchema> getSchemas() {
        return schemas;
    }

    public void setSchemas(@Nullable List<PostgreSchema> schemas) {
        this.schemas = schemas;
    }

    @Nullable
    public List<PostgreTableBase> getTables() {
        return tables;
    }

    public void setTables(@Nullable List<PostgreTableBase> tables) {
        this.tables = tables;
    }
}
