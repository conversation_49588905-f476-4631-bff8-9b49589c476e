
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPScriptObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectState;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.util.Map;

public class PostgreEventTrigger extends PostgreTriggerBase {

    public enum TriggerEventTypes {
        ddl_command_start,
        ddl_command_end,
        table_rewrite,
        sql_drop
    }

    private final PostgreDatabase database;
    private long objectId;
    private long routineId;
    private TriggerEventTypes eventType;
    private String enabledState;
    private String description;
    private String body;

    PostgreEventTrigger(@NotNull PostgreDatabase database, @NotNull String name, @NotNull JDBCResultSet dbResult) {
        super(database, name, true);
        this.database = database;
        this.objectId = JDBCUtils.safeGetLong(dbResult, "oid");
        this.routineId = JDBCUtils.safeGetLong(dbResult, "evtfoid");
        String eventTypeName = JDBCUtils.safeGetString(dbResult, "evtevent");
        this.eventType = CommonUtils.valueOf(TriggerEventTypes.class, eventTypeName);
        this.enabledState = JDBCUtils.safeGetString(dbResult, "evtenabled");
        this.description = JDBCUtils.safeGetString(dbResult, "description");
    }

    public PostgreEventTrigger(@NotNull PostgreDatabase database, String name) {
        super(database, name, false);
        this.database = database;
    }

    @Override
    @Property(viewable = true, order = 2)
    public long getObjectId() {
        return objectId;
    }

    @Property(viewable = true, order = 3)
    public TriggerEventTypes getEventType() {
        return eventType;
    }

    public void setEventType(TriggerEventTypes eventType) {
        this.eventType = eventType;
    }

    @Override
    @Property(viewable = true, order = 4)
    public PostgreProcedure getFunction(DBRProgressMonitor monitor) throws DBException {
        if (routineId == 0) {
            return null;
        }
        return getDatabase().getProcedure(monitor, routineId);
    }

    public void setFunction(PostgreProcedure function) {
        this.routineId = function != null ? function.getObjectId() : 0;
    }

    @Override
    @Property(viewable = true, order = 5)
    public String getEnabledState() {
        return enabledState;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getQuotedIdentifier(this);
    }

    @Override
    @Property(hidden = true, editable = true, updatable = true, order = -1)
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException {
        StringBuilder ddl = new StringBuilder();
        PostgreProcedure function = getFunction(monitor);
        if (function == null) {
            return "-- Event trigger definition is not available - can't read trigger function";
        }
        if (CommonUtils.isEmpty(body)) {
            body = "CREATE EVENT TRIGGER " + getFullyQualifiedName(DBPEvaluationContext.DDL) + " ON " +
                eventType + "\n\tEXECUTE " + function.getProcedureTypeName() + " " + function.getFullQualifiedSignature();
        }
        ddl.append(body); // Body is the main part of the trigger DDL. It doesn't include comments

        if (!CommonUtils.isEmpty(getDescription()) && CommonUtils.getOption(options, DBPScriptObject.OPTION_INCLUDE_COMMENTS)) {
            ddl.append(";\n\nCOMMENT ON EVENT TRIGGER ").append(DBUtils.getQuotedIdentifier(this))
                .append(" IS ")
                .append(SQLUtils.quoteString(this, getDescription())).append(";");
        }

        return ddl.toString();
    }

    @Override
    public void setObjectDefinitionText(String sourceText) {
        this.body = sourceText;
    }

    @Override
    public String getBody() {
        return body;
    }

    @Nullable
    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        return getDatabase().getEventTriggersCache().refreshObject(monitor, getDatabase(), this);
    }

    @NotNull
    @Override
    public DBSObjectState getObjectState() {
        if ("D".equals(enabledState)) {
            return DBSObjectState.INVALID;
        }
        return DBSObjectState.NORMAL;
    }

    @Override
    public void refreshObjectState(@NotNull DBRProgressMonitor monitor) throws DBCException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Refresh triggers state")) {
            try {
                enabledState = JDBCUtils.queryString(session,
                        "SELECT evtenabled FROM @_catalog.@_event_trigger WHERE oid=?".replace("@", getDataSource().getInstancePrefix()),
                        getObjectId());
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }
    }

    @NotNull
    @Override
    public DBSObject getParentObject() {
        return database;
    }

    @Override
    public DBSTable getTable() {
        // Event triggers belong to databases
        return null;
    }

    @Nullable
    @Override
    @Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 100)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
