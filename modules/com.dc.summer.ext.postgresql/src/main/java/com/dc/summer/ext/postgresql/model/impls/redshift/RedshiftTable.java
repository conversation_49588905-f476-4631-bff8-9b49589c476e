
package com.dc.summer.ext.postgresql.model.impls.redshift;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.postgresql.model.PostgreSchema;
import com.dc.summer.ext.postgresql.model.PostgreTableRegular;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSObject;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * RedshiftTable base
 */
public class RedshiftTable extends PostgreTableRegular
{
    private static final Log log = Log.getLog(RedshiftTable.class);

    @Override
    public boolean isRefreshSchemaStatisticsOnTableRefresh() {
        return false;
    }

    public RedshiftTable(PostgreSchema catalog) {
        super(catalog);
    }

    public RedshiftTable(PostgreSchema catalog, ResultSet dbResult) {
        super(catalog, dbResult);
    }

    protected void readTableStatistics(JDBCSession session) throws SQLException {
        try (JDBCPreparedStatement dbStat = session.prepareStatement(
            "SELECT size, tbl_rows FROM SVV_TABLE_INFO WHERE \"schema\"=? AND table_id=?"))
        {
            dbStat.setString(1, getSchema().getName());
            dbStat.setLong(2, getObjectId());
            try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                if (dbResult.next()) {
                    fetchStatistics(dbResult);
                }
            }
        }
    }

    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        if (hasStatistics()) {
            diskSpace = null;
            try (DBCSession session = DBUtils.openMetaSession(monitor, this, "Calculate relation size on disk")) {
                readTableStatistics((JDBCSession) session);
            } catch (Exception e) {
                log.debug("Can't fetch disk space", e);
            }
        }
        return super.refreshObject(monitor);
    }

    protected void fetchStatistics(JDBCResultSet dbResult) throws SQLException {
        diskSpace = dbResult.getLong("size") * 1024 * 1024;
        rowCountEstimate = rowCount = dbResult.getLong("tbl_rows");
    }

}
