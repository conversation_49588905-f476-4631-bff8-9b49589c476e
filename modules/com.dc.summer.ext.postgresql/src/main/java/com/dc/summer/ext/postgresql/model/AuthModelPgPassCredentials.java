
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;

public class AuthModelPgPassCredentials extends AuthModelDatabaseNativeCredentials {

    private transient Exception parseError;

    // Override to remove property mark
    @Override
    public String getUserPassword() {
        return super.getUserPassword();
    }

    public void setParseError(Exception parseError) {
        this.parseError = parseError;
    }

    public Exception getParseError() {
        return parseError;
    }

}
