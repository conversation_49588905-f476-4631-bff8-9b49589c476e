
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.DBException;
import com.dc.summer.model.*;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSTrigger;
import com.dc.code.NotNull;

public abstract class PostgreTriggerBase implements DBSTrigger, DBPQualifiedObject, PostgreObject, PostgreScriptObject, DBPStatefulObject, DBPScriptObjectExt2, DBPNamedObject2, DBPRefreshableObject, DBPSaveableObject {

    private final PostgreDatabase database;
    protected String name;
    private boolean persisted;

    PostgreTriggerBase(@NotNull PostgreDatabase database, String name, boolean persisted) {
        this.database = database;
        this.name = name;
        this.persisted = persisted;
    }

    @NotNull
    @Override
    public PostgreDataSource getDataSource() {
        return database.getDataSource();
    }

    @NotNull
    @Override
    public PostgreDatabase getDatabase() {
        return database;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean isPersisted() {
        return persisted;
    }

    @Override
    public void setPersisted(boolean persisted) {
        this.persisted = persisted;
    }

    public abstract String getEnabledState();

    public abstract String getBody();

    public abstract PostgreProcedure getFunction(DBRProgressMonitor monitor) throws DBException;

    @Override
    public boolean supportsObjectDefinitionOption(String option) {
        return DBPScriptObject.OPTION_INCLUDE_COMMENTS.equals(option);
    }
}
