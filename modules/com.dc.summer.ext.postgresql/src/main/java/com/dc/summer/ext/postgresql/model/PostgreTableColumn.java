
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.ext.postgresql.model.data.type.PostgreGeometryTypeHandler;
import com.dc.summer.model.impl.jdbc.struct.JDBCColumnKeyType;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.gis.DBGeometryDimension;
import com.dc.summer.model.gis.GisAttribute;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBStructUtils;
import com.dc.utils.CommonUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * PostgreTableColumn
 */
public class PostgreTableColumn extends PostgreAttribute<PostgreTableBase> implements PostgrePrivilegeOwner, PostgreScriptObject, GisAttribute, JDBCColumnKeyType {
    private static final Log log = Log.getLog(PostgreTableColumn.class);

    public PostgreTableColumn(DBRProgressMonitor monitor, PostgreTableBase table, PostgreTableColumn source) throws DBException {
        super(monitor, table, source);
    }

    public PostgreTableColumn(PostgreTableBase table) {
        super(table);
    }

    public PostgreTableColumn(DBRProgressMonitor monitor, PostgreTableBase table, JDBCResultSet dbResult) throws DBException {
        super(monitor, table, dbResult);
    }

    @NotNull
    @Override
    public PostgreSchema getSchema() {
        return getTable().getSchema();
    }

    @Override
    public PostgreRole getOwner(DBRProgressMonitor monitor) throws DBException {
        return getTable().getOwner(monitor);
    }

    @Override
    public Collection<PostgrePrivilege> getPrivileges(DBRProgressMonitor monitor, boolean includeNestedObjects) throws DBException {
        return PostgreUtils.extractPermissionsFromACL(monitor, this, getAcl());
    }

    @Override
    public String generateChangeOwnerQuery(String owner) {
        return null;
    }

    @Override
    public int getAttributeGeometrySRID(DBRProgressMonitor monitor) throws DBCException {
        return PostgreGeometryTypeHandler.getGeometrySRID(getTypeMod());
    }

    @NotNull
    @Override
    public DBGeometryDimension getAttributeGeometryDimension(DBRProgressMonitor monitor) throws DBCException {
        return PostgreGeometryTypeHandler.getGeometryDimension(getTypeMod());
    }

    @Nullable
    @Override
    public String getAttributeGeometryType(DBRProgressMonitor monitor) throws DBCException {
        return PostgreGeometryTypeHandler.getGeometryType(getTypeMod());
    }

    @Override
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException {
        return DBStructUtils.generateObjectDDL(monitor, this, options, false);
    }

    @Override
    public void setObjectDefinitionText(String sourceText) throws DBException {

    }

    @Nullable
    @Override
    protected JDBCColumnKeyType getKeyType() {
        return this;
    }

    @Override
    public boolean isInUniqueKey() {
        final List<PostgreTableConstraintBase> cCache = getTable().getSchema().getConstraintCache().getCachedObjects(getTable());
        if (!CommonUtils.isEmpty(cCache)) {
            for (PostgreTableConstraintBase key : cCache) {
                if (key instanceof PostgreTableConstraint && key.getConstraintType() == DBSEntityConstraintType.PRIMARY_KEY) {
                    List<PostgreTableConstraintColumn> cColumns = ((PostgreTableConstraint) key).getColumns();
                    if (!CommonUtils.isEmpty(cColumns)) {
                        for (PostgreTableConstraintColumn cCol : cColumns) {
                            if (cCol.getAttribute() == this) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean isInReferenceKey() {
        return false;
    }

}
