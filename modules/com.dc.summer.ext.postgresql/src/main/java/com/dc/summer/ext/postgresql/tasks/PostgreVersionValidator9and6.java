
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.model.sql.task.SQLToolTaskVersionValidator;
import com.dc.summer.model.struct.DBSObject;

public class PostgreVersionValidator9and6 extends SQLToolTaskVersionValidator<PostgreToolBaseVacuumSettings, DBSObject> {
    @Override
    public int getMajorVersion() {
        return 9;
    }

    @Override
    public int getMinorVersion() {
        return 6;
    }
}
