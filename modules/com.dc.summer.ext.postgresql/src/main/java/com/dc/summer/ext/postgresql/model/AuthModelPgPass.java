
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNative;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;

import java.io.*;
import java.util.Properties;

public class AuthModelPgPass extends AuthModelDatabaseNative<AuthModelPgPassCredentials> {
    private static final Log log = Log.getLog(AuthModelPgPass.class);

    public static final String PGPASSFILE_ENV_VARIABLE = "PGPASSFILE";

    @NotNull
    @Override
    public AuthModelPgPassCredentials createCredentials() {
        return new AuthModelPgPassCredentials();
    }

    @NotNull
    @Override
    public AuthModelPgPassCredentials loadCredentials(@NotNull DBPDataSourceContainer dataSource, @NotNull DBPConnectionConfiguration configuration) {
        AuthModelPgPassCredentials credentials = super.loadCredentials(dataSource, configuration);
        try {
            loadPasswordFromPgPass(credentials, dataSource, configuration);
            credentials.setParseError(null);
        } catch (DBException e) {
            credentials.setParseError(e);
        }
        return credentials;
    }

    @Override
    public Object initAuthentication(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSource dataSource, AuthModelPgPassCredentials credentials, DBPConnectionConfiguration configuration, @NotNull Properties connectProps) throws DBException {
        if (credentials.getParseError() != null) {
            throw new DBCException("Couldn't get password from PGPASS file", credentials.getParseError());
        }
        return super.initAuthentication(monitor, dataSource, credentials, configuration, connectProps);
    }

    private void loadPasswordFromPgPass(AuthModelPgPassCredentials credentials, DBPDataSourceContainer dataSource, DBPConnectionConfiguration configuration) throws DBException {
        // Take database name from original config. Because it may change when user switch between databases.
        DBPConnectionConfiguration originalConfiguration = dataSource.getConnectionConfiguration();
        String conHostName = configuration.getHostName();
        String conHostPort = originalConfiguration.getHostPort();
        String conDatabaseName = originalConfiguration.getDatabaseName();
        String conUserName = originalConfiguration.getUserName();

        if (CommonUtils.isEmpty(conHostPort)) {
            conHostPort = dataSource.getDriver().getDefaultPort();
        }

        String pgPassPath = System.getenv(PGPASSFILE_ENV_VARIABLE);
        if (CommonUtils.isEmpty(pgPassPath)) {
            if (RuntimeUtils.isWindows()) {
                String appData = System.getenv("AppData");
                if (appData == null) {
                    appData = System.getProperty("user.home");
                }
                pgPassPath = appData + "/postgresql/pgpass.conf";
            } else {
                pgPassPath = System.getProperty("user.home") + "/.pgpass";
            }
        }
        File pgPassFile = new File(pgPassPath);
        if (!pgPassFile.exists()) {
            throw new DBException("PgPass file '" + pgPassFile.getAbsolutePath() + "' not found");
        }

        try (Reader r = new InputStreamReader(new FileInputStream(pgPassFile), GeneralUtils.UTF8_CHARSET)) {
            String passString = IOUtils.readToString(r);
            String[] lines = passString.split("\n");
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                // Escape colons
                String[] params = splitPassLine(line);
                if (params == null) {
                    continue;
                }
                String host = params[0];
                String port = params[1];
                String database = params[2];
                String user = params[3];
                String password = params[4];

                if (matchParam(conHostName, host) &&
                    matchParam(conHostPort, port) &&
                    matchParam(conDatabaseName, database)) {
                    if (CommonUtils.isEmpty(conUserName)) {
                        // No user name specified. Get the first matched params
                        //configuration.setUserName(user);
                        //configuration.setUserPassword(password);
                        credentials.setUserName(user);
                        credentials.setUserPassword(password);
                        return;
                    } else if (matchParam(conUserName, user)) {
                        if (!user.equals("*")) {
                            configuration.setUserName(user);
                        }
                        credentials.setUserPassword(password);
                        return;
                    }
                }
            }
        } catch (IOException e) {
            throw new DBException("Error reading pgpass", e);
        }

        throw new DBException("No matches in pgpass");
    }

    @Nullable
    private static String[] splitPassLine(String line) {
        line = line.replace("\\\\", "@BSESC@").replace("\\:", "@CESC@");
        String[] params = line.split(":");
        if (params.length < 5) {
            return null;
        }
        // Unescape colons
        for (int i = 0; i < params.length; i++) {
            params[i] = params[i].replace("@CESC@", ":").replace("@BSESC@", "\\");
        }
        return params;
    }

    private static boolean matchParam(String cfgParam, String passParam) {
        return passParam.equals("*") || passParam.equalsIgnoreCase(cfgParam);
    }
}
