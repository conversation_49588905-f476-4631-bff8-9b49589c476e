package com.dc.summer.ext.elasticsearch.exec;

import com.dc.summer.model.document.exec.DocumentResultSet;
import com.dc.summer.model.qm.QMUtils;


public abstract class ESBaseResultSet extends DocumentResultSet<ESSession, ESBaseStatement> {

    protected Object result;

    protected ESBaseResultSet(ESBaseStatement statement, Object result) {
        super(statement);
        this.result = result;
        if (statement.getSession().isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleResultSetOpen(this);
        }
    }

    @Override
    public void close() {
        if ((this.statement).getSession().isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleResultSetClose(this, itemNumber);
        }
    }
}
