package com.dc.summer.ext.elasticsearch.exec;


import com.dc.summer.DBException;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.ConstantUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public abstract class ESBaseStatement implements DBCStatement {

    protected String method;
    protected String endpoint;
    protected String body;

    protected final ESSession session;

    protected String query;

    protected DBCExecutionSource source;

    protected long updateRowCount = -1L;

    protected long offset;
    protected long limit;
    private int fetchSize = 1000;

    protected String result;

    protected ESBaseStatement(ESSession session, String query) {
        this.session = session;
        this.query = query;

        String[] line = query
                .replaceFirst("\\R", ConstantUtils.DC_PLACE_HOLDER)
                .split(ConstantUtils.DC_PLACE_HOLDER);

        this.method = StringUtils.substringBefore(line[0], " ");
        this.endpoint = StringUtils.substringAfter(line[0], " ").trim();
        this.body = line.length > 1 ? line[1] : null;

    }

    @Override
    public ESSession getSession() {
        return this.session;
    }

    @Override
    public String getQueryString() {
        return this.query;
    }

    @Override
    public DBCExecutionSource getStatementSource() {
        return this.source;
    }

    @Override
    public void setStatementSource(DBCExecutionSource source) {
        this.source = source;
    }

    @Override
    public void addToBatch() throws DBCException {
        throw new DBCException("Not implemented");
    }

    @Override
    public int[] executeStatementBatch() throws DBCException {
        throw new DBCException("Not implemented");
    }

    @Override
    public DBCResultSet openGeneratedKeysResultSet() throws DBCException {
        throw new DBCException("Not implemented");
    }

    @Override
    public long getUpdateRowCount() {
        return updateRowCount;
    }

    @Override
    public boolean nextResults() throws DBCException {
        this.updateRowCount = -1L;
        this.result = null;
        return false;
    }

    @Override
    public void setLimit(long offset, long limit) throws DBCException {
        this.offset = offset;
        this.limit = limit;
    }

    @Override
    public Throwable[] getStatementWarnings() throws DBCException {
        throw new DBCException("Not implemented");
    }

    @Override
    public List<String> getStatementWarningList() throws DBCException {
        throw new DBCException("Not implemented");
    }

    @Override
    public void setStatementTimeout(int timeout) throws DBCException {
    }

    @Override
    public void setResultsFetchSize(int fetchSize) throws DBCException {
        this.fetchSize = Math.min(fetchSize, 10000);
    }

    public long getOffset() {
        return this.offset;
    }

    public long getLimit() {
        return this.limit;
    }

    public int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    public void close() {
        this.result = null;
        if ((this.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementClose(this, this.getUpdateRowCount());
        }
    }

    @Override
    public void cancelBlock(DBRProgressMonitor monitor, Thread blockThread) throws DBException {
        if (blockThread != null) {
            blockThread.interrupt();
        } else {
            throw new DBException("Elasticsearch query cancel not implemented");
        }
    }

}
