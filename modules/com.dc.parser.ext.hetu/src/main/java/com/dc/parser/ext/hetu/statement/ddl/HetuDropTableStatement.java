
package com.dc.parser.ext.hetu.statement.ddl;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.statement.ddl.DropTableStatement;
import com.dc.parser.ext.hetu.statement.HetuStatement;

/**
 * Hetu drop table statement.
 */
@RequiredArgsConstructor
@Getter
public final class HetuDropTableStatement extends DropTableStatement implements HetuStatement {
    
    private final boolean ifExists;
}
