package com.dc.summer.ext.dm.data;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class DmObjectValue<PERSON>and<PERSON> extends JDBCObjectValueHandler {

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        Object object = resultSet.getObject(index);
        if (object == null) {
            return null;
        }
        return object.toString();
    }
}
