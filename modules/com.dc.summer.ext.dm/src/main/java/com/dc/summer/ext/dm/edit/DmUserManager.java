package com.dc.summer.ext.dm.edit;

import java.util.List;
import java.util.Map;

import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.dm.model.DmDataSource;
import com.dc.summer.ext.dm.model.DmUser;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectRenamer;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;

public class DmUserManager extends SQLObjectEditor<DmUser, DmDataSource> implements DBEObjectRenamer<DmUser> {

	@Override
	public long getMakerOptions(DBPDataSource dataSource) {
		return 0;
	}

	@Override
	public DBSObjectCache<? extends DBSObject, DmUser> getObjectsCache(DmUser object) {
		return null;
	}


	@Override
	protected DmUser createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container,
			Object copyFrom, Map<String, Object> options) throws DBException {
		return null;
	}

	@Override
	protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext,
			List<DBEPersistAction> actions, SQLObjectEditor<DmUser, DmDataSource>.ObjectCreateCommand command,
			Map<String, Object> options) throws DBException {		
	}

	@Override
	protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext,
			List<DBEPersistAction> actions, SQLObjectEditor<DmUser, DmDataSource>.ObjectDeleteCommand command,
			Map<String, Object> options) {
	}

	@Override
	public void renameObject(DBECommandContext commandContext, DmUser object, Map<String, Object> options,
			String newName) throws DBException {
	}

}
