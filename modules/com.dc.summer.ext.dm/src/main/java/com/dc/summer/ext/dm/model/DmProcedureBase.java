package com.dc.summer.ext.dm.model;

import com.dc.summer.Log;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.summer.model.struct.rdb.DBSProcedure;
import com.dc.summer.model.struct.rdb.DBSProcedureType;

public abstract class DmProcedureBase<PARENT extends DBSObjectContainer> extends DmObject<PARENT>
		implements DBSProcedure {

	private static final Log log = Log.getLog(DmProcedureBase.class);

	private DBSProcedureType procedureType;

	public DmProcedureBase(PARENT parent, String name, long objectId, DBSProcedureType procedureType) {
		super(parent, name, objectId, true);
		this.procedureType = procedureType;
	}

	@Override
	@Property(viewable = true, editable = true, order = 3)
	public DBSProcedureType getProcedureType() {
		return procedureType;
	}

	public void setProcedureType(DBSProcedureType procedureType) {
		this.procedureType = procedureType;
	}

	@Override
	public DBSObjectContainer getContainer() {
		return getParentObject();
	}

	public abstract DmSchema getSchema();

	public abstract Integer getOverloadNumber();
}
