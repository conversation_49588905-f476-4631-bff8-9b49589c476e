
package com.dc.summer.ext.dm.model;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.ext.dm.model.utils.StringUtil;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.utils.bean.ReflectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DmDataSourceInfo
 */
@Slf4j
class DmDataSourceInfo extends JDBCDataSourceInfo {

    private final DmDataSource dataSource;
    private boolean isDockerVersion = false;

    public DmDataSourceInfo(DmDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(metaData);
        this.dataSource = dataSource;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes() {
        return DmObjectType.values();
    }

    @Override
    public boolean needsTableMetaForColumnResolution() {
        return false;
    }

    @Override
    public boolean supportsLocalTransaction() {
        this.dataSource.getContainer().setLocalTransactional((dbrProgressMonitor, dbcExecutionContext) -> {
            AtomicBoolean hasTransactional = new AtomicBoolean(false);
            try {
                DBExecUtils.executeQuery(
                        dbrProgressMonitor,
                        dbcExecutionContext,
                        "runLocalTransaction",
                        "select dbms_transaction.LOCAL_TRANSACTION_ID from dual",
                        dbcResultSet -> {
                            if (dbcResultSet.nextRow()) {
                                if (dbcResultSet.getAttributeValue("LOCAL_TRANSACTION_ID") != null) {
                                    hasTransactional.set(true);
                                }
                            }
                        });
            } catch (DBException e) {
                log.error(e.getMessage(), e);
            }
            return hasTransactional.get();
        });
        return true;
    }

    @Override
    public String getFormatColumnName(String column) {
        return String.format("\"%s\"", column);
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {
        return String.format("SELECT \n" +
                "        CON.TYPE$,\n" +
                "        COLS.NAME as \"COLUMN_NAME\"\n" +
                "FROM \n" +
                "        SYSCONS AS CON,\n" +
                "        SYSOBJECTS AS OBJ,\n" +
                "        SYSOBJECTS AS TAB,\n" +
                "        SYSOBJECTS AS SCH,\n" +
                "        SYSINDEXES AS INDS,\n" +
                "        SYSCOLUMNS AS COLS\n" +
                "WHERE \n" +
                "SCH.TYPE$='SCH'\n" +
                "AND TAB.subtype$ in ('UTAB','STAB')\n" +
                "AND OBJ.SUBTYPE$='CONS'\n" +
                "AND CON.TYPE$ = 'P'\n" +
                "AND OBJ.ID=CON.ID \n" +
                "AND TAB.id = CON.TABLEID \n" +
                "AND tab.schid = SCH.id\n" +
                "AND CON.INDEXID =INDS.ID\n" +
                "AND TAB.ID = COLS.ID \n" +
                "AND SF_COL_IS_IDX_KEY(INDS.KEYNUM, INDS.KEYINFO, COLS.COLID)=1\n" +
                "and TAB.NAME = '%s' and sch.name = '%s'", tableName, schemaName);
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("COLUMN_NAME") != null) {
                columns.add(map.get("COLUMN_NAME").toString());
            } else if (map.get("column_name") != null) {
                columns.add(map.get("column_name").toString());
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        String query = "select NAME from(\n" +
                "                  select tab_obj_out.name,tab_obj_out.schname from (\n" +
                "                    select  /*+OPTIMIZER_OR_NBEXP(2)*/ TAB_OBJ.NAME, TAB_OBJ.ID, TAB_OBJ.SUBTYPE$, TAB_OBJ.INFO3, TAB_OBJ.SCHID, SCH_OBJ.NAME SCHNAME, \n" +
                "                      TAB_OBJ.CRTDATE, INFO8, TAB_OBJ.INFO2*(PAGE/1024)/1024, TAB_OBJ.INFO1 \n" +
                "                    from (\n" +
                "                        select TAB_OBJ_INNER.NAME, TAB_OBJ_INNER.ID, TAB_OBJ_INNER.SUBTYPE$, TAB_OBJ_INNER.INFO1, TAB_OBJ_INNER.INFO2, TAB_OBJ_INNER.INFO3, \n" +
                "                        TAB_OBJ_INNER.INFO8, TAB_OBJ_INNER.SCHID, TAB_OBJ_INNER.CRTDATE from SYS.SYSOBJECTS TAB_OBJ_INNER , SYS.SYSOBJECTS SCH_OBJ_INNER, SYS.SYSOBJECTS USER_OBJ_INNER \n" +
                "                        where \n" +
                "                            TAB_OBJ_INNER.type$ = 'SCHOBJ'  \n" +
                "                            and TAB_OBJ_INNER.INFO3&0x100000!=0x100000 \n" +
                "                            and TAB_OBJ_INNER.INFO3&0x200000!=0x200000 \n" +
                "                            and TAB_OBJ_INNER.INFO3 & 0x003F not in (0x0A, 0x20) \n" +
                "                            and (TAB_OBJ_INNER.INFO3 & 0x100000000) = 0  \n" +
                "                            and TAB_OBJ_INNER.NAME not like 'CTI$%$_' \n" +
                "                            and TAB_OBJ_INNER.NAME not like '%$AUX' \n" +
                "                            and TAB_OBJ_INNER.NAME not like '%$_AUX' \n" +
                "                            and TAB_OBJ_INNER.NAME not like '%$ALOG' \n" +
                "                            and (TAB_OBJ_INNER.PID=-1 or TAB_OBJ_INNER.PID=0) \n" +
                "                            and TAB_OBJ_INNER.INFO3 & 0x003F != 13 \n" +
                "                            and USER_OBJ_INNER.SUBTYPE$ = 'USER' \n" +
                "                            and SCH_OBJ_INNER.ID = TAB_OBJ_INNER.SCHID \n" +
                "                            and SCH_OBJ_INNER.PID = USER_OBJ_INNER.ID \n" +
                "                            and SF_CHECK_PRIV_OPT(UID(), CURRENT_USERTYPE(), TAB_OBJ_INNER.ID, USER_OBJ_INNER.ID, USER_OBJ_INNER.INFO1, TAB_OBJ_INNER.ID) = 1\n" +
                "                       ) TAB_OBJ, (select ID, NAME from SYS.SYSOBJECTS where TYPE$='SCH') SCH_OBJ \n" +
                "                    where TAB_OBJ.SCHID=SCH_OBJ.ID ) TAB_OBJ_OUT\n";

        if (!isDockerVersion) {
            query = query + "  union all\n" +
                    "  select dynamic_tables.name,dynamic_tables.schname from v$dynamic_tables dynamic_tables, \n" +
                    "    (select id, name from sys.sysobjects where type$='SCH') sch_obj \n" +
                    "where dynamic_tables.schname=sch_obj.name\n";
        }

        query = query + " ) where upper(name) = upper('" + tableName + "') and upper(schname) = upper('" + schemaName + "')";

        return query;
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("NAME") != null) {
                realName = map.get("NAME").toString();
                break;
            } else if (map.get("name") != null) {
                realName = map.get("name").toString();
                break;
            }
        }

        return realName;
    }

    @Override
    public void setDatabaseVersion(boolean isDockerVersion) {
        this.isDockerVersion = isDockerVersion;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "\",\"");
        String values = StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO \"%s\".\"%s\" (%s) VALUES (%s)", schemaName, tableName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES (%s)", tableName, "\"" + columns + "\"", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("\",\""));

        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                        data.add(String.format("'%s'", varcharData));
                    } else {
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        if (null != schemaName) {
            return String.format("INSERT INTO %s.\"%s\" (%s) VALUES %s", schemaName, tableName, "\"" + fields + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES %s", tableName, "\"" + fields + "\"", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE \"%s\".\"%s\"", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE \"%s\"", tableName);
        }
    }

    /**
     * 由于达梦数据库 有私有同义词和公有同义词，所以联查增加条件 DSYNOM,SCHID = 0
     * @param schemaName schema name
     * @param synonym    synonym
     * @return sql
     */
    @Override
    public String getSynonymSql(String schemaName, String synonym) {
        @SQL
        String sql = "SELECT  * from\n" +
                "(\n" +
                "    SELECT \n" +
                "        SYNOM_OBJ_INNER.SCHID, \n" +
                "        SF_DECODE_SYNONYM_OBJECT_NAME ( SYNOM_OBJ_INNER.INFO6, 1 ) AS \"source_schema\",\n" +
                "        SF_DECODE_SYNONYM_OBJECT_NAME ( SYNOM_OBJ_INNER.INFO6, 2 ) AS \"source_table\" \n" +
                "    FROM SYS.SYSOBJECTS SYNOM_OBJ_INNER,SYS.SYSOBJECTS SCH_OBJ_INNER\n" +
                "    WHERE SYNOM_OBJ_INNER.subtype$ = 'SYNOM' and SYNOM_OBJ_INNER.NAME = UPPER('" + synonym + "') and  SCH_OBJ_INNER.NAME = '" + schemaName + "' and  SCH_OBJ_INNER.ID = SYNOM_OBJ_INNER.SCHID \n" +
                "    union all\n" +
                "    SELECT  \n" +
                "        SYNOM_OBJ_INNER.SCHID, \n" +
                "        SF_DECODE_SYNONYM_OBJECT_NAME ( SYNOM_OBJ_INNER.INFO6, 1 ) AS \"source_schema\",\n" +
                "        SF_DECODE_SYNONYM_OBJECT_NAME ( SYNOM_OBJ_INNER.INFO6, 2 ) AS \"source_table\" \n" +
                "    FROM SYS.SYSOBJECTS SYNOM_OBJ_INNER\n" +
                "    WHERE SYNOM_OBJ_INNER.type$ = 'DSYNOM' and SYNOM_OBJ_INNER.NAME = UPPER('" + synonym + "') and  SYNOM_OBJ_INNER.SCHID = 0\n" +
                ") order by SCHID desc limit 1";
        return sql;
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "SELECT SCH_OBJ.NAME as OWNER from (select NAME, ID, PID, CRTDATE, BINTOCHAR(INFO8) DESCR from SYS.SYSOBJECTS where TYPE$='SCH') SCH_OBJ, \n" +
                        " (select NAME, ID from SYS.SYSOBJECTS where TYPE$='UR' and SUBTYPE$='USER') USER_OBJ where SCH_OBJ.PID=USER_OBJ.ID ");

        //获取Schema中table的个数
        String countQuery = "select count(1) as COUNT,schname as OWNER from(\n" +
                "  select tab_obj_out.schname from (\n" +
                "    select  /*+OPTIMIZER_OR_NBEXP(2)*/ TAB_OBJ.NAME, TAB_OBJ.ID, TAB_OBJ.SUBTYPE$, TAB_OBJ.INFO3, TAB_OBJ.SCHID, SCH_OBJ.NAME SCHNAME, \n" +
                "      TAB_OBJ.CRTDATE, INFO8, TAB_OBJ.INFO2*(PAGE/1024)/1024, TAB_OBJ.INFO1 \n" +
                "    from (\n" +
                "        select TAB_OBJ_INNER.NAME, TAB_OBJ_INNER.ID, TAB_OBJ_INNER.SUBTYPE$, TAB_OBJ_INNER.INFO1, TAB_OBJ_INNER.INFO2, TAB_OBJ_INNER.INFO3, \n" +
                "        TAB_OBJ_INNER.INFO8, TAB_OBJ_INNER.SCHID, TAB_OBJ_INNER.CRTDATE from SYS.SYSOBJECTS TAB_OBJ_INNER , SYS.SYSOBJECTS SCH_OBJ_INNER, SYS.SYSOBJECTS USER_OBJ_INNER \n" +
                "        where \n" +
                "            TAB_OBJ_INNER.type$ = 'SCHOBJ'  \n" +
                "            and TAB_OBJ_INNER.INFO3&0x100000!=0x100000 \n" +
                "            and TAB_OBJ_INNER.INFO3&0x200000!=0x200000 \n" +
                "            and TAB_OBJ_INNER.INFO3 & 0x003F not in (0x0A, 0x20) \n" +
                "            and (TAB_OBJ_INNER.INFO3 & 0x100000000) = 0  \n" +
                "            and TAB_OBJ_INNER.NAME not like 'CTI$%$_' \n" +
                "            and TAB_OBJ_INNER.NAME not like '%$AUX' \n" +
                "            and TAB_OBJ_INNER.NAME not like '%$_AUX' \n" +
                "            and TAB_OBJ_INNER.NAME not like '%$ALOG' \n" +
                "            and (TAB_OBJ_INNER.PID=-1 or TAB_OBJ_INNER.PID=0) \n" +
                "            and TAB_OBJ_INNER.INFO3 & 0x003F != 13 \n" +
                "            and USER_OBJ_INNER.SUBTYPE$ = 'USER' \n" +
                "            and SCH_OBJ_INNER.ID = TAB_OBJ_INNER.SCHID \n" +
                "            and SCH_OBJ_INNER.PID = USER_OBJ_INNER.ID \n" +
                "            and SF_CHECK_PRIV_OPT(UID(), CURRENT_USERTYPE(), TAB_OBJ_INNER.ID, USER_OBJ_INNER.ID, USER_OBJ_INNER.INFO1, TAB_OBJ_INNER.ID) = 1\n" +
                "       ) TAB_OBJ, (select ID, NAME from SYS.SYSOBJECTS where TYPE$='SCH') SCH_OBJ \n" +
                "    where TAB_OBJ.SCHID=SCH_OBJ.ID ) TAB_OBJ_OUT\n";
        boolean hasDynamicTables = hasDynamicTables(monitor, context);
        if (!hasDynamicTables) {
            countQuery = countQuery + "  union all\n" +
                    "  select dynamic_tables.schname from v$dynamic_tables dynamic_tables, \n" +
                    "    (select id, name from sys.sysobjects where type$='SCH') sch_obj \n" +
                    "where dynamic_tables.schname=sch_obj.name\n";
        }
        countQuery = countQuery + " ) group by schname";

        List<Map<String, Object>> listCount = DBExecUtils.executeQuery(monitor, context, "get schema count", countQuery);

        List<Map<String, Object>> listCharset = DBExecUtils.executeQuery(monitor, context, "get schema charset",
                "select unicode() as UNICODE");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        String[] charsets = {"GB18030", "UTF-8", "EUC-KR"};
        String charset = "";
        if (listCharset.size() > 0) {
            int unicode = Integer.parseInt(listCharset.get(0).get("UNICODE").toString());
            if (unicode >= 0 && unicode <= 3) {
                charset = charsets[unicode];
            }
        }
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_dm_db_schema");
            returnMap.put("username", map.get("OWNER"));
            returnMap.put("charset", charset);

            if (Arrays.asList("CTISYS", "SYS", "SYSAUDITOR", "SYSDBA", "SYSSSO")
                    .contains(((String) map.get("OWNER")).toUpperCase())) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", 0L);
            for (Map<String, Object> mapCount : listCount) {
                if (map.get("OWNER").equals(mapCount.get("OWNER"))) {
                    returnMap.put("count", Long.parseLong(mapCount.get("COUNT").toString()));
                    break;
                }
            }
            returnList.add(returnMap);
        }

        return returnList;
    }

    private boolean hasDynamicTables(DBRProgressMonitor monitor, DBCExecutionContext executionContext) {
        try {
            DBExecUtils.execute(monitor, executionContext, "hasDynamicTables", "select * from v$dynamic_tables limit 1");
        } catch (Exception e) {
            return true;
        }
        return false;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {

        String comment = isQueryComment() ? " , (select COMMENT$ from SYS.SYSCOLUMNCOMMENTS where SCHNAME=COL.\"schema_name\" and tvname = col.\"table_name\" and COLNAME=COL.NAME and TABLE_TYPE='TABLE') \"comments\"\n" : "";

        return String.format("select \n" +
                "    \"schema_name\",COL.\"table_name\",\"schema_name\" as \"username\",\n" +
                "    NAME as \"column_name\",\n" +
                "    CASE WHEN B.INFO1 IS NULL OR ((B.INFO1>>2) & 0x01)=0 THEN COL.TYPE$ WHEN (B.INFO2 & 0xFF) = 0 THEN 'NUMBER' ELSE 'FLOAT' END as \"data_type\",\n" +
                "    SF_GET_COLUMN_SIZE(\n" +
                "            TYPE$, \n" +
                "            CAST ((CASE WHEN B.INFO1 IS NULL OR ((B.INFO1>>2) & 0x01)=0 THEN COL.LENGTH$ ELSE (B.INFO2 & 0xFF) END) AS INT) ,\n" +
                "            CAST ((CASE WHEN B.INFO1 IS NULL OR ((B.INFO1>>2) & 0x01)=0 THEN COL.SCALE WHEN (B.INFO2 & 0xFF) = 0 THEN 0 ELSE 129 END) AS INT)\n" +
                "    ) \"data_length\", \n" +
                "    if(au.COLID is null,0,1) \"is_primary_key\"\n" +
                "   %s" +
                "from (\n" +
                "    select\n" +
                "        D.name as \"schema_name\",\n" +
                "        C.name as \"table_name\",\n" +
                "        A.name,\n" +
                "        A.LENGTH$,\n" +
                "        A.SCALE,\n" +
                "        A.colid,\n" +
                "        A.id,\n" +
                "        A.TYPE$,\n" +
                "        A.INFO1\n" +
                "    from SYS.SYSCOLUMNS A,SYS.SYSOBJECTS C,SYS.SYSOBJECTS D\n" +
                "    where D.TYPE$='SCH' and A.ID=C.ID and C.schid=D.ID and C.subtype$ in ('UTAB','STAB')\n" +
                "    and D.name='%s' and C.name = '%s'\n" +
                ") COL\n" +
                "LEFT JOIN SYS.SYSCOLINFOS B on COL.ID=B.ID AND COL.COLID=B.COLID\n" +
                "left join (\n" +
                "        SELECT \n" +
                "                COLS.ID,\n" +
                "                COLS.COLID\n" +
                "        FROM \n" +
                "                SYSCONS AS CON,\n" +
                "                SYSOBJECTS AS OBJ,\n" +
                "                SYSOBJECTS AS TAB,\n" +
                "                SYSOBJECTS AS SCH,\n" +
                "                SYSINDEXES AS INDS,\n" +
                "                SYSCOLUMNS AS COLS\n" +
                "        WHERE \n" +
                "        SCH.TYPE$='SCH'\n" +
                "        AND TAB.subtype$ in ('UTAB','STAB')\n" +
                "        AND OBJ.SUBTYPE$='CONS'\n" +
                "        AND CON.TYPE$ = 'P'\n" +
                "        AND OBJ.ID=CON.ID \n" +
                "        AND TAB.id = CON.TABLEID \n" +
                "        AND tab.schid = SCH.id\n" +
                "        AND CON.INDEXID =INDS.ID\n" +
                "        AND TAB.ID = COLS.ID \n" +
                "        AND SF_COL_IS_IDX_KEY(INDS.KEYNUM, INDS.KEYINFO, COLS.COLID)=1\n" +
                "        and TAB.NAME = '%s' and sch.name = '%s' \n" +
                ") au on COL.ID=au.ID AND COL.COLID = au.COLID\n" +
                "order by COL.colid", comment, schemaName, tableName, tableName, schemaName);
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (List.of("REAL", "DOUBLE", "FLOAT", "DECFLOAT", "DEC", "DOUBLE PRECISION").contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            return false;
        }
        return super.showInteger(column);
    }

    @Override
    public List<String> getBigDataColumnType() {
        return Arrays.asList(
                "BLOB", "IMAGE", "LONGVARBINARY",
                "CLOB", "TEXT", "BFILE"
        );
    }

    @Override
    public boolean supportsMultipleResults() {
        return true;
    }


    @Override
    public List<String> getStatementWarningList(DBCStatement dbcStatement, DBCSession session, String sql) throws DBCException, SQLException {

        String warning;
        if (sql.length() > 7) {
            String sqlBegin = sql.substring(0, 7);
            if (sqlBegin.toLowerCase().startsWith("explain")) {
                List<LVal> lvalList = StringUtil.lexN(sql, 2, LVal.Type.LITERAL);
                String first = lvalList.size() > 0 ? lvalList.get(0).value : null;
                warning = lvalList.size() > 1 ? lvalList.get(1).value : null;
                if (StringUtil.equalsIgnoreCase(first, "explain") && !StringUtil.equalsIgnoreCase(warning, "for")) {
                    String content = (String) ReflectUtils.executeMethodForObj(((JDBCSession) session).getOriginal(), "getExplainInfo", new Class[]{String.class}, new String[]{sql.substring(7).trim()});
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(content)) {
                        return List.of(content);
                    }
                }
            }
        }


        return null;
    }

    @Override
    public boolean isIntervalNeedQuotes() {
        return false;
    }

    @Override
    public boolean supportsTableColumnSQL() {
        return true;
    }

    @Override
    public String getSchemaIdSql(String schemaName) {
        return "SELECT SCH_OBJ_INNER.ID FROM SYS.SYSOBJECTS SCH_OBJ_INNER " +
                "WHERE SCH_OBJ_INNER.type$='SCH' and  upper(SCH_OBJ_INNER.NAME) = '" + schemaName + "'";
    }

    @Override
    public List<Long> getSchemaId(List<Map<String, Object>> list) {
        ArrayList<Long> ids = new ArrayList<>();
        for (Map<String, Object> map : list) {
            ids.add((Long) map.get("ID"));
        }
        return ids;
    }

    @Override
    public String getTableRealNameSql(List<Long> schemaIds, String schemaName, String tableName) {
        String schemaId = schemaIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        String query = "select \n" +
                "\tTAB_OBJ_INNER.NAME as NAME\n" +
                "from SYS.SYSOBJECTS TAB_OBJ_INNER\n" +
                "where \n" +
                "    TAB_OBJ_INNER.type$ = 'SCHOBJ'  \n" +
                "    and TAB_OBJ_INNER.INFO3 & 0x100000!=0x100000 \n" +
                "    and TAB_OBJ_INNER.INFO3 & 0x200000!=0x200000 \n" +
                "    and TAB_OBJ_INNER.INFO3 & 0x003F not in (0x0A, 0x20) \n" +
                "    and (TAB_OBJ_INNER.INFO3 & 0x100000000) = 0  \n" +
                "    and (TAB_OBJ_INNER.PID=-1 or TAB_OBJ_INNER.PID=0) \n" +
                "    and TAB_OBJ_INNER.INFO3 & 0x003F != 13 \n" +
                "    and upper(TAB_OBJ_INNER.NAME) = '" + tableName + "'\n" +
                "    and TAB_OBJ_INNER.SCHID in (" + schemaId + ") \n";

        if (!isDockerVersion) {
            query += " union all\n" +
                    "   select NAME from v$dynamic_tables WHERE upper(name) = '" + tableName + "'\n" +
                    "   and upper(schname) = '" + schemaName + "' ";
        }

        return query;
    }
}
