package com.dc.summer.ext.dm.model;

import com.dc.summer.ext.dm.model.source.DmSourceObject;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;

/**
 * Dm Persist action with validation
 * 
 * <AUTHOR>
 *
 */
public class DmObjectValidateAction extends DmObjectPersistAction {

	private final DmSourceObject object;

	public DmObjectValidateAction(DmSourceObject object, DmObjectType objectType, String title, String script) {
		super(objectType, title, script);
		this.object = object;
	}

	@Override
	public void afterExecute(DBCSession session, Throwable error) throws DBCException {
		if(error != null) {
			return ;
		}
	}
	
}
