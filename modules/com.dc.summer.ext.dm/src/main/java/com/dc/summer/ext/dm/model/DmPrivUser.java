package com.dc.summer.ext.dm.model;

import java.sql.ResultSet;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.dm.model.utils.DmUtils;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectLazy;

/**
 * Dm Priv User
 * 
 * <AUTHOR>
 *
 */
public class DmPrivUser extends DmPriv implements DBSObjectLazy<DmDataSource> {

	private Object user;
	private boolean defaultRole;

	public DmPrivUser(DmGrantee user, ResultSet resultSet) {
        super(user, JDBCUtils.safeGetString(resultSet, "GRANTEE"), resultSet);
        this.defaultRole = JDBCUtils.safeGetBoolean(resultSet, "DEFAULT_ROLE", "Y");
        this.user = this.name;
    }

	@NotNull
	@Override
	public String getName() {
		return super.getName();
	}

	@Property(id = DBConstants.PROP_ID_NAME, viewable = true, order = 2, supportsPreview = true)
	public Object getUser(DBRProgressMonitor monitor) throws DBException {
		if (monitor == null) {
			return user;
		}
		return DmUtils.resolveLazyReference(monitor, getDataSource(), getDataSource().userCache, this, null);
	}

	@Property(viewable = true, order = 4)
	public boolean isDefaultRole() {
		return defaultRole;
	}

	@Override
	public Object getLazyReference(Object propertyId) {
		return this.user;
	}

}
