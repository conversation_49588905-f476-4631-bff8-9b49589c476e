package com.dc.summer.ext.dm.model;

import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.dm.model.utils.DmConstants;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPHiddenObject;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableColumn;
import com.dc.summer.model.meta.IPropertyCacheValidator;
import com.dc.summer.model.meta.IPropertyValueListProvider;
import com.dc.summer.model.meta.LazyProperty;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSTypedObjectEx;
import com.dc.summer.model.struct.rdb.DBSTableColumn;
import com.dc.utils.CommonUtils;

public class DmTableColumn extends JDBCTableColumn<DmTableBase>
		implements DBSTableColumn, DBSTypedObjectEx, DBPHiddenObject, DBPNamedObject2 {

	Log log=Log.getLog(DmTableColumn.class);
	
	private DmDataType type;
	private DmDataTypeModifier typeMod;
	private String comment;
	private boolean hidden;
	private Integer scale;

	public DmTableColumn(DmTableBase table) {
		super(table, false);
	}

 //查询是否自增
	public DmTableColumn(DBRProgressMonitor monitor, DmTableBase table, ResultSet dbResult) throws DBException {
		super(table, true);
		setDefaultValue(JDBCUtils.safeGetString(dbResult, "DATA_DEFAULT"));
		setName(JDBCUtils.safeGetString(dbResult, "COLUMN_NAME"));
		setOrdinalPosition(JDBCUtils.safeGetInt(dbResult, "COLUMN_ID"));
		this.typeName = JDBCUtils.safeGetString(dbResult, "DATA_TYPE");
		this.type = DmDataType.resolveDataType(monitor, getDataSource(),
				JDBCUtils.safeGetString(dbResult, "DATA_TYPE_OWNER"), this.typeName);
		this.typeMod = DmDataTypeModifier.resolveTypeModifier(JDBCUtils.safeGetString(dbResult, "DATA_TYPE_MOD"));
		if (this.type != null) {
			this.typeName = type.getFullyQualifiedName(DBPEvaluationContext.DDL);
			this.valueType = type.getTypeID();
		}
		if (typeMod == DmDataTypeModifier.REF) {
			this.valueType = Types.REF;
		}
		String charUsed = JDBCUtils.safeGetString(dbResult, "CHAR_USED");
		setMaxLength(JDBCUtils.safeGetLong(dbResult, "C".equals(charUsed) ? "CHAR_LENGTH" : "DATA_LENGTH"));
		setRequired(!"Y".equals(JDBCUtils.safeGetString(dbResult, "NULLABLE")));
		this.scale = JDBCUtils.safeGetInteger(dbResult, "DATA_SCALE");
		if (this.scale == null) {
			// Scale can be null in case when type was declared without parameters (examples: NUMBER, NUMBER(*), FLOAT)
			if (this.type != null && this.type.getScale() != null) {
				this.scale = this.type.getScale();
			}
		}
		setPrecision(JDBCUtils.safeGetInteger(dbResult, "DATA_PRECISION"));
		this.hidden = JDBCUtils.safeGetBoolean(dbResult, "HIDDEN_COLUMN", DmConstants.YES);
		if(table instanceof DmTable) {
			this.autoGenerated=((DmTable)table).getAutoCloumnNames().contains(name.toUpperCase());
		}
	}

	@NotNull
	@Override
	public DmDataSource getDataSource() {
		return getTable().getDataSource();
	}

	@Property(viewable = true, editable = true, updatable = true, order = 20, listProvider = ColumnTypeNameListProvider.class)
	@Override
	public String getFullTypeName() {
		return DBUtils.getFullTypeName(this);
	}

	public void setFullTypeName(String typeName) throws DBException {
		String plainTypeName;
		int divPos = typeName.indexOf("(");
		if (divPos == -1) {
			plainTypeName = typeName;
		} else {
			plainTypeName = typeName.substring(0, divPos);
			int divPos2 = typeName.indexOf(')', divPos);
			if (divPos2 != -1) {
				String modifiers = typeName.substring(divPos + 1, divPos2);
				int divPos3 = modifiers.indexOf(',');
				if (divPos3 == -1) {
					if (getDataKind() == DBPDataKind.STRING) {
						maxLength = CommonUtils.toInt(modifiers);
					} else {
						precision = CommonUtils.toInt(modifiers);
					}
				} else {
					precision = CommonUtils.toInt(modifiers.substring(0, divPos3).trim());
					scale = CommonUtils.toInt(modifiers.substring(divPos3 + 1).trim());
				}
			}
		}
		DmDataType newDataType = getDataSource().resolveDataType(new VoidProgressMonitor(), plainTypeName);
		if (newDataType == null) {
			throw new DBException("Bad data type: " + plainTypeName);
		}
		this.type = newDataType;
		this.typeName = this.type.getTypeName();
	}

	@Nullable
	@Override
	@Property(viewable = false, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 21, listProvider = ColumnDataTypeListProvider.class)
	public DmDataType getDataType() {
		return type;
	}

	public void setDataType(DmDataType type) {
		this.type = type;
		this.typeName = type == null ? "" : type.getFullyQualifiedName(DBPEvaluationContext.DDL);
	}

	@Property(viewable = true, order = 30)
	public DmDataTypeModifier getTypeMod() {
		return typeMod;
	}

	@Override
	public String getTypeName() {
		return super.getTypeName();
	}

	@Property(viewable = false, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 40)
	@Override
	public long getMaxLength() {
		return super.getMaxLength();
	}

	@Override
	@Property(viewable = false, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 41)
	public Integer getPrecision() {
		return super.getPrecision();
	}

	@Override
	@Property(viewable = false, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 42)
	public Integer getScale() {
		return super.getScale();
	}

	@Property(viewable = true, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 50)
	@Override
	public boolean isRequired() {
		return super.isRequired();
	}

	@Property(viewable = true, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 70)
	@Override
	public String getDefaultValue() {
		return super.getDefaultValue();
	}

	
	@Property(viewable = true, editableExpr = "!object.table.view", updatableExpr = "!object.table.view", order = 60)//是否自增列
	@Override
	public boolean isAutoGenerated() {
		return super.isAutoGenerated();
	}

	public static class CommentLoadValidator implements IPropertyCacheValidator<DmTableColumn> {

		@Override
		public boolean isPropertyCached(DmTableColumn object, Object propertyId) {
			return object.comment != null;
		}

	}

	@Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 100)
	@LazyProperty(cacheValidator = CommentLoadValidator.class)
	public String getComment(DBRProgressMonitor monitor) {
		if (comment == null) {
			// Load comments for all table columns
			try {
				comment=getTable().commentsMap.get(getName());//获取已加载的注释
			} catch (Exception e) {
				// TODO: handle exception
				log.error(e.getMessage());
			}
			
		}
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	void cacheComment() {
		if (this.comment == null) {
			this.comment = "";
		}
	}

	@Nullable
	@Override
	public String getDescription() {
		return comment;
	}

	@Override
	public boolean isHidden() {
		return hidden;
	}

	
	public static class ColumnDataTypeListProvider implements IPropertyValueListProvider<DmTableColumn> {

		@Override
		public boolean allowCustomValue() {
			return false;
		}

		@Override
		public Object[] getPossibleValues(DmTableColumn column) {
			List<DBSObject> dataTypes = new ArrayList<DBSObject>(column.getTable().getDataSource().getLocalDataTypes());
			if (!dataTypes.contains(column.getDataType())) {
				dataTypes.add(column.getDataType());
			}
			Collections.sort(dataTypes, DBUtils.nameComparator());
			return dataTypes.toArray(new DBSDataType[dataTypes.size()]);
		}
	}
}
