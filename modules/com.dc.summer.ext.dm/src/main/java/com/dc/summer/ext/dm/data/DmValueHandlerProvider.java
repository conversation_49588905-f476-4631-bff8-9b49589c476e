package com.dc.summer.ext.dm.data;

import com.dc.summer.ext.dm.model.DmDataSource;
import com.dc.summer.ext.dm.model.utils.DmConstants;
import com.dc.summer.ext.oracle.data.*;
import com.dc.summer.ext.oracle.model.OracleConstants;
import com.dc.summer.ext.oracle.model.OracleDataSource;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.Types;


public class DmValueHandlerProvider extends OracleValueHandlerProvider {


    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {

        DBPDataKind dataKind = typedObject.getDataKind();
        if (DBPDataKind.STRUCT.equals(dataKind)) {
            return new DmObjectValueHandler();
        }

        if (DmConstants.TYPE_NAME_TEXT.equalsIgnoreCase(typedObject.getTypeName()) ||
                DmConstants.TYPE_NAME_SYSDBA_ARR_NUM.equalsIgnoreCase(typedObject.getTypeName()) ||
                DmConstants.TYPE_NAME_SYSDBA_T_CHA.equalsIgnoreCase(typedObject.getTypeName()) ||
                DmConstants.TYPE_NAME_SYSDBA_CLS1.equalsIgnoreCase(typedObject.getTypeName()) ||
                DmConstants.TYPE_NAME_SYSDBA_COMPLEX.equalsIgnoreCase(typedObject.getTypeName()) ||
                DmConstants.TYPE_NAME_ROWID.equalsIgnoreCase(typedObject.getTypeName())) {
            return new DmTextValueHandler();
        }

        switch (typedObject.getTypeID()) {
            case Types.BLOB:
                return OracleBLOBValueHandler.INSTANCE;
            case Types.CLOB:
            case Types.NCLOB:
                return OracleCLOBValueHandler.INSTANCE;
            case Types.TIME_WITH_TIMEZONE:
            case Types.TIMESTAMP_WITH_TIMEZONE:
            case OracleConstants.DATA_TYPE_TIMESTAMP_WITH_TIMEZONE:
                if (((DmDataSource)dataSource).isDriverVersionAtLeast(12, 2)) {
                    return new OracleTemporalAccessorValueHandler(preferences);
                } else {
                    return new OracleTimestampValueHandler(preferences);
                }
            case OracleConstants.DATA_TYPE_REFCURSOR:
                return OracleRefCursorValueHandler.INSTANCE;
        }

        final String typeName = typedObject.getTypeName();
        switch (typeName) {
            case DmConstants.TYPE_NAME_XML:
            case DmConstants.TYPE_FQ_XML:
                return OracleXMLValueHandler.INSTANCE;
            case DmConstants.TYPE_NAME_BFILE:
                return OracleBFILEValueHandler.INSTANCE;
            case DmConstants.TYPE_NAME_REFCURSOR:
                return OracleRefCursorValueHandler.INSTANCE;
            case DmConstants.TYPE_RAW:
                return OracleRawHandler.INSTANCE;
            case DmConstants.TYPE_LONGVARCHAR:
                return DmLongVarCharValueHandler.INSTANCE;
            case DmConstants.TYPE_IMAGE:
            case DmConstants.TYPE_LONGVARBINARY:
                return OracleBLOBValueHandler.INSTANCE;
            case DmConstants.TYPE_DATE:
                return new DmDateValueHandler(preferences);
        }

        if (typeName.contains(OracleConstants.TYPE_NAME_TIMESTAMP) || typedObject.getDataKind() == DBPDataKind.DATETIME) {
            return new OracleTimestampValueHandler(preferences);
        }

        return super.getValueHandler(dataSource, preferences, typedObject);
    }
}
