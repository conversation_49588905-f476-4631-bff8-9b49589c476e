package com.dc.summer.ext.dm.model;

import java.sql.ResultSet;
import java.util.Collection;
import java.util.Map;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.dm.model.source.DmSourceObject;
import com.dc.summer.ext.dm.model.utils.DmUtils;
import com.dc.summer.model.DBPRefreshableObject;
import com.dc.summer.model.DBPScriptObjectExt;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.summer.model.struct.DBSObjectState;
import com.dc.summer.model.struct.rdb.DBSPackage;
import com.dc.summer.model.struct.rdb.DBSProcedure;
import com.dc.summer.model.struct.rdb.DBSProcedureContainer;

public class DmPackage extends DmSchemaObject implements DmSourceObject, DBPScriptObjectExt, DBSObjectContainer,
		DBSPackage, DBPRefreshableObject, DBSProcedureContainer {

	private static final Log log = Log.getLog(DmPackage.class);

	private boolean valid;
	private String sourceDeclaration;
	private String sourceDefinition;

	public DmPackage(DmSchema schema, ResultSet dbResult) {
		super(schema, JDBCUtils.safeGetString(dbResult, "OBJECT_NAME"), true);
		this.valid = "VALID".equals(JDBCUtils.safeGetString(dbResult, "STATUS"));
	}

	public DmPackage(DmSchema schema, String name) {
		super(schema, name, false);
	}

	@Property(viewable = true, order = 3)
	public boolean isValid() {
		return valid;
	}

	@Override
	public DmSourceType getSourceType() {
		return DmSourceType.PACKAGE;
	}

	@Override
	@Property(hidden = true, editable = true, updatable = true, order = -1)
	public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBCException {
		if (sourceDeclaration == null && monitor != null) {
			sourceDeclaration = DmUtils.getSource(monitor, this, false, true);
		}
		return sourceDeclaration;
	}

	public void setObjectDefinitionText(String sourceDeclaration) {
		this.sourceDeclaration = sourceDeclaration;
	}

	@Override
	@Property(hidden = true, editable = true, updatable = true, order = -1)
	public String getExtendedDefinitionText(DBRProgressMonitor monitor) throws DBException {
		if (sourceDefinition == null && monitor != null) {
			sourceDefinition = DmUtils.getSource(monitor, this, true, true);
		}
		return sourceDefinition;
	}

	public void setExtendedDefinitionText(String source) {
		this.sourceDefinition = source;
	}
	@NotNull
	@Override
	public DBSObjectState getObjectState() {
		return valid ? DBSObjectState.NORMAL : DBSObjectState.INVALID;
	}

	@Override
	public void refreshObjectState(DBRProgressMonitor monitor) throws DBCException {
		this.valid = DmUtils.getObjectStatus(monitor, this, DmObjectType.PACKAGE) && 
				DmUtils.getObjectStatus(monitor, this, DmObjectType.PACKAGE_BODY);
	}

	@Override
	public Collection<? extends DBSProcedure> getProcedures(DBRProgressMonitor monitor) throws DBException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DBSProcedure getProcedure(DBRProgressMonitor monitor, String uniqueName) throws DBException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DBSObject refreshObject(DBRProgressMonitor monitor) throws DBException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Collection<? extends DBSObject> getChildren(DBRProgressMonitor monitor) throws DBException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DBSObject getChild(DBRProgressMonitor monitor, String childName) throws DBException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void cacheStructure(DBRProgressMonitor monitor, int scope) throws DBException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public DBEPersistAction[] getCompileActions(DBRProgressMonitor monitor) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Class<? extends DBSObject> getPrimaryChildType(DBRProgressMonitor monitor) throws DBException {
		// TODO Auto-generated method stub
		return null;
	}
}
