package com.dc.summer.ext.dm.data;

import com.dc.summer.ext.dm.model.utils.DmConstants;
import com.dc.summer.model.data.DBDDataFormatter;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class DmDateValueHandler extends JDBCDateTimeValueHandler {


    public DmDateValueHandler(DBDFormatSettings formatSettings) {
        super(formatSettings);
    }


    @Override
    public Object fetchValueObject(DBCSession session, DBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException {
        if (resultSet instanceof JDBCResultSet) {

            if (formatSettings.isUseNativeDateTimeFormat()) {
                try {
                    JDBCResultSet dbResults = (JDBCResultSet) resultSet;
                    return dbResults.getString(index + 1);
                } catch (SQLException e) {
                    log.debug("Can't read date/time value as string: " + e.getMessage());
                }
            }
            try {
                JDBCResultSet dbResults = (JDBCResultSet) resultSet;
                return dbResults.getDate(index + 1);
            } catch (SQLException sqlException) {
                return super.fetchValueObject(session, resultSet, type, index);
            }
        } else {
            return resultSet.getAttributeValue(index);
        }
    }

    @Override
    protected String getFormatterId(DBSTypedObject column) {
        if (DmConstants.TYPE_DATE.equalsIgnoreCase(column.getTypeName())) {
            return DBDDataFormatter.TYPE_NAME_DATE;
        }
        return super.getFormatterId(column);
    }
}
