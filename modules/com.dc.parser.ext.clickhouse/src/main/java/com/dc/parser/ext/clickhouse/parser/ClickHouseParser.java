
package com.dc.parser.ext.clickhouse.parser;

import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.TokenStream;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.ext.clickhouse.parser.autogen.ClickHouseStatementParser;
import com.dc.parser.model.engine.ParseASTNode;

/**
 * ClickHouse parser.
 */
public final class ClickHouseParser extends ClickHouseStatementParser implements SQLParser {
    
    public ClickHouseParser(final TokenStream input) {
        super(input);
    }
    
    @Override
    public ASTNode parse() {
        return new ParseASTNode(execute(), (CommonTokenStream) getTokenStream());
    }
}
