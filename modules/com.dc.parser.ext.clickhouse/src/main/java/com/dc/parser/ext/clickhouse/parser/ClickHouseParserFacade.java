
package com.dc.parser.ext.clickhouse.parser;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.parser.SQLLexer;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.spi.DialectSQLParserFacade;

/**
 * SQL parser facade for ClickHouse.
 */
public final class ClickHouseParserFacade implements DialectSQLParserFacade {
    
    @Override
    public Class<? extends SQLLexer> getLexerClass() {
        return ClickHouseLexer.class;
    }
    
    @Override
    public Class<? extends SQLParser> getParserClass() {
        return ClickHouseParser.class;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.CLICKHOUSE;
    }
}
