

package com.dc.summer.ext.oracle.model;

/**
* Data type modifier
*/
public enum OracleDataTypeModifier {
    REF,
    POINTER;

    public static OracleDataTypeModifier resolveTypeModifier(String typeMod)
    {
        if (typeMod == null || typeMod.length() == 0) {
            return null;
        } else if (typeMod.equals("REF")) {
            return REF;
        } else {
            return POINTER;
        }
    }
}
