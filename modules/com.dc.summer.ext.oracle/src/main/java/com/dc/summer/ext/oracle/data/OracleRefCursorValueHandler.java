
package com.dc.summer.ext.oracle.data;

import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStructValueHandler;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

/**
 * OracleRefCursorValueHandler
 */
public class OracleRefCursorValueHandler extends JD<PERSON><PERSON>tructValueHandler {
    public static final OracleRefCursorValueHandler INSTANCE = new OracleRefCursorValueHandler();

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        Object cursorValue = resultSet.getObject(index);
        return new OracleRefCursor((JDBCSession) session, resultSet.getSourceStatement(), cursorValue);
    }

    @Override
    protected void bindParameter(
        JDBCSession session,
        JDBCPreparedStatement statement,
        DBSTypedObject paramType,
        int paramIndex,
        Object value)
        throws DBCException, SQLException
    {
        throw new DBCException("Cursor value binding not supported");
    }

}
