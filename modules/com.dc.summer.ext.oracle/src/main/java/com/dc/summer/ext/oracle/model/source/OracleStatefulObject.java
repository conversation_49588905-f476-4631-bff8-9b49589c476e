
package com.dc.summer.ext.oracle.model.source;

import com.dc.summer.ext.oracle.model.OracleSchema;
import com.dc.summer.model.DBPStatefulObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.oracle.model.OracleDataSource;
import com.dc.summer.model.struct.DBSObject;

/**
 * OracleStatefulObject
 */
public interface OracleStatefulObject extends DBSObject, DBPStatefulObject
{
    @NotNull
    @Override
    OracleDataSource getDataSource();

    @Nullable
    OracleSchema getSchema();
}
