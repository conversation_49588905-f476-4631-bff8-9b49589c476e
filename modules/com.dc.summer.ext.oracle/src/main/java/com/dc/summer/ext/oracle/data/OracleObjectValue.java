
package com.dc.summer.ext.oracle.data;

import com.dc.summer.model.data.DBDValue;

/**
 * Object value
 */
public class OracleObjectValue implements DBDValue {

    private Object value;

    public OracleObjectValue(Object value)
    {
        this.value = value;
    }

    public Object getValue()
    {
        return value;
    }

    @Override
    public Object getRawValue() {
        return value;
    }

    @Override
    public boolean isNull()
    {
        return value == null;
    }

    @Override
    public boolean isModified() {
        return false;
    }

    @Override
    public void release()
    {

    }
}
