
package com.dc.summer.ext.oracle.model.dict;

/**
 * NLS language dictionary
 */
public enum OracleLanguage
{
	AMERICAN("AMERICAN"),
	<PERSON>ABIC("ARABIC"),
	BENGALI("BEN<PERSON>LI"),
	BRAZILIAN_PORTUGUESE("BRAZILIAN PORTUGUESE  "),
	BULGARIAN("BULGA<PERSON><PERSON>"),
	CANADIAN_FRENCH("CANADIAN FRENCH"),
	CATALAN("CATALAN"),
	SIMPLIFIED_CHINESE ("SIMPLIFIED CHINESE "),
	CROATIAN("CROATIAN"),
	CZECH("CZECH"),
	DANISH("DANISH"),
	DUTCH("DUTCH"),
	EGYPTIAN("EGYP<PERSON><PERSON>"),
	ENGLISH("ENGLISH"),
	ESTONIAN("ESTONIAN"),
	FINNISH("FINNISH"),
	FRENCH("FRENCH"),
	GERMAN_DIN("GERMAN DIN  "),
	GERMAN("GERMAN"),
	GREEK("<PERSON><PERSON><PERSON><PERSON>"),
	<PERSON><PERSON>BR<PERSON><PERSON>("HEBREW  "),
	<PERSON><PERSON><PERSON>RI<PERSON>("<PERSON><PERSON><PERSON><PERSON><PERSON>"),
	ICELANDIC("ICELANDIC"),
	INDONESIAN("INDONESIAN"),
	ITALIAN("ITALIAN"),
	JAPANESE("JAPANESE"),
	KOREAN("KOREAN"),
	LATIN_AMERICAN_SPANISH("LATIN AMERICAN SPANISH  "),
	LATVIAN("LATVIAN"),
	LITHUANIAN("LITHUANIAN"),
	MALAY("MALAY  "),
	MEXICAN_SPANISH("MEXICAN SPANISH"),
	NORWEGIAN("NORWEGIAN"),
	POLISH("POLISH"),
	PORTUGUESE("PORTUGUESE"),
	ROMANIAN  ("ROMANIAN  "),
	RUSSIAN("RUSSIAN"),
	SLOVAK("SLOVAK"),
	SLOVENIAN  ("SLOVENIAN  "),
	SPANISH("SPANISH"),
	SWEDISH("SWEDISH"),
	THAI("THAI  "),
	TRADITIONAL_CHINESE("TRADITIONAL CHINESE"),
	TURKISH("TURKISH"),
	UKRAINIAN("UKRAINIAN"),
	VIETNAMESE("VIETNAMESE");

    private final String language;

    OracleLanguage(String language)
    {
        this.language = language;
    }

    public String getLanguage()
    {
        return language;
    }
}
