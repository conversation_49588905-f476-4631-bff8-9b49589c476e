
package com.dc.summer.ext.oracle.data;

import com.dc.summer.model.impl.data.formatters.BinaryFormatterHex;

/**
 * OracleBinaryFormatter
 */
public class OracleBinaryFormatter extends BinaryFormatterHex {

    public static final OracleBinaryFormatter INSTANCE = new OracleBinaryFormatter();
    private static final String HEX_PREFIX = "'";
    private static final String HEX_POSTFIX = "'";

    @Override
    public String getId()
    {
        return "orahex";
    }

    @Override
    public String getTitle()
    {
        return "Oracle Hex";
    }

    @Override
    public String toString(byte[] bytes, int offset, int length)
    {
        return HEX_PREFIX + super.toString(bytes, offset, length) + HEX_POSTFIX;
    }

    @Override
    public byte[] toBytes(String string)
    {
        if (string.startsWith(HEX_PREFIX)) {
            string = string.substring(
                HEX_PREFIX.length(),
                string.length() - HEX_POSTFIX.length());
        }
        return super.toBytes(string);
    }

}
