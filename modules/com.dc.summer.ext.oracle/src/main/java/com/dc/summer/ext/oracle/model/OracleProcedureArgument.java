
package com.dc.summer.ext.oracle.model;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.model.struct.rdb.DBSProcedureParameter;
import com.dc.summer.model.struct.rdb.DBSProcedureParameterKind;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * OracleProcedureArgument
 */
public class OracleProcedureArgument implements DBSProcedureParameter, DBSTypedObject
{
    private final OracleProcedureBase procedure;
    private String name;
    private int position;
    private int dataLevel;
    private int sequence;
    private OracleParameterMode mode;
    private OracleDataType type;
    private OracleDataType dataType;
    private String packageTypeName;
    private int dataLength;
    private int dataScale;
    private int dataPrecision;
    private List<OracleProcedureArgument> attributes;

    public OracleProcedureArgument(
        DBRProgressMonitor monitor,
        OracleProcedureBase procedure,
        ResultSet dbResult)
    {
        this.procedure = procedure;
        this.name = JDBCUtils.safeGetString(dbResult, "ARGUMENT_NAME");
        this.position = JDBCUtils.safeGetInt(dbResult, "POSITION");
        this.dataLevel = JDBCUtils.safeGetInt(dbResult, "DATA_LEVEL");
        this.sequence = JDBCUtils.safeGetInt(dbResult, "SEQUENCE");
        if (CommonUtils.isEmpty(this.name)) {
            this.mode =OracleParameterMode.RETURN;
        } else {
            this.mode = OracleParameterMode.getMode(JDBCUtils.safeGetString(dbResult, "IN_OUT"));
        }
        final String dataType = JDBCUtils.safeGetString(dbResult, "DATA_TYPE");
        this.type = CommonUtils.isEmpty(dataType) ? null : OracleDataType.resolveDataType(
            monitor,
            procedure.getDataSource(),
            null,
            dataType);
        final String typeName = JDBCUtils.safeGetString(dbResult, "TYPE_NAME");
        final String typeOwner = JDBCUtils.safeGetString(dbResult, "TYPE_OWNER");
        this.packageTypeName = JDBCUtils.safeGetString(dbResult, "TYPE_SUBNAME");
        if (!CommonUtils.isEmpty(typeName) && !CommonUtils.isEmpty(typeOwner) && CommonUtils.isEmpty(this.packageTypeName)) {
            this.dataType = OracleDataType.resolveDataType(
                monitor,
                procedure.getDataSource(),
                typeOwner,
                typeName);
            if (this.dataType == null) {
                this.packageTypeName = typeOwner + "." + typeName;
            }
        } else if (this.packageTypeName != null) {
            packageTypeName = typeName + "." + packageTypeName;
        }
        this.dataLength = JDBCUtils.safeGetInt(dbResult, "DATA_LENGTH");
        this.dataScale = JDBCUtils.safeGetInt(dbResult, "DATA_SCALE");
        this.dataPrecision = JDBCUtils.safeGetInt(dbResult, "DATA_PRECISION");
    }

    @Nullable
    @Override
    public String getDescription()
    {
        return null;
    }

    @NotNull
    @Override
    public OracleDataSource getDataSource()
    {
        return procedure.getDataSource();
    }

    @Override
    public OracleProcedureBase getParentObject()
    {
        return procedure;
    }

    @Override
    public boolean isPersisted()
    {
        return true;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 10)
    public String getName()
    {
        if (CommonUtils.isEmpty(name)) {
            if (dataLevel == 0) {
                // Function result
                return "RESULT";
            } else {
                // Collection element
                return "ELEMENT";
            }
        }
        return name;
    }

    public boolean isResultArgument() {
        return CommonUtils.isEmpty(name) && dataLevel == 0;
    }

    @Property(viewable = true, order = 11)
    public int getPosition()
    {
        return position;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 20)
    public DBSProcedureParameterKind getParameterKind()
    {
        return mode == null ? DBSProcedureParameterKind.UNKNOWN : mode.getParameterKind();
    }

    @Property(viewable = true, order = 21)
    public Object getType()
    {
        return packageTypeName != null ?
            packageTypeName :
            dataType == null ? type : dataType;
    }

    @Override
    @Property(viewable = true, order = 30)
    public long getMaxLength()
    {
        return dataLength;
    }

    @Override
    public long getTypeModifiers() {
        return 0;
    }

    @Override
    public String getTypeName()
    {
        return type == null ? packageTypeName : type.getName();
    }

    @Override
    public String getFullTypeName() {
        return DBUtils.getFullTypeName(this);
    }

    @Override
    public int getTypeID()
    {
        return type == null ? 0 : type.getTypeID();
    }

    @Override
    public DBPDataKind getDataKind()
    {
        return type == null ? DBPDataKind.OBJECT : type.getDataKind();
    }

    @Override
    @Property(viewable = true, order = 40)
    public Integer getScale()
    {
        return dataScale;
    }

    @Override
    @Property(viewable = true, order = 50)
    public Integer getPrecision()
    {
        return dataPrecision;
    }

    public int getDataLevel()
    {
        return dataLevel;
    }

    public int getSequence()
    {
        return sequence;
    }

    @Association
    public List<OracleProcedureArgument> getAttributes()
    {
        return attributes;
    }

    void addAttribute(OracleProcedureArgument attribute)
    {
        if (attributes == null) {
            attributes = new ArrayList<>();
        }
        attributes.add(attribute);
    }

    public boolean hasAttributes()
    {
        return !CommonUtils.isEmpty(attributes);
    }

    @NotNull
    @Override
    public DBSTypedObject getParameterType() {
        return this;
    }
}
