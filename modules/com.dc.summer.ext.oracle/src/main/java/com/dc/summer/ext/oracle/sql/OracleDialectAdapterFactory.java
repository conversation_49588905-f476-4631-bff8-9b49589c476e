

package com.dc.summer.ext.oracle.sql;

import org.eclipse.core.runtime.IAdapterFactory;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.text.parser.TPRuleProvider;

public class OracleDialectAdapterFactory implements IAdapterFactory {

    private static final Class<?>[] CLASSES = new Class[] { TPRuleProvider.class };
    
    @Override
    public <T> T getAdapter(Object adaptableObject, Class<T> adapterType) {
        if (adaptableObject instanceof SQLDialect) {
            if (adapterType == TPRuleProvider.class) {
                return adapterType.cast(new OracleDialectRules());
            }
        }
        return null;
    }

    @Override
    public Class<?>[] getAdapterList() {
        return CLASSES;
    }

}
