package com.dc.summer.ext.kingbase.model;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class KingBaseDataSourceInfo extends JDBCDataSourceInfo {

    private final KingBaseDataSource dataSource;

    public KingBaseDataSourceInfo(KingBaseDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(metaData);
        this.dataSource = dataSource;
    }

    @Override
    public boolean supportsLocalTransaction() {
        this.dataSource.getContainer().setLocalTransactional((dbrProgressMonitor, dbcExecutionContext) -> {
            AtomicBoolean hasTransactional = new AtomicBoolean(false);
            try {
                DBExecUtils.executeQuery(
                        dbrProgressMonitor,
                        dbcExecutionContext,
                        "runLocalTransaction",
                        "select pid as LOCAL_TRANSACTION_ID from sys_locks \n" +
                                "where mode in ('RowExclusiveLock','RowShareLock','ExclusiveLock','AccessExclusiveLock','ShareLock','ShareRowExclusiveLock') \n" +
                                "and pid = (SELECT sys_backend_pid() as pid) and locktype in ('relation','transactionid')",
                        dbcResultSet -> {
                            if (dbcResultSet.nextRow()) {
                                if (dbcResultSet.getAttributeValue("LOCAL_TRANSACTION_ID") != null) {
                                    hasTransactional.set(true);
                                }
                            }
                        });
            } catch (DBException e) {
                log.error(e.getMessage(), e);
            }
            return hasTransactional.get();
        });
//        return true;
        return super.supportsLocalTransaction();
    }

    @Override
    public boolean supportsDDLAutoCommit() {
        return false;
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return true;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {
        return "select d.attname as \"key_column\" from sys_catalog.sys_namespace a \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                " left join sys_catalog.sys_class b on a.oid=b.relnamespace \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                " left join sys_catalog.sys_constraint c on b.oid=c.conrelid \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                " left join sys_catalog.sys_attribute d on d.attrelid = c.conrelid and d.attnum=any(c.conkey) \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                " where a.nspname='" + schemaName + "' and b.relname='" + tableName + "' and contype='p'";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("key_column") != null) {
                columns.add((String) map.get("key_column"));
            } else if (map.get("KEY_COLUMN") != null) {
                columns.add((String) map.get("KEY_COLUMN"));
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {
        return "select tablename,schemaname from sys_tables ".replaceAll("sys", dataSource.getInstancePrefix()) +
                "where upper(schemaname) = upper('" + schemaName + "') " +
                "and upper(tablename) = upper('" + tableName + "')";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("tablename") != null && map.get("schemaname") != null) {
                realName = map.get("schemaname") + "." + map.get("tablename"); // 需要真实framework: schemaname
                break;
            } else if (map.get("TABLENAME") != null && map.get("SCHEMANAME") != null) {
                realName = map.get("SCHEMANAME") + "." + map.get("TABLENAME");
                break;
            }
        }

        return realName;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {

        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {

            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "\",\"");
        String values = StringUtils.join(data, ",");

        if (tableName.contains(".")) {
            String[] dboTable = tableName.split("\\.");
            tableName = String.format("%s.\"%s\"", dboTable[0], dboTable[1]);
        }

        if (StringUtils.isNotEmpty(schemaName)) {
            if (schemaName.contains(".")) {
                String[] names = schemaName.split("\\.");
                schemaName = String.format("%s\".\"%s", names[0], names[1]);
            }
            return String.format("INSERT INTO \"%s\".\"%s\" (%s) VALUES (%s)", schemaName, tableName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES (%s)", tableName, "\"" + columns + "\"", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("\",\""));
        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {

                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                        data.add(String.format("'%s'", varcharData));
                    } else {
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        if (tableName.contains(".")) {
            String[] dboTable = tableName.split("\\.");
            tableName = String.format("%s.\"%s\"", dboTable[0], dboTable[1]);
        }

        if (StringUtils.isNotEmpty(schemaName)) {
            if (schemaName.contains(".")) {
                String[] names = schemaName.split("\\.");
                schemaName = String.format("%s\".\"%s", names[0], names[1]);
            }
            return String.format("INSERT INTO \"%s\".\"%s\" (%s) VALUES %s", schemaName, tableName, "\"" + fields + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES %s", tableName, "\"" + fields + "\"", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        String tablePublic = null;
        String tableTrueName = null;
        if (tableName.contains(".")) {
            String[] tables = tableName.split("\\.");
            tablePublic = tables[0];
            tableTrueName = tables[1];
        } else {
            tableTrueName = tableName;
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(tablePublic)) {
            tableTrueName = String.format("%s\".\"%s", tablePublic, tableTrueName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            if (schemaName.contains(".")) {
                String[] names = schemaName.split("\\.");
                schemaName = String.format("%s\".\"%s", names[0], names[1]);
            }
            return String.format("TRUNCATE TABLE \"%s\".\"%s\"", schemaName, tableTrueName);
        } else {
            return String.format("TRUNCATE TABLE \"%s\"", tableName);
        }
    }

    /**
     * 由于kingbase 有私有同义词和公有同义词，所以联查条件增加查PUBLIC的同义词
     *
     * @param schemaName schema name
     * @param synonym    synonym
     * @return sql
     */
    @Override
    public String getSynonymSql(String schemaName, String synonym) {
        @SQL
        String synonymSql = "SELECT * FROM (\n" +
                "SELECT\n" +
                "        a.refobjnspname as \"source_schema\",\n" +
                "        a.refobjname as \"source_table\"\n" +
                "from sys_synonym a\n" +
                "left join sys_catalog.sys_namespace d on a.synnamespace = d.oid\n" +
                "where (nspname ='" + schemaName + "' OR nspname='PUBLIC') and  synname = '" + synonym + "'\n" +
                "ORDER BY CASE WHEN nspname = '" + schemaName + "' THEN 1 ELSE 2 END\n" +
                ")\n" +
                "WHERE ROWNUM = 1;";
        return synonymSql;
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        ArrayList<Map<String, Object>> returnList = new ArrayList<>();

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get catalog list",
                "select datname as owner,sys_encoding_to_char(encoding) as charset \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                        "from sys_database \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                        "where lower(datname) not in ('template0','template1','template2');");

        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();

            if (map.get("owner") != null) {
                returnMap.put("username", map.get("owner"));
            } else if (map.get("OWNER") != null) {
                returnMap.put("username", map.get("OWNER"));
            } else {
                returnMap.put("username", "");
            }

            if (map.get("charset") != null) {
                returnMap.put("charset", map.get("charset"));
            } else if (map.get("CHARSET") != null) {
                returnMap.put("charset", map.get("CHARSET"));
            } else {
                returnMap.put("charset", "");
            }

            returnMap.put("is_sys", 0);
            returnMap.put("def_dbo_name", "public");

            String dbName = returnMap.get("username").toString();

            List<Map<String, Object>> listSchema = DBExecUtils.executeQuery(monitor, dbNameContextFunc.apply(dbName), "get schema and count",
                    "select nspname as \"username\", count(1) as \"count\"\n" +
                            "from sys_catalog.sys_namespace a\n".replaceAll("sys", dataSource.getInstancePrefix()) +
                            "left join sys_catalog.sys_tables b \n".replaceAll("sys", dataSource.getInstancePrefix()) +
                            "on a.nspname=b.schemaname \n" +
                            "and lower(b.schemaname) not in ('pg_toast','pg_temp_1','pg_toast_temp_1','pg_catalog','information_schema') \n" +
                            "and lower(b.schemaname) not like 'pg_%temp_%'\n" +
                            "where lower(nspname) not in ('pg_toast','pg_temp_1','pg_toast_temp_1','pg_catalog','information_schema') \n" +
                            "and lower(nspname) not like 'pg_%temp_%'\n" +
                            "group by nspname");

            List<Map<String, Object>> innerSchemaInfos = new ArrayList<>();
            long count = 0L;
            for (Map<String, Object> schemaSingle : listSchema) {
                Map<String, Object> schemaInfo = new LinkedHashMap<>();
                schemaInfo.put("username", schemaSingle.get("username"));
                schemaInfo.put("charset", returnMap.get("charset"));
                schemaInfo.put("is_sys", returnMap.get("is_sys"));
                schemaInfo.put("count", Long.parseLong(schemaSingle.get("count").toString()));
                count += Long.parseLong(schemaSingle.get("count").toString());
                schemaInfo.put("def_dbo_name", schemaSingle.get("username"));
                schemaInfo.put("catalog_name", dbName);
                innerSchemaInfos.add(schemaInfo);
            }
            returnMap.put("innerSchemaInfos", innerSchemaInfos);
            returnMap.put("count", count);

            returnList.add(returnMap);
        }

        return returnList;
    }


    @Override
    public boolean isExecuteErrorTransactionInterrupted() {
        if (dataSource.isPg()) {
            return true;
        }
        return false;
    }

    @Override
    public String getFunctionOrProcedureOid(String objectName, String schemaName, List<String> args) {
        return "SELECT \n" +
                "    p.oid,\n" +
                "    p.pronargs,\n" +
                "    p.proargtypes,\n" +
                "    t.oid as toid,\n" +
                "    t.typname\n" +
                "FROM pg_catalog.pg_proc p\n".replaceAll("pg", dataSource.getInstancePrefix()) +
                "LEFT OUTER JOIN pg_catalog.pg_namespace d ON d.oid=p.pronamespace\n".replaceAll("pg", dataSource.getInstancePrefix()) +
                "LEFT OUTER JOIN pg_catalog.pg_type t ON t.oid = any(p.proargtypes)\n".replaceAll("pg", dataSource.getInstancePrefix()) +
                "WHERE lower(p.proname)=lower('" + objectName + "') and lower(d.nspname)=lower('" + schemaName + "') and pronargs=" + args.size();
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        boolean queryComment = isQueryComment();
        String query = "SELECT\n" +
                "  relname AS \"table_name\",\n" +
                "  attname as \"column_name\",\n" +
                "  attnum as \"column_no\",\n" +
                "  d.nspname AS \"schema_name\",\n" +
                "  d.nspname as \"username\",\n" +
                "  CURRENT_database() as \"catalog_name\",\n" +
                "  format_type(a.atttypid, a.atttypmod) as \"data_type\",\n" +
                "  format_type(a.atttypid, a.atttypmod) as \"displaycolumns\",\n" +
                "  '' as \"column_type\",\n" +
                "  '' as \"data_length\",\n" +
                "  '' as \"precision\",\n" +
                "  '' as \"numeric_scale\",\n" +
                "  case attnotnull\n" +
                "    when false then 1\n" +
                "    else 0\n" +
                "  end as \"nullable\",\n";
        if (queryComment) {
            query += "  description as \"comments\",\n";
        }
        query += "  0 as \"is_increment\",\n" +
                "  (\n" +
                "    case\n" +
                "      when (\n" +
                "        select\n" +
                "          contype\n" +
                "        from\n" +
                "          pg_catalog.pg_constraint c\n" +
                "        where\n" +
                "          a.attrelid = c.conrelid\n" +
                "          and a.attnum = any (c.conkey)\n" +
                "        limit\n" +
                "          1\n" +
                "      ) = 'p' then 1\n" +
                "      else 0\n" +
                "    end\n" +
                "  ) as \"is_primary_key\"\n" +
                "from\n" +
                "  pg_catalog.pg_attribute a\n" +
                "  left join pg_catalog.pg_class e on a.attrelid = e.oid\n" +
                "  left join pg_catalog.pg_namespace d on d.oid = e.relnamespace\n";
        if (queryComment) {
            query += "  left join pg_catalog.pg_description b on a.attrelid = b.objoid\n" +
                    "  and a.attnum = b.objsubid\n";
        }
        query += "  LEFT OUTER JOIN pg_catalog.pg_attrdef ad on (\n" +
                "    a.attrelid = ad.adrelid\n" +
                "    AND a.attnum = ad.adnum\n" +
                "  )\n" +
                "where\n" +
                "  a.attisdropped = false\n" +
                "  and lower(relname) not like '_pg%'\n" +
                "  and a.attnum > 0\n" +
                "  and e.relkind in ('r', 't', 'f', 'p')\n" +
                "  and d.nspname = '" + schemaName + "' and relname = '" + tableName + "'\n" +
                /*"  and d.nspname || '.' || relname in ('" + schemaName + "." + tableName + "')\n" +*/
                "order by\n" +
                "  attnum";
        return query;
    }

    @Override
    public boolean supportsCatalog() {
        return true;
    }
}
