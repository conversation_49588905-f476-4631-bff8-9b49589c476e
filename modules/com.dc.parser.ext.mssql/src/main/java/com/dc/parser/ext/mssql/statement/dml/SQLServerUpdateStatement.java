package com.dc.parser.ext.mssql.statement.dml;

import com.dc.parser.ext.mssql.statement.SQLServerStatement;
import com.dc.parser.model.segment.dml.hint.OptionHintSegment;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * SQLServer update statement.
 */
@Setter
public final class SQLServerUpdateStatement extends UpdateStatement implements SQLServerStatement {
    
    private WithSegment withSegment;
    
    private WithTableHintSegment withTableHintSegment;
    
    private OptionHintSegment optionHintSegment;
    
    private OutputSegment outputSegment;

    private TableSegment from;
    
    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get with table hint segment.
     *
     * @return with table hint segment.
     */
    public Optional<WithTableHintSegment> getWithTableHintSegment() {
        return Optional.ofNullable(withTableHintSegment);
    }
    
    /**
     * Get option hint segment.
     *
     * @return option hint segment.
     */
    public Optional<OptionHintSegment> getOptionHintSegment() {
        return Optional.ofNullable(optionHintSegment);
    }
    
    /**
     * Get output segment.
     *
     * @return output segment.
     */
    public Optional<OutputSegment> getOutputSegment() {
        return Optional.ofNullable(outputSegment);
    }

    @Override
    public Optional<TableSegment> getFrom() {
        return Optional.ofNullable(from);
    }
}
