
package com.dc.summer.ext.hsqldb.model;

import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;

import java.util.Arrays;

public class HSQLDialect extends GenericSQLDialect {

    public HSQLDialect() {
        super("HSQLDB", "hsqldb");
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        addDataTypes(
            Arrays.asList(
                "NCHAR",
                "NCLOB"));

        addSQLKeywords(
            Arrays.asList(
                "SYNONYM",
                "COMPARABLE",
                "ELSEIF",
                "END_EXEC",
                "FIRST_VALUE",
                "HANDLER",
                "ITERATE",
                "LAST_VALUE",
                "LEAD",
                "LEAVE",
                "LIKE_REGEX",
                "LOOP",
                "MAX_CARDINALITY",
                "NTH_VALUE",
                "NTILE",
                "OFFSET",
                "PERIOD",
                "RESIGNAL",
                "SIGNAL",
                "STACKED",
                "TRIM_ARRAY",
                "UNTIL",
                "VALUE"
            ));

        addFunctions(
            Arrays.asList(
                "CURRENT_CATALOG",
                "CURRENT_SCHEMA",
                "OCCURRENCES_REGEX",
                "POSITION_REGEX",
                "SUBSTRING_REGEX",
                "TRANSLATE_REGEX"
                ));
    }

    @Override
    public boolean supportsAliasInSelect() {
        return true;
    }

}
