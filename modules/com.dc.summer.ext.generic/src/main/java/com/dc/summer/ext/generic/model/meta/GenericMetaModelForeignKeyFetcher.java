
package com.dc.summer.ext.generic.model.meta;

import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.struct.rdb.DBSForeignKeyDeferability;
import com.dc.summer.model.struct.rdb.DBSForeignKeyModifyRule;

public interface GenericMetaModelForeignKeyFetcher {

    DBSForeignKeyModifyRule fetchUpdateRule(GenericMetaObject foreignKeyObject, JDBCResultSet dbResult);

    DBSForeignKeyModifyRule fetchDeleteRule(GenericMetaObject foreignKeyObject, JDBCResultSet dbResult);

    DBSForeignKeyDeferability fetchDeferability(GenericMetaObject foreignKeyObject, JDBCResultSet dbResult);

}
