
package com.dc.summer.ext.generic.model;

import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.code.NotNull;
import com.dc.summer.model.impl.jdbc.JDBCConstants;
import com.dc.summer.model.impl.jdbc.cache.JDBCBasicDataTypeCache;

/**
 * GenericDataTypeCache
 */
public class GenericDataTypeCache extends JDBCBasicDataTypeCache<GenericStructContainer, GenericDataType>
{

    private static final boolean IGNORE_NUMERIC_TYPES = false;

    public GenericDataTypeCache(GenericStructContainer owner) {
        super(owner);

        if (IGNORE_NUMERIC_TYPES) {
            // The code below was added for a long time ago. Perhaps it is still relevant for some databases.
            // But there are databases like Netezza or Snowflake, which actually contain such data types as NUMBER and NUMERIC.

            // Ignore abstract types. There can be multiple numeric types with the same name
            // but different scale/precision properties
            ignoredTypes.add("NUMBER");
            ignoredTypes.add("NUMERIC");
        }
    }

    @NotNull
    @Override
    protected GenericDataType makeDataType(@NotNull JDBCResultSet dbResult, String name, int valueType) {
        return new GenericDataType(
                owner,
                valueType,
                name,
                JDBCUtils.safeGetString(dbResult, JDBCConstants.LOCAL_TYPE_NAME),
                JDBCUtils.safeGetBoolean(dbResult, JDBCConstants.UNSIGNED_ATTRIBUTE),
                JDBCUtils.safeGetInt(dbResult, JDBCConstants.SEARCHABLE) != 0,
                JDBCUtils.safeGetInt(dbResult, JDBCConstants.PRECISION),
                JDBCUtils.safeGetInt(dbResult, JDBCConstants.MINIMUM_SCALE),
                JDBCUtils.safeGetInt(dbResult, JDBCConstants.MAXIMUM_SCALE));
    }
}
