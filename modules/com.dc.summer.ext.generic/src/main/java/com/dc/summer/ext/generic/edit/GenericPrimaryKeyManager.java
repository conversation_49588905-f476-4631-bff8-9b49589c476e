
package com.dc.summer.ext.generic.edit;

import com.dc.summer.ext.generic.GenericConstants;
import com.dc.summer.ext.generic.model.*;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.impl.sql.edit.struct.SQLConstraintManager;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.*;

import java.util.Map;

/**
 * Generic constraint manager
 */
public class GenericPrimaryKeyManager extends SQLConstraintManager<GenericUniqueKey, GenericTableBase> {

    @Nullable
    @Override
    public DBSObjectCache<? extends DBSObject, GenericUniqueKey> getObjectsCache(GenericUniqueKey object)
    {
        return object.getParentObject().getContainer().getConstraintKeysCache();
    }

    @Override
    public boolean canCreateObject(Object container) {
        return (container instanceof GenericTable)
            && (!(((GenericTable) container).getDataSource().getInfo() instanceof GenericDataSourceInfo) || ((GenericDataSourceInfo) ((GenericTable) container).getDataSource().getInfo()).supportsTableConstraints())
            && GenericUtils.canAlterTable((GenericTable) container);
    }

    @Override
    public boolean canEditObject(GenericUniqueKey object) {
        return GenericUtils.canAlterTable(object);
    }

    @Override
    public boolean canDeleteObject(GenericUniqueKey object) {
        return GenericUtils.canAlterTable(object);
    }

    @Override
    protected GenericUniqueKey createDatabaseObject(
            DBRProgressMonitor monitor, DBECommandContext context, final Object container,
            Object from, Map<String, Object> options)
    {
        GenericTableBase tableBase = (GenericTableBase)container;
        return tableBase.getDataSource().getMetaModel().createConstraintImpl(
            tableBase,
            GenericConstants.BASE_CONSTRAINT_NAME,
            DBSEntityConstraintType.PRIMARY_KEY,
            null,
            false);
    }

    @Override
    protected boolean isLegacyConstraintsSyntax(GenericTableBase owner) {
        return GenericUtils.isLegacySQLDialect(owner);
    }
}
