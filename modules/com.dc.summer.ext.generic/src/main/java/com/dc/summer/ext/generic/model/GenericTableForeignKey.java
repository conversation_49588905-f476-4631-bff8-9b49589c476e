
package com.dc.summer.ext.generic.model;

import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.struct.DBSEntityReferrer;
import com.dc.summer.model.struct.rdb.DBSForeignKeyDeferability;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableForeignKey;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttributeRef;
import com.dc.summer.model.struct.rdb.DBSForeignKeyModifyRule;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * GenericTableForeignKey
 */
public class GenericTableForeignKey extends JDBCTableForeignKey<GenericTableBase, DBSEntityReferrer> {
    private static final Log log = Log.getLog(GenericTableForeignKey.class);

    private DBSForeignKeyDeferability deferability;
    private List<GenericTableForeignKeyColumnTable> columns;

    public GenericTableForeignKey(
        GenericTableBase table,
        String name,
        @Nullable String remarks,
        DBSEntityReferrer referencedKey,
        DBSForeignKeyModifyRule deleteRule,
        DBSForeignKeyModifyRule updateRule,
        DBSForeignKeyDeferability deferability,
        boolean persisted) {
        super(table, name, remarks, referencedKey, deleteRule, updateRule, persisted);
        this.deferability = deferability;
    }

    @NotNull
    @Override
    public GenericDataSource getDataSource() {
        return getTable().getDataSource();
    }

    @Property(viewable = true, order = 7)
    public DBSForeignKeyDeferability getDeferability() {
        return deferability;
    }

    public void setDeferability(DBSForeignKeyDeferability deferability) {
        this.deferability = deferability;
    }

    @Override
    public List<GenericTableForeignKeyColumnTable> getAttributeReferences(DBRProgressMonitor monitor) {
        return columns;
    }

    public void addColumn(GenericTableForeignKeyColumnTable column) {
        if (columns == null) {
            columns = new ArrayList<>();
        }
        this.columns.add(column);
    }

    void setColumns(DBRProgressMonitor monitor, List<GenericTableForeignKeyColumnTable> columns) {
        this.columns = columns;
        final List<? extends DBSEntityAttributeRef> refColumns;
        try {
            refColumns = referencedKey.getAttributeReferences(monitor);
        } catch (DBException e) {
            log.error("Error getting referenced key columns", e);
            return;
        }
        if (refColumns != null && this.columns.size() > refColumns.size()) {
            // [JDBC: Progress bug. All FK columns are duplicated]
            for (int i = 0; i < this.columns.size(); ) {
                boolean duplicate = false;
                String colName = this.columns.get(i).getName();
                for (int k = i + 1; k < this.columns.size(); k++) {
                    String colName2 = this.columns.get(k).getName();
                    if (CommonUtils.equalObjects(colName, colName2)) {
                        this.columns.remove(k);
                        duplicate = true;
                        break;
                    }
                }
                if (!duplicate) {
                    i++;
                }
            }
        }
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getTable().getCatalog(),
            getTable().getSchema(),
            getTable(),
            this);
    }
}
