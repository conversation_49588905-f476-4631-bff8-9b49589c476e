
package com.dc.summer.ext.generic.model.meta;

import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.utils.CommonUtils;

/**
 * Meta column mapping
 */
public class GenericMetaColumn {

    private final String id;
    private final String columnName;
    private final Integer columnIndex;
    private final boolean supported;

    public GenericMetaColumn(IConfigurationElement cfg)
    {
        this.id = cfg.getAttribute("id");
        this.columnName = cfg.getAttribute("name");
        String indexStr = cfg.getAttribute("index");
        if (!CommonUtils.isEmpty(indexStr)) {
            this.columnIndex = Integer.valueOf(indexStr);
        } else {
            this.columnIndex = null;
        }
        String supportedStr = cfg.getAttribute("supported");
        this.supported = !"false".equals(supportedStr);
    }

    public String getId()
    {
        return id;
    }

    public String getColumnName()
    {
        return columnName;
    }

    public int getColumnIndex()
    {
        return columnIndex;
    }

    public boolean isSupported()
    {
        return supported;
    }

    public Object getColumnIdentifier()
    {
        return columnIndex == null ? columnName : columnIndex;
    }
}
