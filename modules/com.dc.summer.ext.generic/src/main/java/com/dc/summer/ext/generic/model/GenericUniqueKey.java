
package com.dc.summer.ext.generic.model;

import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * GenericTableConstraint
 */
public class GenericUniqueKey extends GenericTableConstraint {
    private List<GenericTableConstraintColumn> columns;

    public GenericUniqueKey(GenericTableBase table, String name, @Nullable String remarks, DBSEntityConstraintType constraintType, boolean persisted) {
        super(table, name, remarks, constraintType, persisted);
    }

    /**
     * Copy constructor
     *
     * @param constraint
     */
    GenericUniqueKey(GenericUniqueKey constraint) {
        super(constraint.getTable(), constraint.getName(), constraint.getDescription(), constraint.getConstraintType(), constraint.isPersisted());
        if (constraint.columns != null) {
            this.columns = new ArrayList<>(constraint.columns.size());
            for (GenericTableConstraintColumn sourceColumn : constraint.columns) {
                this.columns.add(new GenericTableConstraintColumn(this, sourceColumn));
            }
        }
    }

    @Override
    public List<GenericTableConstraintColumn> getAttributeReferences(DBRProgressMonitor monitor) {
        return columns;
    }

    public void addColumn(GenericTableConstraintColumn column) {
        if (columns == null) {
            columns = new ArrayList<>();
        }
        this.columns.add(column);
    }

    public void setColumns(List<GenericTableConstraintColumn> columns) {
        this.columns = columns;
        if (!CommonUtils.isEmpty(this.columns) && this.columns.size() > 1) {
            columns.sort(Comparator.comparingInt(GenericTableConstraintColumn::getOrdinalPosition));
        }
    }

    public boolean hasColumn(GenericTableColumn column) {
        if (this.columns != null) {
            for (GenericTableConstraintColumn constColumn : columns) {
                if (constColumn.getAttribute() == column) {
                    return true;
                }
            }
        }
        return false;
    }
}