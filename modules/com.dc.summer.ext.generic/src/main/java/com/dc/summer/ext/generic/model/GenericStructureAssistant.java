
package com.dc.summer.ext.generic.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.GenericConstants;
import com.dc.summer.ext.generic.model.meta.GenericMetaObject;
import com.dc.summer.model.DBPIdentifierCase;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCStructureAssistant;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.struct.AbstractObjectReference;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectReference;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.code.NotNull;
import com.dc.summer.model.impl.jdbc.JDBCConstants;
import com.dc.summer.model.impl.struct.RelationalObjectType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.util.List;

/**
 * GenericStructureAssistant
 */
public class GenericStructureAssistant extends JDBCStructureAssistant<GenericExecutionContext> {
    private final GenericDataSource dataSource;

    GenericStructureAssistant(GenericDataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    @Override
    protected GenericDataSource getDataSource()
    {
        return dataSource;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes()
    {
        return new DBSObjectType[] {
            RelationalObjectType.TYPE_TABLE,
            RelationalObjectType.TYPE_PROCEDURE
            };
    }

    public DBSObjectType[] getHyperlinkObjectTypes()
    {
        return getSupportedObjectTypes();
    }

    @Override
    public DBSObjectType[] getAutoCompleteObjectTypes()
    {
        return getSupportedObjectTypes();
    }

    @Override
    protected void findObjectsByMask(@NotNull GenericExecutionContext executionContext, @NotNull JDBCSession session,
                                     @NotNull DBSObjectType objectType, @NotNull ObjectsSearchParams params,
                                     @NotNull List<DBSObjectReference> references) throws DBException, SQLException {
        DBSObject parentObject = params.getParentObject();
        boolean globalSearch = params.isGlobalSearch();
        String objectNameMask = params.getMask();
        GenericSchema schema = parentObject instanceof GenericSchema ? (GenericSchema)parentObject : (params.isGlobalSearch() ? null : executionContext.getDefaultSchema());
        GenericCatalog catalog = parentObject instanceof GenericCatalog ? (GenericCatalog)parentObject :
                schema == null ? (globalSearch ? null : executionContext.getDefaultCatalog()) : schema.getCatalog();

        final GenericDataSource dataSource = getDataSource();
        DBPIdentifierCase convertCase = params.isCaseSensitive() ? dataSource.getSQLDialect().storesQuotedCase() : dataSource.getSQLDialect().storesUnquotedCase();
        objectNameMask = convertCase.transform(objectNameMask);

        if (objectType == RelationalObjectType.TYPE_TABLE) {
            findTablesByMask(session, catalog, schema, objectNameMask, params.getMaxResults(), references);
        } else if (objectType == RelationalObjectType.TYPE_PROCEDURE) {
            findProceduresByMask(session, catalog, schema, objectNameMask, params.getMaxResults(), references);
        }
    }

    private void findTablesByMask(JDBCSession session, GenericCatalog catalog, GenericSchema schema, String tableNameMask, int maxResults, List<DBSObjectReference> objects)
        throws SQLException, DBException
    {
        final GenericMetaObject tableObject = getDataSource().getMetaObject(GenericConstants.OBJECT_TABLE);
        final DBRProgressMonitor monitor = session.getProgressMonitor();
        try (JDBCResultSet dbResult = session.getMetaData().getTables(
            catalog == null ? null : catalog.getName(),
            schema == null ? null : schema.getName(),
            tableNameMask,
            null)) {
            while (dbResult.next()) {
                if (monitor.isCanceled()) {
                    break;
                }
                String catalogName = GenericUtils.safeGetStringTrimmed(tableObject, dbResult, JDBCConstants.TABLE_CAT);
                String schemaName = GenericUtils.safeGetStringTrimmed(tableObject, dbResult, JDBCConstants.TABLE_SCHEM);
                String tableName = GenericUtils.safeGetStringTrimmed(tableObject, dbResult, JDBCConstants.TABLE_NAME);
                if (CommonUtils.isEmpty(tableName)) {
                    continue;
                }
                objects.add(new TableReference(
                    findContainer(session.getProgressMonitor(), catalog, schema, catalogName, schemaName),
                    tableName,
                    GenericUtils.safeGetString(tableObject, dbResult, JDBCConstants.REMARKS)));
                if (objects.size() >= maxResults) {
                    break;
                }
            }
        }
    }

    private void findProceduresByMask(JDBCSession session, GenericCatalog catalog, GenericSchema schema, String procNameMask, int maxResults, List<DBSObjectReference> objects)
        throws SQLException, DBException
    {
        final GenericMetaObject procObject = getDataSource().getMetaObject(GenericConstants.OBJECT_PROCEDURE);
        DBRProgressMonitor monitor = session.getProgressMonitor();
        try (JDBCResultSet dbResult = session.getMetaData().getProcedures(
            catalog == null ? null : catalog.getName(),
            schema == null ? null : JDBCUtils.escapeWildCards(session, schema.getName()),
            procNameMask)) {
            while (dbResult.next()) {
                if (monitor.isCanceled()) {
                    break;
                }
                String catalogName = GenericUtils.safeGetStringTrimmed(procObject, dbResult, JDBCConstants.PROCEDURE_CAT);
                String schemaName = GenericUtils.safeGetStringTrimmed(procObject, dbResult, JDBCConstants.PROCEDURE_SCHEM);
                String procName = GenericUtils.safeGetStringTrimmed(procObject, dbResult, JDBCConstants.PROCEDURE_NAME);
                String uniqueName = GenericUtils.safeGetStringTrimmed(procObject, dbResult, JDBCConstants.SPECIFIC_NAME);
                if (CommonUtils.isEmpty(procName)) {
                    continue;
                }
                if (CommonUtils.isEmpty(uniqueName)) {
                    uniqueName = procName;
                }
                // Some driver return specific name for regular name
                procName = GenericUtils.normalizeProcedureName(procName);

                objects.add(new ProcedureReference(
                    findContainer(session.getProgressMonitor(), catalog, schema, catalogName, schemaName),
                    catalogName,
                    procName,
                    uniqueName));
                if (objects.size() >= maxResults) {
                    break;
                }
            }
        }
    }

    private GenericStructContainer findContainer(DBRProgressMonitor monitor, GenericCatalog parentCatalog, GenericSchema parentSchema, String catalogName, String schemaName) throws DBException
    {
        GenericCatalog tableCatalog = parentCatalog != null ? parentCatalog : CommonUtils.isEmpty(catalogName) ? null : dataSource.getCatalog(catalogName);
        if (tableCatalog == null && CommonUtils.isEmpty(catalogName) && !CommonUtils.isEmpty(dataSource.getCatalogs()) && dataSource.getCatalogs().size() == 1) {
            // there is only one catalog - let's use it (PostgreSQL)
            tableCatalog = dataSource.getCatalogs().iterator().next();
        }
        GenericSchema tableSchema = parentSchema != null ?
            parentSchema :
            CommonUtils.isEmpty(schemaName) ? null :
                tableCatalog == null ? dataSource.getSchema(schemaName) : tableCatalog.getSchema(monitor, schemaName);
        return tableSchema != null ? tableSchema : tableCatalog != null ? tableCatalog : dataSource;
    }

    private abstract static class ObjectReference extends AbstractObjectReference<DBSObject> {

        ObjectReference(GenericStructContainer container, String name, String description, Class<?> objectClass, DBSObjectType type)
        {
            super(name, container, description, objectClass, type);
        }

        @Override
        public GenericStructContainer getContainer()
        {
            return (GenericStructContainer)super.getContainer();
        }
    }

    private class TableReference extends ObjectReference {

        private TableReference(GenericStructContainer container, String tableName, String description)
        {
            super(container, tableName, description, GenericTable.class, RelationalObjectType.TYPE_TABLE);
        }

        @Override
        public DBSObject resolveObject(DBRProgressMonitor monitor) throws DBException
        {
            GenericTableBase table = getContainer().getTable(monitor, getName());
            if (table == null) {
                throw new DBException("Can't find table '" + getName() + "' in '" + DBUtils.getFullQualifiedName(dataSource, getContainer()) + "'");
            }
            return table;
        }
    }

    private class ProcedureReference extends ObjectReference {

        private final String catalogName;
        private String uniqueName;
        private ProcedureReference(GenericStructContainer container, String catalogName, String procedureName, String uniqueName)
        {
            super(container, procedureName, null, GenericProcedure.class, RelationalObjectType.TYPE_PROCEDURE);
            this.catalogName = catalogName;
            this.uniqueName = uniqueName;
        }

        @Override
        public DBSObject resolveObject(DBRProgressMonitor monitor) throws DBException
        {
            GenericProcedure procedure = null;
            if (getContainer() instanceof GenericSchema) {
                // Try to use catalog name as package name (Oracle)
                if (!CommonUtils.isEmpty(catalogName)) {
                    GenericPackage procPackage = ((GenericSchema)getContainer()).getPackage(monitor, catalogName);
                    if (procPackage != null) {
                        procedure = procPackage.getProcedure(monitor, uniqueName);
                    }
                }
            }
            if (procedure == null) {
                procedure = getContainer().getProcedure(monitor, uniqueName);
            }
            if (procedure == null) {
                throw new DBException("Can't find procedure '" + getName() + "' (" + uniqueName + ")" + "' in '" + DBUtils.getFullQualifiedName(dataSource, getContainer()) + "'");
            }
            return procedure;
        }
    }
}
