
package com.dc.summer.ext.generic.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSTrigger;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.struct.DBSObject;

import java.util.Map;

/**
 * GenericTrigger
 */
public abstract class GenericTrigger<OWNER extends DBSObject> implements DBSTrigger, GenericScriptObject, DBPNamedObject2
{
    @NotNull
    private final OWNER container;
    private String name;
    private String description;
    protected String source;

    public GenericTrigger(@NotNull OWNER container, String name, String description) {
        this.container = container;
        this.name = name;
        this.description = description;
    }

    @NotNull
    @Override
    @Property(viewable = true, editable = true, order = 1)
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Nullable
    @Override
    @Property(viewable = true, length = PropertyLength.MULTILINE, order = 100)
    public String getDescription()
    {
        return description;
    }

    protected void setDescription(String description)
    {
        this.description = description;
    }

    @Override
    public boolean isPersisted()
    {
        return true;
    }

    @NotNull
    public OWNER getContainer() {
        return container;
    }

    @Override
    public OWNER getParentObject()
    {
        return container;
    }

    @NotNull
    @Override
    public GenericDataSource getDataSource()
    {
        return (GenericDataSource) container.getDataSource();
    }

    @Override
    @Property(hidden = true, editable = true, updatable = true, order = -1)
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException
    {
        if (source == null) {
            source = getDataSource().getMetaModel().getTriggerDDL(monitor, this);
        }
        return source;
    }

    public void setSource(String sourceText) throws DBException
    {
        source = sourceText;
    }

}
