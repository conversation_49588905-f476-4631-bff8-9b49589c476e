
package com.dc.summer.ext.db2.i.model;

import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericUniqueKey;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.rdb.DBSTableCheckConstraint;

public class DB2IConstraint extends GenericUnique<PERSON>ey implements DBSTableCheckConstraint {

    private String checkClause;

    DB2IConstraint(GenericTableBase table, String name, @Nullable String remarks, DBSEntityConstraintType constraintType, boolean persisted, @Nullable String checkClause) {
        super(table, name, remarks, constraintType, persisted);
        this.checkClause = checkClause;
    }

    @Override
    @Nullable
    @Property(viewable = true, order = 4)
    public String getCheckConstraintDefinition() {
        return checkClause;
    }

    @Override
    public void setCheckConstraintDefinition(String expression) {
        this.checkClause = expression;
    }
}
