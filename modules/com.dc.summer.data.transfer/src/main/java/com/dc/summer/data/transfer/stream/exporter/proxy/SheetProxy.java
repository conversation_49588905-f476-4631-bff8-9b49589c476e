package com.dc.summer.data.transfer.stream.exporter.proxy;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.lang.reflect.Method;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

public class SheetProxy extends BytesWrittenPHandlerProxy<Sheet> {

    public SheetProxy(Sheet rows, AtomicLong bytesWritten) {
        super(rows, bytesWritten);
    }

    @Override
    public Set<Class<?>> getClassSet() {
        return Set.of(Sheet.class);
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {

        Object result = method.invoke(getT(), args);

        if (method.getName().equals("createRow")) {
            result = new RowProxy((Row) result, getBytesWritten()).getInstance();
        }

        return result;
    }

}
