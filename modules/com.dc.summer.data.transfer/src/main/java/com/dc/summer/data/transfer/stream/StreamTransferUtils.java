

package com.dc.summer.data.transfer.stream;

import com.dc.summer.Log;
import com.dc.utils.CommonUtils;

import java.util.Map;

/**
 * Stream transfer serialize
 */
public class StreamTransferUtils {

    private static final Log log = Log.getLog(StreamTransferUtils.class);

    private static final String DEF_DELIMITER = ",";

    public static String getDelimiterString(Map<String, Object> properties, String propName) {
        String delimString = CommonUtils.toString(properties.get(propName), null);
        if (delimString == null) {
            return DEF_DELIMITER;
        } else {
            return delimString
                    .replace("\\t", "\t")
                    .replace("\\n", "\n")
                    .replace("\\r", "\r");
        }
    }
}
