package com.dc.summer.data.transfer.stream.exporter.proxy;

import org.apache.poi.ss.usermodel.CellBase;
import org.apache.poi.ss.usermodel.Row;

import java.lang.reflect.Method;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

public class RowProxy extends BytesWrittenPHandlerProxy<Row> {

    protected RowProxy(Row cells, AtomicLong bytesWritten) {
        super(cells, bytesWritten);
    }

    @Override
    public Set<Class<?>> getClassSet() {
        return Set.of(Row.class);
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {

        Object result = method.invoke(getT(), args);

        if (method.getName().equals("createCell")) {
            result = new CellProxy((CellBase) result, getBytesWritten(), args, getT()).getInstance();
        }

        return result;
    }
}
