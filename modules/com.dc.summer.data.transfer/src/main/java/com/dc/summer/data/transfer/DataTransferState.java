
package com.dc.summer.data.transfer;

import java.util.ArrayList;
import java.util.List;

/**
 * DataTransferState
 */
public class DataTransferState {

    private List<Throwable> loadErrors = new ArrayList<>();

    public List<Throwable> getLoadErrors() {
        return loadErrors;
    }

    public void addError(Throwable error) {
        loadErrors.add(error);
    }

    public boolean hasErrors() {
        return !loadErrors.isEmpty();
    }
}
