

package com.dc.summer.data.transfer.registry;

import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.impl.AbstractDescriptor;
import com.dc.summer.model.impl.PropertyDescriptor;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.data.transfer.IDataTransferAttributeTransformer;

import java.util.ArrayList;
import java.util.List;

/**
 * DataTransferAttributeTransformerDescriptor
 */
public class DataTransferAttributeTransformerDescriptor extends AbstractDescriptor {
    @NotNull
    private final String id;
    @NotNull
    private final String name;
    private final String description;
    private final ObjectType implType;
    private final List<DBPPropertyDescriptor> properties = new ArrayList<>();

    public DataTransferAttributeTransformerDescriptor(IConfigurationElement config) {
        super(config);

        this.id = config.getAttribute("id");
        this.name = config.getAttribute("label");
        this.description = config.getAttribute("description");
        this.implType = new ObjectType(config.getAttribute("class"));

        for (IConfigurationElement prop : config.getChildren(PropertyDescriptor.TAG_PROPERTY_GROUP)) {
            properties.addAll(PropertyDescriptor.extractProperties(prop));
        }
    }

    @NotNull
    public String getId() {
        return id;
    }

    @NotNull
    public String getName() {
        return name;
    }

    @Nullable
    public String getDescription() {
        return description;
    }

    @NotNull
    public List<DBPPropertyDescriptor> getProperties() {
        return properties;
    }

    public IDataTransferAttributeTransformer createTransformer() throws DBException
    {
        try {
            return implType.getObjectClass(IDataTransferAttributeTransformer.class)
                .getDeclaredConstructor().newInstance();
        } catch (Throwable e) {
            throw new DBException("Can't create attribute transformer instance", e);
        }
    }

    @Override
    public String toString() {
        return id;
    }
}
