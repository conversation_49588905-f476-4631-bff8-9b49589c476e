
package com.dc.summer.data.transfer;

import com.dc.summer.model.runtime.DBRRunnableContext;

import java.util.Map;

/**
 * Transfer settings
 */
public interface IDataTransferSettings {

    void loadSettings(DBRRunnableContext runnableContext, DataTransferSettings dataTransferSettings, Map<String, Object> settings);

    void saveSettings(Map<String, Object> settings);

    String getSettingsSummary();

}
