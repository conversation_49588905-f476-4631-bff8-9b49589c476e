

package com.dc.summer.data.transfer.registry;

import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.impl.AbstractDescriptor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.data.transfer.IDataTransferNode;
import com.dc.summer.data.transfer.IDataTransferSettings;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

import java.util.*;

/**
 * DataTransferNodeDescriptor
 */
public class DataTransferNodeDescriptor extends AbstractDescriptor
{
    public enum NodeType {
        PRODUCER,
        CONSUMER
    }

    @NotNull
    private final String id;
    @NotNull
    private final String name;
    private final String description;
    private final NodeType nodeType;
    private final ObjectType implType;
    private final ObjectType settingsType;
    private final boolean advanced;
    private final List<ObjectType> sourceTypes = new ArrayList<>();
    private final List<DataTransferProcessorDescriptor> processors = new ArrayList<>();

    public DataTransferNodeDescriptor(IConfigurationElement config)
    {
        super(config);

        this.id = config.getAttribute("id");
        this.name = config.getAttribute("label");
        this.description = config.getAttribute("description");
        this.nodeType = CommonUtils.valueOf(NodeType.class, config.getAttribute("type").toUpperCase(Locale.ENGLISH), NodeType.PRODUCER);
        this.implType = new ObjectType(config.getAttribute("class"));
        this.settingsType = new ObjectType(config.getAttribute("settings"));
        this.advanced = CommonUtils.toBoolean(config.getAttribute("advanced"));

        for (IConfigurationElement typeCfg : ArrayUtils.safeArray(config.getChildren("sourceType"))) {
            sourceTypes.add(new ObjectType(typeCfg.getAttribute("type")));
        }

        loadNodeConfigurations(config);
    }

    void loadNodeConfigurations(IConfigurationElement config) {
        List<DataTransferProcessorDescriptor> procList = new ArrayList<>();
        for (IConfigurationElement processorConfig : ArrayUtils.safeArray(config.getChildren("processor"))) {
            procList.add(new DataTransferProcessorDescriptor(this, processorConfig));
        }
        procList.sort(Comparator.comparing(DataTransferProcessorDescriptor::getName));
        this.processors.addAll(procList);
    }

    @NotNull
    public String getId()
    {
        return id;
    }

    @NotNull
    public String getName()
    {
        return name;
    }

    public String getDescription()
    {
        return description;
    }

    public boolean isAdvancedNode() {
        return advanced;
    }

    public Class<? extends IDataTransferNode> getNodeClass()
    {
        return implType.getObjectClass(IDataTransferNode.class);
    }

    public IDataTransferNode<?> createNode() throws DBException
    {
        implType.checkObjectClass(IDataTransferNode.class);
        try {
            return implType.getObjectClass(IDataTransferNode.class).getDeclaredConstructor().newInstance();
        } catch (Throwable e) {
            throw new DBException("Can't create data transformer node", e);
        }
    }

    public IDataTransferSettings createSettings() throws DBException
    {
        settingsType.checkObjectClass(IDataTransferSettings.class);
        try {
            return settingsType.getObjectClass(IDataTransferSettings.class).getDeclaredConstructor().newInstance();
        } catch (Throwable e) {
            throw new DBException("Can't create node settings", e);
        }
    }

    public NodeType getNodeType()
    {
        return nodeType;
    }

    public boolean appliesToType(Class<?> objectType)
    {
        if (!sourceTypes.isEmpty()) {
            for (ObjectType sourceType : sourceTypes) {
                if (sourceType.matchesType(objectType)) {
                    return true;
                }
            }
        }
        for (DataTransferProcessorDescriptor processor : processors) {
            if (processor.appliesToType(objectType)) {
                return true;
            }
        }
        return false;
    }

    public boolean hasProcessors() {
        return !processors.isEmpty();
    }

    public DataTransferProcessorDescriptor[] getProcessors() {
        return processors.toArray(new DataTransferProcessorDescriptor[0]);
    }

    /**
     * Returns data exporter which supports ALL specified object types
     * @param sourceObjects object types
     * @return list of editors
     */
    public List<DataTransferProcessorDescriptor> getAvailableProcessors(Collection<DBSObject> sourceObjects) {
        List<DataTransferProcessorDescriptor> editors = new ArrayList<>();
        for (DataTransferProcessorDescriptor descriptor : processors) {
            boolean supports = true;
            for (DBSObject sourceObject : sourceObjects) {
                if (!descriptor.appliesToType(sourceObject.getClass())) {
                    boolean adapts = false;
                    if (sourceObject instanceof IAdaptable) {
                        if (descriptor.adaptsToType((IAdaptable)sourceObject)) {
                            adapts = true;
                        }
                    }
                    if (!adapts) {
                        supports = false;
                        break;
                    }
                }
            }
            if (supports) {
                editors.add(descriptor);
            }
        }
        return editors;
    }

    public List<DataTransferProcessorDescriptor> getAvailableProcessors(Class<?> objectType) {
        List<DataTransferProcessorDescriptor> procList = new ArrayList<>();
        for (DataTransferProcessorDescriptor descriptor : this.processors) {
            if (descriptor.appliesToType(objectType)) {
                procList.add(descriptor);
            }
        }
        return procList;
    }

    public DataTransferProcessorDescriptor getProcessor(String id)
    {
        for (DataTransferProcessorDescriptor descriptor : processors) {
            if (descriptor.getId().equals(id)) {
                return descriptor;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return id;
    }
}
