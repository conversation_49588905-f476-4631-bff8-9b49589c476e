import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;

public class GenerateAntlr4 {

    public static void main(String[] args) {

        System.out.println("Executing GenerateAntlr4.java");

        String sourceDir = "antlr4";

        File sourceFile = new File(sourceDir);
        System.out.println(sourceFile + " exists: " + sourceFile.exists());
        if (sourceFile.exists()) {
            deleteRecursively(sourceFile);
        }

        String branch;
        if (args.length == 0 || args[0] == null || args[0].isEmpty()) {
            System.out.println("Get branch by git.");
            branch = getBranch();
        } else {
            System.out.println("Get branch by arg.");
            branch = args[0];
        }

        System.out.println("Current branch: " + branch);

        gitClone(branch);

        for (File file : sourceFile.listFiles()) {
            if (file.isDirectory()) {
                String moduleDir = "modules/com.dc.parser.ext." + file.toPath().getFileName();
                if (new File(moduleDir).exists()) {
                    String targetDir = moduleDir + "/src/main/resources/antlr4/imports";
                    File targetFile = new File(targetDir);
                    System.out.println(targetFile + " exists: " + targetFile.exists());
                    if (targetFile.exists()) {
                        deleteRecursively(targetFile);
                    }
                    targetFile.mkdirs();
                    for (File listFile : file.listFiles()) {
                        File renameFile = targetFile.toPath().resolve(listFile.toPath().getFileName()).toFile();
                        System.out.println("Generate antlr4: " + renameFile);
                        listFile.renameTo(renameFile);
                    }
                }
            }
        }

        System.out.println(sourceFile + " exists: " + sourceFile.exists());
        if (sourceFile.exists()) {
            deleteRecursively(sourceFile);
        }
    }

    public static boolean deleteRecursively(File file) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File subFile : files) {
                    deleteRecursively(subFile);
                }
            }
        }
        return file.delete();
    }

    public static void gitClone(String branch) {
        // 要执行的 Git clone 命令
        String[] command = {
                "git",
                "clone",
                "-b",
                branch,
                "*********************:614ac62ae43534781c03c36e/DC-Java/antlr4.git"
        };

        // 设置目标目录（可选）
//        String targetDirectory = "/path/to/clone/directory";

        try {
            Arrays.stream(command).forEach(s -> System.out.print(s + " "));
            System.out.println();
            // 使用 ProcessBuilder 构造命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);

            // 设置工作目录（可选）
//            processBuilder.directory(new java.io.File(targetDirectory));

            // 启动进程
            Process process = processBuilder.start();

            // 捕获标准输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            System.out.println("Git command output:");
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }

            // 捕获错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            System.out.println("Git command errors:");
            while ((line = errorReader.readLine()) != null) {
                System.err.println(line);
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();
            System.out.println("Git command exited with code: " + exitCode);

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }


    public static String getBranch() {
        try {
            // 创建 ProcessBuilder 来执行命令
            ProcessBuilder processBuilder = new ProcessBuilder("git", "branch", "--show-current");
            // 设置工作目录（需要在 Git 仓库目录下执行）
//            processBuilder.directory(new java.io.File("/path/to/your/git/repo"));

            // 启动进程
            Process process = processBuilder.start();

            // 获取命令输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }

            // 等待进程结束
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                return result.toString();
            } else {
                System.err.println("Error: Unable to fetch the current branch. Exit code: " + exitCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
