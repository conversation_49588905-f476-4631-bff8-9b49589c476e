## Summer

为 DataCaptain 项目开发的数据库管理工具。

通过接口进行数据库连接、执行、结果集返回等操作，以及相应的业务功能。

## Building

### 前提
* Java（JDK）11或更高版本。
* Apache Maven 3.6+。
* Git 客户端。

### 编译
* 点击菜单栏 help >> Edit Custom ProPerties... 添加一行：
```
idea.max.intellisense.filesize=99999
```
* 点击 Maven >> summer >> 生命周期 >> compile

## Running

### 前提
Summer 需要 Java 才能运行。使用 Open JDK 11 版本进行执行。

### 运行 Jar
```
# 无 krb5 认证
java -jar com.dc.summer.default.boot-1.0-SNAPSHOT.jar
# 有 krb5 认证
java -jar com.dc.summer.default.boot-1.0-SNAPSHOT.jar --add-export java.security.jgss/sun.security.krb5=ALL-UNNAMED
```
### 启动 Application
* 点击菜单栏【运行】>>【编辑配置】>>【编辑配置模版】>> 选择 Spring Boot >> 虚拟机选项中添加：
```
--add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED --add-opens java.base/jdk.internal.perf=ALL-UNNAMED
```
* 修改启动项目的 application.yml 和 application-dev.yml，建议本地配置环境变量
```
// 8088 8088 换一个端口号
export java_env=dev
export dc_summer_port=8088
export dc_iceage_port=8008
export dc_proxy_port=8008
export logback=classpath:logback.xml
export log_level=DEBUG
```
* 重新生成 Spring Boot 启动器后启动。

## Git

### 提交规范

#### 规定格式

```
<type>(<scope>): <subject>
<!-- 空行 -->
<body>
<!-- 空行 -->
<footer>
```

| 字段       | 必须  | 说明                                                                                                                                              |
|----------| ----  |-------------------------------------------------------------------------------------------------------------------------------------------------|
| *\<type> | 是 | 提交类别（见下面列表）                                                                                                                                     |
| \<scope> | 否 | 用于说明 commit 影响的范围、模块，如果一次commit修改多个模块，建议拆分成多次commit。<br/>尽量使用已有的scope，使用驼峰命名，除具体的模块、组件名之外，可以使用 base 表示基础结构、框架相关的改动，用 misc 表示杂项改动，用 all 表示大范围重构。 |
| *\<subject> | 是 | commit 简短描述目的（不超过50个字符）                                                                                                                         |
| \<body>  | 否(建议填写) | 填写详细描述，主要描述改动之前的情况及修改动机，对于小的修改不作要求，但是重大需求、更新等必须添加body来作说明。                                                                                      |
| \<footer> | 否 | Breaking changes：指明是否产生了破坏性修改，涉及break changes的改动必须指明该项（版本升级、接口参数减少、接口删除、迁移等）。<br/>Closed issues：指明是否影响了某个问题（禅道BUG号、标题）。                         |

#### 提交类别

| 值         | 说明                                      |
|-----------|-----------------------------------------|
| feat      | 添加新功能                                   |
| fix       | 错误 BUG 修复                               |
| docs      | 仅仅修改了文档                                 |
| style     | 不影响代码含义的更改（空格、格式、逗号、分号等）                |
| refactor  | 既不修复错误也不添加功能的代码更改                       |
| pref      | 提高性能的代码更改                               |
| test      | 添加测试或者修改现有测试                            |
| chore     | 不修改src或者test的其余修改（构建流程、或者增加依赖库、辅助工具等）   |
| revert    | 还原以前的提交（git revert）                     |
| build     | 构造工具的或者外部依赖的改动（gulp、cocade、webpack，npm） |
| ci        | 与持续集成服务有关的改动（Jenkins、Gitlab CI、Circle）  |

#### 具体样例

- 详细描述
```
fix(parser): xxx 关键字引起的拆分不准确

 - 排查后发现，是 xxx 问题导致的，通过 xxx 手段进行了处理，修复了 xxx 问题。
 - 顺便添加了一些 xxx 功能的改进。
 ...

BREAKING CHANGE: 取消了拆分接口的 xxx 参数。

Closes 12345 abc数据库，执行 xxx，没有正确的执行！
```
- 简单描述
```
fix: xxx 关键字引起的拆分不准确
```

### 分支规范

#### 基本原则

- master 为保护分支，无法直接在master上进行代码修改和提交。
- ~~dev 为开发分支，编写好的代码通过dev分支，rebase到master分支。~~
- pull 更新项目默认使用 rebase（变基），不要使用 merge（合并）。

#### 分支名称

分支类型_发布时间_具体功能（feature_parser_20230117）

- 分支类型：feature、bugfix、refactor三种类型，即新功能开发、bug修复和代码重构
- 具体功能：使用下划线命名
- 发布时间：时间使用年月日进行命名

#### 版本标签

v主版本号.次版本号.修订号（v1.2.31）
- 主版本号：当你做了不兼容的 API 修改
- 次版本号：当你做了向下兼容的功能性新增
- 修订号：当你做了向下兼容的问题修正

v主版本号.次版本号.修订号-灰度版本号-次数（v2.0.0-alpha-1）
- 灰度版本号：alpha、beta等
- 次数：发布一次加 1

#### 定制分支

客户项目/分支规范（htsc/master）

- 客户项目：提出定制化需求的客户项目，单独使用一个文件夹
- 分支规范：以上所有的分支规则，都可以在客户项目文件下存在。

#### 具体样例

```
.
├── master
├── dev
├── feature_parser_20230117
├── bugfix_sql_console_20230118
├── refactor_work_order_20230119
├── v1.2.31
├── v1.3.1
├── v2.0.0-alpha-1
├── htsc
│   ├── master
│   ├── dev
│   ├── feature_parser_20230117
│   └── v1.2.31
└── ...
```

### 开发流程

#### 简单提交
- 适用于代码量少、代码逻辑简单、紧急修复等情况，流程如下：
1. checkout（签出）需要提交的分支 
2. pull（更新项目）拉取代码 
3. 代码的编写 xxx…… 
4. commit（提交） 
5. 根据【提交规范】填写提交备注 xxx…… 
6. commit and push（提交并推送） 
7. 点击 <u>master</u>
8. 根据【分支规范】填写分支名称 xxx…… 
9. push（推送）
- 第 6 步可以替换 commit（提交），就可以反复编写代码，提交多次记录。

#### 标准提交
适用于代码量大、代码逻辑复杂、周期较长等情况，流程如下：
1. checkout（签出）需要提交的分支 
2. pull（更新项目）拉取代码
3. new branch（新建分支）
4. 根据【分支规范】填写分支名称 xxx…… 
5. 代码的编写 xxx…… 
6. commit（提交） 
7. 根据【提交规范】填写提交备注 xxx…… 
8. commit and push（提交并推送） 
9. push（推送）
- 第 5、6、7、8、9 步可以重复，进行多次提交。

#### 内容总结
 - 简单提交，通过使用本地的 Git 记录，快速在远程仓库新建分支，一次性推送到远程分支，免去了构建本地分支的麻烦。
 - 标准提交，通过使用远程的 Git 记录，少量多次的的提交，免去了反复构建远程分支的麻烦。

### 合并流程

#### 简单提交
- 由于每次推送前都会拉取最新代码，所以不需要和需要提交的分支合并，直接在 Git 提合并请求即可。

#### 标准提交
- 由于使用的是新建分支，和需要提交的分支版本可能存在查询，需要先合并代码后，再去 Git 提合并请求即可。合并流程如下：
1. checkout（签出）新建的分支
2. 选择 origin 中的需要提交的分支
3. merge （合并）到当前分支
4. 如果存在冲突，请解决冲突
5. 如果存在变更，请提交代码
6. push（推送）

#### 其他情况
- 往某个版本优选提交记录，如：历史版本修复主线主线已经修复好的bug、定制化分支实现主线已经开发完的功能等等。流程如下：
1. checkout（签出）需要提交的分支、新建的分支
2. pull（更新项目）拉取代码
3. 打开 Git 日志，搜索指定的变更记录
4. 右键 cherry-pick（优选）
5. 如果存在冲突，请解决冲突
6. 如果存在变更，请提交代码
7. push（推送）

- 往某个版本合并指定分支代码，如：主线版本和定制化版本同时发布一个功能、主线版本和历史版本同时修复几个bug等等。流程如下：
1. checkout（签出）需要提交的分支、新建的分支
2. pull（更新项目）拉取代码
3. 选择 origin 中的指定分支
4. merge （合并）到当前分支
5. 如果存在冲突，请解决冲突
6. 如果存在变更，请提交代码
7. push（推送）

#### Git合并
- 目前为止使用的阿里云的码云，合并到主线、历史版本、定制分支均需要提交合并请求，无法通过本地进行合并。流程如下：
1. 打开项目码云网站
2. 点击【合并请求】
3. 点击【新建合并请求】
4. 来源是新建里的分支，目标是需要提交的分支
5. 检查【提交列表】和【文件改动】是否无误
6. 点击【确定】
- 合并完成后，即可进行打包。

## CI CD
### Continuous Integration（持续集成）
#### 手动打包
- 使用场景一般是某个现场、某个版本进行紧急修复，自动打包时间较长，则需要本地打包快速发出一个 Jar 包。流程如下：
1. 点击【Maven】
2. 选择 summer 
3. 选择 生命周期 
4. 右键【package】中的运行，修改内容：
```
package -Dxjar.password=data-captain -f pom.xml
```
5. 选择 运行配置
6. 双击新生成的【package】
#### 自动打包
- 使用场景为开发环境、测试环境自动部署使用，目前使用的 Jenkins 服务进行打包。流程如下：
1. 打开打包网站 <a>http://192.168.3.251:8080</a>
2. 选择 dc-summer 项目
3. 点击【Build With Parameters】
4. 根据提示信息选择要打包的版本、分支、项目。
5. 点击【Build】

### Continuous Deployment（持续部署）
#### 手动部署
- 使用场景一般是某些环境没有自动部署的配置，只能手动部署到新服务器上，进行使用。流程如下：
1. 使用 FTP 工具登陆远程服务器
2. 进入默认目录，一般在 /home/<USER>/nginx/www/html
3. 选择具体的服务文件夹，如 summer 是 dc-summer-pe/boot
4. 上传 Jar 包
5. 使用 SSH 工具登陆远程服务器。
6. 运行 ``` docker ps ```
7. 运行 ``` docker exec -it 【容器ID】 bash ```
8. 进入到具体的项目下，如 summer 是 dc-summer-pe/bin
9. 运行 ``` sh shutdown.sh && sh startup.sh ```
#### 自动部署
- 使用场景为开发环境、测试环境使用，目前使用部署工具进行。流程如下：
1. 打开部署网站 <a>http://192.168.3.135:8000</a>
2. 点击【部署】中的【部署】
3. 根据提示填写表单，其中 ID 为版本编号 5.2.4.904.5f1c23cbd0 ，例如：
```
dc-java-master新版本发布
打包分支：master
包含项目：com.dc:dc-summer-pe,com.dc:dc-iceage-pe,com.dc:dc-job-admin,com.dc:dc-job,com.dc:dc-broker,com.dc:dc-summer-pa
文件类型：tar.gz
版本类型：enterprise
版本编号：5.2.4.904.5f1c23cbd0
变更记录：
 xxx……
```
4. 点击【执行】
5. 部署过程中，不要关闭网站。

## Structure

### 目录

```
.
├── README.md
├── base                            // 基础包：全局通用或者产品包通用的一些工具，可以直接组装出便于开发的产品。
│   ├── com.dc.database.parser      // 解析聚合
│   ├── com.dc.database.summer      // 执行聚合
│   ├── com.dc.infra                // 解析工具
│   ├── com.dc.repository.mysql     // 持久层仓库：MybatisPlus、批量插入
│   ├── com.dc.repository.redis     // 缓存层仓库：普通存取、发布订阅
│   ├── com.dc.springboot.auth      // 接口权限功能：aksk
│   ├── com.dc.springboot.base      // 接口基础功能：密码加密、日志过滤、优雅停机、流过滤器
│   ├── com.dc.springboot.core      // 接口核心功能：API调用、核心组件、通用配置、外部扩展
│   ├── com.dc.springboot.zuul      // 废弃
│   ├── com.dc.test                 // 测试工具
│   ├── com.dc.utils                // 全局工具
│   ├── org.cugos.wkg               // 开源工具
│   └── pom.xml
├── drivers                 // 驱动包：下载了项目所需要的所有驱动文件，在生产环境，需要把这些文件，提前打到 docker 镜像中。
│   ├── maven               // 有三种方式：Maven 下载使用、指定本地 Jar、Repo 下载使用。
│   │   ├── maven-central
│   │   └── maven-public
│   └── drivers.xml         // 当前项目中使用过的驱动配置文件。
├── modules                             // 模块包：执行相关、解析相关的各种功能，不同的产品可以根据不同的模块自由组合。
│   ├── com.dc.parser.ext.exec          // 执行模块：调用解析的外部入口
│   ├── com.dc.parser.ext.*             // 扩展模块：解析相关的数据库。
│   ├── com.dc.parser.model             // 解析模块：核心的功能、流程、接口、实体等
│   ├── com.dc.summer.data.gis          // 位置模块：pg等有地理信息的，需要使用分析。
│   ├── com.dc.summer.data.transfer     // 传输模块：目前只用到导出
│   ├── com.dc.summer.exec              // 执行模块：调用执行的外部入口
│   ├── com.dc.summer.exec.pool         // 执行连接池：单连接改造成连接池的方式执行
│   ├── com.dc.summer.exec.unity        // 执行跨库SQL：一条SQL跨不同的数据库进行关联执行
│   ├── com.dc.summer.ext.*             // 扩展模块：执行相关的数据库。
│   ├── com.dc.summer.mockdata.engine   // 数据模拟：测试数据构建
│   ├── com.dc.summer.model             // 执行模型：核心的功能、流程、接口、实体等
│   ├── com.dc.summer.model.nosql       // 非关系型数据库模型
│   ├── com.dc.summer.model.sql         // 关系型数据库模型
│   ├── com.dc.summer.parser.*          // 解析模块：SQL 拆分、解析工具。
│   ├── com.dc.summer.registry          // 注册模块：基于 plugin.xml 进行注册。
│   ├── com.dc.summer.struct.compare    // 结构对比：不同数据库的对象结构进行差异对比
│   ├── com.dc.summer.tasks.native      // 任务模块
│   └── pom.xml
├── product                                     // 产品包：具体的产品应用，为了 DC 业务而衍生不同的功能。
│   ├── com.dc.broker                           // 代理 Pyhton Lib 库的执行应用
│   │   ├── com.dc.broker.backend               // 后端处理
│   │   ├── com.dc.broker.base                  // 基础功能
│   │   ├── com.dc.broker.boot[dc-broker]       // 默认启动器
│   │   ├── com.dc.broker.fronted               // 前端接收
│   │   └── pom.xml
│   ├── com.dc.iceage                           // DC 内部的执行应用，通过连接池的方式
│   │   ├── com.dc.iceage.base                  // 基础功能
│   │   ├── com.dc.iceage.boot[dc-iceage-pe]    // 主线启动器
│   │   ├── com.dc.iceage.parser                // 解析检查
│   │   ├── com.dc.iceage.proxy                 // 代理执行
│   │   └── pom.xml
│   ├── com.dc.job                              // XXL Job 改造的任务调度应用
│   │   ├── com.dc.job.admin[dc-job-admin]      // 调度启动器
│   │   ├── com.dc.job.administrator            // 调度逻辑
│   │   ├── com.dc.job.base                     // 基础功能
│   │   ├── com.dc.job.boot[dc-job]             // 任务启动器
│   │   ├── com.dc.job.executor                 // 具体任务
│   │   └── pom.xml
│   ├── com.dc.summer                           // DC 用户的执行应用，通过单连接的方式
│   │   ├── com.dc.summer.base                  // 基础执行
│   │   ├── com.dc.summer.boot[dc-summer-pe]    // 主线启动器
│   │   ├── com.dc.summer.pingan[dc-summer-pa]  // 平安启动器
│   │   ├── com.dc.summer.workorder             // 工单执行
│   │   └── pom.xml
│   ├── pom.xml
│   └── ...
└── pom.xml
```

## Description

### DBeaver

基于 Eclipse 的 RCP 进行的开发。

* 入口为 org . jkiss . dbeaver.ui.app.standalone 的 DBeaverApplication（本项目没有引入）。
* ``DBWorkbench.getPlatform();`` 进行平台的初始化。
* Summer 重写了 Dbeaver 的读取配置文件、注册数据源、下载和加载驱动。
* 最终实现 C 端应用，向 B 端服务的改造，完成数据库整个生命周期的操作。

### Summer

基于 Dbeaver 的业务模块进行的开发。

* 入口为 com.dc.summer.boot 的 SummerApplication（Spring启动器）。
* 通过 Runner 类的 ``init()`` 进行服务的初始化。
* Summer 通过 ExecuteController 暴露访问接口，使用 DataSourceConnectionHandler 进行数据源连接管理，使用 ExecuteHandler 进行执行业务的划分处理。
* 最终实现了 SQL 拆分、SQL 异步执行、打点监控、配置管理、便捷打包。

## Other
### Nacos

#### 概述
本文档描述了在现有项目中新增从 Nacos 配置中心获取配置文件的功能。此功能需要在客户现场实施，并在不影响现有本地配置文件的前提下增加 Nacos 配置中心的支持。

#### 目标
- 增加从 Nacos 配置中心获取配置文件的功能
- 允许用户根据需要启用或禁用 Nacos 配置
- 确保在 Nacos 配置不可用时，应用程序能够回退到本地配置文件

#### 架构图
```
                           +----------------------+
                           |   Nacos Config Server|
                           +----------+-----------+
                                      |
                                      |
                                      v
                           +----------------------+
                           |    Spring Boot App   |
                           |  (bootstrap.yml,     |
                           |   application.yml)   |
                           +----------+-----------+
                                      |
                                      |
                +---------------------+---------------------+
                |                                           |
                v                                           v
  +--------------------------+                  +----------------------------+
  | From Nacos (enabled=true)|                 |  From Local (enabled=false)|
  |  - Retrieves config from |                 |  - Uses local config files |
  |    Nacos server          |                 |    (application.yml,       |
  |  - application.yml       |                 |     application-dev.yml)   |
  |  - application-dev.yml   |                 |     application-env.yml)   |
  |  - application-env.yml   |                 +----------------------------+
  +--------------------------+
```

#### 实现步骤

##### 1. 添加 Maven 依赖
在项目的 `pom.xml` 中添加以下 Maven 依赖：
```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-bootstrap</artifactId>
</dependency>

<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
</dependency>
```

##### 2. 配置 `bootstrap.yml`
在 `src/main/resources` 目录下添加 `bootstrap.yml` 文件，并进行如下配置：
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848              # 指定 Nacos 配置中心的地址和端口号
        enabled: false                              # 是否启用从 Nacos 获取配置文件 (true 为开启，false 为关闭)
        prefix: dc-summer                           # 配置文件的前缀，一般用于区分不同的应用或模块
        file-extension: yaml                        # 配置文件的扩展名，支持 properties 和 yaml
        group: DEFAULT_GROUP                        # 配置文件所在的 Nacos 组，默认是 DEFAULT_GROUP
        extension-configs:                          # 额外的配置文件列表，可以指定多个
          - dataId: dc-job-admin.yaml               # 额外的配置文件的 Data ID
            group: DEFAULT_GROUP                    # 额外的配置文件所在的 Nacos 组
```

##### 3. 适配服务
为四个服务适配 Nacos 配置：
- dc-summer
- dc-iceage
- dc-job
- dc-job-admin

每个服务的 `bootstrap.yml` 文件配置如下：
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848
        enabled: true                                # 根据需要启用或禁用 Nacos
        prefix: <service-name>                       # 替换为具体的服务名，如 dc-summer
        file-extension: yaml
        group: DEFAULT_GROUP
        extension-configs:
          - dataId: <service-name>.yaml          # 替换为具体的 Data ID，如 dc-summer-dev.yaml
            group: DEFAULT_GROUP
```

例如，`dc-summer` 服务的 `bootstrap.yml` 文件应如下：
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848
        enabled: true
        prefix: dc-summer
        file-extension: yaml
        group: DEFAULT_GROUP
        extension-configs:
          - dataId: dc-summer-dev.yaml
            group: DEFAULT_GROUP
```

#### 验证
- 启动每个服务，验证是否正确从 Nacos 配置中心获取配置文件。
- 修改 `enabled` 为 `false`，验证是否回退到本地配置文件。

#### 注意事项
- 确保 Nacos 配置中心的地址和端口正确可达。
- 在本地和 Nacos 配置文件之间切换时，确保配置文件内容的一致性。

#### 总结
通过上述步骤，成功在现有项目中集成了 Nacos 配置中心。此设计确保了在不同环境下的灵活配置管理，提高了配置的可管理性和扩展性。
