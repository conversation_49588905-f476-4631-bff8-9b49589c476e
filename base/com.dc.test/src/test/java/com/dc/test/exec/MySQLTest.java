package com.dc.test.exec;

import com.dc.type.DatabaseType;
import com.dc.utils.CipherUtils;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.util.Random;

public class MySQLTest extends BaseExecTest {

    {
        connectionConfiguration.setDriverId("mysql5");
        connectionConfiguration.setDatabaseType(DatabaseType.MYSQL);
        connectionConfiguration.setHostName("*************");
        connectionConfiguration.setHostPort("3306");
        connectionConfiguration.setServerName("");
        connectionConfiguration.setDatabaseName("");
        connectionConfiguration.setUserName("root");
        String decrypt = CipherUtils.decrypt("RNp2hvU7ydnKirNroRyh0w==");
        connectionConfiguration.setUserPassword(decrypt);
    }

    public static String getRandomString(int length, boolean isCN) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            if (isCN) {
                sb.append((char) (0x4e00 + (int) (Math.random() * (0x9fa5 - 0x4e00 + 1))));
            } else {
                sb.append(str.charAt(random.nextInt(62)));
            }
        }
        return sb.toString();
    }

    @SneakyThrows
    private void insert(String tableName, int contentSize, int rows) {
        execute("drop table if exists " + tableName);
        execute("create table " + tableName + " ( \n" +
                " content_size_" + contentSize + " VARCHAR(" + contentSize + ") \n" +
                ")");
        for (int i = 0; i < rows; i++) {
            String randomString = getRandomString(contentSize, true);
            execute("INSERT INTO " + tableName + " VALUES (?)", randomString);
        }
    }

    @Test
    public void testConnection() {
        String sql = "select 1";
        execute(sql);
    }

    @Test
    public void testOneThousand() {
        String tableName = "test_one_thousand";
        insert(tableName, 21844, 2000);
    }
    @Test
    public void testOneBillion() {
        String tableName = "test_one_billion";
        insert(tableName, 21844, 1000000000);
    }

}
