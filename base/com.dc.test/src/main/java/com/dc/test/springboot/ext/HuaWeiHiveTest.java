package com.dc.test.springboot.ext;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.summer.model.DBConstants;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HuaWeiHiveTest implements DataBaseTest {

    ConnectionConfig connectionConfig = new ConnectionConfig();

    Map<String, String> ignore = new HashMap<>();

    {
        connectionConfig.setConnectionId("test-connection-id");
        connectionConfig.setDriverId("hw_hive");
        connectionConfig.setDatabaseType(DatabaseType.HIVE.getValue());
//        connectionConfig.setUrl("***********************************************************************");
        connectionConfig.setHostName("************");
        connectionConfig.setHostPort("10000");
            connectionConfig.setUrlType(0);
        connectionConfig.setDatabaseName("default");
//        connectionConfig.setCatalogName("hive");
        connectionConfig.setSchemaName("zyn2");
        connectionConfig.setUserName("hetu_user");
        connectionConfig.setUserPassword("");
        connectionConfig.setTestSql("select 1");
        connectionConfig.setAuthModelId("hive_kerberos");
        Map<String, String> authProperties = new HashMap<>();
        authProperties.put(DBConstants.AUTH_PROP_PRINCIPAL, "hive/hadoop.2528eb92_9ec4_4d94_953f_971971f2bcca.com@2528EB92_9EC4_4D94_953F_971971F2BCCA.COM");
        authProperties.put(DBConstants.AUTH_PROP_KEYTAB, "/Users/<USER>/Downloads/user.keytab");
        connectionConfig.setAuthProperties(authProperties);
        Map<String, String> properties = new HashMap<>();
        connectionConfig.setProperties(properties);
        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
        connectionConfig.setIgnore(ignore);
    }

    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return null;
    }

    @Override
    public String getExecuteSql() {
        return "SELECT * FROM `zyn2`.`type_complex01`";
    }

    @Override
    public List<Object> getAddData() {
        return null;
    }

    @Override
    public List<Object> getCreateData() {
        return null;
    }

    @Override
    public List<Object> getDeleteData() {
        return null;
    }

    @Override
    public List<Object> getUpdateData() {
        return null;
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return null;
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return null;
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return List.of("SELECT * FROM (\n" +
                "SELECT 10 as col1, \"test\" as col2, \"test\" as col3\n" +
                ") tmp_table;",
                "CREATE TEMPORARY FUNCTION function_name AS class_name;",
                "SELECT \n" +
                        "month,\n" +
                        "day,\n" +
                        "COUNT(DISTINCT cookieid) AS uv,\n" +
                        "GROUPING__ID  \n" +
                        "FROM test_t5 \n" +
                        "GROUP BY month,day\n" +
                        "WITH ROLLUP \n" +
                        "ORDER BY GROUPING__ID;",
                "SELECT \n" +
                        "day,\n" +
                        "month,\n" +
                        "COUNT(DISTINCT cookieid) AS uv,\n" +
                        "GROUPING__ID  \n" +
                        "FROM test_t5 \n" +
                        "GROUP BY day,month \n" +
                        "WITH ROLLUP \n" +
                        "ORDER BY GROUPING__ID;",
                "SELECT \n" +
                        "month,\n" +
                        "day,\n" +
                        "COUNT(DISTINCT cookieid) AS uv,\n" +
                        "GROUPING__ID \n" +
                        "FROM test_t5 \n" +
                        "GROUP BY month,day \n" +
                        "WITH CUBE \n" +
                        "ORDER BY GROUPING__ID;\n",
                "CREATE TABLE test_t5 (\n" +
                        "month STRING,\n" +
                        "day STRING, \n" +
                        "cookieid STRING \n" +
                        ") ROW FORMAT DELIMITED \n" +
                        "FIELDS TERMINATED BY ',' \n" +
                        "stored as textfile;\n",
                "  SELECT \n" +
                        "  dept,\n" +
                        "  userid,\n" +
                        "  sal,\n" +
                        "  PERCENT_RANK() OVER(ORDER BY sal) AS rn1,   --分组内\n" +
                        "  RANK() OVER(ORDER BY sal) AS rn11,          --分组内RANK值\n" +
                        "  SUM(1) OVER(PARTITION BY NULL) AS rn12,     --分组内总行数\n" +
                        "  PERCENT_RANK() OVER(PARTITION BY dept ORDER BY sal) AS rn2 \n" +
                        "  FROM test_t3;",
                "CREATE PROCEDURE test_even()\n" +
                        "BEGIN\n" +
                        " DECLARE curs SYS_REFCURSOR;\n" +
                        " DECLARE n INT = 0;\n" +
                        " DECLARE result STRING = 'Even numbers are: ';\n" +
                        " even(curs);\n" +
                        " FETCH curs INTO n;\n" +
                        " WHILE (SQLCODE = 0) DO\n" +
                        "    result = result || n || ' ';\n" +
                        "    FETCH curs INTO n;\n" +
                        " END WHILE;\n" +
                        " CLOSE curs;\n" +
                        " PRINT result;\n" +
                        "END;   ");
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return null;
    }

    @Override
    public List<String> getSqlList() {
        return Arrays.asList("SELECT 1");
    }
}
