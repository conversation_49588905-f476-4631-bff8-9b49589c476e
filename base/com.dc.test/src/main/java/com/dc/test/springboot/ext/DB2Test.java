package com.dc.test.springboot.ext;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class DB2Test implements DataBaseTest {


    ConnectionConfig connectionConfig = new ConnectionConfig();

    {
        connectionConfig.setConnectionId("test-connection-id");
//        connectionConfig.setDriverId("oracle_thin");
        connectionConfig.setDatabaseType(DatabaseType.DB2.getValue());
//        connectionConfig.setHostName("*************");
//        connectionConfig.setUrl("");
//        connectionConfig.setHostPort("1521");
//        connectionConfig.setUrlType(1);
//        connectionConfig.setDatabaseName("helowin");
//        connectionConfig.setCatalogName("");
//        connectionConfig.setSchemaName("BOBO");
//        connectionConfig.setUserName("SYSTEM");
//        connectionConfig.setUserPassword("iimbSYGvr5NN/grRHlRQwg==");
//        connectionConfig.setTestSql("");
//        connectionConfig.setAuthModelId("oracle_native");
//        Map<String, String> authProperties = new HashMap<>();
//        authProperties.put("oracle.logon-as", "Normal");
//        connectionConfig.setAuthProperties(authProperties);
//        Map<String, String> properties = new HashMap<>();
//        connectionConfig.setProperties(properties);
//        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
//        ignore.put(DBConstants.AUTH_PROP_SSL, "!");
//        connectionConfig.setIgnore(ignore);
    }

    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return null;
    }

    @Override
    public String getExecuteSql() {
        return null;
    }

    @Override
    public List<Object> getAddData() {
        return null;
    }

    @Override
    public List<Object> getCreateData() {
        return null;
    }

    @Override
    public List<Object> getDeleteData() {
        return null;
    }

    @Override
    public List<Object> getUpdateData() {
        return null;
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return null;
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return null;
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return List.of("CREATE PROCEDURE RPT.PROC_RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\tIN ETL_DATE\tVARCHAR(8),\n" +
                "\tOUT OUT_SQLCODE\tINTEGER,\n" +
                "\tOUT OUT_MSG\tVARCHAR(200)\n" +
                ")\n" +
                "SPECIFIC \"PROC_RPT_GSYW_DP_REV_TOT_VAL\" \n" +
                "LANGUAGE SQL\n" +
                "-- ####################################################################################\n" +
                "-- # 业务说明: \n" +
                "-- # 协议主题-\n" +
                "-- # 主要指标：\n" +
                "-- # 查询参数说明：\n" +
                "-- # 1、ETL_DATE：存储过程运行日期，YYYYMMDD格式。\n" +
                "-- # \n" +
                "-- # 返回参数说明：\n" +
                "-- # 1、OUT_SQLCODE  SQLCODE\n" +
                "-- # 2、OUT_MSG  返回信息说明\n" +
                "-- # 调用示例：\n" +
                "-- # 1、第一次全量初始化或其他原因全量：CALL PROC_DW_EACCT_INFO('20180531','1',?,?)\n" +
                "-- # 2、每日跑批或当日重跑执行： CALL PROC_DW_EACCT_INFO('20180614','2',?,?)\n" +
                "-- # \n" +
                "-- # 调度频次：日\n" +
                "-- # 数据存储：当前日期，历史数据查看拉链表。\n" +
                "-- # \n" +
                "-- # 依赖表清单：\n" +
                "-- # DDS.NFCP_KNA_CUST【电子账户表】\n" +
                "-- # \n" +
                "-- # 调用表清单：\n" +
                "-- # DW.DIM_DATE 【仓库日期维表】\n" +
                "-- #\n" +
                "-- # 调用存储过程列表：\n" +
                "-- # 1、\n" +
                "-- # 调用自定义函数列表：\n" +
                "-- # 1、\n" +
                "-- # 操作目标表：SESSION.TMP_ZFCX_01\n" +
                "-- # 写入日志表：ETL.PROCLOG\n" +
                "-- #\n" +
                "-- # 修改记录：\n" +
                "-- # 1、日期：\n" +
                "-- # 当前版本：V1.0\n" +
                "-- ####################################################################################\n" +
                "P1: BEGIN\n" +
                "\tDECLARE SQLCODE  INTEGER   DEFAULT 0;            --SQL返回代码;--\n" +
                "\tDECLARE SQLSTATE CHAR(5)   DEFAULT '00000';      --SQL默认返回代码'00000'\n" +
                "\tDECLARE OUT_SQLSTATE CHAR(5) DEFAULT '00000'; \n" +
                "\t\n" +
                "\tDECLARE V_CACTION VARCHAR(200);                  --存储过程各个阶段功能描述\n" +
                "\tDECLARE V_CPROCNAME  VARCHAR(128) DEFAULT 'RPT.PROC_RPT_GSYW_DP_REV_TOT_VAL'; --存储过程名称;  \n" +
                "\tDECLARE V_INTOTB_NM  VARCHAR(128) DEFAULT 'RPT.RPT_GSYW_DP_REV_TOT_VAL'; --操作目标表，多个目标表中间用#分割;  \n" +
                "\tDECLARE V_ISTEP SMALLINT DEFAULT 0;              --存储过程运行步骤计数器   \n" +
                "\tDECLARE V_ETL_DT DATE;\t\t\t\t\t\t\t --跑批日期(标准DATE类型)  \n" +
                "\tDECLARE V_ETL_DATE CHAR(8);                      --跑批日期(YYYYMMDD)         \n" +
                "\tDECLARE V_LAST_MONTH_END CHAR(8);                --上月末  \n" +
                "\tDECLARE V_LAST_YEAR_END CHAR(8);                 --上年末\n" +
                "\tDECLARE V_MON_FLAG\tCHAR(1);\t\t\t\t\t --月末标志\n" +
                "\tDECLARE V_EXESQL\tVARCHAR(4000);\t\t\t\t --动态执行SQL\n" +
                "\t\n" +
                "\tDECLARE V_EQ\tVARCHAR(6);\t\t\t\t \t\t --日期动态比较符号\n" +
                "\tDECLARE V_NMB\tINTEGER;\n" +
                "\t\n" +
                "\t--------------------------------异常处理声明------------------------------------\n" +
                "\t--SQL警告处理动作：继续;\n" +
                "\tDECLARE CONTINUE HANDLER FOR SQLWARNING,NOT FOUND\n" +
                "\tBEGIN\n" +
                "\t\tVALUES (CHAR(SQLCODE),SQLSTATE) INTO OUT_SQLCODE,OUT_SQLSTATE;\n" +
                "\t    SET OUT_MSG = 'SQL警告信息，SQLCODE='||OUT_SQLCODE||'，SQLSTATE='||OUT_SQLSTATE||':' || V_CACTION;    \n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\t\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,OUT_MSG);\n" +
                "\t\tCOMMIT;\n" +
                "\t\t\n" +
                "\t\t--重置sqlcode值\n" +
                "\t\tSET OUT_SQLCODE = 0;\n" +
                "\t\tSET OUT_SQLSTATE = '00000';\n" +
                "\tEND;\n" +
                "\n" +
                "\t--SQL产生的异常处理处理动作：退出;\n" +
                "\tDECLARE EXIT HANDLER FOR SQLSTATE '80001' \n" +
                "\tBEGIN\n" +
                "\t\tVALUES (CHAR(SQLCODE),SQLSTATE) INTO OUT_SQLCODE,OUT_SQLSTATE;\n" +
                "\t\tSET OUT_MSG = '自定义异常退出，SQLCODE='||OUT_SQLCODE||'，SQLSTATE='||OUT_SQLSTATE||':' || V_CACTION;\n" +
                "\t\tROLLBACK;\n" +
                "\t\t\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP+1),OUT_SQLCODE,OUT_SQLSTATE,OUT_MSG);\n" +
                "\t\tCOMMIT;\n" +
                "\tEND;\n" +
                "\t\n" +
                "\tDECLARE EXIT HANDLER FOR SQLEXCEPTION\n" +
                "\tBEGIN\n" +
                "\t    VALUES (CHAR(SQLCODE),SQLSTATE) INTO OUT_SQLCODE,OUT_SQLSTATE;\n" +
                "\t    SET OUT_MSG = 'SQL异常退出，SQLCODE='||OUT_SQLCODE||'，SQLSTATE='||OUT_SQLSTATE||':' || V_CACTION || ' FAILED.';--    \n" +
                "\t\tROLLBACK;\n" +
                "\t\t\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP+1),OUT_SQLCODE,OUT_SQLSTATE,OUT_MSG);\n" +
                "\t\tCOMMIT;\n" +
                "\tEND;\n" +
                "\n" +
                "    --------------------------------临时表声明------------------------------------\n" +
                "\t--普惠小微贷款\n" +
                "\tDECLARE GLOBAL TEMPORARY TABLE SESSION.TMP_PHXW (\n" +
                "\t\t DATA_DATE\t\t    DATE NOT NULL  \t\t\t--数据日期\n" +
                "\t\t,LV1_ORG_NO\t\t    VARCHAR(8)\t\t    \t--所属支行\n" +
                "\t\t,INDEX_CODE\t\t    VARCHAR(20)\t\t    \t--数据标志\n" +
                "        ,TOT_VAL            DECIMAL(20,5) \t        --当前余额\n" +
                "        ,RATN_LAST_MONTH\tDECIMAL(20,5)\t\t\t--上月余额\n" +
                "        ,COMP_LAST_YR       DECIMAL(20,5)            --上年余额  \n" +
                "\t)\n" +
                "\tDISTRIBUTE BY HASH (DATA_DATE) \n" +
                "\tON COMMIT PRESERVE ROWS NOT LOGGED WITH REPLACE\n" +
                "\t;\n" +
                "\t-------------------------------变量初始化------------------------------------\n" +
                "\t--自定义变量赋值\n" +
                "\tSET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：初始化变量';\n" +
                "\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "    SET OUT_SQLCODE = 0;--\n" +
                "    SET OUT_MSG = '' ;--\n" +
                "    \n" +
                "    SET V_ETL_DATE = ETL_DATE;\n" +
                "\tSET V_ETL_DT = TO_DATE(V_ETL_DATE,'YYYY-MM-DD');\n" +
                "\t\n" +
                "    SELECT\tMON_FLAG\t\t\t--月末标志,1:月末，0:非月末\n" +
                "    \t\t,LAST_FIN_MON_END\t--上月末 \n" +
                "\t        ,LAST_FIN_YEAR_END \t--上年末\n" +
                "\tINTO \tV_MON_FLAG\n" +
                "\t\t\t,V_LAST_MONTH_END --上月末 \n" +
                "\t     \t,V_LAST_YEAR_END   --上年末   \n" +
                "\tFROM DW.DIM_DATE\n" +
                "\tWHERE\tDATE_ID = V_ETL_DATE\n" +
                "\t;\t\n" +
                "\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION)\n" +
                "\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\tCOMMIT;\n" +
                "\n" +
                "\t-------------------------------参数合法性检查------------------------------------\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步： 参数合法性检查';\n" +
                "\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\tCOMMIT;\n" +
                "\t\n" +
                "    IF V_LAST_MONTH_END IS NULL OR V_LAST_YEAR_END IS NULL \n" +
                "\tTHEN\n" +
                "\t\tSET V_CACTION = '日期参数不合法！无法查询到日期相关维度信息。';\n" +
                "\t\tSIGNAL SQLSTATE '80001';\n" +
                "\tEND IF;\n" +
                "    \n" +
                "    -------------------------------删除当日已跑数据------------------------------------\n" +
                "\n" +
                "\t\tSET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步： 删除当日已跑数据';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT;\n" +
                "\t\t\n" +
                "\t\t--清空目标表\n" +
                "\t\tDELETE FROM RPT.RPT_GSYW_DP_REV_TOT_VAL WHERE DATA_DATE=V_ETL_DATE;\n" +
                "\n" +
                "\t-------------------------------业务逻辑处理------------------------------------\t\n" +
                "\t\tSET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'总存款余额';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\t\t\n" +
                "\t\tINSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL(\n" +
                "\t\t\tDATA_DATE\t\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t    --支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t    --指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t    --比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t    --比上年\n" +
                "\t\t)\t\n" +
                "\t\tSELECT \n" +
                "\t\t\tV_ETL_DATE\n" +
                "            ,CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END LV1_ORG_NO\n" +
                "\t\t\t,'总存款余额'\t\t\n" +
                "\t\t\t,CURR_VAL\n" +
                "\t\t\t,CURR_VAL-LST_MON_VAL\n" +
                "\t\t\t,CURR_VAL-LST_YR_VAL \n" +
                "\t\tFROM JZFY.DEP_INDEX_SUM\n" +
                "        WHERE DATA_DATE =V_ETL_DATE\n" +
                "        AND INDEX_CD='2010'\n" +
                "        AND (LENGTH(ORG_NO)='8' OR ORG_NO='000000')\n" +
                "        WITH UR;\n" +
                "\t\tCOMMIT;\n" +
                "\n" +
                "\t\tSET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'对公存款余额';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "        INSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\t\t\tDATA_DATE\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t--支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t--指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t--比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t--比上年\n" +
                "\t\t)\t\n" +
                "\t\tSELECT \n" +
                "\t\t\tV_ETL_DATE\n" +
                "\t\t\t,CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END LV1_ORG_NO\n" +
                "\t\t\t,'对公存款余额'\n" +
                "\t\t\t,CURR_VAL\n" +
                "\t\t\t,CURR_VAL-LST_MON_VAL\n" +
                "\t\t\t,CURR_VAL-LST_YR_VAL \n" +
                "\t\tFROM JZFY.DEP_INDEX_SUM\n" +
                "        WHERE DATA_DATE =V_ETL_DATE\n" +
                "        AND INDEX_CD='201004'\n" +
                "        AND (LENGTH(ORG_NO)='8' OR ORG_NO='000000')\n" +
                "        WITH UR;\n" +
                "\t    COMMIT;\n" +
                "\n" +
                "        SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'个人存款余额';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "   \n" +
                "    INSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\t\t\tDATA_DATE\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t--支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t--指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t--比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t--比上年\n" +
                "\t\t)\t\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END LV1_ORG_NO\n" +
                "    \t,'个人存款余额'\n" +
                "    \t,CURR_VAL\n" +
                "    \t,CURR_VAL-LST_MON_VAL\n" +
                "    \t,CURR_VAL-LST_YR_VAL \n" +
                "    FROM JZFY.DEP_INDEX_SUM\n" +
                "    WHERE DATA_DATE =V_ETL_DATE\n" +
                "        AND INDEX_CD='201003'\n" +
                "        AND (LENGTH(ORG_NO)='8' OR ORG_NO='000000')\n" +
                "    WITH UR;\n" +
                "\tCOMMIT;\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'总贷款余额';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "        \n" +
                "    INSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\t\t\tDATA_DATE\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t--支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t--指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t--比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t--比上年\n" +
                "\t\t)\t\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END LV1_ORG_NO\n" +
                "    \t,'总贷款余额'\n" +
                "    \t,CURR_VAL\n" +
                "    \t,CURR_VAL-LST_MON_VAL\n" +
                "    \t,CURR_VAL-LST_YR_VAL \n" +
                "    from JZFY.LOAN_INDEX_SUM\n" +
                "    WHERE INDEX_CD='1010'\n" +
                "\t    AND (LENGTH(ORG_NO)='8' OR ORG_NO='000000')\n" +
                "\t    AND DATA_DATE =V_ETL_DATE\n" +
                "    WITH UR;\n" +
                "    COMMIT;\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'对公贷款余额';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "    INSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\t\t\tDATA_DATE\t\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t--支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t--指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t--比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t--比上年\n" +
                "\t\t)\t\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END LV1_ORG_NO\n" +
                "    \t,'对公贷款余额'\n" +
                "    \t,SUM(CURR_VAL)\n" +
                "    \t,SUM(CURR_VAL-LST_MON_VAL)\n" +
                "    \t,SUM(CURR_VAL-LST_YR_VAL) \n" +
                "    from JZFY.LOAN_INDEX_SUM\n" +
                "    WHERE INDEX_CD IN ('1010010102','1010010212','101002','101004','101005','101006')\n" +
                "    \tAND (LENGTH(ORG_NO)='8' OR ORG_NO='000000')\n" +
                "    \tAND DATA_DATE =V_ETL_DATE\n" +
                "    GROUP BY CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END\n" +
                "    WITH UR;\n" +
                "    COMMIT;\n" +
                "\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'对私贷款余额';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "    INSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\t\t\tDATA_DATE\t\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t--支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t--指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t--比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t--比上年\n" +
                "\t\t)\t\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END LV1_ORG_NO\n" +
                "    \t,'对私贷款余额'\n" +
                "    \t,SUM(CURR_VAL)\n" +
                "    \t,SUM(CURR_VAL-LST_MON_VAL)\n" +
                "    \t,SUM(CURR_VAL-LST_YR_VAL) \n" +
                "    from JZFY.LOAN_INDEX_SUM\n" +
                "    WHERE INDEX_CD IN ('1010010101','1010010211','101003','101007')\n" +
                "    \tAND (LENGTH(ORG_NO)='8' OR ORG_NO='000000')\n" +
                "      AND DATA_DATE =V_ETL_DATE \n" +
                "    GROUP BY CASE WHEN LENGTH(ORG_NO)='8'  THEN LEFT(ORG_NO,6) ELSE  ORG_NO  END\n" +
                "    WITH UR;\n" +
                "    COMMIT;\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'普惠小微对公贷款';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "  \n" +
                "    INSERT INTO SESSION.TMP_PHXW (\n" +
                "    \tDATA_DATE\n" +
                "    \t,LV1_ORG_NO\n" +
                "    \t,INDEX_CODE\n" +
                "    \t,TOT_VAL\n" +
                "    )\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,'000000'\n" +
                "    \t,'普惠小微对公贷款'\n" +
                "    \t,SUM(BAL) VAL \n" +
                "    FROM RPT.RPT_CYCX_KB055_1\n" +
                "    WHERE DATA_DT=V_ETL_DATE\n" +
                "    GROUP BY DATA_DT \n" +
                "    UNION ALL\n" +
                "\t\tSELECT \n" +
                "\t\t\tV_ETL_DATE\n" +
                "\t\t\t,SUBSTR(ORG_LV1,2,6) ORG_LV1\n" +
                "\t\t\t,'普惠小微对公贷款'\n" +
                "\t\t\t,SUM(BAL) VAL \n" +
                "\t\tFROM RPT.RPT_CYCX_KB055_1\n" +
                "\t\tWHERE DATA_DT=V_ETL_DATE\n" +
                "\t\tGROUP BY SUBSTR(ORG_LV1,2,6)\n" +
                "    WITH UR;\n" +
                "    COMMIT;\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'普惠小微上月对公贷款';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "    MERGE INTO SESSION.TMP_PHXW  A \n" +
                "\t\tUSING (\n" +
                "\t\t\t   SELECT \n" +
                "\t\t\t    \tV_ETL_DATE\n" +
                "\t\t\t    \t,'000000' LV1_ORG_NO\n" +
                "\t\t\t    \t,'普惠小微对公贷款'\n" +
                "\t\t\t    \t,SUM(BAL) VAL \n" +
                "\t\t\t    FROM RPT.RPT_CYCX_KB055_1\n" +
                "\t\t\t    WHERE DATA_DT=V_LAST_MONTH_END\n" +
                "\t\t\t    GROUP BY DATA_DT \n" +
                "\t\t\t    UNION ALL\n" +
                "\t\t\t\t\tSELECT \n" +
                "\t\t\t\t\t\tV_ETL_DATE\n" +
                "\t\t\t\t\t\t,SUBSTR(ORG_LV1,2,6) ORG_LV1\n" +
                "\t\t\t\t\t\t,'普惠小微对公贷款'\n" +
                "\t\t\t\t\t\t,SUM(BAL) VAL \n" +
                "\t\t\t\t\tFROM RPT.RPT_CYCX_KB055_1\n" +
                "\t\t\t\t\tWHERE DATA_DT=V_LAST_MONTH_END\n" +
                "\t\t\t\t\tGROUP BY SUBSTR(ORG_LV1,2,6)\n" +
                "\t\t\t) B ON A.LV1_ORG_NO=B.LV1_ORG_NO AND A.INDEX_CODE='普惠小微对公贷款'\n" +
                "\t\t\tWHEN MATCHED THEN UPDATE SET A.RATN_LAST_MONTH=B.VAL\n" +
                "\t\t\t;\n" +
                "            COMMIT;\n" +
                "        \n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'普惠小微上年对公贷款';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                " \n" +
                "    MERGE INTO SESSION.TMP_PHXW  A \n" +
                "\t\tUSING (\n" +
                "\t\t\t   SELECT \n" +
                "\t\t\t    \tV_ETL_DATE\n" +
                "\t\t\t    \t,'000000' LV1_ORG_NO\n" +
                "\t\t\t    \t,'普惠小微对公贷款'\n" +
                "\t\t\t    \t,SUM(BAL) VAL \n" +
                "\t\t\t    FROM RPT.RPT_CYCX_KB055_1\n" +
                "\t\t\t    WHERE DATA_DT=V_LAST_YEAR_END\n" +
                "\t\t\t    GROUP BY DATA_DT \n" +
                "\t\t\t    UNION ALL\n" +
                "\t\t\t\t\tSELECT \n" +
                "\t\t\t\t\t\tV_ETL_DATE\n" +
                "\t\t\t\t\t\t,SUBSTR(ORG_LV1,2,6) ORG_LV1\n" +
                "\t\t\t\t\t\t,'普惠小微对公贷款'\n" +
                "\t\t\t\t\t\t,SUM(BAL) VAL \n" +
                "\t\t\t\t\tFROM RPT.RPT_CYCX_KB055_1\n" +
                "\t\t\t\t\tWHERE DATA_DT=V_LAST_YEAR_END\n" +
                "\t\t\t\t\tGROUP BY SUBSTR(ORG_LV1,2,6)\n" +
                "\t\t\t) B ON A.LV1_ORG_NO=B.LV1_ORG_NO AND A.INDEX_CODE='普惠小微对公贷款'\n" +
                "\t\t\tWHEN MATCHED THEN UPDATE SET A.COMP_LAST_YR=B.VAL\n" +
                "\t\t\t;\n" +
                "            COMMIT;\n" +
                "        \n" +
                "        \n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'普惠小微对私贷款';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "    \n" +
                "    INSERT INTO SESSION.TMP_PHXW (\n" +
                "    \tDATA_DATE\n" +
                "    \t,LV1_ORG_NO\n" +
                "    \t,INDEX_CODE\n" +
                "    \t,TOT_VAL\n" +
                "    )\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,'000000'\n" +
                "    \t,'普惠小微对私贷款'\n" +
                "    \t,SUM(BAL) VAL \n" +
                "    FROM RPT.RPT_CYCX_KB055_2\n" +
                "    WHERE DATA_DT=V_ETL_DATE\n" +
                "    GROUP BY DATA_DT \n" +
                "    UNION ALL\n" +
                "\t\tSELECT \n" +
                "\t\t\tV_ETL_DATE\n" +
                "\t\t\t,SUBSTR(ORG_LV1,2,6) ORG_LV1\n" +
                "\t\t\t,'普惠小微对私贷款'\n" +
                "\t\t\t,SUM(BAL) VAL \n" +
                "\t\tFROM RPT.RPT_CYCX_KB055_2\n" +
                "\t\tWHERE DATA_DT=V_ETL_DATE\n" +
                "\t\tGROUP BY SUBSTR(ORG_LV1,2,6)\n" +
                "    WITH UR;\n" +
                "    COMMIT;\n" +
                "    \n" +
                "\n" +
                "        SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'普惠小微上月对私贷款';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "    MERGE INTO SESSION.TMP_PHXW  A \n" +
                "\t\tUSING (\n" +
                "\t\t\t   SELECT \n" +
                "\t\t\t    \tV_ETL_DATE\n" +
                "\t\t\t    \t,'000000' LV1_ORG_NO\n" +
                "\t\t\t    \t,'普惠小微对私贷款'\n" +
                "\t\t\t    \t,SUM(BAL) VAL \n" +
                "\t\t\t    FROM RPT.RPT_CYCX_KB055_2\n" +
                "\t\t\t    WHERE DATA_DT=V_LAST_MONTH_END\n" +
                "\t\t\t    GROUP BY DATA_DT \n" +
                "\t\t\t    UNION ALL\n" +
                "\t\t\t\t\tSELECT \n" +
                "\t\t\t\t\t\tV_ETL_DATE\n" +
                "\t\t\t\t\t\t,SUBSTR(ORG_LV1,2,6) ORG_LV1\n" +
                "\t\t\t\t\t\t,'普惠小微对私贷款'\n" +
                "\t\t\t\t\t\t,SUM(BAL) VAL \n" +
                "\t\t\t\t\tFROM RPT.RPT_CYCX_KB055_2\n" +
                "\t\t\t\t\tWHERE DATA_DT=V_LAST_MONTH_END\n" +
                "\t\t\t\t\tGROUP BY SUBSTR(ORG_LV1,2,6)\n" +
                "\t\t\t) B ON A.LV1_ORG_NO=B.LV1_ORG_NO AND A.INDEX_CODE='普惠小微对私贷款'\n" +
                "\t\t\tWHEN MATCHED THEN UPDATE SET A.RATN_LAST_MONTH=B.VAL\n" +
                "\t\t\t;\n" +
                "      COMMIT;\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，抽取'||V_ETL_DATE||'普惠小微上年末对私贷款';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "    MERGE INTO SESSION.TMP_PHXW  A \n" +
                "\t\tUSING (\n" +
                "\t\t\t   SELECT \n" +
                "\t\t\t    \tV_ETL_DATE\n" +
                "\t\t\t    \t,'000000' LV1_ORG_NO\n" +
                "\t\t\t    \t,'普惠小微对私贷款'\n" +
                "\t\t\t    \t,SUM(BAL) VAL \n" +
                "\t\t\t    FROM RPT.RPT_CYCX_KB055_2\n" +
                "\t\t\t    WHERE DATA_DT=V_LAST_YEAR_END\n" +
                "\t\t\t    GROUP BY DATA_DT \n" +
                "\t\t\t    UNION ALL\n" +
                "\t\t\t\t\tSELECT \n" +
                "\t\t\t\t\t\tV_ETL_DATE\n" +
                "\t\t\t\t\t\t,SUBSTR(ORG_LV1,2,6) ORG_LV1\n" +
                "\t\t\t\t\t\t,'普惠小微对私贷款'\n" +
                "\t\t\t\t\t\t,SUM(BAL) VAL \n" +
                "\t\t\t\t\tFROM RPT.RPT_CYCX_KB055_2\n" +
                "\t\t\t\t\tWHERE DATA_DT=V_LAST_YEAR_END\n" +
                "\t\t\t\t\tGROUP BY SUBSTR(ORG_LV1,2,6)\n" +
                "\t\t\t) B ON A.LV1_ORG_NO=B.LV1_ORG_NO AND A.INDEX_CODE='普惠小微对私贷款'\n" +
                "\t\t\tWHEN MATCHED THEN UPDATE SET A.COMP_LAST_YR=B.VAL\n" +
                "\t\t\t;\n" +
                "        COMMIT;\n" +
                "\n" +
                "    SET V_CACTION = '第'||TRIM(CHAR(V_ISTEP))||'步：，插入'||V_ETL_DATE||'普惠小微到落地表';\n" +
                "\t\tSET V_ISTEP = V_ISTEP + 1;\n" +
                "\t\tINSERT INTO ETL.PROCLOG(PROCNAME,WORKDATE,WORKTIME,TABLENAME,SQLLEVEL,SQLCODE,SQLSTATE,ACTION) \n" +
                "\t\t\tVALUES(V_CPROCNAME,V_ETL_DATE,CURRENT TIMESTAMP,V_INTOTB_NM, CHAR(V_ISTEP),OUT_SQLCODE,OUT_SQLSTATE,V_CACTION);\n" +
                "\t\tCOMMIT; \n" +
                "\n" +
                "   INSERT INTO RPT.RPT_GSYW_DP_REV_TOT_VAL (\n" +
                "\t\t\tDATA_DATE\t\t\t\t--数据日期\n" +
                "\t\t\t,LV1_ORG_NO\t\t\t--支行\n" +
                "\t\t\t,INDEX_CODE\t\t\t--指标名称\n" +
                "\t\t\t,TOT_VAL \t\t\t\t--当前值\n" +
                "\t\t\t,RATN_LAST_MONTH\t--比上月\n" +
                "\t\t\t,COMP_LAST_YR\t\t--比上年1\n" +
                "\t\t)\t\n" +
                "    SELECT \n" +
                "    \tV_ETL_DATE\n" +
                "    \t,LV1_ORG_NO\n" +
                "    \t,'普惠小微贷款余额'\n" +
                "    \t,SUM(TOT_VAL)\n" +
                "    \t,SUM(TOT_VAL)-SUM(RATN_LAST_MONTH)\n" +
                "    \t,SUM(TOT_VAL)-SUM(COMP_LAST_YR)\n" +
                "    FROM SESSION.TMP_PHXW \n" +
                "    WHERE DATA_DATE=V_ETL_DATE\n" +
                "    GROUP BY LV1_ORG_NO\n" +
                "    WITH UR;\n" +
                "    COMMIT;\n" +
                "\n" +
                "\n" +
                "END P1 ;\n");
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return null;
    }

    @Override
    public List<String> getSqlList() {
        return null;
    }
}
