package com.dc.test.springboot.ext;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("pg")
public class PGTest implements DataBaseTest {

    ConnectionConfig connectionConfig = new ConnectionConfig();

    {
        connectionConfig.setConnectionId("id-123");
        connectionConfig.setDriverId("postgres-jdbc");
        connectionConfig.setDatabaseType(DatabaseType.PG_SQL.getValue());
        connectionConfig.setHostName("************");
//        connectionConfig.setUrl("");
        connectionConfig.setHostPort("5433");
//        connectionConfig.setUrlType(1);
        connectionConfig.setDatabaseName("bobo");
        connectionConfig.setCatalogName("bobo");
        connectionConfig.setSchemaName("test");
        connectionConfig.setUserName("postgres");
        connectionConfig.setUserPassword("RNp2hvU7ydnKirNroRyh0w==");
//        connectionConfig.setTestSql("");
//        connectionConfig.setAuthModelId("oracle_native");
//        Map<String, String> authProperties = new HashMap<>();
//        authProperties.put("oracle.logon-as", "Normal");
//        connectionConfig.setAuthProperties(authProperties);
//        Map<String, String> properties = new HashMap<>();
//        connectionConfig.setProperties(properties);
//        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
//        ignore.put(DBConstants.AUTH_PROP_SSL, "!");
//        connectionConfig.setIgnore(ignore);
    }


    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return null;
    }

    @Override
    public String getExecuteSql() {
        return "SELECT * FROM \"cyschema1\".\"cy3\";";
    }

    @Override
    public List<Object> getAddData() {
        return null;
    }

    @Override
    public List<Object> getCreateData() {
        return null;
    }

    @Override
    public List<Object> getDeleteData() {
        return null;
    }

    @Override
    public List<Object> getUpdateData() {
        return null;
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return null;
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return null;
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return null;
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return null;
    }

    @Override
    public List<String> getSqlList() {
        return null;
    }
}
