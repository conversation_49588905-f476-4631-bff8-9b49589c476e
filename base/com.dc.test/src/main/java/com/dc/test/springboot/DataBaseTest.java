package com.dc.test.springboot;

import com.dc.springboot.core.model.database.ConnectionConfig;

import java.util.List;
import java.util.Map;

public interface DataBaseTest {

    ConnectionConfig getConnectionConfig();

    List<String> getInitSql();

    String getExecuteSql();

    List<Object> getAddData();

    List<Object> getCreateData();

    List<Object> getDeleteData();

    List<Object> getUpdateData();

    Map<String, Object> getUpdateValues();

    List<String> getPrimaryKeyColumns();

    List<String> getParseScriptForWhole();

    List<String> getParseScriptForSplit();

    List<String> getSqlList();

    default String getToken() {
        return "summer-test-session-" + getClass().getSimpleName();
    }

}
