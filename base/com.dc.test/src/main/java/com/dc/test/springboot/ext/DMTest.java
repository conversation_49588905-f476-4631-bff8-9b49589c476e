package com.dc.test.springboot.ext;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class DMTest implements DataBaseTest {

    ConnectionConfig connectionConfig = new ConnectionConfig();

    {
//        connectionConfig.setConnectionId("test-connection-id");
//        connectionConfig.setDriverId("oracle_thin");
        connectionConfig.setDatabaseType(DatabaseType.DM.getValue());
//        connectionConfig.setHostName("*************");
//        connectionConfig.setUrl("");
//        connectionConfig.setHostPort("1521");
//        connectionConfig.setUrlType(1);
//        connectionConfig.setDatabaseName("helowin");
//        connectionConfig.setCatalogName("");
//        connectionConfig.setSchemaName("BOBO");
//        connectionConfig.setUserName("SYSTEM");
//        connectionConfig.setUserPassword("iimbSYGvr5NN/grRHlRQwg==");
//        connectionConfig.setTestSql("");
//        connectionConfig.setAuthModelId("oracle_native");
//        Map<String, String> authProperties = new HashMap<>();
//        authProperties.put("oracle.logon-as", "Normal");
//        connectionConfig.setAuthProperties(authProperties);
//        Map<String, String> properties = new HashMap<>();
//        connectionConfig.setProperties(properties);
//        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
//        ignore.put(DBConstants.AUTH_PROP_SSL, "!");
//        connectionConfig.setIgnore(ignore);
    }

    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return null;
    }

    @Override
    public String getExecuteSql() {
        return null;
    }

    @Override
    public List<Object> getAddData() {
        return null;
    }

    @Override
    public List<Object> getCreateData() {
        return null;
    }

    @Override
    public List<Object> getDeleteData() {
        return null;
    }

    @Override
    public List<Object> getUpdateData() {
        return null;
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return null;
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return null;
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return List.of("CREATE CLASS CLS1 AS V ARR_NUM3;END;",
                " CREATE TYPE COMPLEX AS OBJECT(\n" +
                "    RPART REAL,\n" +
                "    IPART REAL,\n" +
                "    FUNCTION PLUS(X COMPLEX) RETURN COMPLEX,\n" +
                "    FUNCTION LES(X COMPLEX) RETURN COMPLEX\n" +
                "  );",
                " CREATE TYPE BODY COMPLEX AS\n" +
                        "    FUNCTION PLUS(X COMPLEX) RETURN COMPLEX IS\n" +
                        "    BEGIN\n" +
                        "    RETURN COMPLEX(RPART X.RPART, IPART X.IPART);\n" +
                        "    END;\n" +
                        "\n" +
                        "    FUNCTION LES(X COMPLEX) RETURN COMPLEX IS\n" +
                        "    BEGIN\n" +
                        "      RETURN COMPLEX(RPART-X.RPART, IPART-X.IPART);\n" +
                        "    END;\n" +
                        "  END;", "CREATE\n" +
                        "OR REPLACE PACKAGE PersonPackage AS E_NoPerson\n" +
                        "EXCEPTION;\n" +
                        "\n" +
                        "PersonCount INT;\n" +
                        "\n" +
                        "Pcur CURSOR;\n" +
                        "\n" +
                        "PROCEDURE AddPerson (Pname VARCHAR(100), Pcity varchar(100));\n" +
                        "\n" +
                        "PROCEDURE RemovePerson (Pname VARCHAR(100), Pcity varchar(100));\n" +
                        "\n" +
                        "PROCEDURE RemovePerson (Pid INT);\n" +
                        "\n" +
                        "FUNCTION GetPersonCount RETURN INT;\n" +
                        "\n" +
                        "PROCEDURE PersonList;\n" +
                        "\n" +
                        "END PersonPackage;");
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return List.of("CREATE CLASS CLS1 AS V ARR_NUM3;END;CREATE CLASS CLS1 AS V ARR_NUM3;END;");
    }

    @Override
    public List<String> getSqlList() {
        return null;
    }

}