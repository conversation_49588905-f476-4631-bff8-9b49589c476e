//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.cugos.wkg.internal;

import java.util.List;
import org.antlr.v4.runtime.NoViableAltException;
import org.antlr.v4.runtime.Parser;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.RuntimeMetaData;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.Vocabulary;
import org.antlr.v4.runtime.VocabularyImpl;
import org.antlr.v4.runtime.atn.ATN;
import org.antlr.v4.runtime.atn.ATNDeserializer;
import org.antlr.v4.runtime.atn.ParserATNSimulator;
import org.antlr.v4.runtime.atn.PredictionContextCache;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.tree.ParseTreeListener;
import org.antlr.v4.runtime.tree.ParseTreeVisitor;
import org.antlr.v4.runtime.tree.TerminalNode;

public class JSONParser extends Parser {
    protected static final DFA[] _decisionToDFA;
    protected static final PredictionContextCache _sharedContextCache;
    public static final int T__0 = 1;
    public static final int T__1 = 2;
    public static final int T__2 = 3;
    public static final int T__3 = 4;
    public static final int T__4 = 5;
    public static final int T__5 = 6;
    public static final int T__6 = 7;
    public static final int T__7 = 8;
    public static final int T__8 = 9;
    public static final int STRING = 10;
    public static final int NUMBER = 11;
    public static final int WS = 12;
    public static final int RULE_json = 0;
    public static final int RULE_obj = 1;
    public static final int RULE_pair = 2;
    public static final int RULE_array = 3;
    public static final int RULE_value = 4;
    public static final String[] ruleNames;
    private static final String[] _LITERAL_NAMES;
    private static final String[] _SYMBOLIC_NAMES;
    public static final Vocabulary VOCABULARY;
    /** @deprecated */
    @Deprecated
    public static final String[] tokenNames;
    public static final String _serializedATN = "\u0003悋Ꜫ脳맭䅼㯧瞆奤\u0003\u000e:\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0003\u0002\u0003\u0002\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0007\u0003\u0013\n\u0003\f\u0003\u000e\u0003\u0016\u000b\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0005\u0003\u001c\n\u0003\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0007\u0005&\n\u0005\f\u0005\u000e\u0005)\u000b\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0005\u0005/\n\u0005\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0005\u00068\n\u0006\u0003\u0006\u0002\u0002\u0007\u0002\u0004\u0006\b\n\u0002\u0002\u0002>\u0002\f\u0003\u0002\u0002\u0002\u0004\u001b\u0003\u0002\u0002\u0002\u0006\u001d\u0003\u0002\u0002\u0002\b.\u0003\u0002\u0002\u0002\n7\u0003\u0002\u0002\u0002\f\r\u0005\n\u0006\u0002\r\u0003\u0003\u0002\u0002\u0002\u000e\u000f\u0007\u0003\u0002\u0002\u000f\u0014\u0005\u0006\u0004\u0002\u0010\u0011\u0007\u0004\u0002\u0002\u0011\u0013\u0005\u0006\u0004\u0002\u0012\u0010\u0003\u0002\u0002\u0002\u0013\u0016\u0003\u0002\u0002\u0002\u0014\u0012\u0003\u0002\u0002\u0002\u0014\u0015\u0003\u0002\u0002\u0002\u0015\u0017\u0003\u0002\u0002\u0002\u0016\u0014\u0003\u0002\u0002\u0002\u0017\u0018\u0007\u0005\u0002\u0002\u0018\u001c\u0003\u0002\u0002\u0002\u0019\u001a\u0007\u0003\u0002\u0002\u001a\u001c\u0007\u0005\u0002\u0002\u001b\u000e\u0003\u0002\u0002\u0002\u001b\u0019\u0003\u0002\u0002\u0002\u001c\u0005\u0003\u0002\u0002\u0002\u001d\u001e\u0007\f\u0002\u0002\u001e\u001f\u0007\u0006\u0002\u0002\u001f \u0005\n\u0006\u0002 \u0007\u0003\u0002\u0002\u0002!\"\u0007\u0007\u0002\u0002\"'\u0005\n\u0006\u0002#$\u0007\u0004\u0002\u0002$&\u0005\n\u0006\u0002%#\u0003\u0002\u0002\u0002&)\u0003\u0002\u0002\u0002'%\u0003\u0002\u0002\u0002'(\u0003\u0002\u0002\u0002(*\u0003\u0002\u0002\u0002)'\u0003\u0002\u0002\u0002*+\u0007\b\u0002\u0002+/\u0003\u0002\u0002\u0002,-\u0007\u0007\u0002\u0002-/\u0007\b\u0002\u0002.!\u0003\u0002\u0002\u0002.,\u0003\u0002\u0002\u0002/\t\u0003\u0002\u0002\u000208\u0007\f\u0002\u000218\u0007\r\u0002\u000228\u0005\u0004\u0003\u000238\u0005\b\u0005\u000248\u0007\t\u0002\u000258\u0007\n\u0002\u000268\u0007\u000b\u0002\u000270\u0003\u0002\u0002\u000271\u0003\u0002\u0002\u000272\u0003\u0002\u0002\u000273\u0003\u0002\u0002\u000274\u0003\u0002\u0002\u000275\u0003\u0002\u0002\u000276\u0003\u0002\u0002\u00028\u000b\u0003\u0002\u0002\u0002\u0007\u0014\u001b'.7";
    public static final ATN _ATN;

    /** @deprecated */
    @Deprecated
    public String[] getTokenNames() {
        return tokenNames;
    }

    public Vocabulary getVocabulary() {
        return VOCABULARY;
    }

    public String getGrammarFileName() {
        return "JSON.g4";
    }

    public String[] getRuleNames() {
        return ruleNames;
    }

    public String getSerializedATN() {
        return "\u0003悋Ꜫ脳맭䅼㯧瞆奤\u0003\u000e:\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0003\u0002\u0003\u0002\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0007\u0003\u0013\n\u0003\f\u0003\u000e\u0003\u0016\u000b\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0005\u0003\u001c\n\u0003\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0007\u0005&\n\u0005\f\u0005\u000e\u0005)\u000b\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0005\u0005/\n\u0005\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0005\u00068\n\u0006\u0003\u0006\u0002\u0002\u0007\u0002\u0004\u0006\b\n\u0002\u0002\u0002>\u0002\f\u0003\u0002\u0002\u0002\u0004\u001b\u0003\u0002\u0002\u0002\u0006\u001d\u0003\u0002\u0002\u0002\b.\u0003\u0002\u0002\u0002\n7\u0003\u0002\u0002\u0002\f\r\u0005\n\u0006\u0002\r\u0003\u0003\u0002\u0002\u0002\u000e\u000f\u0007\u0003\u0002\u0002\u000f\u0014\u0005\u0006\u0004\u0002\u0010\u0011\u0007\u0004\u0002\u0002\u0011\u0013\u0005\u0006\u0004\u0002\u0012\u0010\u0003\u0002\u0002\u0002\u0013\u0016\u0003\u0002\u0002\u0002\u0014\u0012\u0003\u0002\u0002\u0002\u0014\u0015\u0003\u0002\u0002\u0002\u0015\u0017\u0003\u0002\u0002\u0002\u0016\u0014\u0003\u0002\u0002\u0002\u0017\u0018\u0007\u0005\u0002\u0002\u0018\u001c\u0003\u0002\u0002\u0002\u0019\u001a\u0007\u0003\u0002\u0002\u001a\u001c\u0007\u0005\u0002\u0002\u001b\u000e\u0003\u0002\u0002\u0002\u001b\u0019\u0003\u0002\u0002\u0002\u001c\u0005\u0003\u0002\u0002\u0002\u001d\u001e\u0007\f\u0002\u0002\u001e\u001f\u0007\u0006\u0002\u0002\u001f \u0005\n\u0006\u0002 \u0007\u0003\u0002\u0002\u0002!\"\u0007\u0007\u0002\u0002\"'\u0005\n\u0006\u0002#$\u0007\u0004\u0002\u0002$&\u0005\n\u0006\u0002%#\u0003\u0002\u0002\u0002&)\u0003\u0002\u0002\u0002'%\u0003\u0002\u0002\u0002'(\u0003\u0002\u0002\u0002(*\u0003\u0002\u0002\u0002)'\u0003\u0002\u0002\u0002*+\u0007\b\u0002\u0002+/\u0003\u0002\u0002\u0002,-\u0007\u0007\u0002\u0002-/\u0007\b\u0002\u0002.!\u0003\u0002\u0002\u0002.,\u0003\u0002\u0002\u0002/\t\u0003\u0002\u0002\u000208\u0007\f\u0002\u000218\u0007\r\u0002\u000228\u0005\u0004\u0003\u000238\u0005\b\u0005\u000248\u0007\t\u0002\u000258\u0007\n\u0002\u000268\u0007\u000b\u0002\u000270\u0003\u0002\u0002\u000271\u0003\u0002\u0002\u000272\u0003\u0002\u0002\u000273\u0003\u0002\u0002\u000274\u0003\u0002\u0002\u000275\u0003\u0002\u0002\u000276\u0003\u0002\u0002\u00028\u000b\u0003\u0002\u0002\u0002\u0007\u0014\u001b'.7";
    }

    public ATN getATN() {
        return _ATN;
    }

    public JSONParser(TokenStream input) {
        super(input);
        this._interp = new ParserATNSimulator(this, _ATN, _decisionToDFA, _sharedContextCache);
    }

    public final JsonContext json() throws RecognitionException {
        JsonContext _localctx = new JsonContext(this._ctx, this.getState());
        this.enterRule(_localctx, 0, 0);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(10);
            this.value();
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final ObjContext obj() throws RecognitionException {
        ObjContext _localctx = new ObjContext(this._ctx, this.getState());
        this.enterRule(_localctx, 2, 1);

        try {
            this.setState(25);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 1, this._ctx)) {
                case 1:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(12);
                    this.match(1);
                    this.setState(13);
                    this.pair();
                    this.setState(18);
                    this._errHandler.sync(this);

                    for(int _la = this._input.LA(1); _la == 2; _la = this._input.LA(1)) {
                        this.setState(14);
                        this.match(2);
                        this.setState(15);
                        this.pair();
                        this.setState(20);
                        this._errHandler.sync(this);
                    }

                    this.setState(21);
                    this.match(3);
                    break;
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(23);
                    this.match(1);
                    this.setState(24);
                    this.match(3);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final PairContext pair() throws RecognitionException {
        PairContext _localctx = new PairContext(this._ctx, this.getState());
        this.enterRule(_localctx, 4, 2);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(27);
            this.match(10);
            this.setState(28);
            this.match(4);
            this.setState(29);
            this.value();
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final ArrayContext array() throws RecognitionException {
        ArrayContext _localctx = new ArrayContext(this._ctx, this.getState());
        this.enterRule(_localctx, 6, 3);

        try {
            this.setState(44);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 3, this._ctx)) {
                case 1:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(31);
                    this.match(5);
                    this.setState(32);
                    this.value();
                    this.setState(37);
                    this._errHandler.sync(this);

                    for(int _la = this._input.LA(1); _la == 2; _la = this._input.LA(1)) {
                        this.setState(33);
                        this.match(2);
                        this.setState(34);
                        this.value();
                        this.setState(39);
                        this._errHandler.sync(this);
                    }

                    this.setState(40);
                    this.match(6);
                    break;
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(42);
                    this.match(5);
                    this.setState(43);
                    this.match(6);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final ValueContext value() throws RecognitionException {
        ValueContext _localctx = new ValueContext(this._ctx, this.getState());
        this.enterRule(_localctx, 8, 4);

        try {
            this.setState(53);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 1:
                    this.enterOuterAlt(_localctx, 3);
                    this.setState(48);
                    this.obj();
                    break;
                case 2:
                case 3:
                case 4:
                case 6:
                default:
                    throw new NoViableAltException(this);
                case 5:
                    this.enterOuterAlt(_localctx, 4);
                    this.setState(49);
                    this.array();
                    break;
                case 7:
                    this.enterOuterAlt(_localctx, 5);
                    this.setState(50);
                    this.match(7);
                    break;
                case 8:
                    this.enterOuterAlt(_localctx, 6);
                    this.setState(51);
                    this.match(8);
                    break;
                case 9:
                    this.enterOuterAlt(_localctx, 7);
                    this.setState(52);
                    this.match(9);
                    break;
                case 10:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(46);
                    this.match(10);
                    break;
                case 11:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(47);
                    this.match(11);
            }
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    static {
        RuntimeMetaData.checkVersion("4.7.1", "4.7.1");
        _sharedContextCache = new PredictionContextCache();
        ruleNames = new String[]{"json", "obj", "pair", "array", "value"};
        _LITERAL_NAMES = new String[]{null, "'{'", "','", "'}'", "':'", "'['", "']'", "'true'", "'false'", "'null'"};
        _SYMBOLIC_NAMES = new String[]{null, null, null, null, null, null, null, null, null, null, "STRING", "NUMBER", "WS"};
        VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);
        tokenNames = new String[_SYMBOLIC_NAMES.length];

        int i;
        for(i = 0; i < tokenNames.length; ++i) {
            tokenNames[i] = VOCABULARY.getLiteralName(i);
            if (tokenNames[i] == null) {
                tokenNames[i] = VOCABULARY.getSymbolicName(i);
            }

            if (tokenNames[i] == null) {
                tokenNames[i] = "<INVALID>";
            }
        }

        _ATN = (new ATNDeserializer()).deserialize("\u0003悋Ꜫ脳맭䅼㯧瞆奤\u0003\u000e:\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0003\u0002\u0003\u0002\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0007\u0003\u0013\n\u0003\f\u0003\u000e\u0003\u0016\u000b\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0005\u0003\u001c\n\u0003\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0007\u0005&\n\u0005\f\u0005\u000e\u0005)\u000b\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0005\u0005/\n\u0005\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0005\u00068\n\u0006\u0003\u0006\u0002\u0002\u0007\u0002\u0004\u0006\b\n\u0002\u0002\u0002>\u0002\f\u0003\u0002\u0002\u0002\u0004\u001b\u0003\u0002\u0002\u0002\u0006\u001d\u0003\u0002\u0002\u0002\b.\u0003\u0002\u0002\u0002\n7\u0003\u0002\u0002\u0002\f\r\u0005\n\u0006\u0002\r\u0003\u0003\u0002\u0002\u0002\u000e\u000f\u0007\u0003\u0002\u0002\u000f\u0014\u0005\u0006\u0004\u0002\u0010\u0011\u0007\u0004\u0002\u0002\u0011\u0013\u0005\u0006\u0004\u0002\u0012\u0010\u0003\u0002\u0002\u0002\u0013\u0016\u0003\u0002\u0002\u0002\u0014\u0012\u0003\u0002\u0002\u0002\u0014\u0015\u0003\u0002\u0002\u0002\u0015\u0017\u0003\u0002\u0002\u0002\u0016\u0014\u0003\u0002\u0002\u0002\u0017\u0018\u0007\u0005\u0002\u0002\u0018\u001c\u0003\u0002\u0002\u0002\u0019\u001a\u0007\u0003\u0002\u0002\u001a\u001c\u0007\u0005\u0002\u0002\u001b\u000e\u0003\u0002\u0002\u0002\u001b\u0019\u0003\u0002\u0002\u0002\u001c\u0005\u0003\u0002\u0002\u0002\u001d\u001e\u0007\f\u0002\u0002\u001e\u001f\u0007\u0006\u0002\u0002\u001f \u0005\n\u0006\u0002 \u0007\u0003\u0002\u0002\u0002!\"\u0007\u0007\u0002\u0002\"'\u0005\n\u0006\u0002#$\u0007\u0004\u0002\u0002$&\u0005\n\u0006\u0002%#\u0003\u0002\u0002\u0002&)\u0003\u0002\u0002\u0002'%\u0003\u0002\u0002\u0002'(\u0003\u0002\u0002\u0002(*\u0003\u0002\u0002\u0002)'\u0003\u0002\u0002\u0002*+\u0007\b\u0002\u0002+/\u0003\u0002\u0002\u0002,-\u0007\u0007\u0002\u0002-/\u0007\b\u0002\u0002.!\u0003\u0002\u0002\u0002.,\u0003\u0002\u0002\u0002/\t\u0003\u0002\u0002\u000208\u0007\f\u0002\u000218\u0007\r\u0002\u000228\u0005\u0004\u0003\u000238\u0005\b\u0005\u000248\u0007\t\u0002\u000258\u0007\n\u0002\u000268\u0007\u000b\u0002\u000270\u0003\u0002\u0002\u000271\u0003\u0002\u0002\u000272\u0003\u0002\u0002\u000273\u0003\u0002\u0002\u000274\u0003\u0002\u0002\u000275\u0003\u0002\u0002\u000276\u0003\u0002\u0002\u00028\u000b\u0003\u0002\u0002\u0002\u0007\u0014\u001b'.7".toCharArray());
        _decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];

        for(i = 0; i < _ATN.getNumberOfDecisions(); ++i) {
            _decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
        }

    }

    public static class ValueContext extends ParserRuleContext {
        public TerminalNode STRING() {
            return this.getToken(10, 0);
        }

        public TerminalNode NUMBER() {
            return this.getToken(11, 0);
        }

        public ObjContext obj() {
            return (ObjContext)this.getRuleContext(ObjContext.class, 0);
        }

        public ArrayContext array() {
            return (ArrayContext)this.getRuleContext(ArrayContext.class, 0);
        }

        public ValueContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 4;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).enterValue(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).exitValue(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof JSONVisitor ? (T) ((JSONVisitor)visitor).visitValue(this) : visitor.visitChildren(this);
        }
    }

    public static class ArrayContext extends ParserRuleContext {
        public List<ValueContext> value() {
            return this.getRuleContexts(ValueContext.class);
        }

        public ValueContext value(int i) {
            return (ValueContext)this.getRuleContext(ValueContext.class, i);
        }

        public ArrayContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 3;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).enterArray(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).exitArray(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof JSONVisitor ? (T) ((JSONVisitor)visitor).visitArray(this) : visitor.visitChildren(this);
        }
    }

    public static class PairContext extends ParserRuleContext {
        public TerminalNode STRING() {
            return this.getToken(10, 0);
        }

        public ValueContext value() {
            return (ValueContext)this.getRuleContext(ValueContext.class, 0);
        }

        public PairContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 2;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).enterPair(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).exitPair(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof JSONVisitor ? (T) ((JSONVisitor)visitor).visitPair(this) : visitor.visitChildren(this);
        }
    }

    public static class ObjContext extends ParserRuleContext {
        public List<PairContext> pair() {
            return this.getRuleContexts(PairContext.class);
        }

        public PairContext pair(int i) {
            return (PairContext)this.getRuleContext(PairContext.class, i);
        }

        public ObjContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 1;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).enterObj(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).exitObj(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof JSONVisitor ? (T) ((JSONVisitor)visitor).visitObj(this) : visitor.visitChildren(this);
        }
    }

    public static class JsonContext extends ParserRuleContext {
        public ValueContext value() {
            return (ValueContext)this.getRuleContext(ValueContext.class, 0);
        }

        public JsonContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 0;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).enterJson(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof JSONListener) {
                ((JSONListener)listener).exitJson(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof JSONVisitor ? (T) ((JSONVisitor)visitor).visitJson(this) : visitor.visitChildren(this);
        }
    }
}
