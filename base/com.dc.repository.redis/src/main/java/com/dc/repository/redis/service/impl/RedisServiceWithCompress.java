package com.dc.repository.redis.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("redisServiceWithCompress")
public class RedisServiceWithCompress extends AbstractRedisService {

    @Resource
    private RedisTemplate<String, Object> redisTemplateWithCompress;

    @Override
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplateWithCompress;
    }

    @Override
    public boolean printLog() {
        return false;
    }

}
