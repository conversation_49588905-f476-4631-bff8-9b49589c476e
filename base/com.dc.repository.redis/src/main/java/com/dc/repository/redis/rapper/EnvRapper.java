package com.dc.repository.redis.rapper;

import com.dc.repository.redis.model.*;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
public class EnvRapper extends BaseRapper {

    private static final String PREFIX = "env_";
    private static final String HASH_KEY_CONNECTION = "connection";
    private static final String HASH_KEY_USER = "user";
    private static final String HASH_KEY_SCHEMA = "schema";
    private static final String HASH_KEY_PARAMS_CONFIG = "params_config";
    private static final String HASH_KEY_REVIEW_CONFIG = "review_config";
    private static final String HASH_KEY_PERMISSIONS_DICTIONARY = "permissions_dictionary";

    public Env get(String token) {
        Map<Object, Object> map = redisService.hmget(PREFIX + token);
        Env env = new Env();
        env.setConnection(readValue(map.get(HASH_KEY_CONNECTION), EnvConnection.class));
        env.setUser(readValue(map.get(HASH_KEY_USER), EnvUser.class));
        env.setSchema(readValue(map.get(HASH_KEY_SCHEMA), EnvSchema.class));
        env.setParamsConfig(readValue(map.get(HASH_KEY_PARAMS_CONFIG), EnvParamsConfig.class));
        List<String> premissionDictionary = readValue(map.get(HASH_KEY_PERMISSIONS_DICTIONARY), new TypeReference<List<String>>() {
        });
        env.setPermDict(new EnvPermDict(premissionDictionary));
        Object object = map.get(HASH_KEY_REVIEW_CONFIG);
        if ("false".equals(object)) {
            EnvReviewConfig reviewConfig = new EnvReviewConfig();
            reviewConfig.setSkipReview(true);
            env.setReviewConfig(reviewConfig);
        } else {
            env.setReviewConfig(readValue(object, EnvReviewConfig.class));
        }
        return env;
    }

    public EnvConnection getConnection(String token) {
        return readValue(redisService.hget(PREFIX + token, HASH_KEY_CONNECTION), EnvConnection.class);
    }

    public void setConnection(String token, EnvConnection connection) {
        redisService.hset(PREFIX + token, HASH_KEY_CONNECTION, writeValueAsString(connection));
    }

    public EnvUser getUser(String token) {
        return readValue(redisService.hget(PREFIX + token, HASH_KEY_USER), EnvUser.class);
    }

    public void setUser(String token, EnvUser user) {
        redisService.hset(PREFIX + token, HASH_KEY_USER, writeValueAsString(user));
    }

    public EnvSchema getSchema(String token) {
        return readValue(redisService.hget(PREFIX + token, HASH_KEY_SCHEMA), EnvSchema.class);
    }

    public void setSchema(String token, EnvSchema schema) {
        redisService.hset(PREFIX + token, HASH_KEY_SCHEMA, writeValueAsString(schema));
    }

    public EnvParamsConfig getParamsConfig(String token) {
        return readValue(redisService.hget(PREFIX + token, HASH_KEY_PARAMS_CONFIG), EnvParamsConfig.class);
    }

    public void setParamsConfig(String token, EnvParamsConfig paramsConfig) {
        redisService.hset(PREFIX + token, HASH_KEY_PARAMS_CONFIG, writeValueAsString(paramsConfig));
    }

    public EnvReviewConfig getReviewConfig(String token) {
        Object object = redisService.hget(PREFIX + token, HASH_KEY_REVIEW_CONFIG);
        if ("false".equals(object)) {
            EnvReviewConfig reviewConfig = new EnvReviewConfig();
            reviewConfig.setSkipReview(true);
            return reviewConfig;
        } else {
            return readValue(object, EnvReviewConfig.class);
        }
    }

    public void setReviewConfig(String token, EnvReviewConfig reviewConfig) {
        redisService.hset(PREFIX + token, HASH_KEY_REVIEW_CONFIG, writeValueAsString(reviewConfig));
    }

}
