package com.dc.repository.redis.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class EnvUser {

    // 唯一值
    private String userId;

    // 用户名
    private String username;

    // 真实姓名
    private String realName;

    // 所有相关组织id
    private String orgIds;

    // 直接关联组织id
    @JsonProperty("r_org_ids")
    private String rOrgIds;

    // 组织名
    private String organizationName;

    private String hostname;

    private String ip;

    private List<Integer> userCatalogIds;

    private String userCatalog;

    @JsonIgnore
    public String getOperationUser() {
        return username + "(" + realName + ")";
    }

}
