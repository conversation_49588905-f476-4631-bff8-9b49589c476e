package com.dc.repository.redis.client;

import com.dc.repository.redis.listener.CloseDataSourceListener;
import com.dc.repository.redis.listener.RefreshDataSourceListener;
import com.dc.springboot.core.model.database.DataSourceMessage;
import org.springframework.stereotype.Service;


@Service
public class DataSourceClient extends BaseRedisClient {

    public void refresh(DataSourceMessage message) {
        publish(RefreshDataSourceListener.class, message);
    }

    public void close(DataSourceMessage message) {
        publish(CloseDataSourceListener.class, message);
    }

}
