package com.dc.repository.h2.config;

import com.baomidou.mybatisplus.core.MybatisSqlSessionFactoryBuilder;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
@Configuration
@MapperScan("com.dc.repository.mysql.mapper")
@ConfigurationProperties(value = "spring")
public class MyBatisH2Config {

    @Getter
    @Setter
    private HikariConfig h2;

    @Bean
    public DataSource dataSource() {
        h2.setDriverClassName("org.h2.Driver");
        return new HikariDataSource(h2);
    }

    @Bean
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws IOException {
        String resource = "mybatis-config.xml";
        InputStream inputStream = Resources.getResourceAsStream(resource);
        MybatisSqlSessionFactoryBuilder builder = new MybatisSqlSessionFactoryBuilder();
        SqlSessionFactory sqlSessionFactory = builder.build(inputStream);
        sqlSessionFactory.getConfiguration().setEnvironment(
                new org.apache.ibatis.mapping.Environment(
                        "development",
                        new org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory(),
                        dataSource
                )
        );
        return sqlSessionFactory;
    }

}
