package com.dc.repository.h2.mapper;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dc.annotation.SQL;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;

import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class TableSqlMapper {

    public static void executeDropTableSql(SqlSession sqlSession, Class<?> entityClass) throws SQLException {

        try {
            @SQL String sql = "DROP TABLE IF EXISTS " + entityClass.getAnnotation(TableName.class).value() + ";";
            log.debug("==> Preparing: {}", sql);
            int i = sqlSession.getConnection().createStatement().executeUpdate(sql);
            log.debug("<==   Updates: {}", i);
        } catch (Exception ignored) {
            // nothing to do here
        }
    }

    public static void executeCreateTableSql(SqlSession sqlSession, Class<?> entityClass) throws SQLException {

        try {
            String sql = TableSqlMapper.generateCreateTableSql(entityClass);
            log.debug("==> Preparing: {}", sql);
            int i = sqlSession.getConnection().createStatement().executeUpdate(sql);
            log.debug("<==   Updates: {}", i);
        } catch (Exception ignored) {
            // nothing to do here
        }
    }

    public static String generateCreateTableSql(Class<?> entityClass) {
        String tableName = entityClass.getAnnotation(TableName.class).value();
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" (");

        Field[] fields = entityClass.getDeclaredFields();
        List<String> columns = new ArrayList<>();
        for (Field field : fields) {
            String columnName = field.getName();
            String columnType = getColumnType(field);
            String columnAnnotation = getColumnAnnotation(field);
            columns.add(columnName + " " + columnType + " " + columnAnnotation);
        }
        sql.append(String.join(", ", columns));
        sql.append(");");

        return sql.toString();
    }

    private static String getColumnType(Field field) {
        Class<?> fieldType = field.getType();
        if (fieldType == Long.class || fieldType == long.class) {
            return "BIGINT";
        } else if (fieldType == String.class) {
            return "VARCHAR(255)";
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return "INT";
        } else if (fieldType == Date.class) {
            return "DATETIME";
        }
        return "VARCHAR(255)";
    }

    private static String getColumnAnnotation(Field field) {
        TableId tableId = field.getAnnotation(TableId.class);
        if (tableId != null) {
            return "PRIMARY KEY";
        }
        return "";
    }

}
