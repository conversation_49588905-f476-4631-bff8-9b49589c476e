<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcAccountSysPrivsMapper">
    <insert id="replaceInto" parameterType="java.util.List">
        REPLACE INTO dc_account_sys_privs (
        id,
        resource_id,
        account_id,
        is_expire,
        privilege_expire,
        gmt_create,
        gmt_modified,
        is_delete,
        grantee,
        privilege,
        admin_option
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.resourceId},
            #{item.accountId},
            #{item.isExpire},
            #{item.privilegeExpire},
            #{item.gmtCreate},
            #{item.gmtModified},
            #{item.isDelete},
            #{item.grantee},
            #{item.privilege},
            #{item.adminOption}
            )
        </foreach>
    </insert>

    <insert id="replaceIntoNoAdminOption" parameterType="java.util.List">
        INSERT INTO dc_account_sys_privs (
        id,
        resource_id,
        account_id,
        is_expire,
        privilege_expire,
        gmt_create,
        gmt_modified,
        is_delete,
        grantee,
        privilege,
        admin_option
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.resourceId},
            #{item.accountId},
            #{item.isExpire},
            #{item.privilegeExpire},
            #{item.gmtCreate},
            #{item.gmtModified},
            #{item.isDelete},
            #{item.grantee},
            #{item.privilege},
            #{item.adminOption}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        resource_id = VALUES(resource_id),
        account_id = VALUES(account_id),
        grantee = VALUES(grantee),
        privilege = VALUES(privilege),
        is_delete = VALUES(is_delete),
        gmt_create = VALUES(gmt_create),
        gmt_modified = VALUES(gmt_modified),
        is_expire = VALUES(is_expire),
        privilege_expire = VALUES(privilege_expire)
    </insert>
</mapper>