package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;


@Data
@TableName("dc_account_object_privs")
public class DcAccountObjPrivs {

    @TableId(type = IdType.AUTO)
    private Long id; // 主键

    @TableField("resource_id")
    private Integer resourceId; // 资源id

    @TableField("account_id")
    private Integer accountId; // 账号主键id

    @TableField("is_expire")
    private Byte isExpire; // 权限是否有过期

    @TableField("privilege_expire")
    private LocalDateTime privilegeExpire; // 权限截止时间

    @TableField("gmt_create")
    private LocalDateTime gmtCreate; // 创建时间

    @TableField("gmt_modified")
    private LocalDateTime gmtModified; // 更新时间

    @TableField("is_delete")
    private Byte isDelete; // 是否删除

    @TableField("grantee")
    private String grantee; // 被授予者

    @TableField("object_type")
    private String objectType; // 类型

    @TableField("catalog_name")
    private String catalogName; // 预留 catalog 名

    @TableField("schema_name")
    private String schemaName; // 对象 schema 名

    @TableField("object_name")
    private String objectName; // 对象名

    @TableField("privilege")
    private String privilege; // 授予的权限

    @TableField("grantor")
    private String grantor; // 授予者

    @TableField("grantable")
    private Byte grantable; // 是否可授予

    @TableField("hierarchy")
    private Byte hierarchy; // 层级

    public String getSchemaObject() {
        if (StringUtils.isBlank(schemaName)) {
            return "\"" + objectName + "\"";
        }
        return "\"" + schemaName + "\"" + "." + "\"" + objectName + "\"";
    }

}
