package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dc_privs_inspection_info")
public class DcPrivsInspectionInfo {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer inspection_account_id;
    private Integer auth_type;
    private String detail;
    private Integer is_delete;
    private LocalDateTime gmt_create;
    private LocalDateTime gmt_modified;
}

