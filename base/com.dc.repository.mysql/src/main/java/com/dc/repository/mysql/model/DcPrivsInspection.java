package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dc_privs_inspection")
public class DcPrivsInspection {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer inspection_id;
    private String code;
    private String resource_name;
    private Integer db_type;
    private String account;
    private Integer status;
    private String inspection_opinion;
    private String last_inspector;
    private Integer is_delete;
    private LocalDateTime gmt_create;
    private LocalDateTime gmt_inspecte;
    private LocalDateTime gmt_modified;
}
