package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_customization_config")
public class CustomizationConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "platform")
    private Integer platform;

    @TableField(value = "used")
    private String used;


    @TableField(value = "name_key")
    private String nameKey;

    @TableField(value = "name")
    private String name;

    @TableField(value = "content_type")
    private String contentType;

    @TableField(value = "content")
    private String content;

    @TableField(value = "extra")
    private String extra;


    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

}
