package com.dc.repository.mysql.utils;

import com.dc.utils.StackTraceUtils;
import com.dc.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.xmltags.XMLLanguageDriver;
import org.apache.ibatis.session.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MyBatisUtils {

    /**
     * 支持解析的的标签
     * mybatis 有很多标签，但是 DC 目前只用到以下标签
     */
    private static final String SUPPORTS_TAGS = "if|where";


    private MyBatisUtils() {
    }

    private static final XMLLanguageDriver XML_LANGUAGE_DRIVER = new XMLLanguageDriver();
    private static final Configuration CONFIGURATION = new Configuration();

    /**
     * select #{one} -> select ?
     */
    public static BoundSql getBindSql(String sql, Map<String, Object> params) {
        params = MapUtils.isNotEmpty(params) ? new HashMap<>(params) : new HashMap<>();
        retainDollarSign(sql, params);
        return parserScriptSql(sql, params);
    }

    /**
     * select #{one} -> select '1'
     */
    public static BoundSql getShowSql(String sql, Map<String, Object> params) {
        params = MapUtils.isNotEmpty(params) ? new HashMap<>(params) : new HashMap<>();
        retainDollarSign(sql, params);
        // #{}参数 换成 ${}参数 - 带 '
        sql = sql.replaceAll("#\\{(.*?)}", "'\\${$1}'");
        return parserScriptSql(sql, params);
    }

    /**
     * select #{one} -> select 1
     */
    public static BoundSql getParseSql(String sql, Map<String, Object> params) {
        params = MapUtils.isNotEmpty(params) ? new HashMap<>(params) : new HashMap<>();
        retainDollarSign(sql, params);
        // #{}参数 换成 ${}参数 - 不带 '
        sql = sql.replaceAll("#\\{(.*?)}", "\\${$1}");
        return parserScriptSql(sql, params);
    }

    private static BoundSql parserScriptSql(String sql, Map<String, Object> params) {

        // 转译 sql
        sql = sql.replaceAll("--.*", ""); // 去除单行注释（-- 开头的部分）
        sql = sql.replaceAll("/\\*.*?\\*/", ""); // 去除多行注释（/** */ 之间的部分）
        sql = sql.trim();
        String regex = "</?(?=" + SUPPORTS_TAGS + ")[^>]+>";  // 正则匹配非 <if> 和 <where> 标签及其内容
        sql = StringUtils.reverseReplace(sql, regex, MyBatisUtils::escapeSpecialCharacters);

        log.debug("[动态 SQL 解析器] - 转义 SQL ({}):\n{}", StackTraceUtils.getPreviousMethodName(), sql);

        // 解析 sql
        String dynamicSqlXml = "<script>" + sql + "</script>";
        SqlSource sqlSource = XML_LANGUAGE_DRIVER.createSqlSource(CONFIGURATION, dynamicSqlXml, Map.class);
        BoundSql boundSql = sqlSource.getBoundSql(params);
        log.debug("[动态 SQL 解析器] - 解析 SQL ({}):\n{}", StackTraceUtils.getPreviousMethodName(), boundSql.getSql());

        return boundSql;
    }

    /**
     * 转义特殊字符
     * mybatis 有几个特殊字符，无法支持，必须提前转义一下
     */
    private static String escapeSpecialCharacters(String input) {
        // 定义转义规则
        String[][] escapeRules = {
                {"&", "&amp;"}, // 必须最先替换，否则可能重复替换
                {"<", "&lt;"},
                {">", "&gt;"},
                {"'", "&apos;"},
                {"\"", "&quot;"}
        };

        // 依次应用每个替换规则
        for (String[] rule : escapeRules) {
            input = input.replaceAll(Pattern.quote(rule[0]), Matcher.quoteReplacement(rule[1]));
        }

        return input;
    }

    /**
     * 保留 ${} 参数
     * 目前 DC 不支持使用 ${} 来替换，但是 mybatis 却可以，所以把值替换成相同的 ${} 。
     */
    private static void retainDollarSign(String sql, Map<String, Object> params) {
        log.debug("[动态 SQL 解析器] - 原始 SQL ({}):\n{}", StackTraceUtils.getPreviousMethodName(), sql);
        // 保留 ${} 参数
        Matcher matcher = Pattern.compile("\\$\\{([^}]+)}").matcher(sql);
        while (matcher.find()) {
            params.put(matcher.group(1), matcher.group(0));
        }
    }

    public static void main(String[] args) {
        // 动态生成伪 XML 结构
        String sql = "-- <if>\n" +
                "select * -- <if>\n" +
                "        from order_sql_parse t /* <if> */\n" +
                "        <where>\n" +
//                "            t.order_id =${order_id}\n" +
                "            and t.script_name = #{script_name}\n" +
//                "            <if test='execute_status != null and execute_status != \"\"'>\n" +
                "            <if test=\"execute_status != null and 's'.equals(execute_status)\">\n" +
                "            and t.execute_status != #{execute_status}\n" +
                "            </if>\n" +
                "            and t.is_skip = #{is_skip}\n" +
                "            and t.type > #{type}\n" +
                "        </where>;select 1 ";

        HashMap<String, Object> map = new HashMap<>();

        map.put("script_name", "test.csv");
        map.put("execute_status", "s");
        map.put("is_skip", "0");
        map.put("type", "1");

        sql = "select 1";
        sql = "SELECT * FROM TEST1 WHERE 1=1 \n" +
                "<if test=\"ID=='ss'\">\n" +
                "    AND ID < #{ID}\n" +
                "</if>";
        map.clear();
        map.put("ID", "ss");

        sql = "select #{123}";
        map.clear();
        map.put("123", 1);

        sql = "SELECT FZJG,HPZL,HPHM FROM TRFF_APP.VEHICLE WHERE INSTR(ZT, 'B') = 0 AND INSTR(ZT, 'M') = 0 AND INSTR(ZT, 'P') = 0 AND CCDJRQ >= TO_DATE(#{CCDJRQ}, 'YYYY-MM-DD') AND CCDJRQ <TO_DATE(#{CCDJRQ}, 'YYYY-MM-DD')";
//        sql = "SELECT FZJG,HPZL,HPHM FROM TRFF_APP.VEHICLE WHERE INSTR(ZT, 'B') = 0 AND INSTR(ZT, 'M') = 0 AND INSTR(ZT, 'P') = 0 AND CCDJRQ &gt;= TO_DATE(#{CCDJRQ}, 'YYYY-MM-DD') AND CCDJRQ &lt; TO_DATE(#{CCDJRQ}, 'YYYY-MM-DD')";
//        sql = "SELECT FZJG,HPZL,HPHM FROM TRFF_APP.VEHICLE WHERE INSTR(ZT, 'B') = 0 AND INSTR(ZT, 'M') = 0 AND INSTR(ZT, 'P') = 0 <![CDATA[   AND CCDJRQ >= TO_DATE(#{CCDJRQ}, 'YYYY-MM-DD') AND CCDJRQ < TO_DATE(#{CCDJRQ}, 'YYYY-MM-DD')  ]]>";
//        sql = "select '&1'";
        map.clear();
        map.put("1", 1);

//        System.out.println(sql.replace("\n", " ").replaceAll("\\s+", " "));
//        System.out.println(new Gson().toJson(map));

        System.out.println("--------------show------------------");
        MyBatisUtils.getShowSql(sql, map);
        System.out.println("--------------bind------------------");
        MyBatisUtils.getBindSql(sql, map);
        System.out.println("--------------parse------------------");
        MyBatisUtils.getParseSql(sql, map);

    }

}
