package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_sys_user")
public class User {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    private String username;

    @TableField("real_name")
    private String realName;

    @TableField("is_active")
    private Long isActive;

    private Long gender;

    @TableField("open_id")
    private String openId;

    @TableField("is_new")
    private Long isNew;

    @TableField("gmt_modified")
    private Date gmtModified;

    private Long platform;

    @TableField("is_first_login")
    private Long isFirstLogin;

    @TableField("is_def_pwd")
    private Long isDefPwd;

    private String password;

    private String phone;

    private String extra;

    @TableField("create_mode")
    private Long createMode;

    private String email;

    @TableField("unique_key")
    private String uniqueKey;

    @TableField("org_ids")
    private String orgIds;

    @TableField("r_org_ids")
    private String rOrgIds;

    @TableField("role_ids")
    private String roleIds;

    @TableField("is_delete")
    private String isDelete;

}
