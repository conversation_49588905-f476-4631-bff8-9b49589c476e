package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("dc_connection_attempts")
public class DcConnectionAttempts {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("connect_id")
    private String connect_id;

    @TableField("schema_id")
    private String schema_id;

    @TableField("is_success")
    private String is_success;

    @TableField("is_delete")
    private String is_delete;

    @TableField("gmt_create")
    private String gmt_create;

    @TableField("gmt_modified")
    private String gmt_modified;


}
