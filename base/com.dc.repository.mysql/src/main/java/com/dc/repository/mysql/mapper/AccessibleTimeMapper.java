package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.model.AccessibleTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 访问管控表
 */
@Mapper
public interface AccessibleTimeMapper {

    AccessibleTime getInstanceAccessibleTime(@Param("con_id") String conId);

    List<AccessibleTime> getSchemasAccessibleTime(@Param("con_id") String conId, @Param("schema_ids") List<String> schemaIds);

    List<AccessibleTime> getTablesAccessibleTime(@Param("con_id") String conId, @Param("schema_ids") List<String> schemaIds, @Param("table_names") List<String> tableNames);

    List<AccessibleTime> getInstanceAccessibleTimes(@Param("con_id") String conId);

    List<AccessibleTime> getSchemaAccessibleTimes(@Param("con_id") String conId, @Param("schema_ids") Set<String> schemaIds);

    List<AccessibleTime> getTableAccessibleTimes(@Param("con_id") String conId, @Param("schema_ids") Set<String> schemaIds, @Param("table_names") Set<String> tableNames);

}
