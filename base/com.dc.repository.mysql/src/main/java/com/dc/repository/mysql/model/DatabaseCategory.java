package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_db_category")
public class DatabaseCategory {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "unique_key")
    private String uniqueKey;

    @TableField(value = "category_name")
    private String categoryName;

    @TableField(value = "category_names")
    private String categoryNames;

    @TableField(value = "category_ids")
    private String categoryIds;

    @TableField(value = "pid")
    private String pid;

    @TableField(value = "uid")
    private String uid;

    @TableField(value = "business_id")
    private String businessId;

    @TableField(value = "sync")
    private String sync;

    @TableField(value = "sort")
    private Integer sort;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

}
