package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.PaUserSuperior;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


@Mapper
public interface PaUserSuperiorMapper extends EasyBaseMapper<PaUserSuperior> {

    List<PaUserSuperior> getUsersByList(Map paParam);

    void updateBatchById(List<PaUserSuperior> list);

    @Update("truncate table pa_user_superior")
    void truncateTable();

}
