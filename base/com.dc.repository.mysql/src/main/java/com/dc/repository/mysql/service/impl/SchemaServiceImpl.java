package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.service.SchemaService;
import com.google.protobuf.ServiceException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class SchemaServiceImpl implements SchemaService {

    @Resource
    SchemaMapper schemaMapper;

    @Override
    public Schema get(String uniqueKey) throws Exception {
        Schema schema = schemaMapper.selectOne(new QueryWrapper<Schema>().lambda()
                .eq(Schema::getUnique_key, uniqueKey)
                .eq(Schema::getIs_delete, 0));
        Optional.ofNullable(schema).orElseThrow(() -> new ServiceException("schema not found"));
        return schema;
    }

    @Override
    public List<Schema> findSchemaList(List<String> uuidList) {
        List<Schema> data = schemaMapper.selectList(new QueryWrapper<Schema>().lambda()
                .in(Schema::getUnique_key, uuidList.stream().distinct().collect(Collectors.toList()))
                .eq(Schema::getIs_delete, 0));
        return data;
    }
}
