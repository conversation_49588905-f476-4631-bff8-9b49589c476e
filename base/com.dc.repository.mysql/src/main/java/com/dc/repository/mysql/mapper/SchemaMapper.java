package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.Schema;
import com.dc.type.DatabaseType;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper
public interface SchemaMapper extends EasyBaseMapper<Schema> {

    Schema updateTableCountById(@Param("unique_key") String uniqueKey, @Param("table_count") Integer tableCount);

    Schema updateSchemaCharsetById(@Param("unique_key") String uniqueKey, @Param("charset") String charset);

    Schema updateSchemaIsSysById(@Param("unique_key") String uniqueKey, @Param("is_sys") Integer isSys);

    void updateSchemaIsPrivate(@Param("connect_id") String connectId, @Param("schema_names")  List<String> schemaNames, @Param("is_private") Integer isPrivate);

    Boolean isPrivateSchema(String schemaUniqueKey);

    List<String> getAllPrivateSchemaId(@Param("connectId") String connectId);

    /**
     * 不建议接使用，请使用 MetaDataStoreService.getRealSchema()
     * <br/>
     * SQL:
     * <br/>
     * {@code
     *         select *
     *         from dc_db_schema
     *         where connect_id = #{unique_key}
     *         and (
     *             (
     *                 db_type not in
     *                 <foreach collection="db_types" item="db_type" index="index" open="(" close=")" separator=",">
     *                     #{db_type}
     *                 </foreach>
     *                 and lower(schema_name) = lower(#{schema_name})
     *             )
     *             or
     *             (
     *                 db_type in
     *                 <foreach collection="db_types" item="db_type" index="index" open="(" close=")" separator=",">
     *                     #{db_type}
     *                 </foreach>
     *                 and lower(schema_name) = lower(#{schema_name})
     *                 and lower(catalog_name) = lower(#{catalog_name})
     *             )
     *         )
     *         and is_delete = 0
     * }
     */
    default List<Schema> getSchemasByNameIgnoreCase(String connectId, String catalogName, String schemaName) {
        List<Integer> dbTypes = DatabaseType.getCatalogDatabaseIntegerValueList();
        return selectList(Wrappers.<Schema>lambdaQuery()
                .eq(Schema::getConnect_id, connectId)
                .apply("lower(schema_name) = lower({0})", schemaName)
                .eq(Schema::getIs_delete, 0)
                .apply(StringUtils.isNotBlank(catalogName), "lower(catalog_name) = lower({0})", catalogName))
                .stream()
                .filter(schema -> {
                    if (dbTypes.contains(schema.getDb_type()) && catalogName != null && schema.getCatalog_name() != null) {
                        return catalogName.equalsIgnoreCase(schema.getCatalog_name());
                    } else {
                        return !dbTypes.contains(schema.getDb_type());
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 通用获取 schema
     */
    default Optional<Schema> getSchemaByUniqueKey(String uniqueKey) {
        return Optional.ofNullable(
                selectOne(Wrappers.<Schema>lambdaQuery()
                        .eq(Schema::getUnique_key, uniqueKey)
                        .eq(Schema::getIs_delete, 0)));
    }

    /**
     * es 只有一个虚拟 schema
     */
    default Optional<Schema> getSchemaByConnectId(String connectId) {
        return Optional.ofNullable(
                selectOne(Wrappers.<Schema>lambdaQuery()
                        .eq(Schema::getConnect_id, connectId)
                        .eq(Schema::getIs_delete, 0)));
    }

    /**
     * 跨库查询，根据 catalogName、schemaName、connectId 反查 schema
     */
    default Optional<Schema> getSchemaByName(String connectId, String catalogName, String schemaName) {
        return Optional.ofNullable(
                selectOne(Wrappers.<Schema>lambdaQuery()
                        .eq(Schema::getConnect_id, connectId)
                        .eq(StringUtils.isNotBlank(catalogName), Schema::getCatalog_name, catalogName)
                        .eq(Schema::getSchema_name, schemaName)
                        .eq(Schema::getIs_delete, 0)));
    }

}
