package com.dc.repository.mysql.service.impl;


import com.dc.repository.mysql.mapper.DcWorkOrderMapper;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.service.OrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


@Service
public class OrderServiceImpl implements OrderService {

    @Resource
    private DcWorkOrderMapper dcWorkOrderMapper;

    @Override
    public Order getById(Integer id) {
        return dcWorkOrderMapper.getById(id);
    }

    @Override
    public int updateStatus(Order order) {
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setAudit_status(order.getAudit_status());
        updateOrder.setCurrent_status(order.getCurrent_status());
        return dcWorkOrderMapper.updateById(updateOrder);
    }

    @Override
    public int updateStatusWithReason(Order order) {
        return dcWorkOrderMapper.updateStatusWithReason(order);
    }

    @Override
    public void updateStatusByModel(Integer id, Integer status) {
        Order order = new Order();
        order.setId(id);
        order.setCurrent_status(status);
        updateStatus(order);
    }

    @Override
    public int updateFailStatus(Map<String, Object> map) {
        return dcWorkOrderMapper.updateFailStatus(map);
    }

    @Override
    public int updateAuditStatus(Map<String, Object> map) {
        return dcWorkOrderMapper.updateAuditStatus(map);
    }

    @Override
    public int updateStatus(Order order, Integer status) {
        return dcWorkOrderMapper.updateByStatus(order, status);
    }

    @Override
    public int existsOrderExecute(Integer id) {
        return dcWorkOrderMapper.existsOrderExecute(id);
    }

    @Override
    public int updateToken(Order order) {
        return dcWorkOrderMapper.updateToken(order);
    }

    @Override
    public int updateCheckFailReason(Order order) {
        return dcWorkOrderMapper.updateCheckFailReason(order);
    }

    @Override
    public int updateExecuteFailReason(Order order) {
        return dcWorkOrderMapper.updateExecuteFailReason(order);
    }

    public int updateOrderStatus(Order order) {
        return dcWorkOrderMapper.updateOrderStatus(order);
    }

    @Override
    public int updateStatusAndCheckFailReason(Order order) {
        return dcWorkOrderMapper.updateStatusAndCheckFailReason(order);
    }

    @Override
    public int updateCurrentStatus(Order order) {
        return dcWorkOrderMapper.updateStatus(order);
    }


}
