package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户配置表
 */
@Data
@TableName("dc_sys_user_config")
public class UserLimit {

    private Long id;

    private String gmt_create;

    private String gmt_modified;

    /**
     * 用户级别参数key(select_limit,delete_limit)
     */
    private String pd_key;

    /**
     * 用户级别value
     */
    private String pd_value;

    /**
     * 所属功能
     */
    private String action;

    /**
     * 描述
     */
    private String comment;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 是否删除: 0 否 1 是
     */
    private Long is_delete;

}
