package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.DcAccountObjPrivs;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcAccountObjPrivsMapper extends EasyBaseMapper<DcAccountObjPrivs> {
    default List<DcAccountObjPrivs> getPrivileges(LambdaQueryWrapper<DcAccountObjPrivs> wrapper) {
        return selectList(wrapper);
    }

    default Integer removePrivilege(LambdaQueryWrapper<DcAccountObjPrivs> wrapper) {
        return delete(wrapper);
    }

    Integer insertIgnore(List<DcAccountObjPrivs> dcAccountObjPrivs);

    Integer replaceInto(List<DcAccountObjPrivs> dcAccountObjPrivs);

    Integer replaceIntoNoGrantable(List<DcAccountObjPrivs> dcAccountObjPrivs);

}
