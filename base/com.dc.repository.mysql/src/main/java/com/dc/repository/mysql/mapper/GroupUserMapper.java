package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.CiGroupUser;
import com.dc.repository.mysql.model.InstanceRole;
import com.dc.repository.mysql.model.PermissionRule;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


@Mapper
public interface GroupUserMapper extends EasyBaseMapper<CiGroupUser> {

    List<InstanceRole> getInstanceRole();

    List<PermissionRule> getUserAuth(Map<String, Object> map);

    List<PermissionRule> getUserAccountDirectConnectionAuth(Map<String, Object> map);

    List<PermissionRule> getUserTableGroupAuth(Map<String, Object> map);

    Boolean hasConnectionAuth(Map<String, Object> map);

    void deleteByUserId(List<String> userIds);

    InstanceRole getPrivateTableInstanceRole();

    @MapKey("user_id")
    List<Map<String, String>> getUserSoonExpireAuth(@Param("now") long now, @Param("nowPlusThreshold") long nowPlusThreshold);
}
