package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.OrderSqlParse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface OrderSqlParserMapper extends EasyBaseMapper<OrderSqlParse> {

    int count(Map<String, Object> params);

    List<OrderSqlParse> query(Map<String, Object> param);

    List<OrderSqlParse> queryByEqual(Map<String, Object> param);

    void updateBatch(List<OrderSqlParse> list);

    void update(OrderSqlParse sqlParse);

    void updateAllPauseSqlParse(OrderSqlParse orderSqlParse);

    void renewOrderSqlParser(OrderSqlParse orderSqlParse);

    void updateLogAndExecuteStatus(OrderSqlParse orderSqlParse);

}
