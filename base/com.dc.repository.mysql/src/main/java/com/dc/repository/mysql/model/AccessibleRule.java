package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 访问规则表
 */
@Data
@TableName("dc_db_accessible_rule")
public class AccessibleRule {

    @TableId
    private Long id; //主键

    @TableField("code_num")
    private String codeNum; //规则编号

    @TableField("ip")
    private String ip; //ip地址

    @TableField("operations")
    private String operations; //特例放行，逗号拼接

    @TableField("type")
    private String type; //时间段类型

    @TableField("accessible_time")
    private String accessibleTime; //json串

    @TableField("description")
    private String description;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

}
