package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.CustomizationConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface CustomizationConfigMapper extends EasyBaseMapper<CustomizationConfig> {

    default List<CustomizationConfig> findByNameKey(String nameKey) {
        return selectList(Wrappers.<CustomizationConfig>lambdaQuery()
                .eq(CustomizationConfig::getIsDelete, 0)
                .eq(CustomizationConfig::getName<PERSON>ey, nameKey)
        );
    }

}
