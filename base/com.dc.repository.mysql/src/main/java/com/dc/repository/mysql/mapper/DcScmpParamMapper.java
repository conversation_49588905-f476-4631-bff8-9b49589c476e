package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.model.DcScmpParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcScmpParamMapper extends BaseMapper<DcScmpParam> {

    default List<DcScmpParam> getParamByDcJobId(Integer dcJobId) {
        return selectList(Wrappers.<DcScmpParam>lambdaQuery()
                .eq(DcScmpParam::getDcJobId, dcJobId));
    }

}
