package com.dc.springboot.core.client;

import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.parser.ParserCheckMessage;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ParserSqlClient extends BaseRestClient {

    public List<WebSQLParserResult> preCheckSql(Client client, ParserCheckMessage message) {
        return postJava(client.toIceageClient().getUrl("/sql/preCheckSql"), message, new ParameterizedTypeReference<Result<List<WebSQLParserResult>>>() {
        });
    }

    public List<WebSQLParserResult> executeSql(Client client, ParserParamDto message) {
        return postJava(client.toIceageClient().getUrl("/sql/executeSql"), message, new ParameterizedTypeReference<Result<List<WebSQLParserResult>>>() {
        });
    }

    public List<WebSQLParserResult> preCheckSql(Client client, ParserParamDto message) {
        return postJava(client.toIceageClient().getUrl("/sql/preCheckSql"), message, new ParameterizedTypeReference<Result<List<WebSQLParserResult>>>() {
        });
    }

}
