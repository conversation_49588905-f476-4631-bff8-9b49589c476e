package com.dc.springboot.core.model.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("PHP返回结果")
public class BackendResult<T> {

    @ApiModelProperty(value = "结果实体")
    private T data;

    @ApiModelProperty(value = "结果代码")
    private int status;

    @ApiModelProperty(value = "结果信息")
    private String message;

    public boolean isSuccess() {
        return status == 0;
    }

}
