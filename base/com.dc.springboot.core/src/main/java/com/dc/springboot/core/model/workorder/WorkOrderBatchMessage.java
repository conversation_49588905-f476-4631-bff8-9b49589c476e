package com.dc.springboot.core.model.workorder;

import com.dc.springboot.core.model.message.ExecuteEvent;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ToString(callSuper = true)
@ApiModel("批量变更工单信息")
public class WorkOrderBatchMessage {
    @Valid
    @NotNull
    @ApiModelProperty(value = "工单模型", required = true)
    @JsonProperty("data")
    private WorkOrderModel workOrderModel;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @JsonProperty("schema_data")
    @ApiModelProperty(value = "使用当前执行器的schema集合")
    private List<SchemaInfo> schema_list;
}
