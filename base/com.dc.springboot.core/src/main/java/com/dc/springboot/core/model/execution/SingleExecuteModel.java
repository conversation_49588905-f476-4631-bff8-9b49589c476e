package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.component.DefaultMapper;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.sensitive.SqlDesensitization;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.Valid;
import java.util.List;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("单一执行模型")
public class SingleExecuteModel extends SqlExecuteModel {

    @Valid
    @ApiModelProperty(value = "SQL脱敏")
    private SqlDesensitization sqlDesensitization = new SqlDesensitization();

    @ApiModelProperty(value = "表名", example = "time")
    private String tableName;

    @ApiModelProperty(value = "结果集只读", example = "false")
    private boolean readOnly = true;

    @JsonProperty("is_limit")
    @ApiModelProperty(value = "启用限制", example = "true")
    private boolean enableLimit;

    @ApiModelProperty(value = "主键列", example = "id", dataType = "List")
    private List<String> primaryKeyColumns;

    @ApiModelProperty(value = "dcl授权信息模型", allowEmptyValue = true, hidden = true)
    private PrivilegeModel privilegeModel = new PrivilegeModel();

    @JsonIgnore
    public BatchExecuteModel getBatchExecuteModel() {
        if (this instanceof BatchExecuteModel) {
            return (BatchExecuteModel) this;
        } else {
            return DefaultMapper.INSTANCE.toBatchExecuteModel(this);
        }
    }
}
