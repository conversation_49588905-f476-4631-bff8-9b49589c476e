package com.dc.springboot.core.model.data;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;

/**
 * 行数据
 * 这是结果的集合，每一行数据都是一个Map。
 * Map 的 key 是 列名，String 类型。
 * Map 的 value 是这一行的值，Object 类型。
 * 这些行最后拼接成 ArrayList 返回。
 */
public class RowsData extends ArrayList<Map<String, Object>> {
    public RowsData(int initialCapacity) {
        super(initialCapacity);
    }

    public RowsData() {
    }

    public RowsData(@NotNull Collection<? extends Map<String, Object>> c) {
        super(c);
    }
}
