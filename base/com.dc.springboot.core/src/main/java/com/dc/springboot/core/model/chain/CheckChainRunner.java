package com.dc.springboot.core.model.chain;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Check<PERSON>hainRunner<T> implements ChainRunner<T> {

    @Override
    public boolean run(T t, Chain<T> chain) {
        if (t == null) {
            log.error("检查执行异常！ - null");
            return false;
        }
        if (chain == null) {
            return false;
        }
        boolean proceed = true;
        long currentTimeMillis = System.currentTimeMillis();
        try {
            proceed = chain.proceed(t);
        } catch (Exception e) {
            log.error("检查执行异常！", e);
        } finally {
            log.debug("Check ({}) -> Chain of Responsibility ({}), spend [{}] ms.", proceed, chain.getClass().getSimpleName(), System.currentTimeMillis() - currentTimeMillis);
        }
        if (proceed) {
            if (chain.next() != null) {
                return run(t, chain.next());
            } else {
                return true;
            }
        }
        return false;
    }
}
