package com.dc.springboot.core.model.workorder;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ExecuteOrderBatchMessage {

    @Valid
    @NotNull
    @ApiModelProperty(value = "执行工单模型", required = true)
    @JsonProperty("data")
    private ExecuteOrderModel executeOrderModel;

    @Valid
    @NotNull
    @ApiModelProperty(value = "执行人用户信息", required = true)
    @JsonProperty("user")
    private OperatorUserInfo operatorUserInfo;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @ApiModelProperty(value = "审批人")
    private String operationUser;

    @ApiModelProperty(value = "工单敏感权限")
    private String enableDesensitizeType;

    @JsonProperty("schema_data")
    @ApiModelProperty(value = "使用当前执行器的schema集合")
    private List<SchemaInfo> schema_list;
}
