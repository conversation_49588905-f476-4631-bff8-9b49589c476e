package com.dc.springboot.core.component;

import com.dc.springboot.core.model.condition.CloseDataSourceCondition;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@Conditional(CloseDataSourceCondition.class)
public class CloseDataSourceHolder implements ApplicationRunner {

    private static final long INITIAL_DELAY = 0;
    private static final long PERIOD = 200;
    private static final TimeUnit UNIT = TimeUnit.MILLISECONDS;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1);
        executor.scheduleAtFixedRate(() -> {
            try {
                DataSourceConnectionHandler.getFake().closeExpiredDataSource();
            } catch (Exception e) {
                log.error("任务异常！", e);
            }
        }, INITIAL_DELAY, PERIOD * 300, UNIT);
    }
}
