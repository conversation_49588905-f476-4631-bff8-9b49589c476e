package com.dc.springboot.core.model.type;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderSchemaTaskStatus {

    IN_LINE(0, "排队中"),
    WAIT_CHECK(1, "未校验"),
    CHECKING(2, "校验中"),
    FAIL(3, "失败"),
    SUCCESS(4, "成功"),
    IN_EXECUTION(5, "执行中"),
    INTERRUPTED(6, "已中断"),
    INTERRUPTING(7, "正在中断"),
    SKIP(8, "跳过"),

    NONE(99, "未知");

    private final Integer value;
    private final String name;

    public static OrderSchemaTaskStatus of(Integer currentStatus) {
        for (OrderSchemaTaskStatus value : OrderSchemaTaskStatus.values()) {
            if (value.value.compareTo(currentStatus) == 0) {
                return value;
            }
        }
        return NONE;
    }

    public static OrderSchemaTaskStatus of(OrderSqlExecuteStatus orderSqlExecuteStatus) {
        switch (orderSqlExecuteStatus) {
            case execute_fail:
                return FAIL;
            case execute_success:
                return SUCCESS;
            case execute_interrupt:
                return INTERRUPTED;
        }
        return NONE;
    }
}
