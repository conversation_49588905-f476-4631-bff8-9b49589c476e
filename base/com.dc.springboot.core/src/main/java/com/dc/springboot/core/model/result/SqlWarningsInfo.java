package com.dc.springboot.core.model.result;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class SqlWarningsInfo {


    private final DBRProgressMonitor monitor;

    private final WebSQLQueryResult queryResult;

    SqlWarningsInfo(DBRProgressMonitor monitor, WebSQLQueryResult queryResult) {
        this.monitor = monitor;
        this.queryResult = queryResult;
    }

    public void beforeExecute(DBCStatement dbcStatement, DBPDataSource dataSource, DBCSession session, String sql) throws DBCException {
        try {

            List<String> statementWarningList = dataSource.getInfo().getStatementWarningList(dbcStatement, session, sql);
            queryResult.setWarnings(statementWarningList);
        } catch (Exception e) {
            log.error("获取 sqlWarning 失败: {}", e.getMessage());
        }
    }

    public void afterExecute(DBCExecutionContext executionContext) {
        try {
            if (executionContext instanceof JDBCExecutionContext) {
                List<String> warnings = ((JDBCExecutionContext) executionContext).getDataSource().readServerOutput(monitor, executionContext, null, null, null);
                if (queryResult.getWarnings() != null && queryResult.getWarnings().size() > 0) {
                    if (warnings == null || warnings.size() > 0) {
                        warnings = new ArrayList<>();
                    }
                    warnings.addAll(queryResult.getWarnings());
                }
                queryResult.setWarnings(warnings);
            }
        } catch (Exception e) {
            log.error("获取 Output 失败！", e);
        }
    }
}
