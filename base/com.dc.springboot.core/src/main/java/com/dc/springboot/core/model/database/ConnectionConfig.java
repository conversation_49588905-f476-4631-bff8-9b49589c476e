package com.dc.springboot.core.model.database;

import com.dc.springboot.core.component.DefaultMapper;
import com.dc.summer.exec.config.SessionConfig;
import com.dc.type.AuthSourceType;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.type.DatabaseType;
import com.dc.summer.model.connection.DBPConnectionBootstrap;
import com.dc.summer.model.connection.DBPConnectionType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("连接配置")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ConnectionConfig extends TestConnectionMessage {

    @NotBlank
    @ApiModelProperty(value = "驱动ID - 详情请见 /dc-summer/monitor/database/stat.home （drivers）", required = true, example = "mysql8")
    private String driverId;

    @NotBlank
    @ApiModelProperty(value = "连接ID", required = true, example = "mock_connection")
    private String connectionId;

    @ApiModelProperty(value = "连接类型 - DEV、TEST、PROD", example = "DEV")
    private String connectionType;

    @ApiModelProperty(value = "保持活动间隔", example = "0")
    private int keepAliveInterval;

    @ApiModelProperty(value = "关闭空闲间隔", example = "0")
    private int closeIdleInterval;

    @ApiModelProperty(value = "字符集", example = "1208")
    private String charset;

    @ApiModelProperty(value = "连接详情", example = "mock_connection")
    private String connectionDesc;

    @ApiModelProperty(value = "单节点最大连接数量", example = "100")
    private Integer maximumContextSize;

    @ApiModelProperty(value = "会话保持时间", example = "1800")
    private Long keepAliveTime;

    @ApiModelProperty(value = "心跳时间 - 安全规则中配置的检查连接时间）", example = "1")
    private Long heartBeatTime;

    @ApiModelProperty(value = "查询超时时间", example = "180")
    private Integer queryTimeout;

    @ApiModelProperty(value = "网络超时时间", example = "180")
    private Integer networkTimeout;

    @ApiModelProperty(value = "事务隔离级别", example = "2")
    private Integer transactionIsolationLevel = 2;

    @ApiModelProperty(value = "oracle 数据库名", example = "abc")
    private String dbName;

    @ApiModelProperty(value = "实例是否可用", example = "true")
    private boolean activeInstance;

    @JsonIgnore
    private ConnectionConfiguration connectionConfiguration;

    /**
     * 事物级别参考
     * @see com.dc.summer.model.impl.jdbc.JDBCTransactionIsolation
     */
    @JsonIgnore
    public ConnectionConfiguration getConnectionConfiguration() {

        if (connectionConfiguration == null) {
            super.download();
            ConnectionConfiguration connectionConfiguration = DefaultMapper.INSTANCE.toConnectionConfiguration(this, SessionConfig.getInstance());
            connectionConfiguration.setDatabaseType(DatabaseType.of(super.getDatabaseType()));
            connectionConfiguration.setAuthSourceType(AuthSourceType.of(super.getAuthSource()));
            connectionConfiguration.setConnectionType(DBPConnectionType.of(connectionType));
            DBPConnectionBootstrap bootstrap = connectionConfiguration.getBootstrap();
            bootstrap.setDefaultTransactionIsolation(transactionIsolationLevel);
            convertUrlType(connectionConfiguration);
            connectionConfiguration.setUserPassword(getConnectionUserPassword());
            this.connectionConfiguration = connectionConfiguration;
        }

        return connectionConfiguration;
    }

    public String makeConcatSchemaName() {
        if (StringUtils.isNotBlank(getCatalogName()) && !StringUtils.isEmpty(getSchemaName())) {
            return getCatalogName() + "." + getSchemaName();
        }
        if (StringUtils.isNotBlank(getCatalogName())) {
            return getCatalogName();
        }
        if (StringUtils.isNotBlank(getSchemaName())) {
            return getSchemaName();
        }
        return null;
    }

}
