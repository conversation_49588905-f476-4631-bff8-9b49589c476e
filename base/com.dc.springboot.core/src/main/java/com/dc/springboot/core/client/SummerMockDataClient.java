package com.dc.springboot.core.client;

import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.database.MockDataMessage;
import org.springframework.stereotype.Service;

@Service
public class SummerMockDataClient extends BaseRestClient {

    public void mockdataGenerate(Client client, MockDataMessage message) {
        post(client.toSummerClient().getUrl("/mock-data/generate"), message);
    }
}
