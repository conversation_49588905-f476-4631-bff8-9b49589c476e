package com.dc.springboot.core.model.result;

import com.dc.summer.model.exec.DBCExecutionContext;

public class HighLightInvalid implements HighLightInfo {


    @Override
    public void execValidate(DBCExecutionContext executionContext) {

    }

    @Override
    public void isReExecute() {

    }

    @Override
    public void execError(Throwable error) {
    }

    @Override
    public void execSuccess() {
    }

    @Override
    public void execFinally(DBCExecutionContext executionContext) {
    }

}
