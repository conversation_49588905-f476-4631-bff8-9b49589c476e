
package com.dc.infra.spi;

import com.dc.infra.spi.annotation.SingletonSPI;
import com.google.common.base.Preconditions;
import lombok.SneakyThrows;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Registered ShardingSphere SPI.
 * 
 * @param <T> type of service
 */
class RegisteredShardingSphereSPI<T> {
    
    private final Class<T> serviceInterface;
    
    private final Collection<T> services;
    
    RegisteredShardingSphereSPI(final Class<T> serviceInterface) {
        this.serviceInterface = serviceInterface;
        validate();
        services = load();
    }
    
    private void validate() {
        Preconditions.checkNotNull(serviceInterface, "SPI interface is null.");
        Preconditions.checkArgument(serviceInterface.isInterface(), "SPI `%s` is not an interface.", serviceInterface);
    }
    
    private Collection<T> load() {
        Collection<T> result = new LinkedList<>();
//        for (T each : ServiceLoader.load(serviceInterface)) {
        for (T each : RegisterLoader.load(serviceInterface)) {
            result.add(each);
        }
        return result;
    }
    
    Collection<T> getServiceInstances() {
        return null == serviceInterface.getAnnotation(SingletonSPI.class) ? createNewServiceInstances() : getSingletonServiceInstances();
    }
    
    @SneakyThrows(ReflectiveOperationException.class)
    @SuppressWarnings("unchecked")
    private Collection<T> createNewServiceInstances() {
        Collection<T> result = new LinkedList<>();
        for (Object each : services) {
            result.add((T) each.getClass().getDeclaredConstructor().newInstance());
        }
        return result;
    }
    
    private Collection<T> getSingletonServiceInstances() {
        return services;
    }
}
