
package com.dc.infra.database.enums;

import com.google.common.base.Strings;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Quote character.
 */
@RequiredArgsConstructor
@Getter
public enum QuoteCharacter {
    
    BACK_QUOTE("`", "`"),
    
    SINGLE_QUOTE("'", "'"),
    
    QUOTE("\"", "\""),
    
    BRACKETS("[", "]"),
    
    PARENTHESES("(", ")"),
    
    NONE("", "");
    
    private static final Map<Character, QuoteCharacter> BY_FIRST_CHAR = new HashMap<>(QuoteCharacter.values().length - 1, 1L);
    
    static {
        for (QuoteCharacter each : values()) {
            if (NONE == each) {
                continue;
            }
            BY_FIRST_CHAR.put(each.startDelimiter.charAt(0), each);
        }
    }
    
    private final String startDelimiter;
    
    private final String endDelimiter;
    
    /**
     * Get quote character.
     * 
     * @param value value to be get quote character
     * @return value of quote character
     */
    public static QuoteCharacter getQuoteCharacter(final String value) {
        if (Strings.isNullOrEmpty(value)) {
            return NONE;
        }
        return BY_FIRST_CHAR.getOrDefault(value.charAt(0), NONE);
    }
    
    /**
     * Wrap value with quote character.
     * 
     * @param value value to be wrapped
     * @return wrapped value
     */
    public String wrap(final String value) {
        return startDelimiter + value + endDelimiter;
    }
    
    /**
     * Unwrap value with quote character.
     *
     * @param value value to be unwrapped
     * @return unwrapped value
     */
    public String unwrap(final String value) {
        return isWrapped(value) ? value.substring(startDelimiter.length(), value.length() - endDelimiter.length()) : value;
    }
    
    /**
     * Is wrapped by quote character.
     * 
     * @param value value to be judged
     * @return is wrapped or not
     */
    public boolean isWrapped(final String value) {
        return value.startsWith(startDelimiter) && value.endsWith(endDelimiter);
    }
}
