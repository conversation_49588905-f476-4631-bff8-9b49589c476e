
package com.dc.infra.exception.type.kernel.category;

import com.dc.infra.exception.sqlstate.SQLState;
import com.dc.infra.exception.type.kernel.KernelSQLException;

/**
 * Meta data SQL exception.
 */
public abstract class MetaDataSQLException extends KernelSQLException {
    
    private static final long serialVersionUID = 884884613851959565L;
    
    private static final int KERNEL_CODE = 0;
    
    protected MetaDataSQLException(final SQLState sqlState, final int errorCode, final String reason, final Object... messageArgs) {
        super(sqlState, KERNEL_CODE, errorCode, reason, messageArgs);
    }
    
    protected MetaDataSQLException(final SQLState sqlState, final int errorCode, final String reason, final Exception cause) {
        super(sqlState, KERNEL_CODE, errorCode, reason, cause);
    }
}
