
package com.dc.infra.exception.type.kernel.category;

import com.dc.infra.exception.sqlstate.SQLState;
import com.dc.infra.exception.type.kernel.KernelSQLException;

/**
 * Connection SQL exception.
 */
public abstract class ConnectionSQLException extends KernelSQLException {
    
    private static final long serialVersionUID = -8121891030054008537L;
    
    private static final int KERNEL_CODE = 3;
    
    protected ConnectionSQLException(final SQLState sqlState, final int errorCode, final String reason, final Object... messageArgs) {
        super(sqlState, KERNEL_CODE, errorCode, reason, messageArgs);
    }
    
    protected ConnectionSQLException(final SQLState sqlState, final int errorCode, final String reason, final Exception cause) {
        super(sqlState, KERNEL_CODE, errorCode, reason, cause);
    }
}
