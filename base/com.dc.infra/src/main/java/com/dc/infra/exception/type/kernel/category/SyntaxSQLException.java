
package com.dc.infra.exception.type.kernel.category;

import com.dc.infra.exception.sqlstate.SQLState;
import com.dc.infra.exception.type.kernel.KernelSQLException;

/**
 * Syntax SQL exception.
 */
public abstract class SyntaxSQLException extends KernelSQLException {
    
    private static final long serialVersionUID = -895529952047297716L;
    
    private static final int KERNEL_CODE = 2;
    
    protected SyntaxSQLException(final SQLState sqlState, final int errorCode, final String reason, final Object... messageArgs) {
        super(sqlState, KERNEL_CODE, errorCode, reason, messageArgs);
    }
}
