<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>summer</artifactId>
        <groupId>com.dc</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>summer.base</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>com.dc.database.parser</module>
        <module>com.dc.database.summer</module>
        <module>com.dc.infra</module>
        <module>com.dc.repository.redis</module>
        <module>com.dc.repository.mysql</module>
        <module>com.dc.utils</module>
        <module>org.cugos.wkg</module>
        <module>com.dc.test</module>
        <module>com.dc.springboot.auth</module>
        <module>com.dc.springboot.base</module>
        <module>com.dc.springboot.core</module>
        <module>com.dc.springboot.zuul</module>
        <module>com.dc.repository.h2</module>
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

</project>