
### api 接口 ak/sk 功能
#### 1. 涉及服务：
- dc-iceage-pe、dc-job、dc-job-admin、dc-summer-pe、dc-summer-pa（除了SSO和Auth控制器）

#### 2. 认证改造
- 参数说明：通过配置文件的参数控制，第一层级【auth】，第二层级【api-conf】。
- 实现逻辑：调用方在 header 中设置 auth_sign（使用公钥加密），接收方在 header 中获取 auth_sign（使用私钥解密）， 接收方根据签名中的 timestamp（毫秒级时间戳），判断当前请求是否过期。
- 加密算法：使用 SM2 （国密非对称加密）进行认证，密钥对生成器标准名称为 sm2p256v1 ，密钥类型名称 EC ，加密算法名称 SM2 ，提供者名称 BC 。生成后的密钥、生成后的密码，初始状态均为字节数组，通过 Base64 编码与解码，最终使用字符串进行传输与存储。
- 对比示例：加密前 {"timestamp":1718093506850}，加密后 BPN9wMTYoSyCZMtRwyiKA6PrMcp2fmiWUDv/5VamojglyP0WYE/SGE5LK4ACoUppbBMTz8I4e1jkzdzeWfwZodnxpGjQbiY7ieqBvMqmjBg2ujHmhRUd3hiWNvOWEgqctxM7mPJC+d10a2T5uKAj2TQFbwRwA6iaAlOZJQ==

#### 3. 使用场景
##### 3.1 默认值
```
auth:
  api-conf:
    secret-key:
    access-key:
    validity-period:
```
##### 3.1 自定义
```
auth:
  api-conf:
    secret-key: MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg01kFqNqu0wGOIWfbqsl7kLNzs3qv94evpAl+BD1lr/6gCgYIKoEcz1UBgi2hRANCAATCf+YkjBkOVqnGJvOvreVpiG6MW07rNb87ggC/XJSdu4gYHXm6BPiAbaNOglRGJzvPAFs/l/lT9Y2aer7syHAe
    access-key: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEwn/mJIwZDlapxibzr63laYhujFtO6zW/O4IAv1yUnbuIGB15ugT4gG2jToJURic7zwBbP5f5U/WNmnq+7MhwHg==
    # 30 秒过期
    validity-period: 30s
```

##### 4. 环境变量
- auth_api_conf_secret_key（默认MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgS+FAwpw+zZQTAhNhUgY7XMDmKYxshGbfmBOEBnJy1HKgCgYIKoEcz1UBgi2hRANCAAT05a4Zg/dkwKbCgEMXFSvv2utGrQGiZWWKplNOh59CSzCKVIwJVYBnWybuzfcTCMP2Axl21Q2YtkdzoTebw6KD）
- auth_api_conf_access_key（默认MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE9OWuGYP3ZMCmwoBDFxUr79rrRq0BomVliqZTToefQkswilSMCVWAZ1sm7s33EwjD9gMZdtUNmLZHc6E3m8Oigw==）
- auth_api_conf_validity_period（默认1m）