package com.dc.utils;

import com.dc.annotation.Ignore;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

@Slf4j
public class KeyUtils {

    private static final String STRING_MAP = "_";
    private static final String STRING_REDUCE = "-";

    public static String generateUniqueKey(Object obj) {

        if (obj == null) {
            return null;
        }

        Map<String, Object> map = new TreeMap<>(String::compareTo);

        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Ignore ignore = field.getDeclaredAnnotation(Ignore.class);
                if (ignore != null || Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers())) {
                    continue;
                }

                Object o = null;
                try {
                    o = field.get(obj);
                } catch (IllegalAccessException e) {
                    log.error("generateUniqueKey error : ", e);
                }
                if (o != null) {
                    if (!String.valueOf(o).isBlank()) {
                        map.put(field.getName(), o);
                    }
                }
            }
            clazz = clazz.getSuperclass(); //得到父类,然后赋给自己
        }

        return map.keySet()
                .stream()
                .map(k -> k + STRING_MAP + map.get(k))
                .reduce((s1, s2) -> s1 + STRING_REDUCE + s2)
                .orElse(null);

    }

    public static String generateUniqueKey(Object obj, String prefix) {
        String uniqueKey = generateUniqueKey(obj);
        if (prefix != null && !prefix.isBlank()) {
            uniqueKey = prefix + STRING_REDUCE + uniqueKey;
        }
        return uniqueKey;
    }

}
