package com.dc.io;

import java.io.*;
import java.nio.charset.StandardCharsets;

public class FastReader implements AutoCloseable {
    private final FileInputStream fis;
    private final InputStreamReader isr;
    private final BufferedReader br;

    public FastReader(File file) throws FileNotFoundException {
        this.fis = new FileInputStream(file);
        this.isr = new InputStreamReader(fis, StandardCharsets.UTF_8);
        this.br = new BufferedReader(isr);
    }

    public String read() throws IOException {

        return br.readLine();
    }

    public void close() throws Exception {
        br.close();
        isr.close();
        fis.close();
    }
}
