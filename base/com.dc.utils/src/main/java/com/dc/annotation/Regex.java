package com.dc.annotation;

import com.dc.utils.verification.RegexType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Regex {

    RegexType is() default RegexType.NONE;

    RegexType not() default RegexType.NONE;

}
